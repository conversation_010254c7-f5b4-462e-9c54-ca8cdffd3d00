{"version": 3, "sources": ["app.js"], "names": ["$", "Components", "prototype", "initTooltipPlugin", "fn", "tooltip", "initPopoverPlugin", "popover", "initSlimScrollPlugin", "slimScroll", "height", "position", "size", "touchScrollStep", "color", "initCustomModalPlugin", "on", "e", "preventDefault", "Custombox", "modal", "content", "target", "this", "attr", "effect", "overlay", "open", "initCounterUp", "each", "idx", "obj", "counterUp", "delay", "time", "initPeityCharts", "colors", "split", "width", "peity", "fill", "data", "initKnob", "knob", "init", "<PERSON><PERSON><PERSON><PERSON>", "window", "j<PERSON><PERSON><PERSON>", "RightSidebar", "$bootstrapStylesheet", "$appStylesheet", "$originalBSStylesheet", "$originalAppStylesheet", "self", "checked", "prop", "App", "$body", "$window", "_resetSidebarScroll", "slimscroll", "wheelStep", "initMenu", "$this", "event", "toggleClass", "removeClass", "metisMenu", "document", "closest", "length", "hasClass", "pageUrl", "location", "href", "addClass", "parent", "prev", "slideToggle", "initLayout", "Waves"], "mappings": "CAUA,SAAAA,GACA,aAEA,IAAAC,EAAA,aAGAA,EAAAC,UAAAC,kBAAA,WACAH,EAAAI,GAAAC,SAAAL,EAAA,2BAAAK,WAIAJ,EAAAC,UAAAI,kBAAA,WACAN,EAAAI,GAAAG,SAAAP,EAAA,2BAAAO,WAIAN,EAAAC,UAAAM,qBAAA,WAEAR,EAAAI,GAAAK,YAAAT,EAAA,eAAAS,WAAA,CACAC,OAAA,OACAC,SAAA,QACAC,KAAA,MACAC,gBAAA,GACAC,MAAA,aAKAb,EAAAC,UAAAa,sBAAA,WACAf,EAAA,+BAAAgB,GAAA,QAAA,SAAAC,GACAA,EAAAC,iBACA,IAAAC,UAAAC,MAAA,CACAC,QAAA,CACAC,OAAAtB,EAAAuB,MAAAC,KAAA,QACAC,OAAAzB,EAAAuB,MAAAC,KAAA,mBAEAE,QAAA,CACAZ,MAAAd,EAAAuB,MAAAC,KAAA,wBAIAG,UAKA1B,EAAAC,UAAA0B,cAAA,WACA5B,EAAAuB,MAAAC,KAAA,eAAAxB,EAAAuB,MAAAC,KAAA,cACAxB,EAAAuB,MAAAC,KAAA,cAAAxB,EAAAuB,MAAAC,KAAA,aACAxB,EAAA,6BAAA6B,KAAA,SAAAC,EAAAC,GACA/B,EAAAuB,MAAAS,UAAA,CACAC,MAAA,IACAC,KAAA,UAMAjC,EAAAC,UAAAiC,gBAAA,WACAnC,EAAA,6BAAA6B,KAAA,SAAAC,EAAAC,GACA,IAAAK,EAAApC,EAAAuB,MAAAC,KAAA,eAAAxB,EAAAuB,MAAAC,KAAA,eAAAa,MAAA,KAAA,GACAC,EAAAtC,EAAAuB,MAAAC,KAAA,cAAAxB,EAAAuB,MAAAC,KAAA,cAAA,GACAd,EAAAV,EAAAuB,MAAAC,KAAA,eAAAxB,EAAAuB,MAAAC,KAAA,eAAA,GACAxB,EAAAuB,MAAAgB,MAAA,MAAA,CACAC,KAAAJ,EACAE,MAAAA,EACA5B,OAAAA,MAIAV,EAAA,+BAAA6B,KAAA,SAAAC,EAAAC,GACA,IAAAK,EAAApC,EAAAuB,MAAAC,KAAA,eAAAxB,EAAAuB,MAAAC,KAAA,eAAAa,MAAA,KAAA,GACAC,EAAAtC,EAAAuB,MAAAC,KAAA,cAAAxB,EAAAuB,MAAAC,KAAA,cAAA,GACAd,EAAAV,EAAAuB,MAAAC,KAAA,eAAAxB,EAAAuB,MAAAC,KAAA,eAAA,GACAxB,EAAAuB,MAAAgB,MAAA,QAAA,CACAC,KAAAJ,EACAE,MAAAA,EACA5B,OAAAA,MAIAV,EAAA,mCAAA6B,KAAA,SAAAC,EAAAC,GACA/B,EAAAuB,MAAAgB,MAAA,WAIAvC,EAAA,8BAAA6B,KAAA,SAAAC,EAAAC,GACA/B,EAAAuB,MAAAgB,MAAA,OAAAvC,EAAAuB,MAAAkB,UAIAzC,EAAA,6BAAA6B,KAAA,SAAAC,EAAAC,GACA,IAAAK,EAAApC,EAAAuB,MAAAC,KAAA,eAAAxB,EAAAuB,MAAAC,KAAA,eAAAa,MAAA,KAAA,GACAC,EAAAtC,EAAAuB,MAAAC,KAAA,cAAAxB,EAAAuB,MAAAC,KAAA,cAAA,GACAd,EAAAV,EAAAuB,MAAAC,KAAA,eAAAxB,EAAAuB,MAAAC,KAAA,eAAA,GACAxB,EAAAuB,MAAAgB,MAAA,MAAA,CACAC,KAAAJ,EACAE,MAAAA,EACA5B,OAAAA,OAKAT,EAAAC,UAAAwC,SAAA,WACA1C,EAAA,wBAAA6B,KAAA,SAAAC,EAAAC,GACA/B,EAAAuB,MAAAoB,UAKA1C,EAAAC,UAAA0C,KAAA,WAEArB,KAAApB,oBACAoB,KAAAjB,oBACAiB,KAAAf,uBACAe,KAAAR,wBACAQ,KAAAK,gBACAL,KAAAY,kBACAZ,KAAAmB,YAGA1C,EAAAC,WAAA,IAAAA,EAAAD,EAAAC,WAAA4C,YAAA5C,EAzHA,CA2HA6C,OAAAC,QAGA,SAAA/C,GACA,aAEA,IAAAgD,EAAA,WACAzB,KAAA0B,qBAAAjD,EAAA,yBACAuB,KAAA2B,eAAAlD,EAAA,mBACAuB,KAAA4B,sBAAAnD,EAAA,yBAAAwB,KAAA,QACAD,KAAA6B,uBAAApD,EAAA,mBAAAwB,KAAA,SAGAwB,EAAA9C,UAAA0C,KAAA,WACA,IAAAS,EAAA9B,KAEAvB,EAAA,sBAAAgB,GAAA,SAAA,WACAO,KAAA+B,UACAD,EAAAJ,qBAAAzB,KAAA,OAAA6B,EAAAF,uBACAE,EAAAH,eAAA1B,KAAA,OAAA6B,EAAAD,wBAEApD,EAAA,qBAAAuD,KAAA,WAAA,GACAvD,EAAA,oBAAAuD,KAAA,WAAA,MAIAvD,EAAA,qBAAAgB,GAAA,SAAA,WACAO,KAAA+B,UACAD,EAAAJ,qBAAAzB,KAAA,OAAAxB,EAAAuB,MAAAkB,KAAA,YACAY,EAAAH,eAAA1B,KAAA,OAAAxB,EAAAuB,MAAAkB,KAAA,aAEAzC,EAAA,sBAAAuD,KAAA,WAAA,GACAvD,EAAA,oBAAAuD,KAAA,WAAA,MAIAvD,EAAA,oBAAAgB,GAAA,SAAA,WACAO,KAAA+B,UACAD,EAAAJ,qBAAAzB,KAAA,OAAA6B,EAAAF,uBACAE,EAAAH,eAAA1B,KAAA,OAAAxB,EAAAuB,MAAAkB,KAAA,aAEAzC,EAAA,sBAAAuD,KAAA,WAAA,GACAvD,EAAA,qBAAAuD,KAAA,WAAA,OAKAvD,EAAAgD,aAAA,IAAAA,EAAAhD,EAAAgD,aAAAH,YAAAG,EA5CA,CA8CAF,OAAAC,QAEA,SAAA/C,GACA,aAEA,IAAAwD,EAAA,WACAjC,KAAAkC,MAAAzD,EAAA,QACAuB,KAAAmC,QAAA1D,EAAA8C,SAMAU,EAAAtD,UAAAyD,oBAAA,WAEA3D,EAAA,oBAAA4D,WAAA,CACAlD,OAAA,OACAC,SAAA,QACAC,KAAA,MACAE,MAAA,UACA+C,UAAA,EACAhD,gBAAA,MAOA2C,EAAAtD,UAAA4D,SAAA,WACA,IAAAC,EAAAxC,KAGAvB,EAAA,uBAAAgB,GAAA,QAAA,SAAAgD,GACAA,EAAA9C,iBACA6C,EAAAN,MAAAQ,YAAA,kBACA,KAAAF,EAAAL,QAAApB,QACAyB,EAAAN,MAAAQ,YAAA,YAEAF,EAAAN,MAAAS,YAAA,YAIAH,EAAAJ,wBAIA3D,EAAA,cAAAmE,YAGAJ,EAAAJ,sBAGA3D,EAAA,qBAAAgB,GAAA,QAAA,SAAAC,GACAjB,EAAA,QAAAiE,YAAA,uBAGAjE,EAAAoE,UAAApD,GAAA,QAAA,OAAA,SAAAC,GACA,EAAAjB,EAAAiB,EAAAK,QAAA+C,QAAA,iCAAAC,QAIA,EAAAtE,EAAAiB,EAAAK,QAAA+C,QAAA,8BAAAC,QAAAtE,EAAAiB,EAAAK,QAAAiD,SAAA,uBACA,EAAAvE,EAAAiB,EAAAK,QAAA+C,QAAA,uBAAAC,SAIAtE,EAAA,QAAAkE,YAAA,qBACAlE,EAAA,QAAAkE,YAAA,qBAKAlE,EAAA,gBAAA6B,KAAA,WACA,IAAA2C,EAAA1B,OAAA2B,SAAAC,KAAArC,MAAA,QAAA,GACAd,KAAAmD,MAAAF,IACAxE,EAAAuB,MAAAoD,SAAA,UACA3E,EAAAuB,MAAAqD,SAAAD,SAAA,aACA3E,EAAAuB,MAAAqD,SAAAA,SAAAD,SAAA,WACA3E,EAAAuB,MAAAqD,SAAAA,SAAAC,OAAAF,SAAA,UACA3E,EAAAuB,MAAAqD,SAAAA,SAAAA,SAAAD,SAAA,aACA3E,EAAAuB,MAAAqD,SAAAA,SAAAA,SAAAA,SAAAD,SAAA,WACA3E,EAAAuB,MAAAqD,SAAAA,SAAAA,SAAAA,SAAAA,SAAAD,SAAA,gBAKA3E,EAAA,kBAAAgB,GAAA,QAAA,SAAAgD,GACAhE,EAAAuB,MAAA0C,YAAA,QACAjE,EAAA,eAAA8E,YAAA,QAQAtB,EAAAtD,UAAA6E,WAAA,WAEA,KAAAxD,KAAAmC,QAAApB,SAAAf,KAAAmC,QAAApB,SAAA,KACAf,KAAAkC,MAAAkB,SAAA,YAEA,GAAApD,KAAAkC,MAAAhB,KAAA,kBACAlB,KAAAkC,MAAAS,YAAA,aAMAV,EAAAtD,UAAA0C,KAAA,WACA,IAAAmB,EAAAxC,KACAA,KAAAwD,aACAxD,KAAAuC,WACA9D,EAAAC,WAAA2C,OAGA5C,EAAAgD,aAAAJ,OAGAmB,EAAAL,QAAA1C,GAAA,SAAA,SAAAC,GACAA,EAAAC,iBACA6C,EAAAgB,aACAhB,EAAAJ,yBAIA3D,EAAAwD,IAAA,IAAAA,EAAAxD,EAAAwD,IAAAX,YAAAW,EA3HA,CA8HAV,OAAAC,QAEA,SAAA/C,GACA,aAEA8C,OAAAC,OADAS,IAAAZ,OAFA,GAMAoC,MAAApC", "file": "app.min.js", "sourcesContent": ["/*\r\nTemplate Name: Uplon - Responsive Bootstrap 4 Admin Dashboard\r\nAuthor: CoderThemes\r\nVersion: 2.0.0\r\nWebsite: https://coderthemes.com/\r\nContact: <EMAIL>\r\nFile: Main Js File\r\n*/\r\n\r\n\r\n!function ($) {\r\n    \"use strict\";\r\n\r\n    var Components = function () { };\r\n\r\n    //initializing tooltip\r\n    Components.prototype.initTooltipPlugin = function () {\r\n        $.fn.tooltip && $('[data-toggle=\"tooltip\"]').tooltip()\r\n    },\r\n\r\n    //initializing popover\r\n    Components.prototype.initPopoverPlugin = function () {\r\n        $.fn.popover && $('[data-toggle=\"popover\"]').popover()\r\n    },\r\n\r\n    //initializing Slimscroll\r\n    Components.prototype.initSlimScrollPlugin = function () {\r\n        //You can change the color of scroll bar here\r\n        $.fn.slimScroll && $(\".slimscroll\").slimScroll({\r\n            height: 'auto',\r\n            position: 'right',\r\n            size: \"5px\",\r\n            touchScrollStep: 20,\r\n            color: '#9ea5ab'\r\n        });\r\n    },\r\n\r\n    //initializing custom modal\r\n    Components.prototype.initCustomModalPlugin = function() {\r\n        $('[data-plugin=\"custommodal\"]').on('click', function(e) {\r\n            e.preventDefault();\r\n            var modal = new Custombox.modal({\r\n                content: {\r\n                    target: $(this).attr(\"href\"),\r\n                    effect: $(this).attr(\"data-animation\")\r\n                },\r\n                overlay: {\r\n                    color: $(this).attr(\"data-overlayColor\")\r\n                }\r\n            });\r\n            // Open\r\n            modal.open();\r\n        });\r\n    },\r\n\r\n    // Counterup\r\n    Components.prototype.initCounterUp = function() {\r\n        var delay = $(this).attr('data-delay')?$(this).attr('data-delay'):100; //default is 100\r\n        var time = $(this).attr('data-time')?$(this).attr('data-time'):1200; //default is 1200\r\n         $('[data-plugin=\"counterup\"]').each(function(idx, obj) {\r\n            $(this).counterUp({\r\n                delay: 100,\r\n                time: 1200\r\n            });\r\n         });\r\n    },\r\n\r\n    //peity charts\r\n    Components.prototype.initPeityCharts = function() {\r\n        $('[data-plugin=\"peity-pie\"]').each(function(idx, obj) {\r\n            var colors = $(this).attr('data-colors')?$(this).attr('data-colors').split(\",\"):[];\r\n            var width = $(this).attr('data-width')?$(this).attr('data-width'):20; //default is 20\r\n            var height = $(this).attr('data-height')?$(this).attr('data-height'):20; //default is 20\r\n            $(this).peity(\"pie\", {\r\n                fill: colors,\r\n                width: width,\r\n                height: height\r\n            });\r\n        });\r\n        //donut\r\n         $('[data-plugin=\"peity-donut\"]').each(function(idx, obj) {\r\n            var colors = $(this).attr('data-colors')?$(this).attr('data-colors').split(\",\"):[];\r\n            var width = $(this).attr('data-width')?$(this).attr('data-width'):20; //default is 20\r\n            var height = $(this).attr('data-height')?$(this).attr('data-height'):20; //default is 20\r\n            $(this).peity(\"donut\", {\r\n                fill: colors,\r\n                width: width,\r\n                height: height\r\n            });\r\n        });\r\n\r\n        $('[data-plugin=\"peity-donut-alt\"]').each(function(idx, obj) {\r\n            $(this).peity(\"donut\");\r\n        });\r\n\r\n        // line\r\n        $('[data-plugin=\"peity-line\"]').each(function(idx, obj) {\r\n            $(this).peity(\"line\", $(this).data());\r\n        });\r\n\r\n        // bar\r\n        $('[data-plugin=\"peity-bar\"]').each(function(idx, obj) {\r\n            var colors = $(this).attr('data-colors')?$(this).attr('data-colors').split(\",\"):[];\r\n            var width = $(this).attr('data-width')?$(this).attr('data-width'):20; //default is 20\r\n            var height = $(this).attr('data-height')?$(this).attr('data-height'):20; //default is 20\r\n            $(this).peity(\"bar\", {\r\n                fill: colors,\r\n                width: width,\r\n                height: height\r\n            });\r\n         });\r\n    },\r\n\r\n    Components.prototype.initKnob = function() {\r\n        $('[data-plugin=\"knob\"]').each(function(idx, obj) {\r\n           $(this).knob();\r\n        });\r\n    },\r\n\r\n    //initilizing\r\n    Components.prototype.init = function () {\r\n        var $this = this;\r\n        this.initTooltipPlugin(),\r\n        this.initPopoverPlugin(),\r\n        this.initSlimScrollPlugin(),\r\n        this.initCustomModalPlugin(),\r\n        this.initCounterUp(),\r\n        this.initPeityCharts(),\r\n        this.initKnob();\r\n    },\r\n\r\n    $.Components = new Components, $.Components.Constructor = Components\r\n\r\n}(window.jQuery),\r\n\r\n\r\nfunction ($) {\r\n    \"use strict\";\r\n\r\n    var RightSidebar = function () { \r\n        this.$bootstrapStylesheet = $('#bootstrap-stylesheet'),\r\n        this.$appStylesheet = $('#app-stylesheet'),\r\n        this.$originalBSStylesheet = $('#bootstrap-stylesheet').attr('href'),\r\n        this.$originalAppStylesheet = $('#app-stylesheet').attr('href');\r\n    };\r\n\r\n    RightSidebar.prototype.init = function() {\r\n        var self = this;\r\n\r\n        $(\"#light-mode-switch\").on('change', function() {\r\n            if (this.checked) {\r\n                self.$bootstrapStylesheet.attr('href', self.$originalBSStylesheet);\r\n                self.$appStylesheet.attr('href', self.$originalAppStylesheet);\r\n\r\n                $(\"#dark-mode-switch\").prop('checked', false);\r\n                $(\"#rtl-mode-switch\").prop('checked', false);\r\n            }\r\n        });\r\n\r\n        $(\"#dark-mode-switch\").on('change', function() {\r\n            if (this.checked) {\r\n                self.$bootstrapStylesheet.attr('href', $(this).data('bsstyle'));\r\n                self.$appStylesheet.attr('href',  $(this).data('appstyle'));\r\n\r\n                $(\"#light-mode-switch\").prop('checked', false);\r\n                $(\"#rtl-mode-switch\").prop('checked', false);\r\n            }\r\n        });\r\n\r\n        $(\"#rtl-mode-switch\").on('change', function() {\r\n            if (this.checked) {\r\n                self.$bootstrapStylesheet.attr('href', self.$originalBSStylesheet);\r\n                self.$appStylesheet.attr('href',  $(this).data('appstyle'));\r\n\r\n                $(\"#light-mode-switch\").prop('checked', false);\r\n                $(\"#dark-mode-switch\").prop('checked', false);\r\n            }\r\n        });\r\n    },\r\n\r\n    $.RightSidebar = new RightSidebar, $.RightSidebar.Constructor = RightSidebar\r\n\r\n}(window.jQuery),\r\n\r\nfunction ($) {\r\n    'use strict';\r\n\r\n    var App = function () {\r\n        this.$body = $('body'),\r\n        this.$window = $(window)\r\n    };\r\n\r\n    /**\r\n    Resets the scroll\r\n    */\r\n    App.prototype._resetSidebarScroll = function () {\r\n        // sidebar - scroll container\r\n        $('.slimscroll-menu').slimscroll({\r\n            height: 'auto',\r\n            position: 'right',\r\n            size: \"5px\",\r\n            color: '#9ea5ab',\r\n            wheelStep: 5,\r\n            touchScrollStep: 20\r\n        });\r\n    },\r\n\r\n    /** \r\n     * Initlizes the menu - top and sidebar\r\n    */\r\n    App.prototype.initMenu = function () {\r\n        var $this = this;\r\n\r\n        // Left menu collapse\r\n        $('.button-menu-mobile').on('click', function (event) {\r\n            event.preventDefault();\r\n            $this.$body.toggleClass('sidebar-enable');\r\n            if ($this.$window.width() >= 768) {\r\n                $this.$body.toggleClass('enlarged');\r\n            } else {\r\n                $this.$body.removeClass('enlarged');\r\n            }\r\n\r\n            // sidebar - scroll container\r\n            $this._resetSidebarScroll();\r\n        });\r\n\r\n        // sidebar - main menu\r\n        $(\"#side-menu\").metisMenu();\r\n\r\n        // sidebar - scroll container\r\n        $this._resetSidebarScroll();\r\n\r\n        // right side-bar toggle\r\n        $('.right-bar-toggle').on('click', function (e) {\r\n            $('body').toggleClass('right-bar-enabled');\r\n        });\r\n\r\n        $(document).on('click', 'body', function (e) {\r\n            if ($(e.target).closest('.right-bar-toggle, .right-bar').length > 0) {\r\n                return;\r\n            }\r\n\r\n            if ($(e.target).closest('.left-side-menu, .side-nav').length > 0 || $(e.target).hasClass('button-menu-mobile')\r\n                || $(e.target).closest('.button-menu-mobile').length > 0) {\r\n                return;\r\n            }\r\n\r\n            $('body').removeClass('right-bar-enabled');\r\n            $('body').removeClass('sidebar-enable');\r\n            return;\r\n        });\r\n\r\n        // activate the menu in left side bar based on url\r\n        $(\"#side-menu a\").each(function () {\r\n            var pageUrl = window.location.href.split(/[?#]/)[0];\r\n            if (this.href == pageUrl) {\r\n                $(this).addClass(\"active\");\r\n                $(this).parent().addClass(\"mm-active\"); // add active to li of the current link\r\n                $(this).parent().parent().addClass(\"mm-show\");\r\n                $(this).parent().parent().prev().addClass(\"active\"); // add active class to an anchor\r\n                $(this).parent().parent().parent().addClass(\"mm-active\");\r\n                $(this).parent().parent().parent().parent().addClass(\"mm-show\"); // add active to li of the current link\r\n                $(this).parent().parent().parent().parent().parent().addClass(\"mm-active\");\r\n            }\r\n        });\r\n\r\n        // Topbar - main menu\r\n        $('.navbar-toggle').on('click', function (event) {\r\n            $(this).toggleClass('open');\r\n            $('#navigation').slideToggle(400);\r\n        });\r\n\r\n    },\r\n\r\n    /** \r\n     * Init the layout - with broad sidebar or compact side bar\r\n    */\r\n    App.prototype.initLayout = function () {\r\n        // in case of small size, add class enlarge to have minimal menu\r\n        if (this.$window.width() >= 768 && this.$window.width() <= 1024) {\r\n            this.$body.addClass('enlarged');\r\n        } else {\r\n            if (this.$body.data('keep-enlarged') != true) {\r\n                this.$body.removeClass('enlarged');\r\n            }\r\n        }\r\n    },\r\n\r\n    //initilizing\r\n    App.prototype.init = function () {\r\n        var $this = this;\r\n        this.initLayout();\r\n        this.initMenu();\r\n        $.Components.init();\r\n\r\n        // right sidebar\r\n        $.RightSidebar.init();\r\n\r\n        // on window resize, make menu flipped automatically\r\n        $this.$window.on('resize', function (e) {\r\n            e.preventDefault();\r\n            $this.initLayout();\r\n            $this._resetSidebarScroll();\r\n        });\r\n    },\r\n\r\n    $.App = new App, $.App.Constructor = App\r\n\r\n\r\n}(window.jQuery),\r\n//initializing main application module\r\nfunction ($) {\r\n    \"use strict\";\r\n    $.App.init();\r\n}(window.jQuery);\r\n\r\n// Waves Effect\r\nWaves.init();"]}