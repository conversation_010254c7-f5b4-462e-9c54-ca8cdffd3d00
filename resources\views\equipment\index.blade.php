@extends('warehouse.content')
@section('resource')
@vite(['resources/js/equipment.js','resources/js/style.js'])
@endsection
@section('contentho')
<style>
    /* Pagination styling */
    .pagination {
        display: flex;
        justify-content: center;
        list-style: none;
        padding: 0;
        margin-top: 15px;
    }
    .pagination .page-item {
        margin: 0 2px;
    }
    .pagination .page-item .page-link {
        color: #333;
        background-color: #fff;
        border: 1px solid #ddd;
        padding: 6px 12px;
        text-decoration: none;
        border-radius: 4px;
        cursor: pointer;
    }
    .pagination .page-item.active .page-link {
        color: #fff;
        background-color: #28a745;
        border-color: #28a745;
    }
    .pagination .page-item .page-link:hover {
        background-color: #f8f9fa;
    }
    #requisitions-pagination {
        margin-top: 15px;
    }
</style>
<div class="row bgwhite page-title-box shadow-kit mb-1">
    <div class="col d-flex align-items-center" style="max-width: fit-content;">
        <button class="button-menu-mobile text-blue-500 btn-sm">
            <i style="font-size: 30px;" class="mdi mdi-menu"></i>
        </button>
        <div class="text">
            <p class="font-bold m-0">{{ session('name') }}</p>
        </div>
    </div>
    <div class="col">
        <ul class="list-unstyled topnav-menu float-right mb-0">
            <li class="dropdown notification-list">
                <a class="nav-link dropdown-toggle" href="#" data-bs-toggle="dropdown" aria-expanded="false">
                    <i class="mdi mdi-bell-outline noti-icon"></i>
                    <span class="badge badge-danger badge-pill float-right badgenotif"></span>
                </a>
                <div class="dropdown-menu dropdown-menu-end dropdown-lg">
                    <div class="dropdown-item noti-title bg-primary">
                        <h5 class="m-0 text-white d-flex justify-content-between align-items-center">
                            Notifikasi
                            <a href="javascript:void(0);" class="text-white" id="clear-all">
                                <small></small>
                            </a>
                        </h5>
                    </div>
                    <div class="noti-scroll" id="notification-list">
                        <!-- Notifications will be dynamically inserted here -->
                    </div>
                </div>
            </li>
        </ul>
    </div>
</div>

<div class="row m-2">
    <div class="col">
        <div class="rounded-lg row p-4 bgwhite shadow-kit">
            <div class="col">
                <h2 class="h4 font-bold block">Perlengkapan Terdaftar</h2>
            </div>
            <div class="col">
                <div class="mb-4 float-right block">
                    <label for="search" class="text-sm font-medium text-gray-700">Search Equipment:</label>
                    <input type="text" id="search" name="search" class="form-control" placeholder="Search by name...">
                </div>
            </div>
            <table class="table tabel-bordered">
                <thead class="table-dark text-white">
                    <tr>
                        <th class="p-2">ID</th>
                        <th class="p-2">Name</th>
                        <th class="p-2">Description</th>
                        <th class="p-2">Aksi</th>
                    </tr>
                </thead>
                <tbody id="equipmentTableBody">
                </tbody>
            </table>
            <div id="equipment-pagination" class="mt-3"></div>
        </div>
        <div class=" rounded-lg row mt-2 p-4 bgwhite shadow-kit">
            <div class="d-flex justify-content-between align-items-center w-100 mb-4">
                <h2 class="h4 font-bold m-0">Perlengkapan Yang Ada</h2>
                <div class="d-flex gap-3">
                    <div class="max200">
                        <select id="site_id" name="site_id" class="btn btn-sm btn-success" onchange="window.applyFilters()">
                            <option value="">All Sites</option>
                            @foreach($sites as $site)
                            <option value="{{ $site->site_id }}">{{ $site->site_name }}</option>
                            @endforeach
                        </select>
                    </div>
                    <div class="max200">
                        <select id="status" name="status" class="btn btn-sm btn-success" onchange="window.applyFilters()">
                            <option value="">Tampilkan semua</option>
                            @foreach($statuses as $status)
                            <option value="{{ $status }}">{{ $status }}</option>
                            @endforeach
                        </select>
                    </div>
                </div>
            </div>

            <table class="table">
                <thead class="table-dark text-white tabel-bordered">
                    <tr>
                        <th class="p-2">No</th>
                        <th class="p-2">Nama Perlengkapan</th>
                        <th class="p-2">Site</th>
                        <th class="p-2">Status</th>
                        <th class="p-2">Jumlah</th>
                        <th class="p-2">Tanggal Dibuat</th>
                        <th>Aksi</th>
                    </tr>
                </thead>
                <tbody id="equipmentStockTableBody">
                </tbody>
            </table>
            <div id="stock-pagination" class="mt-3"></div>
        </div>
    </div>
    <div class="col max500">
        <div class="ml-2 bg-white shadow-kit rounded-lg p-4 m-0">
            <h2 class="h6 mt-4">*Buat Data Perlengkapan Yang Baru</h2>
            <form id="equipmentForm" class="space-y-4">
                <input type="hidden" id="equipment_id" name="equipment_id">
                <div>
                    <label for="name" class="block text-sm font-medium text-gray-700">Nama Peralatan</label>
                    <input type="text" id="name" name="name" required class="form-control">
                </div>
                <div>
                    <label for="description" class="block text-sm font-medium text-gray-700">Keterangan <span class="text-green-500">*Optional</span> </label>
                    <textarea id="description" name="description" class="form-control"></textarea>
                </div>

                <div class="flex space-x-2">
                    <button type="button" onclick="saveEquipment()" class="btn btn-primary">Simpan</button>
                    <button type="button" onclick="closeForm()" class="btn btn-danger">Batal</button>
                </div>
            </form>
        </div>
        <div class="ml-2 bg-white shadow-kit rounded-lg p-4 m-0 mt-1">
            <h2 class="text-xl font-bold mb-4">Perlengkapan Stock Form</h2>
            <form id="equipmentStockForm" class="space-y-4" data-stock-id="">
                <div>
                    <label for="equipment_id_stock" class="block text-sm font-medium text-gray-700">Perlengkapan</label>
                    <select id="equipment_id_stock" name="equipment_id" class="form-control">
                        @foreach($equipments as $equipment)
                        <option value="{{ $equipment->id }}">{{ $equipment->name }}</option>
                        @endforeach
                    </select>
                </div>

                <div>
                    <label for="quantity" class="block text-sm font-medium text-gray-700">Jumlah</label>
                    <input type="number" class="form-control" min="1" value="1" id="quantity" name="quantity">
                </div>

                <div>
                    <label for="status_stock" class="block text-sm font-medium text-gray-700">Status Peralatan</label>
                    <select id="status_stock" name="status" class="form-control" required>
                        <option value="">Pilih Status Keadaan Peralatan</option>
                        <option value="Baik">Baik</option>
                        <option value="Cukup Baik">Cukup Baik</option>
                        <option value="Kurang Baik">Kurang Baik</option>
                        <option value="Rusak">Rusak</option>
                    </select>
                </div>

                <div>
                    <label for="site_id_stock" class="block text-sm font-medium text-gray-700">Site Tujuan</label>
                    <select id="site_id_stock" name="site_id" class="form-control" required>
                        <option value="">Pilih Site Tujuan</option>
                        @foreach($sites as $site)
                        <option value="{{ $site->site_id }}">{{ $site->site_name }}</option>
                        @endforeach
                    </select>
                </div>

                <div>
                    <label for="received_at_stock" class="block text-sm font-medium text-gray-700">Tanggal</label>
                    <input value="{{ date('Y-m-d') }}" type="date" id="received_at_stock" name="received_at" class="form-control">
                </div>
                <button type="button" onclick="saveEquipmentStock()" class="btn btn-primary">Simpan</button>
                <button type="button" onclick="cancelsave()" class="btn btn-secondary">Batal</button>
            </form>
        </div>
    </div>
</div>
@endsection