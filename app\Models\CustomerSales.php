<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class CustomerSales extends Model
{
    use HasFactory;

    protected $table = 'customersales';

    protected $fillable = [
        'code',
        'nama_customer',
        'alamat',
    ];

    protected $appends = ['total_invoice', 'total_pembayaran', 'saldo_piutang'];

    // Relationship with Penawaran (still using nama_customer for backward compatibility)
    public function penawarans()
    {
        return $this->hasMany(Penawaran::class, 'customer', 'nama_customer');
    }

    // Relationship with Invoices (now using customer_code)
    public function invoices()
    {
        return $this->hasMany(Invoice::class, 'customer_code', 'code');
    }

    /**
     * Calculate total invoice amount for this customer
     */
    public function getTotalInvoiceAttribute()
    {
        return $this->invoices()->get()->sum('total_amount');
    }

    /**
     * Calculate total payments (from invoices with 'Lunas' status only)
     */
    public function getTotalPembayaranAttribute()
    {
        return $this->invoices()
            ->where('payment_status', 'Lunas')
            ->get()
            ->sum('total_amount');
    }

    /**
     * Calculate outstanding balance (total_invoice - total_pembayaran)
     */
    public function getSaldoPiutangAttribute()
    {
        return $this->total_invoice - $this->total_pembayaran;
    }

    /**
     * Scope to search customers by code or name
     */
    public function scopeSearch($query, $search)
    {
        return $query->where(function ($q) use ($search) {
            $q->where('code', 'LIKE', "%{$search}%")
              ->orWhere('nama_customer', 'LIKE', "%{$search}%");
        });
    }
}
