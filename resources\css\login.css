:root {
    --primary-color: rgba(255, 255, 255, 0.9);
    --secondary-color: rgba(255, 255, 255, 0.3);
}

/* <PERSON><PERSON>k partikel latar belakang */
.background-effects {
    position: absolute;
    width: 100%;
    height: 100%;
    z-index: 1;
}

.particle {
    position: absolute;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.1);
    animation: float 20s infinite linear;
}

@keyframes float {
    0% {
        transform: translateY(0) translateX(0) scale(0);
    }

    100% {
        transform: translateY(-100vh) translateX(100vw) scale(1);
    }
}

.container {
    position: relative;
    z-index: 2;
    display: flex;
    backdrop-filter: blur(10px);
    background-color: rgba(15, 18, 179, 0.24);
    border-radius: 25px;
    border: 1px solid var(--secondary-color);
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
    overflow: hidden;
    flex-direction: row;
    width: 80%;
    max-width: 900px;
}

.login-section {
    padding: 40px;
    width: 50%;
    box-sizing: border-box;
}

.login-box {
    text-align: center;
}

h2 {
    color: var(--primary-color);
    margin-bottom: 30px;
    font-size: 2.5em;
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.1);
}

.input-group {
    position: relative;
    margin-bottom: 25px;
}

.input-group i {
    position: absolute;
    left: 15px;
    top: 50%;
    transform: translateY(-50%);
    color: var(--primary-color);
}

input {
    width: 100%;
    padding: 15px 15px 15px 45px;
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid var(--secondary-color);
    border-radius: 8px;
    color: var(--primary-color);
    font-size: 1em;
    backdrop-filter: blur(5px);
    transition: all 0.3s ease;
    box-sizing: border-box;
}

input::placeholder {
    color: rgba(255, 255, 255, 0.7);
}

input:focus {
    outline: none;
    background: rgba(15, 5, 77, 0.445);
    color: rgb(251, 255, 0);
    border-color: rgb(255, 255, 255);
}

button {
    width: 100%;
    padding: 15px;
    background: rgb(40, 21, 211);
    border: 1px solid var(--secondary-color);
    border-radius: 8px;
    color: var(--primary-color);
    font-size: 1.1em;
    cursor: pointer;
    transition: all 0.3s ease;
    margin-top: 15px;
    backdrop-filter: blur(5px);
    box-sizing: border-box;
}

button:hover {
    background: rgb(255, 255, 255);
    color: blueviolet;
    transform: translateY(-2px);
}

.footer a {
    color: rgba(255, 255, 255, 0.7);
    text-decoration: none;
    font-size: 0.9em;
    transition: color 0.3s ease;
}

.footer a:hover {
    color: var(--primary-color);
}

.chart-section {
    padding: 20px;
    display: flex;
    align-items: center;
    border-left: 1px solid var(--secondary-color);
    width: 50%;
    box-sizing: border-box;
}

.chart-section img {
    max-width: 100%;
    height: auto;
    border-radius: 10px;
}

/* Media query for smaller screens */
@media (max-width: 768px) {
    .container {
        flex-direction: column;
        width: 95%;
    }

    .login-section {
        width: 100%;
        padding: 20px;
    }

    .chart-section {
        display: none;
        /* Hide the chart section on smaller screens */
    }

    /* Ensure login section takes full width when chart section is hidden */
    .login-section {
        width: 100%;
        border-right: none;
        /* Remove the right border if it exists */
    }
}