<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('cashier_transactions', function (Blueprint $table) {
            $table->id();
            $table->string('user_id', 50);
            $table->foreign('user_id')
                ->references('employee_id')
                ->on('users')
                ->onDelete('cascade');
            $table->enum('type', ['pengeluaran', 'penerimaan']);
            $table->text('description')->nullable();
            $table->decimal('amount', 15, 2);
            $table->string('attachment', 255)->nullable();
            $table->datetime('transaction_date');
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('cashier_transactions');
    }
};
