@extends('warehouse.content')
@section('contentho')

<div class="row bgwhite page-title-box shadow-kit mb-1">
    <div class="col d-flex align-items-center" style="max-width: fit-content;">
        <button class="button-menu-mobile text-blue-500 btn-sm">
            <i style="font-size: 30px;" class="mdi mdi-menu"></i>
        </button>
        <div class="text">
            <p class="font-bold m-0">{{ session('name') }}</p>
        </div>
    </div>

    <div class="col">
        <ul class="list-unstyled topnav-menu float-right mb-0">
            <li class="dropdown notification-list">
                <a class="nav-link dropdown-toggle" href="#" data-bs-toggle="dropdown" aria-expanded="false">
                    <i class="mdi mdi-bell-outline noti-icon"></i>
                    <span class="badge badge-danger badge-pill float-right badgenotif"></span>
                </a>
                <div class="dropdown-menu dropdown-menu-end dropdown-lg">
                    <div class="dropdown-item noti-title bg-primary">
                        <h5 class="m-0 text-white d-flex justify-content-between align-items-center">
                            Notifikasi
                            <a href="javascript:void(0);" class="text-white" id="clear-all">
                                <small></small>
                            </a>
                        </h5>
                    </div>
                    <div class="noti-scroll" id="notification-list">
                        <!-- Notifications will be dynamically inserted here -->
                    </div>
                </div>
            </li>
        </ul>
    </div>
</div>

<div class="row p-2">
    <div class="col">
        <div class="shadow-kit bgwhite p-4">
            <h1 class="h4">Log Aktivitas Site</h1>
            <div class="d-flex flex-wrap mb-3">
                <select class="form-control max300 mr-4 mb-2" id="siteFilter" onchange="searchLogs()">
                    <option value="all">Semua Site</option>
                    @foreach ($sites as $site)
                    <option value="{{ $site->site_id }}" {{ $site->site_id == $siteId ? 'selected' : '' }}>
                        {{ $site->site_name }}
                    </option>
                    @endforeach
                </select>
                <input class="form-control max300 mr-4 mb-2" type="text" id="searchInput" placeholder="Cari Log..." onkeyup="searchLogs()">
                <input class="form-control max300 mr-4 mb-2" type="date" id="dateFilter" value="{{ date('Y-m-d') }}" onchange="searchLogs()">
            </div>
            <table class="table mt-3" id="logTable">
                <thead class="table-dark text-white">
                    <tr>
                        <th class="p-2">No</th>
                        <th class="p-2">Nama</th>
                        <th class="p-2">Aksi</th>
                        <th class="p-2">Deskripsi</th>
                        <th class="p-2">Tabel Diperbaharui</th>
                        <th class="p-2">Waktu</th>
                    </tr>
                </thead>
                <tbody>
                </tbody>
            </table>
            <div id="logs-pagination" class="mt-3">
                <!-- Custom pagination will be rendered here by JavaScript -->
            </div>
        </div>
    </div>

</div>

<style>
    /* Pagination styling */
    .pagination {
        display: flex;
        justify-content: center;
        list-style: none;
        padding: 0;
        margin-top: 15px;
    }

    .pagination .page-item {
        margin: 0 2px;
    }

    .pagination .page-item .page-link {
        color: #333;
        background-color: #fff;
        border: 1px solid #ddd;
        padding: 6px 12px;
        text-decoration: none;
        border-radius: 4px;
        cursor: pointer;
    }

    .pagination .page-item.active .page-link {
        color: #fff;
        background-color: #28a745;
        border-color: #28a745;
    }

    .pagination .page-item .page-link:hover {
        background-color: #f8f9fa;
    }

    #logs-pagination {
        margin-top: 15px;
    }
</style>

@endsection
@vite('resources/js/adminho/Log.js')