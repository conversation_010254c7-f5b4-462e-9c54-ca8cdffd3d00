<!DOCTYPE html>
<html>

<head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
    <title>Surat Pengeluaran Barang</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            font-size: 11px;
            line-height: 1.4;
            margin: 20px;
        }

        /* Header styles */
        .header-table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 15px;
        }

        .header-table td {
            vertical-align: top;
            padding: 2px 5px;
            border: none;
        }

        .header-table .logo {
            width: 100px;
            text-align: center;
        }

        .header-table .company-details {
            text-align: left;
            padding-left: 10px;
            width: 60%;
        }

        .header-table .company-details h2 {
            margin: 0 0 5px 0;
            font-size: 14px;
            font-weight: bold;
        }

        .header-table .company-details p {
            margin: 0;
            font-size: 10px;
            line-height: 1.3;
        }

        .header-table .document-number {
            text-align: right;
            width: 20%;
        }

        /* Title styles */
        .document-title {
            text-align: center;
            font-size: 16px;
            font-weight: bold;
            margin: 15px 0;
            text-transform: uppercase;
        }

        .document-title-underline {
            border-bottom: 1px solid #000;
            margin-bottom: 15px;
        }

        /* Info table styles */
        .info-table {
            width: 48%;
            border-collapse: collapse;
            margin-bottom: 15px;
            float: left;
        }

        .info-table td {
            border: 1px solid #000;
        }

        /* Number table styles */
        .number-table {
            width: 30%;
            border-collapse: collapse;
            margin-bottom: 15px;
            float: right;
        }

        .number-table td {
            padding: 3px;
            border: 1px solid #000;
        }

        /* Clearfix for floating tables */
        .clearfix::after {
            content: "";
            clear: both;
            display: table;
        }

        /* Main table styles */
        .main-table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 20px;
        }

        .main-table th,
        .main-table td {
            border: 1px solid #000;
            padding: 5px;
            text-align: center;
            font-size: 10px;
        }

        .main-table th {
            background-color: #f2f2f2;
            font-weight: bold;
        }

        /* Adjust for landscape orientation */
        @page {
            size: landscape;
        }

        /* Signature section */
        .signature-table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 5px;
        }

        .signature-table td {
            width: 33.33%;
            text-align: center;
            vertical-align: bottom;
            padding-top: 5px;
            font-weight: bold;
            font-size: 10px;
        }

        /* Other styles */
        .transaction-header {
            background-color: #e6f2ff;
            font-weight: bold;
        }

        .parts-table {
            margin-bottom: 15px;
            width: 100%;
        }

        .parts-table th {
            background-color: #f2f2f2;
        }

        .status-badge {
            padding: 2px 4px;
            font-size: 10px;
            display: inline-block;
        }

        .text-right {
            text-align: right;
        }

        .text-center {
            text-align: center;
        }

        .text-left {
            text-align: left;
        }

        .total-row {
            font-weight: bold;
            background-color: #f8f9fa;
        }
    </style>
</head>

<body>
    <!-- Header with company info -->
    <table class="header-table">
        <tr>
            <td class="logo">
                <!-- Use a base64 encoded logo or a placeholder -->
                <div style="border:1px solid #ccc; width:70px; height: 50px; margin:auto; line-height:50px; text-align:center; font-size:9px; color: #aaa;"><img style="max-width: 100px;" src="{{ public_path('assets/images/logo-small.png') }}" alt=""></div>
            </td>
            <td class="company-details">
                <h2>PT. PUTERA WIBOWO BORNEO</h2>
                <p>
                    <strong>Address : Jl. Palam Raya No. 10B Kel. Guntung Manggis,<br>
                        Kec. Landasan Ulin Banjar Baru, Kalimantan Selatan<br>
                        Kode Pos 70721</strong><br>
                    <strong>Phone : (+62) 811-5139-971</strong><br>
                    <strong>Email : <EMAIL></strong>
                </p>
            </td>
        </tr>
    </table>

    <!-- Document Title -->
    <div class="document-title-underline"></div>
    <div class="document-title">SURAT PENGELUARAN BARANG</div>

    <!-- Document Info -->
    <div>
        <table class="info-table">
            <tr>
                <td style="margin: 0; padding: 0px 0px 0px 4px;">NO SPB OUT</td>
                <td style="margin: 0; padding: 0px 0px 0px 4px;">: PWB-{{ $site_name ?? 'PPA' }}/{{ \Carbon\Carbon::now()->format('m/Y') }}/{{ $transactions[0]->id ?? '000' }}</td>
            </tr>
            <tr>
                <td style="margin: 0; padding: 0px 0px 0px 4px;">LOKASI</td>
                <td style="margin: 0; padding: 0px 0px 0px 4px;">: JOB SITE {{ $site_name ?? 'PPA' }}</td>
            </tr>
            <tr>
                <td style="margin: 0; padding: 0px 0px 0px 4px;">TANGGAL</td>
                <td style="margin: 0; padding: 0px 0px 0px 4px;">: {{ \Carbon\Carbon::parse($date ?? \Carbon\Carbon::now())->format('d/m/Y') }}</td>
            </tr>
        </table>

        <table class="number-table">
            <tr>
                <td  style="max-width: 10px">NO. WO</td>
                <td style="max-width: 10px">: </td>
            </tr>
            <tr>
                <td>NO. DO/BAPP</td>
                <td>: DO-PPA-{{ date('Y') }}</td>
            </tr>
        </table>
        <div class="clearfix"></div>
    </div>

    <!-- Main Content Table -->
    <table class="main-table">
        <thead>
            <tr>
                <th style="width:5%;">NO</th>
                <th style="width:15%;">PART NO</th>
                <th style="width:40%;">DESCRIPTION</th>
                <th style="width:10%;">QTY</th>
                <th style="width:10%;">SATUAN</th>
                <th style="width:10%;">NO. UNIT</th>
                <th style="width:10%;">KETERANGAN</th>
            </tr>
        </thead>
        <tbody>
            @if(count($transactions) > 0)
            @php $no = 1; @endphp
            @foreach($transactions as $transaction)
            @php
            $partCount = count($transaction->parts);
            $firstPart = true;
            @endphp

            @foreach($transaction->parts as $partIndex => $part)
            <tr>
                <td style="margin: 0; padding: 0;">{{ $no++ }}</td>
                <td style="margin: 0; padding: 0;">{{ $part->partInventory->part->part_code }}</td>
                <td class="text-left" style="margin: 0; padding: 0;">{{ $part->partInventory->part->part_name }}</td>
                <td style="margin: 0; padding: 0;">{{ $part->quantity }}</td>
                <td style="margin: 0; padding: 0;">{{ $part->eum }}</td>
                @if($firstPart)
                <td style="margin: 0; padding: 0;" rowspan="{{ $partCount }}">{{ $transaction->unit->unit_code }}</td>
                <td style="margin: 0; padding: 0;" rowspan="{{ $partCount }}">{{ $transaction->remarks ?? 'MAINTENANCE ' . $transaction->unit->unit_type }}</td>
                @php $firstPart = false; @endphp
                @endif
            </tr>
            @endforeach
            @endforeach

            <!-- Add empty rows to make the table look complete -->
            @php $totalRows = $no - 1; @endphp
            @for($i = $totalRows; $i < 10; $i++)
                <tr>
                <td>&nbsp;</td>
                <td></td>
                <td></td>
                <td></td>
                <td></td>
                <td></td>
                <td></td>
                </tr>
                @endfor
                @else
                <tr>
                    <td colspan="7" class="text-center">No parts found.</td>
                </tr>
                <!-- Add empty rows to make the table look complete -->
                @for($i = 0; $i < 9; $i++)
                    <tr>
                    <td>&nbsp;</td>
                    <td></td>
                    <td></td>
                    <td></td>
                    <td></td>
                    <td></td>
                    <td></td>
                    </tr>
                    @endfor
                    @endif
        </tbody>
    </table>

    <!-- Signature Section -->
    <table class="signature-table">
        <tr>
            <td>YANG MENYERAHKAN</td>
            <td>MENGETAHUI</td>
            <td>PENERIMA</td>
        </tr>
        <tr>
            <td style="padding-top: 60px;">Admin Logistik PWB</td>
            <td style="padding-top: 60px;">Logistik PPA</td>
            <td style="padding-top: 60px;">Dept. Plant PPA</td>
        </tr>
    </table>

    <!-- Hidden original content for reference -->
    <div style="display: none;">
        @if(count($transactions) > 0)
        <div class="footer">
            <p><strong>Grand Total: {{ $totalPrice }}</strong></p>
        </div>
        @endif
    </div>
</body>

</html>