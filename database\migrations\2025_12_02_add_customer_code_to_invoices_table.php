<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('invoices', function (Blueprint $table) {
            // Add customer_code column if it doesn't exist
            if (!Schema::hasColumn('invoices', 'customer_code')) {
                $table->string('customer_code')->nullable()->after('customer');
            }
        });

        // Migrate existing data: try to match customer names to customer codes
        $this->migrateExistingData();
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('invoices', function (Blueprint $table) {
            if (Schema::hasColumn('invoices', 'customer_code')) {
                $table->dropColumn('customer_code');
            }
        });
    }

    /**
     * Migrate existing invoice data to use customer codes
     */
    private function migrateExistingData(): void
    {
        // Get all invoices with customer names
        $invoices = DB::table('invoices')->whereNotNull('customer')->get();

        foreach ($invoices as $invoice) {
            // Try to find a matching customer by name
            $customer = DB::table('customersales')
                ->where('nama_customer', $invoice->customer)
                ->first();

            if ($customer) {
                // Update the invoice with the customer code
                DB::table('invoices')
                    ->where('id', $invoice->id)
                    ->update(['customer_code' => $customer->code]);
            }
        }
    }
};
