<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class EquipmentStock extends Model
{
    use HasFactory;

    protected $fillable = [
        'equipment_id',
        'status',
        'quantity',
        'site_id',
        'received_at',
    ];

    public function equipment()
    {
        return $this->belongsTo(Equipment::class);
    }

    public function site()
    {
        return $this->belongsTo(Site::class, 'site_id', 'site_id'); // Assuming you have a Site model and site_id is the primary key
    }
}
