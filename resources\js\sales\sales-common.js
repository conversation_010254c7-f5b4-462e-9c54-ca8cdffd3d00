// Common JavaScript for sales pages
// This file intentionally does not include notification functionality

document.addEventListener('DOMContentLoaded', function() {
    // Initialize Bootstrap components
    const dropdownElementList = [].slice.call(document.querySelectorAll('.dropdown-toggle'));
    dropdownElementList.map(function (dropdownToggleEl) {
        return new bootstrap.Dropdown(dropdownToggleEl);
    });

    // Remove any neumorphism class if it was previously applied
    document.body.classList.remove('neumorphism-active');

    // Clear any stored neumorphism preference for sales pages
    if (localStorage.getItem('neumorphismActive')) {
        localStorage.removeItem('neumorphismActive');
    }
});
