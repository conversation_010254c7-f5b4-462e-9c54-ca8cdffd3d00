<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('invoices', function (Blueprint $table) {
            $table->string('model_unit')->nullable()->after('lokasi');
            $table->string('hmkm')->nullable()->after('model_unit');
            $table->string('transfer_to')->nullable()->after('notes');
            $table->string('bank_account')->nullable()->after('transfer_to');
            $table->string('bank_branch')->nullable()->after('bank_account');
            $table->foreignId('penawaran_id')->nullable()->after('id');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('invoices', function (Blueprint $table) {
            $table->dropColumn(['model_unit', 'hmkm', 'transfer_to', 'bank_account', 'bank_branch']);
            $table->dropColumn('penawaran_id');
        });
    }
};
