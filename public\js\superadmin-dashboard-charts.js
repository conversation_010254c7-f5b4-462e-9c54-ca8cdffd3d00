/**
 * Superadmin Dashboard Charts
 * Handles rendering charts for the dashboard
 */

document.addEventListener('DOMContentLoaded', function() {
    // Initialize monthly invoice chart
    initMonthlyInvoiceChart();

    // Listen for date range change events
    document.addEventListener('dateRangeChanged', function() {
        // Reload charts when date range changes
        initMonthlyInvoiceChart();
    });
});

/**
 * Initialize monthly invoice chart
 */
function initMonthlyInvoiceChart() {
    const chartElement = document.getElementById('monthlyInvoiceChart');
    if (!chartElement) return;

    // Get chart data from hidden element
    const dataElement = document.getElementById('monthlyInvoiceData');
    if (!dataElement) return;

    const monthlyInvoiceData = JSON.parse(dataElement.getAttribute('data-monthly-invoice'));
    const currentYear = dataElement.getAttribute('data-current-year');

    // Destroy existing chart if it exists
    if (window.monthlyInvoiceChart) {
        window.monthlyInvoiceChart.destroy();
    }

    // Create the chart
    const ctx = chartElement.getContext('2d');
    window.monthlyInvoiceChart = new Chart(ctx, {
        type: 'bar',
        data: {
            labels: monthlyInvoiceData.labels,
            datasets: monthlyInvoiceData.datasets || [{
                label: `Pendapatan ${currentYear}`,
                data: monthlyInvoiceData.values || [],
                backgroundColor: 'rgba(34, 82, 151, 0.7)',
                borderColor: 'rgba(34, 82, 151, 1)',
                borderWidth: 1
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            scales: {
                x: {
                    stacked: true
                },
                y: {
                    stacked: true,
                    beginAtZero: true,
                    ticks: {
                        callback: function(value) {
                            return 'Rp ' + formatNumber(value);
                        }
                    }
                }
            },
            plugins: {
                tooltip: {
                    callbacks: {
                        label: function(context) {
                            return context.dataset.label + ': Rp ' + formatNumber(context.raw);
                        },
                        footer: function(tooltipItems) {
                            let sum = 0;
                            tooltipItems.forEach(function(tooltipItem) {
                                sum += tooltipItem.parsed.y;
                            });
                            return 'Total: Rp ' + formatNumber(sum);
                        }
                    }
                },
                legend: {
                    position: 'bottom',
                    labels: {
                        boxWidth: 12,
                        padding: 15
                    }
                }
            }
        }
    });
}

/**
 * Format number with thousand separators
 *
 * @param {number} number
 * @returns {string}
 */
function formatNumber(number) {
    return new Intl.NumberFormat('id-ID').format(number);
}
