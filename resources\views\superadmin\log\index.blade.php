<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="utf-8" />
    <title>Log Aktivitas Semua Site</title>
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta content="Portal PWB" name="description" />
    <meta content="PWB" name="author" />
    <meta http-equiv="X-UA-Compatible" content="IE=edge" />
    <meta name="csrf-token" content="{{ csrf_token() }}">
    <link rel="shortcut icon" href="{{ asset('assets/images/logo-small.png') }}">
    <!-- Styles -->
    @vite([
        'resources/assets/css/bootstrap.min.css',
        'resources/assets/css/icons.min.css',
        'resources/assets/css/app.min.css',
        'resources/css/app.css'
    ])
    <style>
        body {
            background: url('{{ asset('assets/images/homewalpaper.jpg') }}');
            background-size: cover;
            background-position: center;
            background-attachment: fixed;
            min-height: 100vh;
        }

        .header {
            background-color: #fff;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
            padding: 15px 0;
            position: sticky;
            top: 0;
            z-index: 1000;
        }

        .header-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .header-title h1 {
            font-size: 1.5rem;
            margin: 0;
            color: #333;
        }

        .header-actions .btn {
            margin-left: 10px;
        }

        .main-content {
            padding: 30px 0;
        }

        .filter-card {
            background-color: #fff;
            border-radius: 10px;
            box-shadow: 0 0 10px rgba(0, 0, 0, 0.05);
            padding: 20px;
            margin-bottom: 30px;
        }

        .log-card {
            background-color: #fff;
            border-radius: 10px;
            box-shadow: 0 0 10px rgba(0, 0, 0, 0.05);
            padding: 20px;
        }

        .table-responsive {
            overflow-x: auto;
        }

        .table th {
            background-color: #343a40;
            color: #fff;
            font-weight: 500;
            border: none;
        }

        .table td {
            vertical-align: middle;
        }

        .pagination {
            margin-top: 20px;
            justify-content: center;
        }

        .pagination .page-item.active .page-link {
            background-color: #343a40;
            border-color: #343a40;
        }

        .pagination .page-link {
            color: #343a40;
        }

        .pagination .page-link:hover {
            background-color: #e9ecef;
        }

        .skeleton-row {
            animation: pulse 1.5s infinite;
            background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
            background-size: 200% 100%;
            height: 40px;
            margin-bottom: 10px;
            border-radius: 5px;
        }

        @keyframes pulse {
            0% {
                background-position: -200% 0;
            }
            100% {
                background-position: 200% 0;
            }
        }

        @media (max-width: 768px) {
            .header-content {
                flex-direction: column;
                align-items: flex-start;
            }

            .header-actions {
                margin-top: 15px;
                width: 100%;
                display: flex;
                justify-content: flex-end;
            }

            .filter-row {
                flex-direction: column;
            }

            .filter-col {
                margin-bottom: 15px;
            }
        }
    </style>
</head>

<body>
    <div class="header">
        <div class="container">
            <div class="header-content">
                <div class="header-title">
                    <h1><i class="mdi mdi-history"></i> Log Aktivitas Semua Site</h1>
                </div>
                <div class="header-actions">
                    <a href="{{ route('superadmin.log.logout') }}" class="btn btn-danger">
                        <i class="mdi mdi-logout"></i> Keluar
                    </a>
                </div>
            </div>
        </div>
    </div>

    <div class="main-content">
        <div class="container">
            <div class="filter-card">
                <div class="row filter-row">
                    <div class="col-md-3 col-sm-6 filter-col">
                        <div class="mb-3">
                            <label for="site_filter" class="form-label">Site</label>
                            <select class="btn btn-primary" id="site_filter">
                                <option value="all">Semua Site</option>
                                @foreach($sites as $site)
                                <option value="{{ $site->site_id }}">{{ $site->site_name }}</option>
                                @endforeach
                            </select>
                        </div>
                    </div>
                    <div class="col-md-3 col-sm-6 filter-col">
                        <div class="mb-3">
                            <label for="start_date" class="form-label">Tanggal Mulai</label>
                            <input type="date" class="form-control" id="start_date" value="{{ date('Y-m-d') }}">
                        </div>
                    </div>
                    <div class="col-md-3 col-sm-6 filter-col">
                        <div class="mb-3">
                            <label for="end_date" class="form-label">Tanggal Akhir</label>
                            <input type="date" class="form-control" id="end_date" value="{{ date('Y-m-d') }}">
                        </div>
                    </div>
                    <div class="col-md-3 col-sm-6 filter-col">
                        <div class="mb-3">
                            <label for="search_input" class="form-label">Cari</label>
                            <input type="text" class="form-control" id="search_input" placeholder="Cari log...">
                        </div>
                    </div>
                </div>
            </div>

            <div class="log-card">
                <div class="table-responsive">
                    <table class="table table-bordered" id="logTable">
                        <thead>
                            <tr>
                                <th class="p-2">No</th>
                                <th class="p-2">Site</th>
                                <th class="p-2">Nama</th>
                                <th class="p-2">Aksi</th>
                                <th class="p-2">Deskripsi</th>
                                <th class="p-2">Tabel</th>
                                <th class="p-2">Waktu</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td colspan="7" class="text-center">Memuat data...</td>
                            </tr>
                        </tbody>
                    </table>
                </div>
                <div id="pagination-container" class="mt-3">
                    <!-- Pagination will be rendered here by JavaScript -->
                </div>
            </div>
        </div>
    </div>

    <!-- Scripts -->
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
    @vite('resources/js/superadmin-log.js')
</body>

</html>
