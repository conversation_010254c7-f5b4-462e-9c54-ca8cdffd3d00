@extends('layouts.app')

@section('content')
<div class="container">
    <div class="row justify-content-center">
        <div class="col-md-8">
            <div class="card" style="background-color: #e0e5ec; border-radius: 30px; box-shadow: 20px 20px 60px #bec5d0, -20px -20px 60px #ffffff; border: none; overflow: hidden; position: relative; width: 100%; max-width: 500px; margin: 0 auto;">
                <div class="card-header" style="background-color: #e0e5ec; border-bottom: none; position: relative; padding: 30px 20px 10px; text-align: center;">
                    <div style="display: flex; justify-content: center; align-items: center; margin-bottom: 20px; width: 100%;">
                        <img src="{{ asset('assets/images/logo-small.png') }}" alt="PWB Logo" style="max-width: 100px; filter: drop-shadow(3px 3px 5px rgba(0, 0, 0, 0.3)); border-radius: 50%; padding: 15px; background-color: #e0e5ec; box-shadow: 8px 8px 16px #bec5d0, -8px -8px 16px #ffffff; margin: 0 auto;">
                    </div>
                    <h4 class="mb-0" style="color: #eb3124; font-weight: 600; text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.1); text-align: center; width: 100%;">Error</h4>
                </div>
                <div class="card-body" style="padding: 30px; text-align: center;">
                    <div class="alert" style="background-color: #e0e5ec; border: none; border-radius: 15px; box-shadow: inset 4px 4px 8px #bec5d0, inset -4px -4px 8px #ffffff; padding: 20px; text-align: center;">
                        <h5 style="color: #510968; font-weight: 600; margin-bottom: 15px; text-align: center;">{{ $message }}</h5>
                        @if(config('app.debug') && isset($error))
                            <hr style="border-color: rgba(81, 9, 104, 0.2);">
                            <p class="mb-0" style="color: #225297; text-align: center;"><strong>Detail Error:</strong> {{ $error }}</p>
                        @endif
                    </div>

                    <div class="text-center mt-4">
                        <a href="{{ $back_url ?? '/' }}" class="btn-back" style="border: none; position: relative; background-color: #e0e5ec; border-radius: 50px; padding: 15px 35px; font-weight: 500; color: #225297; box-shadow: 8px 8px 16px #bec5d0, -8px -8px 16px #ffffff; transition: all 0.3s ease; font-size: 16px; text-decoration: none; display: inline-block; letter-spacing: 0.5px; margin: 0 auto;">
                            <i class="mdi mdi-arrow-left"></i> Kembali
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
    body {
        background-color: #e0e5ec;
        font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        margin: 0;
        padding: 0;
    }

    .container {
        width: 100%;
        max-width: 1200px;
        margin: 0 auto;
        display: flex;
        justify-content: center;
        align-items: center;
    }

    .row {
        width: 100%;
        display: flex;
        justify-content: center;
    }

    .col-md-8 {
        width: 100%;
        max-width: 800px;
        display: flex;
        justify-content: center;
    }

    .btn-back:hover {
        box-shadow: inset 6px 6px 10px #bec5d0, inset -6px -6px 10px #ffffff;
        color: #eb3124;
        transform: translateY(2px);
    }

    .btn-back:active {
        box-shadow: inset 8px 8px 16px #bec5d0, inset -8px -8px 16px #ffffff;
    }

    @media (max-width: 768px) {
        .card {
            margin: 0 15px;
            border-radius: 25px;
        }

        .card-body {
            padding: 20px;
        }

        .btn-back {
            padding: 12px 30px;
            width: 100%;
        }
    }
</style>
@endsection
