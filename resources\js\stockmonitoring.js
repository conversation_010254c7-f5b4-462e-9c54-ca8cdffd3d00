// resources/js/adminho/stockmonitoring.js
import bootstrap from './bootstrap-init';

document.addEventListener('DOMContentLoaded', function() {
    let currentSortColumn = null;
    let isAscending = true;

    function loadData() {
        const siteId = document.getElementById('site_id');
        const startDateElement = document.getElementById('start_date');
        const endDateElement = document.getElementById('end_date');
        const searchElement = document.getElementById('search');
        const tableBody = document.getElementById('inventory-table-body');

        if (!siteId || !startDateElement || !endDateElement || !searchElement || !tableBody) {
            console.error('One or more elements not found.');
            return;
        }

        const siteIdValue = siteId.value;
        const startDate = startDateElement.value;
        const endDate = endDateElement.value;
        const search = searchElement.value;



        const xhr = new XMLHttpRequest();
        xhr.open('POST', '/inventory/get-data', true);
        xhr.setRequestHeader('Content-Type', 'application/json');
        xhr.setRequestHeader('X-CSRF-TOKEN', document.querySelector('meta[name="csrf-token"]').getAttribute('content'));

        xhr.onload = function() {
            if (xhr.status >= 200 && xhr.status < 300) {
                const data = JSON.parse(xhr.responseText);
                populateTable(data);
            } else {
                console.error('Request failed with status:', xhr.status);
            }
        };

        xhr.onerror = function() {
            console.error('Request failed');
        };

        xhr.send(JSON.stringify({
            site_id: siteIdValue,
            start_date: startDate,
            end_date: endDate,
            search: search
        }));
    }

    function formatDate(dateString) {
        if (!dateString) return '-';

        const date = new Date(dateString);
        const options = {
            day: '2-digit',
            month: 'long',
            year: 'numeric'
        };

        return date.toLocaleDateString('id-ID', options);
    }

    function populateTable(data) {
        const tableBody = document.getElementById('inventory-table-body');
        if (!tableBody) {
            console.error('inventory-table-body not found');
            return;
        }

        tableBody.innerHTML = ''; // Clear existing data
        let totalStock = 0;
        let totalIn = 0;
        let totalOut = 0;

        data.forEach(item => {
            totalStock += parseInt(item.stock_quantity);
            totalIn += parseInt(item.total_in);
            totalOut += parseInt(item.total_out);
            const row = document.createElement('tr');
            if (item.status === 'Not Ready') {
                row.classList.add('bg-danger');
                row.classList.add('text-white');
            }else if (item.status === 'Medium') {
                row.classList.add('bg-warning');
            }
            row.innerHTML = `
                <td>${item.part_name}</td>
                <td>${item.min_stock}</td>
                <td>${item.max_stock}</td>
                <td>${item.stock_quantity}</td>
                <td>${item.total_in}</td>
                <td>${item.total_out}</td>
                <td>${item.status}</td>
            `;
            row.addEventListener('click', () => showDetail(item.part_inventory_id));
            tableBody.appendChild(row);
        });

        const totalRow = document.createElement('tr');
        totalRow.innerHTML = `
            <td colspan="3" class="pt-2 pb-2 font-bold">Total</td>
            <td class="pt-2 pb-2 font-bold">${totalStock}</td>
            <td class="pt-2 pb-2 font-bold">${totalIn}</td>
            <td class="pt-2 pb-2 font-bold">${totalOut}</td>
            <td class="pt-2 pb-2 font-bold">-</td>
        `;
        tableBody.appendChild(totalRow);
    }

    function showDetail(partInventoryId) {
        const startDateElement = document.getElementById('start_date');
        const endDateElement = document.getElementById('end_date');

        if (!startDateElement || !endDateElement) {
            console.error('start_date or end_date not found');
            return;
        }

        const startDate = startDateElement.value;
        const endDate = endDateElement.value;

        const xhr = new XMLHttpRequest();
        xhr.open('POST', '/inventory/get-detail', true);
        xhr.setRequestHeader('Content-Type', 'application/json');
        xhr.setRequestHeader('X-CSRF-TOKEN', document.querySelector('meta[name="csrf-token"]').getAttribute('content'));

        xhr.onload = function() {
            if (xhr.status >= 200 && xhr.status < 300) {
                const response = JSON.parse(xhr.responseText);
                populateDetailModal(response.in, response.out);
                const detailModal = new bootstrap.Modal(document.getElementById('detailModal'));
                detailModal.show();
            } else {
                console.error('Request failed with status:', xhr.status);
            }
        };

        xhr.onerror = function() {
            console.error('Request failed');
        };

        xhr.send(JSON.stringify({
            part_inventory_id: partInventoryId,
            start_date: startDate,
            end_date: endDate
        }));
    }

    function populateDetailModal(inData, outData) {
        const inTableBody = document.getElementById('in-table-body');
        const outTableBody = document.getElementById('out-table-body');

        if (!inTableBody || !outTableBody) {
            console.error('in-table-body or out-table-body not found');
            return;
        }

        inTableBody.innerHTML = "";
        outTableBody.innerHTML = "";

        if (inData.length === 0) {
            const emptyRow = document.createElement('tr');
            emptyRow.innerHTML = `<td class="p-2 text-center" colspan="5">Tidak ditemukan data history in</td>`;
            inTableBody.appendChild(emptyRow);
        } else {
            let totalstok = 0;
            inData.forEach(item => {
                totalstok += parseInt(item.quantity) || 0;

                const row = document.createElement('tr');
                row.innerHTML = `
                    <td class="p-2">${formatDate(item.date_in)}</td>
                    <td class="p-2">${item.quantity}</td>
                    <td class="p-2">${item.employee_name}</td>
                    <td class="p-2">${item.notes || '-'}</td>`;
                inTableBody.appendChild(row);
            });

            const row2 = document.createElement('tr');
            row2.innerHTML = `
                <td class="p-2 font-bold">Total</td>
                <td class="p-2 font-bold">${totalstok}</td>
                <td class="p-2" colspan="3"></td>`;
            inTableBody.appendChild(row2);
        }

        if (outData.length === 0) {
            const emptyRow = document.createElement('tr');
            emptyRow.innerHTML = `<td class="p-2 text-center" colspan="5">Tidak ditemukan data history out</td>`;
            outTableBody.appendChild(emptyRow);
        } else {
            let totalstok = 0;
            outData.forEach(item => {
                totalstok += parseInt(item.quantity) || 0;

                const row = document.createElement('tr');
                row.innerHTML = `
                    <td class="p-2">${formatDate(item.date_out)}</td>
                    <td class="p-2">${item.quantity}</td>
                    <td class="p-2">${item.employee_name}</td>
                    <td class="p-2">${item.notes || '-'}</td>
                `;
                outTableBody.appendChild(row);
            });

            const rowtotal = document.createElement('tr');
            rowtotal.innerHTML = `
                <td class="p-2 font-bold">Total</td>
                <td class="p-2 font-bold">${totalstok}</td>
                <td class="p-2" colspan="3"></td>`;
            outTableBody.appendChild(rowtotal);
        }
    }

    function sortTable(columnIndex) {
        const tableBody = document.getElementById('inventory-table-body');
        if (!tableBody) {
            console.error('inventory-table-body not found');
            return;
        }

        const rows = Array.from(tableBody.querySelectorAll('tr'));

        if (currentSortColumn === columnIndex) {
            isAscending = !isAscending;
        } else {
            currentSortColumn = columnIndex;
            isAscending = true;
        }

        rows.sort((a, b) => {
            const aValue = a.querySelectorAll('td')[columnIndex].textContent.trim();
            const bValue = b.querySelectorAll('td')[columnIndex].textContent.trim();

            let comparison = 0;
            if (columnIndex === 1 || columnIndex === 2 || columnIndex === 3) {
                comparison = parseFloat(aValue) - parseFloat(bValue);
            } else {
                comparison = aValue.localeCompare(bValue);
            }

            return isAscending ? comparison : -comparison;
        });

        tableBody.innerHTML = '';
        rows.forEach(row => tableBody.appendChild(row));
    }

    // Attach event listeners to the sortable headers
    const sortableHeaders = document.querySelectorAll('th[onclick*="sortTable"]');
    sortableHeaders.forEach(header => {
        header.addEventListener('click', function() {
            const columnIndex = parseInt(this.getAttribute('onclick').match(/\d+/)[0]);
            sortTable(columnIndex);
        });
    });

    // Load data saat halaman dimuat
    loadData();

    // Attach loadData to the onchange events of the select and input elements.
    document.getElementById('site_id').addEventListener('change', loadData);
    document.getElementById('start_date').addEventListener('change', loadData);
    document.getElementById('end_date').addEventListener('change', loadData);
    document.getElementById('search').addEventListener('input', loadData);

    // Make functions available globally if needed (though it's better to avoid global scope)
    window.sortTable = sortTable;
    window.loadData = loadData;
});