import Chart from 'chart.js/auto';

document.addEventListener('DOMContentLoaded', function() {
    const siteColors = {};

    function getColorForSite(siteName) {
        if (!siteColors[siteName]) {
            const letters = '0123456789ABCDEF';
            let color = '#';
            for (let i = 0; i < 6; i++) {
                color += letters[Math.floor(Math.random() * 16)];
            }
            siteColors[siteName] = color;
        }
        return siteColors[siteName];
    }

    function createChart(canvasId, chartData, chartType) {
        const ctx = document.getElementById(canvasId).getContext('2d');
        const datasets = chartData.map(siteData => ({
            label: siteData.site_name,
            data: siteData.data,
            borderColor: getColorForSite(siteData.site_name),
            backgroundColor: 'transparent',
            tension: 0.4,
            pointRadius: 3,
            pointHoverRadius: 5
        }));

        new Chart(ctx, {
            type: 'line',
            data: {
                labels: chartData[0].labels,
                datasets: datasets
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                scales: {
                    y: {
                        beginAtZero: true,
                        title: {
                            display: true,
                            text: 'Quantity'
                        }
                    },
                    x: {
                        title: {
                            display: true,
                            text: window.groupBy.charAt(0).toUpperCase() + window.groupBy.slice(1)
                        }
                    }
                },
                plugins: {
                    title: {
                        display: true,
                        text: `${chartType} Stock (${window.groupBy.charAt(0).toUpperCase() + window.groupBy.slice(1)})`,
                        font: { size: 16 }
                    },
                    legend: { position: 'top', align: 'center' }
                }
            }
        });
    }

    // Create both charts
    createChart('inStockChart', window.inStockData, 'In');
    createChart('outStockChart', window.outStockData, 'Out');
});