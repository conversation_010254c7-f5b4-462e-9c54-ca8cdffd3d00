<?php

namespace App\Http\Controllers\Sites;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use App\Models\PartInventory;
use App\Models\Site;
use App\Models\Part;
use Illuminate\View\View;

class InventoryCardController extends Controller
{
    public function __construct()
    {
        // No middleware check, we'll handle it in each method
    }

    public function index(Request $request)
    {
        // Check if user is logged in and has a site_id
        if (!session('site_id') || !session('role')) {
            return redirect('/login')->withErrors(['username' => 'Silakan login terlebih dahulu']);
        }

        $siteId = session('site_id');
        $site = Site::findOrFail($siteId);

        $parts = PartInventory::with(['part', 'site'])
            ->where('site_id', $siteId)
            ->orderBy('updated_at', 'desc')
            ->paginate(15);

        return view('sites.inventorycard', compact('site', 'parts'));
    }

    public function getInventoryData(Request $request)
    {
        // Check if user is logged in and has a site_id
        if (!session('site_id') || !session('role')) {
            return response()->json(['error' => 'Unauthorized'], 401);
        }

        $siteId = session('site_id');
        $page = $request->input('page', 1);
        $perPage = $request->input('per_page', 15);
        $searchTerm = $request->input('search', '');
        $partType = $request->input('part_type', '');
        $statusFilter = $request->input('status_filter', '');
        $sortBy = $request->input('sort_by', 'part_code');
        $sortDirection = $request->input('sort_direction', 'asc');

        $query = PartInventory::with(['part', 'site'])
            ->where('site_id', $siteId);

        // Apply search filter if provided
        if (!empty($searchTerm)) {
            $query->where(function($q) use ($searchTerm) {
                $q->whereHas('part', function($subQ) use ($searchTerm) {
                    $subQ->where('part_code', 'LIKE', "%{$searchTerm}%")
                        ->orWhere('part_name', 'LIKE', "%{$searchTerm}%");
                })
                ->orWhere('site_part_name', 'LIKE', "%{$searchTerm}%");
            });
        }

        // Apply part type filter if provided
        if (!empty($partType)) {
            $query->whereHas('part', function($q) use ($partType) {
                $q->where('part_type', $partType);
            });
        }

        // Apply status filter if provided
        if (!empty($statusFilter)) {
            if ($statusFilter === 'Lainnya') {
                // Lainnya = parts with min_stock = 0 AND max_stock = 0
                $query->where('min_stock', 0)->where('max_stock', 0);
            } else {
                // For other status filters, we'll filter after calculating status
                // Store the filter for later use
            }
        }

        // Apply sorting
        if ($sortBy === 'part_code' || $sortBy === 'part_name') {
            // Check if parts table is already joined
            $sql = $query->toSql();
            if (strpos($sql, 'join `parts`') === false) {
                $query->join('parts', 'part_inventories.part_code', '=', 'parts.part_code')
                      ->select('part_inventories.*', 'parts.part_name', 'parts.part_code as part_code_sort');
            }
            $query->orderBy('parts.' . $sortBy, $sortDirection);
        } elseif ($sortBy === 'status') {
            // For status sorting, we'll sort after data processing
            // Keep default order for now
            $query->orderBy('part_inventories.part_code', 'asc');
        } else {
            $query->orderBy('part_inventories.' . $sortBy, $sortDirection);
        }

        // For status filters other than 'Lainnya', we need to get all data first to calculate status
        if (!empty($statusFilter) && $statusFilter !== 'Lainnya') {
            // Create a fresh query for counting to avoid join issues
            $countQuery = PartInventory::with(['part', 'site'])
                ->where('site_id', $siteId);

            // Apply same filters for counting
            if (!empty($searchTerm)) {
                $countQuery->where(function($q) use ($searchTerm) {
                    $q->whereHas('part', function($subQ) use ($searchTerm) {
                        $subQ->where('part_code', 'LIKE', "%{$searchTerm}%")
                            ->orWhere('part_name', 'LIKE', "%{$searchTerm}%");
                    })
                    ->orWhere('site_part_name', 'LIKE', "%{$searchTerm}%");
                });
            }

            if (!empty($partType)) {
                $countQuery->whereHas('part', function($q) use ($partType) {
                    $q->where('part_type', $partType);
                });
            }

            // Get all data to calculate status and then filter
            $allInventories = $countQuery->get();
            $filteredData = [];

            foreach ($allInventories as $inventory) {
                $minStock = $inventory->min_stock ?? 0;
                $maxStock = $inventory->max_stock ?? 0;
                $currentStock = $inventory->stock_quantity ?? 0;
                $averageStock = ($minStock + $maxStock) / 2;

                $status = 'Lainnya';

                if ($minStock == 0 && $maxStock == 0) {
                    $status = 'Lainnya';
                } else {
                    if ($currentStock < $averageStock && ($minStock != 0 || $maxStock != 0)) {
                        $status = 'Not Ready';
                    } elseif ($currentStock >= $averageStock && $currentStock <= $maxStock) {
                        $status = 'Medium';
                    } elseif ($currentStock > $maxStock) {
                        $status = 'Ready';
                    }
                }

                if ($status === $statusFilter) {
                    $filteredData[] = $inventory;
                }
            }

            $total = count($filteredData);
            $inventories = collect($filteredData)->slice(($page - 1) * $perPage, $perPage);
        } else {
            // Normal pagination for other cases
            // Create a fresh query for counting to avoid join issues
            $countQuery = PartInventory::where('site_id', $siteId);

            // Apply same filters for counting
            if (!empty($searchTerm)) {
                $countQuery->where(function($q) use ($searchTerm) {
                    $q->whereHas('part', function($subQ) use ($searchTerm) {
                        $subQ->where('part_code', 'LIKE', "%{$searchTerm}%")
                            ->orWhere('part_name', 'LIKE', "%{$searchTerm}%");
                    })
                    ->orWhere('site_part_name', 'LIKE', "%{$searchTerm}%");
                });
            }

            if (!empty($partType)) {
                $countQuery->whereHas('part', function($q) use ($partType) {
                    $q->where('part_type', $partType);
                });
            }

            if (!empty($statusFilter) && $statusFilter === 'Lainnya') {
                $countQuery->where('min_stock', 0)->where('max_stock', 0);
            }

            $total = $countQuery->count();
            $inventories = $query->skip(($page - 1) * $perPage)
                                ->take($perPage)
                                ->get();
        }

        $data = [];
        foreach ($inventories as $inventory) {
            // Calculate status based on correct logic from memories
            $minStock = $inventory->min_stock ?? 0;
            $maxStock = $inventory->max_stock ?? 0;
            $currentStock = $inventory->stock_quantity ?? 0;
            $averageStock = ($minStock + $maxStock) / 2;

            $status = 'Lainnya'; // Default for parts with min_stock = 0 AND max_stock = 0

            // Exclude parts with min_stock = 0 from ready/not ready calculations
            if ($minStock == 0 && $maxStock == 0) {
                $status = 'Lainnya';
            } else {
                // Not ready = stock < average(min,max) when min/max ≠ 0
                if ($currentStock < $averageStock && ($minStock != 0 || $maxStock != 0)) {
                    $status = 'Not Ready';
                }
                // Medium = stock >= average(min,max) but <= max
                elseif ($currentStock >= $averageStock && $currentStock <= $maxStock) {
                    $status = 'Medium';
                }
                // Ready = stock > max or (stock = 0 and min = max = 0)
                elseif ($currentStock > $maxStock) {
                    $status = 'Ready';
                }
            }

            // Skip if part relationship is missing
            if (!$inventory->part) {
                continue;
            }

            $itemData = [
                'part_code' => $inventory->part->part_code,
                'part_name' => $inventory->site_part_name ?? $inventory->part->part_name,
                'stock_quantity' => $inventory->stock_quantity,
                'min_stock' => $inventory->min_stock,
                'max_stock' => $inventory->max_stock,
                'price' => $inventory->price ?? 0,
                'status' => $status
            ];

            // Add itemcode for IMK site
            if ($siteId === 'IMK') {
                $itemData['itemcode'] = $inventory->item_code ?? '-';
            }

            $data[] = $itemData;
        }

        // Apply status sorting if needed
        if ($sortBy === 'status') {
            $statusOrder = ['Not Ready' => 1, 'Medium' => 2, 'Ready' => 3, 'Lainnya' => 4];
            usort($data, function($a, $b) use ($statusOrder, $sortDirection) {
                $aOrder = $statusOrder[$a['status']] ?? 5;
                $bOrder = $statusOrder[$b['status']] ?? 5;

                if ($sortDirection === 'desc') {
                    return $bOrder <=> $aOrder;
                }
                return $aOrder <=> $bOrder;
            });
        }

        return response()->json([
            'data' => $data,
            'current_page' => (int)$page,
            'per_page' => (int)$perPage,
            'last_page' => ceil($total / $perPage),
            'total' => $total
        ]);
    }

    public function getPartTypes()
    {
        // Check if user is logged in and has a site_id
        if (!session('site_id') || !session('role')) {
            return response()->json(['error' => 'Unauthorized'], 401);
        }

        $partTypes = Part::PART_TYPES;
        return response()->json(['part_types' => $partTypes]);
    }
}
