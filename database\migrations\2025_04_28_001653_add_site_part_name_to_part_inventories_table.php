<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('part_inventories', function (Blueprint $table) {
            $table->string('site_part_name')->nullable()->after('part_code');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('part_inventories', function (Blueprint $table) {
            $table->dropColumn('site_part_name');
        });
    }
};
