<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Technician extends Model
{
    use HasFactory;

    protected $primaryKey = 'technician_id';
    
    protected $fillable = [
        'name',
    ];

    /**
     * Get the daily reports that use this technician.
     */
    public function dailyReports()
    {
        return $this->belongsToMany(DailyReport::class, 'daily_report_technicians', 'technician_id', 'daily_report_id');
    }
}
