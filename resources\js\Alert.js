import Swal from "sweetalert2";

window.showAlert = function (type, title, message) {
    let colors = {
        success: "#28a745",
        error: "#d33",
        warning: "#ffc107",
    };

    Swal.fire({
        icon: type, // success, error, warning
        title: title,
        text: message,
        confirmButtonColor: colors[type] || "#3085d6",
    });
};

document.addEventListener("DOMContentLoaded", function () {
    let alertBox = document.querySelector("#alert-message");
    if (alertBox) {
        showAlert(
            alertBox.dataset.type,
            alertBox.dataset.title,
            alertBox.dataset.message
        );
    }
});
