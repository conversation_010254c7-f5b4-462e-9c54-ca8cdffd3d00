<?php
/**
 * Test script to verify the division parts detail API endpoint
 * Run this from the command line: php test_division_api.php
 */

// Include Laravel bootstrap
require_once __DIR__ . '/vendor/autoload.php';

$app = require_once __DIR__ . '/bootstrap/app.php';
$app->make(Illuminate\Contracts\Console\Kernel::class)->bootstrap();

use App\Http\Controllers\SuperadminController;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Carbon\Carbon;

echo "Testing Division Parts Detail API Endpoint\n";
echo "==========================================\n\n";

// Test 1: Check if there are any unit transactions in the database
echo "1. Checking for unit transactions in database...\n";
$totalTransactions = DB::table('unit_transactions')->count();
echo "   Total unit transactions: $totalTransactions\n";

if ($totalTransactions == 0) {
    echo "   No unit transactions found. The modal will be empty.\n\n";
} else {
    echo "   Unit transactions found. Checking date range...\n\n";
}

// Test 2: Check for unit transaction parts
echo "2. Checking for unit transaction parts...\n";
$totalParts = DB::table('unit_transaction_parts')->count();
echo "   Total unit transaction parts: $totalParts\n\n";

// Test 3: Check for parts with specific types
echo "3. Checking for parts by type...\n";
$partTypes = ['AC', 'TYRE', 'FABRIKASI'];
foreach ($partTypes as $type) {
    $count = DB::table('unit_transaction_parts')
        ->join('unit_transactions', 'unit_transaction_parts.unit_transaction_id', '=', 'unit_transactions.id')
        ->join('part_inventories', 'unit_transaction_parts.part_inventory_id', '=', 'part_inventories.part_inventory_id')
        ->join('parts', 'part_inventories.part_code', '=', 'parts.part_code')
        ->where('parts.part_type', $type)
        ->count();
    echo "   $type parts: $count\n";
}
echo "\n";

// Test 4: Check recent transactions (last 30 days)
echo "4. Checking recent transactions (last 30 days)...\n";
$startDate = Carbon::now()->subDays(30)->startOfDay();
$endDate = Carbon::now()->endOfDay();

$recentCount = DB::table('unit_transaction_parts')
    ->join('unit_transactions', 'unit_transaction_parts.unit_transaction_id', '=', 'unit_transactions.id')
    ->whereBetween('unit_transactions.created_at', [$startDate, $endDate])
    ->count();

echo "   Recent transactions (last 30 days): $recentCount\n";

if ($recentCount > 0) {
    echo "   Recent transactions found. Testing API with current month...\n\n";
    
    // Test 5: Test the actual API endpoint
    echo "5. Testing API endpoint with AC division...\n";
    
    $controller = new SuperadminController();
    $request = new Request([
        'division' => 'AC',
        'start_date' => $startDate->format('Y-m-d'),
        'end_date' => $endDate->format('Y-m-d')
    ]);
    
    try {
        $response = $controller->getDivisionPartsDetail($request);
        $data = json_decode($response->getContent(), true);
        
        echo "   API Response Status: " . $response->getStatusCode() . "\n";
        echo "   Success: " . ($data['success'] ? 'true' : 'false') . "\n";
        
        if (isset($data['data'])) {
            echo "   Data count: " . count($data['data']) . "\n";
            
            if (count($data['data']) > 0) {
                echo "   Sample record:\n";
                $sample = $data['data'][0];
                echo "     - Part Code: " . ($sample['part_code'] ?? 'N/A') . "\n";
                echo "     - Part Name: " . ($sample['part_name'] ?? 'N/A') . "\n";
                echo "     - Part Type: " . ($sample['part_type'] ?? 'N/A') . "\n";
                echo "     - Site: " . ($sample['site_name'] ?? 'N/A') . "\n";
                echo "     - Quantity: " . ($sample['quantity'] ?? 'N/A') . "\n";
                echo "     - Price: " . ($sample['price'] ?? 'N/A') . "\n";
            }
        }
        
        if (isset($data['summary'])) {
            echo "   Summary:\n";
            echo "     - Total Items: " . ($data['summary']['total_items'] ?? 'N/A') . "\n";
            echo "     - Total Quantity: " . ($data['summary']['total_quantity'] ?? 'N/A') . "\n";
            echo "     - Total Value: " . ($data['summary']['total_value'] ?? 'N/A') . "\n";
        }
        
    } catch (Exception $e) {
        echo "   Error: " . $e->getMessage() . "\n";
    }
} else {
    echo "   No recent transactions found. Testing with broader date range...\n\n";
    
    // Test with all-time data
    echo "5. Testing API endpoint with all-time data...\n";
    
    $controller = new SuperadminController();
    $request = new Request([
        'division' => 'AC',
        'month' => Carbon::now()->format('Y-m')
    ]);
    
    try {
        $response = $controller->getDivisionPartsDetail($request);
        $data = json_decode($response->getContent(), true);
        
        echo "   API Response Status: " . $response->getStatusCode() . "\n";
        echo "   Success: " . ($data['success'] ? 'true' : 'false') . "\n";
        echo "   Data count: " . (isset($data['data']) ? count($data['data']) : 0) . "\n";
        
    } catch (Exception $e) {
        echo "   Error: " . $e->getMessage() . "\n";
    }
}

echo "\n";
echo "Test completed. Check the Laravel logs for detailed query information.\n";
echo "Log file location: storage/logs/laravel.log\n";
