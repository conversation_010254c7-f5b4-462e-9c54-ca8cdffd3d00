@extends('warehouse.content')
@section('contentho')
@section('title', 'Site Management')
<div class="row bgwhite page-title-box shadow-kit mb-1 rounded-lg">
    <div class="col d-flex align-items-center" style="max-width: fit-content;">
        <button class="button-menu-mobile text-blue-500 btn-sm">
            <i style="font-size: 30px;" class="mdi mdi-menu"></i>
        </button>
        <div class="text">
            <p class="font-bold m-0">{{ session('name') }}</p>
        </div>
    </div>
    <div class="col">
        <ul class="list-unstyled topnav-menu float-right mb-0">
            <li class="dropdown notification-list">
                <a class="nav-link dropdown-toggle" href="#" data-bs-toggle="dropdown" aria-expanded="false">
                    <i class="mdi mdi-bell-outline noti-icon"></i>
                    <span class="badge badge-danger badge-pill float-right badgenotif"></span>
                </a>
                <div class="dropdown-menu dropdown-menu-end dropdown-lg">
                    <div class="dropdown-item noti-title bg-primary">
                        <h5 class="m-0 text-white d-flex justify-content-between align-items-center">
                            Notifikasi
                            <a href="javascript:void(0);" class="text-white" id="clear-all">
                                <small></small>
                            </a>
                        </h5>
                    </div>
                    <div class="noti-scroll" id="notification-list">
                        <!-- Notifications will be dynamically inserted here -->
                    </div>
                </div>
            </li>
        </ul>
    </div>
</div>

<div class="row pt-2 mr-2">
    <div class="col bgwhite shadow-kit mr-2 ml-2 rounded-lg">
        <div class="ml-4 p-4">
            <h1 class="text-xl font-bold mb-4">Site Management</h1>
            <!-- Form Input -->
            <table id="siteTable" class="table min-w-full bg-white table-bordered">
                <thead class="table-dark text-white">
                    <tr class="text-left p-2">
                        <th class="p-2">No</th>
                        <th class="p-2">Site Name</th>
                        <th class="p-2">Address</th>
                        <th class="p-2">Aksi</th>
                    </tr>
                </thead>
                <tbody>
                    <!-- Data akan diisi oleh JavaScript -->
                </tbody>
            </table>
            <div id="site-pagination" class="mt-3">
                <!-- Custom pagination will be rendered here by JavaScript -->
            </div>
        </div>
    </div>
    <div class="col bgwhite shadow-kit max400 rounded-lg">
        <div class=" p-4  ml-2">
            <h5 class="text-bold font-bold mb-4">Tambahkan Site</h5>
            <form id="siteForm" class="mb-4">
                <input type="hidden" id="siteId">
                <div class="mb-4">
                    <label for="siteName" class="block text-sm font-medium text-gray-700">Site Name</label>
                    <input type="text" id="siteName" name="siteName" class="form-control">
                </div>
                <div class="mb-4">
                    <label for="address" class="block text-sm font-medium text-gray-700">Address</label>
                    <input type="text" id="address" name="address" class="form-control">
                </div>
                <button type="submit" class="bg-blue-500 text-white px-4 py-2 rounded">Save</button>
            </form>
        </div>
    </div>
</div>
<hr class="mt-2">
<div class="row mt-2 mr-2">
    <div class="col bgwhite shadow-kit mr-2 ml-2 rounded-lg">
        <div class="ml-4 p-4">
            <h1 class="text-xl font-bold mb-4">Supplier Management</h1>
            <table id="supplierTable" class="min-w-full bg-white table-bordered">
                <thead class="table-dark text-white">
                    <tr class="p-2">
                        <th class="p-2">No</th>
                        <th class="p-2">Supplier Name</th>
                        <th class="p-2">Address</th>
                        <th class="p-2">Contact Person</th>
                        <th class="p-2">Aksi</th>
                    </tr>
                </thead>
                <tbody>
                    <!-- Data akan diisi oleh JavaScript -->
                </tbody>
            </table>
            <div id="supplier-pagination" class="mt-3">
                <!-- Custom pagination will be rendered here by JavaScript -->
            </div>
        </div>
    </div>
    <div class="col shadow-kit bgwhite max400 rounded-lg">
        <div class=" p-4  ml-2">
            <h5 class="text-bold font-bold mb-4">Tambahkan Supplier</h5>
            <form id="supplierForm" class="mb-4">
                <input type="hidden" id="supplierIdhidden" name="supplierIdhidden">
                <!-- <div class="mb-4"> -->
                <!-- <label for="supplierIdInput" class="block text-sm font-medium text-gray-700">Supplier ID <span class="text-red-300"> * Gunakan dengan Hati-hati </span></label> -->
                <input type="hidden" id="supplierIdInput" name="supplierIdInput" class="mt-1 block w-full rounded-md border-gray-300 shadow-sm">
                <!-- </div> -->
                <div class="mb-4">
                    <label for="supplierName" class="block text-sm font-medium text-gray-700">Supplier Name</label>
                    <input type="text" id="supplierName" name="supplier_name" class="form-control">
                </div>
                <div class="mb-4">
                    <label for="address" class="block text-sm font-medium text-gray-700">Address</label>
                    <input type="text" id="alamat" name="alamat" class="form-control">
                </div>
                <div class="mb-4">
                    <label for="contactPerson" class="block text-sm font-medium text-gray-700">Contact Person</label>
                    <input type="text" id="contactPerson" name="contact_person" class="form-control">
                </div>
                <button type="submit" class="bg-blue-500 text-white px-4 py-2 rounded">Save</button>
            </form>
        </div>
    </div>
</div>
@endsection
@section('resource')
@vite('resources/js/adminho/crudsupplier.js')
@vite(['resources/js/site/Crudsite.js','resources/js/style.js'])
<style>
    /* Pagination styling */
    .pagination {
        display: flex;
        justify-content: center;
        list-style: none;
        padding: 0;
        margin-top: 15px;
    }
    .pagination .page-item {
        margin: 0 2px;
    }
    .pagination .page-item .page-link {
        color: #333;
        background-color: #fff;
        border: 1px solid #ddd;
        padding: 6px 12px;
        text-decoration: none;
        border-radius: 4px;
        cursor: pointer;
    }
    .pagination .page-item.active .page-link {
        color: #fff;
        background-color: #28a745;
        border-color: #28a745;
    }
    .pagination .page-item .page-link:hover {
        background-color: #f8f9fa;
    }
    #site-pagination, #supplier-pagination {
        margin-top: 15px;
    }
</style>
<script>
    // Initial pagination data
    window.sitePaginationData = {
        current_page: 1,
        per_page: 5,
        last_page: 1,
        total: 0
    };

    window.supplierPaginationData = {
        current_page: 1,
        per_page: 10,
        last_page: 1,
        total: 0
    };
</script>
@endsection