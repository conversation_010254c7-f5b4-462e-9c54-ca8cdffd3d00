body {
    background-color: #f8f9fa; /* Light gray background */
    font-family: 'Nunito', sans-serif; /* Modern font */
}

.card {
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.03); /* Softer shadow */
    border: none;
}

.btn-primary {
    background-color: #5dade2; /* Softer primary color */
    border-color: #5dade2;
    font-weight: 500; /* Medium font weight */
}

.btn-primary:hover {
    background-color: #45a2bb; /* Darker shade on hover */
    border-color: #45a2bb;
}

.form-control {
    border-radius: 5px;
    border-color: #ced4da;
}

/* Header Styles */
.page-title-box {
    background-color: #ffffff;
    border-radius: 0.5rem;
    box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
}

.button-menu-mobile {
    background: transparent;
    border: none;
    padding: 0.5rem;
    color: #5dade2;
    transition: color 0.2s;
}

.button-menu-mobile:hover {
    color: #2980b9;
}

.button-menu-mobile i {
    font-size: 1.5rem;
}

.form-check-inline {
    margin-right: 1rem;
}

.form-check-input:checked {
    background-color: #5dade2;
    border-color: #5dade2;
}

.noti-icon {
    font-size: 1.25rem;
    position: relative;
}

.noti-icon-badge {
    position: absolute;
    top: -8px;
    right: -8px;
    font-size: 0.75rem;
    padding: 0.25rem 0.5rem;
}

.dropdown-menu {
    box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
    border: none;
}

.noti-scroll {
    max-height: 300px;
    overflow-y: auto;
}

.gap-2 {
    gap: 0.5rem;
}

.gap-3 {
    gap: 1rem;
}

/* Table Styles */
.table {
    margin-bottom: 0; /* Remove default table margin */
}

.table thead th {
    background-color: #f1f5f8; /* Light gray header */
    color: #495057; /* Darker text */
    font-weight: 600; /* Semi-bold */
    border-bottom: 1px solid #dee2e6; /* Thin separator */
}

.table td, .table th {
    border-top: none;
    padding: 0.75rem;
}

/* Stock Cards */
.stock-card {
    background-color: white;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.03);
}

.stock-card .card-body {
    padding: 1rem;
}

/* Status Colors */
.table-light-danger {
    background-color: #f8d7da; /* Muted danger */
}

.table-light-warning {
    background-color: #fff3cd; /* Muted warning */
}

/* Text Styles */
.text-uppercase {
    font-weight: 600; /* Semi-bold */
    color: #495057; /* Darker text */
}
