<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class MergedPartSite extends Model
{
    use HasFactory;

    protected $table = 'merged_part_sites';

    protected $fillable = [
        'part_merge_id',
        'site_id',
    ];

    public function partMerge()
    {
        return $this->belongsTo(PartMerge::class, 'part_merge_id', 'part_merge_id');
    }

    public function site()
    {
        return $this->belongsTo(Site::class, 'site_id', 'site_id');
    }
}