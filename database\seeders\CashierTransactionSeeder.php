<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\CashierTransaction;
use App\Models\User;
use Carbon\Carbon;

class CashierTransactionSeeder extends Seeder
{
    /**
     * Run the database seeder.
     */
    public function run(): void
    {
        // Get kasir user
        $kasir = User::where('role', 'kasir')->first();
        
        if (!$kasir) {
            $this->command->error('No kasir user found. Please run KasirUserSeeder first.');
            return;
        }

        // Sample transactions for current month
        $transactions = [
            [
                'user_id' => $kasir->employee_id,
                'type' => 'pengeluaran',
                'description' => 'Pembelian alat tulis kantor',
                'amount' => 150000,
                'transaction_date' => Carbon::now()->subDays(5),
            ],
            [
                'user_id' => $kasir->employee_id,
                'type' => 'pengeluaran',
                'description' => 'Biaya transportasi pengiriman dokumen',
                'amount' => 75000,
                'transaction_date' => Carbon::now()->subDays(4),
            ],
            [
                'user_id' => $kasir->employee_id,
                'type' => 'penerimaan',
                'description' => 'Penerimaan dari penjualan scrap material',
                'amount' => 500000,
                'transaction_date' => Carbon::now()->subDays(3),
            ],
            [
                'user_id' => $kasir->employee_id,
                'type' => 'pengeluaran',
                'description' => 'Pembelian konsumsi rapat',
                'amount' => 200000,
                'transaction_date' => Carbon::now()->subDays(2),
            ],
            [
                'user_id' => $kasir->employee_id,
                'type' => 'penerimaan',
                'description' => 'Pengembalian uang muka karyawan',
                'amount' => 300000,
                'transaction_date' => Carbon::now()->subDays(1),
            ],
            [
                'user_id' => $kasir->employee_id,
                'type' => 'pengeluaran',
                'description' => 'Biaya parkir dan tol',
                'amount' => 50000,
                'transaction_date' => Carbon::now(),
            ],
        ];

        foreach ($transactions as $transaction) {
            CashierTransaction::create($transaction);
        }

        $this->command->info('Sample cashier transactions created successfully!');
        $this->command->info('Total transactions: ' . count($transactions));
        $this->command->info('Total pengeluaran: Rp ' . number_format(
            collect($transactions)->where('type', 'pengeluaran')->sum('amount'), 0, ',', '.'
        ));
        $this->command->info('Total penerimaan: Rp ' . number_format(
            collect($transactions)->where('type', 'penerimaan')->sum('amount'), 0, ',', '.'
        ));
    }
}
