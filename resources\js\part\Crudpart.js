import Swal from "sweetalert2";
import $ from "jquery";

// Global variables for pagination
let currentPage = 1;
const itemsPerPage = 20;
let currentSearch = '';
let currentType = '';

document.addEventListener("DOMContentLoaded", function () {
    // Initialize form event listeners
    initFormListeners();

    // Load initial data
    loadPartsData();

    // Initialize search and filter event listeners
    initSearchAndFilter();
});

function initFormListeners() {
    // Part name input hint
    document.getElementById("part_name").addEventListener("input", function () {
        let hint = document.getElementById("part_name_hint");
        if (this.value.length > 0) {
            hint.style.display = "block";
        } else {
            hint.style.display = "none";
        }
    });

    // Remove spaces from part code
    document.getElementById("part_code_display").addEventListener("input", function () {
        this.value = this.value.replace(/\s/g, "");
    });

    // Format price input with Indonesian currency format
    document.getElementById("price").addEventListener("input", function() {
        // Remove non-numeric characters
        let value = this.value.replace(/[^0-9]/g, "");

        // Format with thousand separators
        if (value) {
            value = parseInt(value, 10);
            this.value = new Intl.NumberFormat('id-ID').format(value);
        } else {
            this.value = "0";
        }
    });

    // Cancel button
    $("#cancelButton").on("click", function () {
        resetForm();
    });

    // Form submission
    $("#partForm").on("submit", handleFormSubmit);
}

function initSearchAndFilter() {
    // Search input with debounce
    let timeoutId;
    $("#searchInput").on("keyup", function () {
        let search = $(this).val();
        clearTimeout(timeoutId);
        timeoutId = setTimeout(function () {
            currentSearch = search;
            currentPage = 1; // Reset to page 1 when searching
            loadPartsData();
        }, 500);
    });

    // Type filter
    $("#typeFilter").on("change", function () {
        currentType = $(this).val();
        currentPage = 1; // Reset to page 1 when changing filter
        loadPartsData();
    });
}

// Function to load parts data via AJAX
function loadPartsData() {
    // Show loading indicator
    const tableBody = document.getElementById('partsTableBody');
    tableBody.innerHTML = `
        <tr>
            <td colspan="8" class="text-center">
                <div class="spinner-border text-primary" role="status">
                    <span class="visually-hidden">Loading...</span>
                </div>
                <p>Loading data...</p>
            </td>
        </tr>
    `;

    // Prepare parameters
    const params = new URLSearchParams();
    params.append('page', currentPage);
    params.append('per_page', itemsPerPage);

    if (currentSearch) {
        params.append('search', currentSearch);
    }

    if (currentType) {
        params.append('type', currentType);
    }

    // Fetch data
    fetch(`/parts/data?${params.toString()}`, {
        headers: {
            'X-Requested-With': 'XMLHttpRequest',
            'Accept': 'application/json'
        }
    })
    .then(response => {
        if (!response.ok) {
            throw new Error(`HTTP error! Status: ${response.status}`);
        }
        return response.json();
    })
    .then(data => {
        renderTable(data);
        renderPagination(data);
    })
    .catch(error => {
        console.error('Error loading parts data:', error);
        tableBody.innerHTML = `
            <tr>
                <td colspan="8" class="text-center text-danger">
                    <p>Error loading data. Please try again.</p>
                </td>
            </tr>
        `;
    });
}

// Function to render table data
function renderTable(data) {
    const tableBody = document.getElementById('partsTableBody');

    if (data.data.length === 0) {
        tableBody.innerHTML = `
            <tr>
                <td colspan="8" class="text-center">
                    <p>No parts found.</p>
                </td>
            </tr>
        `;
        return;
    }

    let html = '';
    data.data.forEach((part, index) => {
        const rowNumber = (data.current_page - 1) * data.per_page + index + 1;
        // Format price with Indonesian currency format
        const formattedPrice = new Intl.NumberFormat('id-ID').format(part.price || 0);

        html += `
            <tr>
                <td>${rowNumber}</td>
                <td>${part.part_code}</td>
                <td>${part.part_name}</td>
                <td>${part.bin_location || '-'}</td>
                <td>${part.part_type}</td>
                <td>Rp ${formattedPrice}</td>
                <td>${part.eum || 'EA'}</td>
                <td>
                    <button
                        data-part-code="${part.part_code}"
                        data-part-name="${part.part_name}"
                        data-bin-location="${part.bin_location || ''}"
                        data-part-type="${part.part_type}"
                        data-price="${part.price || 0}"
                        data-eum="${part.eum || 'EA'}"
                        class="edit-part-btn btn btn-sm btn-secondary">Edit</button>
                    <button
                        data-part-code="${part.part_code}"
                        class="delete-part-btn btn btn-sm btn-danger">Hapus</button>
                </td>
            </tr>
        `;
    });

    tableBody.innerHTML = html;

    // Add event listeners to the newly created buttons
    attachTableEventListeners();
}

// Function to attach event listeners to table buttons
function attachTableEventListeners() {
    // Edit buttons
    document.querySelectorAll('.edit-part-btn').forEach(button => {
        button.addEventListener('click', function() {
            handleEditButtonClick(this);
        });
    });

    // Delete buttons
    document.querySelectorAll('.delete-part-btn').forEach(button => {
        button.addEventListener('click', function() {
            handleDeleteButtonClick(this);
        });
    });
}

// Function to render pagination
function renderPagination(data) {
    try {
        const container = document.getElementById('parts-pagination');
        if (!container) {
            console.error('Pagination container not found');
            return;
        }

        // Update pagination info
        const paginationInfo = document.getElementById('pagination-info');
        if (paginationInfo) {
            const start = ((data.current_page - 1) * data.per_page) + 1;
            const end = Math.min(start + data.per_page - 1, data.total);
            paginationInfo.textContent = `Menampilkan ${start}-${end} dari ${data.total} item (Halaman ${data.current_page} dari ${data.last_page})`;
        }

        container.innerHTML = '';

        // Only show pagination controls if there are multiple pages
        if (data.last_page > 1) {
            const pagination = document.createElement('ul');
            pagination.className = 'pagination pagination-rounded ';

            // Previous button
            if (data.current_page > 1) {
                pagination.appendChild(createPaginationItem(data.current_page - 1, '«'));
            }

            // Calculate which page numbers to show
            let startPage = Math.max(1, data.current_page - 2);
            let endPage = Math.min(data.last_page, startPage + 4);

            // Adjust if we're near the end
            if (endPage - startPage < 4) {
                startPage = Math.max(1, endPage - 4);
            }

            // First page and ellipsis if needed
            if (startPage > 1) {
                pagination.appendChild(createPaginationItem(1, 1, 1 === data.current_page));
                if (startPage > 2) {
                    const ellipsis = document.createElement('li');
                    ellipsis.className = 'page-item disabled';
                    const span = document.createElement('span');
                    span.className = 'page-link';
                    span.textContent = '...';
                    ellipsis.appendChild(span);
                    pagination.appendChild(ellipsis);
                }
            }

            // Page numbers
            for (let i = startPage; i <= endPage; i++) {
                pagination.appendChild(createPaginationItem(i, i, i === data.current_page));
            }

            // Last page and ellipsis if needed
            if (endPage < data.last_page) {
                if (endPage < data.last_page - 1) {
                    const ellipsis = document.createElement('li');
                    ellipsis.className = 'page-item disabled';
                    const span = document.createElement('span');
                    span.className = 'page-link';
                    span.textContent = '...';
                    ellipsis.appendChild(span);
                    pagination.appendChild(ellipsis);
                }
                pagination.appendChild(createPaginationItem(data.last_page, data.last_page, data.last_page === data.current_page));
            }

            // Next button
            if (data.current_page < data.last_page) {
                pagination.appendChild(createPaginationItem(data.current_page + 1, '»'));
            }

            container.appendChild(pagination);

            // Add event listeners to pagination links
            container.querySelectorAll('.page-link').forEach(link => {
                link.addEventListener('click', function(e) {
                    e.preventDefault();
                    currentPage = parseInt(this.dataset.page);
                    loadPartsData();
                });
            });
        }
    } catch (error) {
        console.error('Error rendering pagination:', error);
    }
}

// Function to create pagination item
function createPaginationItem(page, text, isActive = false) {
    const li = document.createElement('li');
    li.className = `page-item ${isActive ? 'active' : ''}`;

    const a = document.createElement('a');
    a.className = 'page-link';
    a.href = '#';
    a.textContent = text;
    a.dataset.page = page;

    li.appendChild(a);
    return li;
}

// Variables for form handling
let isEditMode = false;
let currentPartCode = null;

// Function to reset form
function resetForm() {
    isEditMode = false;
    currentPartCode = null;
    $("#formTitle").text("Tambah Part Baru");
    $("#submitButton").text("Simpan");
    $("#partForm")[0].reset();
    $("#part_code").val("");
    $("#part_code_display").val("");
    $("#part_code_display").prop("disabled", false);
    $("#cancelButton").hide();
}

// Function to handle form submission
function handleFormSubmit(e) {
    e.preventDefault();
    var form = $(this);
    let url = isEditMode ? `/parts/${currentPartCode}` : "/parts";
    let type = isEditMode ? "PUT" : "POST";

    // Get form data and prepare it for submission
    let formData = new FormData(form[0]);
    let serializedData = new URLSearchParams(formData);

    // Process price field - remove formatting before sending
    let priceField = document.getElementById("price");
    if (priceField) {
        let rawPrice = priceField.value.replace(/\./g, "");
        serializedData.set("price", rawPrice);
    }

    // Make sure EUM is included
    let eumField = document.getElementById("eum");
    if (eumField) {
        serializedData.set("eum", eumField.value || 'EA');
    }

    // Log the data being sent for debugging
    console.log("Sending data:", Object.fromEntries(serializedData));

    $.ajax({
        url: url,
        type: type,
        data: serializedData.toString(),
        contentType: 'application/x-www-form-urlencoded',
        success: function (response) {
            showSweetAlert("Berhasil!", response.message, "success", function() {
                resetForm();
                loadPartsData(); // Reload the table data
            });
        },
        error: function (xhr, status, error) {
            console.error("Error response:", xhr.responseJSON);
            let errorMessage = "Gagal Menyimpan Data ! Pastikan Code dan Nama Part Tidak ada yang sama.";

            if (xhr.responseJSON?.errors) {
                errorMessage = Object.values(xhr.responseJSON.errors).join("\n");
            } else if (xhr.responseJSON?.message) {
                errorMessage = xhr.responseJSON.message;
            }

            showSweetAlert(
                "Error!",
                errorMessage,
                "error"
            );
        },
    });
}

// Function to handle edit button click
function handleEditButtonClick(button) {
    isEditMode = true;
    var partCode = $(button).data("part-code");
    var partName = $(button).data("part-name");
    var binLocation = $(button).data("bin-location");
    var partType = $(button).data("part-type");
    var price = $(button).data("price");
    var eum = $(button).data("eum");

    currentPartCode = partCode;

    $("#formTitle").text("Edit Part");
    $("#submitButton").text("Update");

    $("#part_code").val(partCode);
    $("#part_code_display").val(partCode);
    $("#part_name").val(partName);
    $("#bin_location").val(binLocation);
    $("#part_type").val(partType);
    $("#price").val(price);
    $("#eum").val(eum);
    $("#part_code_display").prop("disabled", true);
    $("#cancelButton").show();
}

// Function to handle delete button click
function handleDeleteButtonClick(button) {
    const partCode = $(button).data("part-code");
    const url = `/parts/${partCode}`;

    Swal.fire({
        title: "Apakah Anda yakin?",
        text: "Data ini akan dihapus secara permanen!",
        icon: "warning",
        showCancelButton: true,
        confirmButtonColor: "#d33",
        cancelButtonColor: "#3085d6",
        confirmButtonText: "Ya, hapus!",
        cancelButtonText: "Batal",
        allowOutsideClick: false,  // Prevent closing by clicking outside
        allowEscapeKey: false      // Prevent closing by pressing Esc key
    }).then((result) => {
        if (result.isConfirmed) {
            // Get CSRF token
            const token = document.querySelector('meta[name="csrf-token"]').getAttribute('content');

            $.ajax({
                url: url,
                type: "DELETE",
                data: {
                    _token: token
                },
                success: function (response) {
                    if (response.success) {
                        showSweetAlert("Terhapus!", response.message, "success", function() {
                            loadPartsData(); // Reload the table data
                        });
                    } else {
                        showSweetAlert("Gagal!", response.message, "error");
                    }
                },
                error: function (xhr, status, error) {
                    console.error("DELETE request failed:", error);
                    showSweetAlert(
                        "Gagal!",
                        "Terjadi kesalahan saat menghapus data. Silakan coba lagi.",
                        "error"
                    );
                },
            });
        }
    });
}

// Function to show sweet alert
function showSweetAlert(title, text, icon, callback) {
    const options = {
        title: title,
        text: text,
        icon: icon
    };

    if (icon === 'success') {
        // Success alerts with confirmation button
        options.showConfirmButton = true;
        options.confirmButtonText = 'OK';
        options.confirmButtonColor = '#3085d6';
    } else if (icon === 'error') {
        // Error alerts require user confirmation
        options.showConfirmButton = true;
        options.confirmButtonText = 'OK';
        options.confirmButtonColor = '#3085d6';
    } else {
        // Default behavior for other alert types
        options.showConfirmButton = true;
    }

    return Swal.fire(options).then((result) => {
        if (callback && typeof callback === 'function') {
            callback(result);
        }
    });
}
