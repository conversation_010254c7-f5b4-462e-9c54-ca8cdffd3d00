document.addEventListener('DOMContentLoaded', function() {
    // Get DOM elements
    const mobileMenuToggle = document.getElementById('mobileMenuToggle');
    const mobileMenuClose = document.getElementById('mobileMenuClose');
    const mobileMenu = document.getElementById('mobileMenu');
    const mobileMenuOverlay = document.getElementById('mobileMenuOverlay');
    function openMobileMenu() {
        if (mobileMenu && mobileMenuOverlay) {
            mobileMenu.classList.add('active');
            mobileMenuOverlay.classList.add('active');
            document.body.style.overflow = 'hidden';
            setTimeout(() => {
                mobileMenu.style.right = '0';
            }, 10);
        }
    }
    function closeMobileMenu() {
        if (mobileMenu && mobileMenuOverlay) {
            mobileMenu.style.right = '-100%';
            mobileMenuOverlay.classList.remove('active');
            setTimeout(() => {
                mobileMenu.classList.remove('active');
                document.body.style.overflow = ''; 
            }, 300);
        }
    }

    // Add event listeners
    if (mobileMenuToggle) {
        mobileMenuToggle.addEventListener('click', function(e) {
            e.preventDefault();
            e.stopPropagation();
            openMobileMenu();
        });
    }

    if (mobileMenuClose) {
        mobileMenuClose.addEventListener('click', function(e) {
            e.preventDefault();
            e.stopPropagation();
            closeMobileMenu();
        });
    }

    if (mobileMenuOverlay) {
        mobileMenuOverlay.addEventListener('click', function(e) {
            e.preventDefault();
            closeMobileMenu();
        });
    }
    const navLinks = document.querySelectorAll('.nav-link');
    navLinks.forEach(link => {
        link.addEventListener('click', function() {
            if (window.innerWidth <= 992) {
                closeMobileMenu();
            }
        });
    });
    window.addEventListener('resize', function() {
        if (window.innerWidth > 992) {
            // Reset styles for desktop view
            if (mobileMenu) {
                mobileMenu.style.right = '';
                mobileMenu.classList.remove('active');
                document.body.style.overflow = '';
            }
            if (mobileMenuOverlay) {
                mobileMenuOverlay.classList.remove('active');
            }
        }
    });
    let touchStartX = 0;
    let touchEndX = 0;

    document.addEventListener('touchstart', function(e) {
        touchStartX = e.changedTouches[0].screenX;
    }, false);

    document.addEventListener('touchend', function(e) {
        touchEndX = e.changedTouches[0].screenX;
        handleSwipe();
    }, false);

    function handleSwipe() {
        if (mobileMenu && mobileMenu.classList.contains('active') && touchEndX < touchStartX - 50) {
            closeMobileMenu();
        }
        if (mobileMenu && !mobileMenu.classList.contains('active') && touchStartX < 30 && touchEndX > touchStartX + 50) {
            openMobileMenu();
        }
    }
});
