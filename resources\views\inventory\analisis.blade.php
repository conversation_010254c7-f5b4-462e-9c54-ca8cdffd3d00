@extends('warehouse.content')
@section('resource')
@vite('resources/js/part/Analisis.js')
@endsection
@section('contentho')
<div class="row bgwhite page-title-box shadow-kit mb-1">
    <div class="col d-flex align-items-center" style="max-width: fit-content;">
        <button class="button-menu-mobile text-blue-500 btn-sm">
            <i style="font-size: 30px;" class="mdi mdi-menu"></i>
        </button>
        <div class="text">
            <p class="font-bold m-0">{{ session('name') }}</p>
        </div>
    </div>
    <div class="col">
        <ul class="list-unstyled topnav-menu float-right mb-0">
            <li class="dropdown notification-list">
                <a class="nav-link dropdown-toggle" href="#" data-bs-toggle="dropdown" aria-expanded="false">
                    <i class="mdi mdi-bell-outline noti-icon"></i>
                    <span class="badge badge-danger badge-pill float-right badgenotif"></span>
                </a>
                <div class="dropdown-menu dropdown-menu-end dropdown-lg">
                    <div class="dropdown-item noti-title bg-primary">
                        <h5 class="m-0 text-white d-flex justify-content-between align-items-center">
                            Notifikasi
                            <a href="javascript:void(0);" class="text-white" id="clear-all">
                                <small></small>
                            </a>
                        </h5>
                    </div>
                    <div class="noti-scroll" id="notification-list">
                        <!-- Notifications will be dynamically inserted here -->
                    </div>
                </div>
            </li>
        </ul>
    </div>
</div>
<div class="row bgwhite shadow-kit m-1">
    <div class="container p-4">
        <h1 class="text-2xl font-bold mb-4">Inventory Analysis</h1>
        <div class="mb-2 flex">
            <label for="start_date" class="block text-gray-700 text-sm font-bold mb-2">Start Date:</label>
            <input type="date" id="start_date" class="maxfit form-control ml-3 ">
            <label for="end_date" class="block text-gray-700 text-sm font-bold mb-2 ml-3">End Date:</label>
            <input type="date" id="end_date" class="maxfit form-control ml-3 ">
            <label for="site_id" class="maxfit block text-gray-700 text-sm font-bold mb-2  ml-3">Site:</label>
            <select id="site_id" class=" form-control maxfit  ml-3">
                <option value="">All Sites</option>
                @foreach($sites as $site)
                <option value="{{ $site->site_id }}">{{ $site->site_name }}</option>
                @endforeach
            </select>
            <label for="part_category" class="block text-gray-700 text-sm font-bold mb-2  ml-3">Part Category:</label>
            <select id="part_category" class="form-control maxfit  ml-3">
                <option value="">All Categories</option>
                @foreach($partCategories as $category)
                <option value="{{ $category }}">{{ $category }}</option>
                @endforeach
            </select>
            <label for="part_category" class="block text-gray-700 text-sm font-bold mb-2 ml-3">Jumlah Data</label>
            <input type="number" class="form-control ml-3 maxfit" value="3" name="limit" id="limit">
            <button id="analyzeButton" class="btn btn-primary ml-3">Lihat Hasil</button>
        </div>
        <div id="results" class="mt-8">
        </div>
    </div>
</div>
@endsection