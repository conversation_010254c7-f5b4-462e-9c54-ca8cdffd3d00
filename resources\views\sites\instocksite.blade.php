@extends('sites.content')
@section('contentsite')
@section('title', 'History In Part')
<!-- mulai content disin -->
<div class="row bgwhite page-title-box shadow-kit mb-1">
    <div class="col d-flex align-items-center" style="max-width: fit-content;">
        <button class="button-menu-mobile text-blue-500 btn-sm">
            <i style="font-size: 30px;" class="mdi mdi-menu"></i>
        </button>
        <div class="text">
            <p class="font-bold m-0">{{ session('name') }}</p>
        </div>
    </div>

    <div class="col">
        <ul class="list-unstyled topnav-menu float-right mb-0">
            <li class="dropdown notification-list">
                <a class="nav-link dropdown-toggle" href="#" data-bs-toggle="dropdown" aria-expanded="false">
                    <i class="mdi mdi-bell-outline noti-icon"></i>
                    <span class="badge badge-danger badge-pill float-right badgenotif"></span>
                </a>
                <div class="dropdown-menu dropdown-menu-end dropdown-lg">
                    <div class="dropdown-item noti-title bg-primary">
                        <h5 class="m-0 text-white d-flex justify-content-between align-items-center">
                            Notifikasi
                            <a href="javascript:void(0);" class="text-white" id="clear-all">
                                <small></small>
                            </a>
                        </h5>
                    </div>
                    <div class="noti-scroll" id="notification-list">
                        <!-- Notifications will be dynamically inserted here -->
                    </div>
                </div>
            </li>
        </ul>
    </div>
</div>
<div class="row m-1 mt-3">
    <div class="col bgwhite shadow-kit p-4">
        <h4 class="h4 font-bold text-uppercase">Daftar Pengiriman Dari Warehouse</h4>
        <hr>
        <table class="table table-bordered" id="transactions-table">
            <thead class="table-dark text-white">
                <tr>
                    <th class="p-2">ID</th>
                    <th class="p-2">Part Code</th>
                    <th class="p-2">Part Name</th>
                    <th class="p-2">Tanggal</th>
                    <th class="p-2">Jumlah Dikirim</th>
                    <th class="p-2">Jumlah Diterima</th>
                    <th class="p-2">Status</th>
                    <th class="p-2">Aksi</th>
                    <th class="p-2">lampiran</th>
                </tr>
            </thead>
            <tbody>
            </tbody>
        </table>
        <div id="transactions-pagination" class="mt-3">
            <!-- Pagination will be rendered here by JavaScript -->
        </div>
    </div>
    <div class="col max500 right hidden p-4 bgwhite shadow-kit ml-4">
        <div id="adjustment-form">
            <h2 class="h4 font-bold text-uppercase">Konfirmasi Pengerimaan</h2>
            <form id="adjust-form">
                @csrf
                <input type="hidden" id="adjust-transaction-id" name="transaction_id">
                <div>
                    <label for="quantity_received">Jumlah Diterima:</label>
                    <input class="form-control" type="number" id="quantity_received" name="quantity_received" step="0.1" min="0.1">
                    <span id="quantity_received_error" class="error"></span>
                </div>

                <div>
                    <label for="discrepancy_type">Permasalahan (jika ada):</label>
                    <select class="form-control" id="discrepancy_type" name="discrepancy_type">
                        <option value="">Pilih...</option>
                        <option value="Kekurangan">Kekurangan</option>
                        <option value="Kerusakan">Kerusakan</option>
                    </select>
                    <span id="discrepancy_type_error" class="primary">jumlah otomatis akan dilaporakan ke HO</span>
                </div>
                <div>
                    <label for="discrepancy_reason">Alasan Selisih:</label>
                    <textarea class="form-control" id="discrepancy_reason" name="discrepancy_reason"></textarea>
                    <span id="discrepancy_reason_error" class="error"></span>
                </div>
                <div class="mt-4">
                    <button class=" btn btn-sm btn-primary" type="button" id="submit-adjustment-form-btn">Simpan Penyesuaian</button>
                    <button class="btn btn-sm btn-danger" type="button" id="close-adjustment-form-btn">Batal</button>
                </div>
            </form>
            <div id="adjust-message"></div>
        </div>
    </div>
    <div id="message"></div>
</div>
</div>
<div class="row m-3">
    <div class="col bgwhite shadow-kit">
        <div class="m-2 p-3">
            <h1 class="h4 text-uppercase">Tabel In Stock </h1>
            <div class="col">
                <div class="d-flex flex-wrap align-items-end gap-3 mb-3">
                    <div class="mb-3">
                        <label for="start_date" class="form-label">Start Date</label>
                        <input type="hidden" id="site_id" value="{{ $site_id }}">
                        <input type="date" class="form-control" id="start_date">
                    </div>
                    <div class="mb-3">
                        <label for="end_date" class="form-label">End Date</label>
                        <input type="date" class="form-control" id="end_date">
                    </div>
                    <div class="mb-3">
                        <label for="search_input" class="form-label">Cari Nama / Code</label>
                        <div class="input-group">
                            <input type="text" class="form-control" id="search_input" placeholder="Cari nama / Code">
                        </div>
                    </div>
                </div>
                <div id="instock-table-container">
                    <table class="table table-bordered">
                        <thead>
                            <tr>
                                <th>Part Code</th>
                                <th>Part Name</th>
                                <th>Date In</th>
                                <th>Quantity</th>
                                <th>Notes</th>
                            </tr>
                        </thead>
                        <tbody>
                        </tbody>
                    </table>
                </div>
                <div id="pagination-container" class="mt-3">
                </div>
            </div>
        </div>
    </div>
    <div class="col ml-0 max500">
        <div class="row">
            <div class="col ml-2">
                <div class="card shadow-kit bgwhite p-4 mb-0">
                    <h3>Total In-Stocks</h3>
                    <p id="total" class="display-4 font-bold">{{ $totalQuantity }}</p>
                </div>
            </div>
            <div class="col mr-2">
                <div class="card shadow-kit bgwhite p-3 mb-0">
                    <h3>Pintasan Download Laporan</h3>
                    <div class="my-4 d-between">
                    <button class="btn btn-sm btn-primary report-btn" data-type="in">
                            <i class="mdi mdi-file-import mr-1"></i> Export In
                        </button>
                        <button class="btn btn-sm btn-success report-btn ml-2" data-type="out">
                            <i class="mdi mdi-file-export mr-1"></i> Export Out
                        </button>
                    </div>
                </div>
            </div>
        </div>
        <div class="col p-0">
            <div class="card shadow-kit bgwhite p-4 mt-2">
                <h3>Aktifitas Terakhir</h3>
                <hr class="mt-2">
                <ul id="activityList">
                </ul>
            </div>
        </div>
    </div>
</div>
<div id="suratJalanModal" class="modal fade" tabindex="-1" role="dialog" aria-labelledby="suratJalanModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-xl" style=" max-width: 50% !important;">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="suratJalanModalLabel">Lampiran</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <iframe id="suratJalanFrame" style="width: 100%; height: 500px;" frameborder="0"></iframe>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Tutup</button>
                <a id="downloadSuratJalan" href="#" class="btn btn-primary" download>Unduh</a>
            </div>
        </div>
    </div>
</div>
<!-- Filter Modal -->
<div id="exportModal" class="modal" tabindex="-1" role="dialog" aria-labelledby="exportModalLabel" aria-hidden="true">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="font-bold text-uppercase" id="title">Filter Laporan</h5>
                <span class="close" aria-label="Close">&times;</span>
            </div>
            <form id="exportForm" method="GET">
                <input type="hidden" name="report_type" id="reportType">
                <div class="modal-body">
                    <div class="form-grid">
                        <div class="d-flex gap-3">
                            <div class="form-group flex-grow-1">
                                <label for="start_date">Tanggal Mulai</label>
                                <input type="date" class="form-control" name="start_date">
                            </div>
                            <div class="form-group flex-grow-1">
                                <label for="end_date">Tanggal Selesai</label>
                                <input type="date" class="form-control" name="end_date">
                            </div>
                        </div>
                        <div class="d-flex gap-3">
                            <div class="form-group flex-grow-1">
                                <label for="sort_by">Urutkan Berdasarkan</label>
                                <select class="custom-select" name="sort_by">
                                    <option value="date">Tanggal</option>
                                    <option value="part_name">Nama Part</option>
                                </select>
                            </div>
                            <div class="form-group flex-grow-1 hidden">
                                <label for="site_id">Site</label>
                                <select class="custom-select" name="site_id">
                                    <option value="{{ session('site_id') }}" selected>Semua Site</option>
                                </select>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <!-- <button type="button" class="btn btn-secondary" id="closeModal">Tutup</button> -->
                    <button type="button" class="btn btn-pink btn-rounded waves-effect waves-light" id="exportPdf">
                        <i class="mdi mdi-file-pdf-box mr-1"></i> Export PDF
                    </button>
                    <button type="button" class="btn btn-pink btn-rounded waves-effect waves-light" id="exportExcel">
                        <i class="mdi mdi-file-excel mr-1"></i> Export Excel
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>
@endsection
@section('resourcesite')
@vite(['resources/js/site/Instocksite.js'])
@vite(['resources/js/laporan.js'])
@endsection