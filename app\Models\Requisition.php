<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Requisition extends Model
{
    use HasFactory;

    protected $primaryKey = 'requisition_id';
    // app/Models/Requisition.php
    protected $fillable = [
        'site_id',
        'title',
        'notes',
        'requisition_type',
        'requisition_date',
        'modified_by',
        'status',
        'confirmation_date',
    ];
    public function requisitionDetails()
    {
        return $this->hasMany(RequisitionDetail::class, 'requisition_id');
    }
    public function Details()
    {
        return $this->hasMany(RequisitionDetail::class, 'requisition_id');
    }

    public function site()
    {
        return $this->belongsTo(Site::class, 'site_id', 'site_id');
    }

    // Hapus relasi yang tidak diperlukan
    public function part()
    {
        return $this->belongsTo(Part::class, 'part_code');
    }
}

