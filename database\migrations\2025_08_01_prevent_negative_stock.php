<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // First, fix any existing negative stock quantities
        $this->fixNegativeStockQuantities();
        
        // Then add the check constraint to prevent future negative values
        if (DB::connection()->getDriverName() === 'mysql') {
            // For MySQL, we need to use a trigger since it doesn't support check constraints directly
            DB::unprepared('
                CREATE TRIGGER prevent_negative_stock_quantity
                BEFORE UPDATE ON part_inventories
                FOR EACH ROW
                BEGIN
                    IF NEW.stock_quantity < 0 THEN
                        SIGNAL SQLSTATE \'45000\' 
                        SET MESSAGE_TEXT = \'Stock quantity cannot be negative\';
                    END IF;
                END;
            ');
            
            DB::unprepared('
                CREATE TRIGGER prevent_negative_stock_quantity_insert
                BEFORE INSERT ON part_inventories
                FOR EACH ROW
                BEGIN
                    IF NEW.stock_quantity < 0 THEN
                        SIGNAL SQLSTATE \'45000\' 
                        SET MESSAGE_TEXT = \'Stock quantity cannot be negative\';
                    END IF;
                END;
            ');
        } else {
            // For PostgreSQL or other databases that support check constraints
            Schema::table('part_inventories', function (Blueprint $table) {
                $table->check('stock_quantity >= 0', 'check_stock_quantity_not_negative');
            });
        }
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        if (DB::connection()->getDriverName() === 'mysql') {
            DB::unprepared('DROP TRIGGER IF EXISTS prevent_negative_stock_quantity');
            DB::unprepared('DROP TRIGGER IF EXISTS prevent_negative_stock_quantity_insert');
        } else {
            Schema::table('part_inventories', function (Blueprint $table) {
                $table->dropCheck('check_stock_quantity_not_negative');
            });
        }
    }
    
    /**
     * Fix any existing negative stock quantities by setting them to 0
     */
    private function fixNegativeStockQuantities(): void
    {
        DB::table('part_inventories')
            ->where('stock_quantity', '<', 0)
            ->update(['stock_quantity' => 0]);
    }
};
