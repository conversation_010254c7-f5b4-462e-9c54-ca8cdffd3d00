/*
 * Superadmin Scaling CSS
 * This file contains styles for scaling down components on large screens
 * to make the UI more compact and usable on high-resolution displays.
 * It also includes responsive styles for mobile devices.
 */

/* Scale down components on large screens */
@media (min-width: 1920px) {
    .dashboard-container {
        transform: scale(0.5);
        transform-origin: top center;
        width: 200%; /* Double the width to compensate for scaling */
        max-width: none;
        margin: 0 auto;
        height: auto;
        overflow: visible;
        position: relative;
    }

    body {
        min-height: 50vh; /* Adjust body height to match scaled content */
        overflow-x: hidden;
    }

    /* Adjust header for scaling */
    .login-theme-header {
        width: 100%;
        position: relative;
    }

    /* Adjust content wrapper for scaling */
    .content-wrapper {
        width: 100%;
        position: relative;
    }

    /* Adjust skeleton loader for scaling */
    #skeleton-loader {
        width: 100%;
        position: relative;
    }

    /* Adjust modal sizes for scaling */
    .modal-dialog {
        max-width: 200%; /* Double the width to compensate for scaling */
    }

    /* Adjust table font sizes for better readability after scaling */
    .table {
        font-size: 1.5rem;
    }

    /* Adjust button sizes for better clickability after scaling */
    .btn {
        padding: 0.75rem 1.5rem;
        font-size: 1.25rem;
    }

    /* Adjust form control sizes for better usability after scaling */
    .form-control {
        padding: 0.75rem 1.5rem;
        font-size: 1.25rem;
    }
}

/* Mobile Responsive Styles */
@media (max-width: 992px) {
    /* Disable scaling on mobile */
    .dashboard-container {
        transform: none !important;
        width: 100% !important;
        max-width: 100% !important;
    }

    body {
        min-height: 100vh !important;
    }

    /* Improve table responsiveness */
    .table-responsive {
        border: 0;
        margin-bottom: 0;
    }

    /* Adjust card spacing for mobile */
    .card {
        margin-bottom: 15px;
    }

    .card-body {
        padding: 0.75rem;
    }

    /* Adjust chart heights for mobile */
    .chart-container {
        height: 180px !important;
    }
}

/* Small Mobile Devices */
@media (max-width: 576px) {
    /* Further adjustments for very small screens */
    .card-header {
        padding: 0.5rem 0.75rem;
    }

    .card-body {
        padding: 0.5rem;
    }

    .chart-container {
        height: 150px !important;
    }

    /* Make badges more compact */
    .badge {
        padding: 0.25rem 0.5rem;
        font-size: 0.75rem;
    }

    /* Adjust modal padding */
    .modal-body {
        padding: 0.75rem;
    }

    .modal-header, .modal-footer {
        padding: 0.5rem 0.75rem;
    }

    /* Adjust table cell padding */
    .table th, .table td {
        padding: 0.4rem 0.5rem;
        font-size: 0.8rem;
    }
}

/* Dynamic scaling script to be included in all superadmin pages */
/*
document.addEventListener('DOMContentLoaded', function() {
    const dashboardContainer = document.querySelector('.dashboard-container');

    // Function to dynamically adjust scaling based on screen width
    function adjustScaling() {
        const screenWidth = window.innerWidth;

        // For screens wider than 1920px, apply dynamic scaling
        if (screenWidth >= 1920) {
            // Calculate scale factor (50% at 1920px, gradually decreasing as screen gets larger)
            const scaleFactor = Math.max(0.5, 1920 / screenWidth);

            // Apply scaling transform
            dashboardContainer.style.transform = `scale(${scaleFactor})`;
            dashboardContainer.style.transformOrigin = 'top center';
            dashboardContainer.style.width = `${(100 / scaleFactor)}%`;
            dashboardContainer.style.maxWidth = 'none';

            // Adjust body height to match scaled content
            document.body.style.minHeight = `${50 * scaleFactor}vh`;
        } else {
            // Reset styles for smaller screens
            dashboardContainer.style.transform = '';
            dashboardContainer.style.transformOrigin = '';
            dashboardContainer.style.width = '';
            dashboardContainer.style.maxWidth = '';
            document.body.style.minHeight = '';
        }
    }

    // Initial adjustment
    adjustScaling();

    // Adjust scaling on window resize
    window.addEventListener('resize', adjustScaling);
});
*/
