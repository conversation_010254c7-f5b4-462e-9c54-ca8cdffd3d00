@extends('sites.content')
@section('contentsite')
<!-- mulai content disin -->
<div class="row bgwhite page-title-box shadow-kit mb-1">
    <div class="col d-flex align-items-center" style="max-width: fit-content;">
        <button class="button-menu-mobile text-blue-500 btn-sm">
            <i style="font-size: 30px;" class="mdi mdi-menu"></i>
        </button>
        <div class="text">
            <p class="font-bold m-0">{{ session('name') }}</p>
        </div>
    </div>

    <div class="col">
        <ul class="list-unstyled topnav-menu float-right mb-0">
            <li class="dropdown notification-list">
                <a class="nav-link dropdown-toggle" href="#" data-bs-toggle="dropdown" aria-expanded="false">
                    <i class="mdi mdi-bell-outline noti-icon"></i>
                    <span class="badge badge-danger badge-pill float-right badgenotif"></span>
                </a>
                <div class="dropdown-menu dropdown-menu-end dropdown-lg">
                    <div class="dropdown-item noti-title bg-primary">
                        <h5 class="m-0 text-white d-flex justify-content-between align-items-center">
                            Notifikasi
                            <a href="javascript:void(0);" class="text-white" id="clear-all">
                                <small></small>
                            </a>
                        </h5>
                    </div>
                    <div class="noti-scroll" id="notification-list">
                        <!-- Notifications will be dynamically inserted here -->
                    </div>
                </div>
            </li>
        </ul>
    </div>
</div>

<div class="row p-2">
    <div class="col">
        <div class="shadow-kit bgwhite p-4">
            <h1 class="h4">Log Aktivitas Anda</h1>
            <div class="row mb-4">
                <div class="col-md-3">
                    <label for="start_date" class="form-label">Dari Tanggal</label>
                    <input class="form-control" type="date" id="start_date">
                </div>
                <div class="col-md-3">
                    <label for="end_date" class="form-label">Sampai Tanggal</label>
                    <input class="form-control" type="date" id="end_date">
                </div>
                <div class="col-md-3">
                    <label for="action_filter" class="form-label">Jenis Aksi</label>
                    <select class="form-control" id="action_filter">
                        <option value="all">Semua Aksi</option>
                        @foreach($statuses as $status)
                        <option value="{{ $status }}">{{ $status }}</option>
                        @endforeach
                    </select>
                </div>
                <div class="col-md-3">
                    <label for="search_input" class="form-label">Cari</label>
                    <input class="form-control" type="text" id="search_input" placeholder="Cari Log...">
                </div>
            </div>
            <div class="table-responsive">
                <table class="table table-bordered w-100" id="logTable">
                    <thead class="table-dark text-white">
                        <tr>
                            <th class="p-2">No</th>
                            <th class="p-2">Nama</th>
                            <th class="p-2">Aksi</th>
                            <th class="p-2">Deskripsi</th>
                            <th class="p-2">Tabel Diperbaharui</th>
                            <th class="p-2">Waktu</th>
                        </tr>
                    </thead>
                    <tbody>
                        <!-- Data will be loaded here by JavaScript -->
                    </tbody>
                </table>
            </div>
            <div id="pagination-container" class="mt-3">
                <!-- Pagination will be rendered here by JavaScript -->
            </div>
        </div>
    </div>

</div>
@endsection
@vite('resources/js/site/Logsite.js')