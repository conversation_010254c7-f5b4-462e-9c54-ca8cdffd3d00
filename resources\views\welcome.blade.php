<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Chart & SweetAlert2 Test</title>
    <style>
        .chart-container {
            width: 80%;
            margin: auto;
        }
    </style>
    @vite(['resources/css/app.css', 'resources/js/chart.js','resources/js/style.js '])
<body>
    <div class="container">
        <h1>Dashboard</h1>
        <form method="GET" action="{{ route('dashboard') }}" class="mb-4">
            <div class="row g-3 align-items-center">
                <!-- Filter controls remain the same -->
                <div class="col-auto">
                    <div class="btn-group" role="group">
                        <input type="radio" class="btn-check" name="group_by" id="day" value="day" {{ $groupBy === 'day' ? 'checked' : '' }}>
                        <label class="btn btn-outline-primary" for="day">Day</label>
                        <input type="radio" class="btn-check" name="group_by" id="week" value="week" {{ $groupBy === 'week' ? 'checked' : '' }}>
                        <label class="btn btn-outline-primary" for="week">Week</label>
                        <input type="radio" class="btn-check" name="group_by" id="month" value="month" {{ $groupBy === 'month' ? 'checked' : '' }}>
                        <label class="btn btn-outline-primary" for="month">Month</label>
                    </div>
                </div>
                <div class="col-auto">
                    <input type="date" class="form-control" name="start_date" value="{{ $startDate }}">
                </div>
                <div class="col-auto">
                    <input type="date" class="form-control" name="end_date" value="{{ $endDate }}">
                </div>
                <div class="col-auto">
                    <button type="submit" class="btn btn-primary">Apply Filter</button>
                </div>
            </div>
        </form>

        <!-- In Stock Chart -->
        <div class="mb-5">
            <h3>In Stock History</h3>
            <div style="min-height: 400px;" class="chart-container">
                <canvas id="inStockChart"></canvas>
            </div>
        </div>

        <!-- Out Stock Chart -->
        <div class="mb-5">
            <h3>Out Stock History</h3>
            <div style="min-height: 400px;" class="chart-container">
                <canvas id="outStockChart"></canvas>
            </div>
        </div>

        <script>
            window.inStockData = @json($inStockChartData);
            window.outStockData = @json($outStockChartData);
            window.groupBy = @json($groupBy);
        </script>
    </div>
</body>

</html>