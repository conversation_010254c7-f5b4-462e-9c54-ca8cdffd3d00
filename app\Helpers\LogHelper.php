<?php

namespace App\Helpers;

use App\Models\LogAktivitas;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Session;

class LogHelper
{
    public static function createDirectLog(string $action, string $description, string $table, Request $request): LogAktivitas
    {
        // Always use the correct column name (description, not decription)
        return LogAktivitas::create([
            'site_id' => session('site_id'),
            'name' => session('name'),
            'action' => $action,
            'description' => $description,
            'table' => $table,
            'ip_address' => $request->ip(),
        ]);
    }
    public static function createLog(string $action, string $description, string $table, Request $request): LogAktivitas
    {
        // Ensure description is never empty and is specific
        if (empty($description)) {
            $name = session('name');
            $ipAddress = $request->ip();
            $timestamp = now()->format('Y-m-d H:i:s');

            // Try to extract specific item information from the request
            $itemInfo = "";

            // Check for common item identifiers in the request
            $possibleItemFields = ['part_name', 'part_code', 'unit_code', 'unit_type', 'item_name', 'name'];
            foreach ($possibleItemFields as $field) {
                if ($request->has($field) && !empty($request->input($field))) {
                    $itemInfo = ": " . $request->input($field);
                    break;
                }
            }

            // Create a more specific description based on common action patterns
            if (strpos($action, 'Menambahkan') !== false || strpos($action, 'Membuat') !== false) {
                $description = "User {$name} menambahkan {$table}{$itemInfo}. IP: {$ipAddress}, Timestamp: {$timestamp}";
            }
            elseif (strpos($action, 'Mengubah') !== false) {
                $description = "User {$name} mengubah {$table}{$itemInfo}. IP: {$ipAddress}, Timestamp: {$timestamp}";
            }
            elseif (strpos($action, 'Menghapus') !== false || strpos($action, 'Hapus') !== false) {
                $description = "User {$name} menghapus {$table}{$itemInfo}. IP: {$ipAddress}, Timestamp: {$timestamp}";
            }
            else {
                $description = "User {$name} melakukan {$action} pada {$table}{$itemInfo}. IP: {$ipAddress}, Timestamp: {$timestamp}";
            }
        }

        // Always use the correct column name
        return LogAktivitas::create([
            'site_id' => session('site_id'),
            'name' => session('name'),
            'action' => $action,
            'description' => $description, // Always use 'description', not 'decription'
            'table' => $table,
            'ip_address' => $request->ip(),
        ]);
    }
    public static function logCreate(string $itemType, string $itemName, string $table, Request $request, array $additionalInfo = []): LogAktivitas
    {
        $additionalInfoStr = !empty($additionalInfo) ? ' dengan ' . implode(', ', $additionalInfo) : '';
        $description = "User " . session('name') . " menambahkan {$itemType}: {$itemName}{$additionalInfoStr}";

        return self::createLog("Menambahkan {$itemType}", $description, $table, $request);
    }

    public static function logUpdate(string $itemType, string $itemName, string $table, Request $request, array $changes = []): LogAktivitas
    {
        $changesStr = !empty($changes) ? ' dengan perubahan ' . implode(', ', $changes) : '';
        $description = "User " . session('name') . " mengubah {$itemType}: {$itemName}{$changesStr}";

        return self::createLog("Mengubah {$itemType}", $description, $table, $request);
    }

    public static function logDelete(string $itemType, string $itemName, string $table, Request $request): LogAktivitas
    {
        $description = "User " . session('name') . " menghapus {$itemType}: {$itemName}";

        return self::createLog("Menghapus {$itemType}", $description, $table, $request);
    }

    public static function logPasswordChange(string $username, Request $request): LogAktivitas
    {
        $description = "User " . session('name') . " mengubah password untuk akun: {$username}";

        return self::createLog("Mengubah Password", $description, "Users", $request);
    }

    public static function logTokenGeneration(string $username, string $generatedBy, Request $request): LogAktivitas
    {
        $description = "Admin {$generatedBy} membuat token reset password untuk user: {$username}";

        return self::createLog("Membuat Token Reset", $description, "Users", $request);
    }

    public static function logPartMerge(string $newPartName, array $oldParts, Request $request): LogAktivitas
    {
        $oldPartsStr = implode(', ', array_map(function($part) {
            return $part['name'] . ' (' . $part['jumlah'] . ')';
        }, $oldParts));

        $description = "User " . session('name') . " menggabungkan part menjadi {$newPartName} dari part: {$oldPartsStr}";

        return self::createLog("Menggabungkan Part", $description, "Part Merge", $request);
    }

    public static function logUnitTransaction(string $action, string $unitType, string $status, array $parts, Request $request): LogAktivitas
    {
        $partsStr = implode(', ', array_map(function($part) {
            return $part['name'] . ' (' . $part['quantity'] . ')';
        }, $parts));

        // Customize description based on action
        if ($action === 'membuat') {
            $description = "User " . session('name') . " membuat transaksi unit {$unitType} dengan status {$status} dan mengeluarkan part: {$partsStr} dari inventory";
            $actionTitle = 'Membuat Unit Transaction';
        } elseif ($action === 'mengubah status') {
            $description = "User " . session('name') . " mengubah status transaksi unit {$unitType} menjadi {$status} dengan part: {$partsStr}";
            $actionTitle = 'Mengubah Status Unit Transaction';
        } elseif ($action === 'menghapus') {
            $description = "User " . session('name') . " menghapus transaksi unit {$unitType} dengan status {$status} dan mengembalikan part: {$partsStr} ke inventory";
            $actionTitle = 'Menghapus Unit Transaction';
        } else {
            $description = "User " . session('name') . " {$action} transaksi unit {$unitType} dengan status {$status} dan part: {$partsStr}";
            $actionTitle = "{$action} Unit Transaction";
        }

        return self::createLog($actionTitle, $description, "Unit Transaction", $request);
    }
}
