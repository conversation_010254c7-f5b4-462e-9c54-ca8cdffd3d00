<table class="table">
    <thead  class="table-dark text-white">
        <tr>
            <th>Kode Part</th>
            <th>Nama Part</th>
            <th>Site</th>
            <th>Min Stock</th>
            <th>Action</th>
        </tr>
    </thead>
    <tbody>
        @foreach($parts as $part)
            @foreach($part->sites as $site)
                <tr>
                    <td>{{ $part->code_part }}</td>
                    <td>{{ $part->part_name }}</td>
                    <td>{{ $site->site_name }}</td>
                    <td>{{ $site->pivot->min_stock }}</td>
                    <td>
                    <button class="btn btn-danger btn-sm" onclick="deletePart('{{ $site->pivot->id_partinventory }}')">Hapus</button>
                    </td>
                </tr>
            @endforeach
        @endforeach
    </tbody>
</table>
{{ $parts->links() }}