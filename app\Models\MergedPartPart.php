<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class MergedPartPart extends Model
{
    use HasFactory;

    protected $table = 'merged_part_parts';

    protected $fillable = [
        'part_merge_id',
        'part_code',
    ];

    public function partMerge()
    {
        return $this->belongsTo(PartMerge::class, 'part_merge_id', 'part_merge_id');
    }

    public function part()
    {
        return $this->belongsTo(Part::class, 'part_code', 'part_code');
    }
}