import Swal from "sweetalert2";
import bootstrap from '../bootstrap-init';

// Global variables for pagination
let currentPage = 1;
const itemsPerPage = 15; // 15 items per page for part table

// Function to reload data - moved outside DOMContentLoaded to be globally accessible
function reloadData(page = 1) {
    currentPage = page;
    loadLastActivities();

    // Get filter values
    const searchName = document.getElementById('searchName')?.value || '';
    const searchStock = document.getElementById('searchStock')?.value || '';
    const partType = document.getElementById('partType')?.value || 'all';

    // Show loading indicator
    const tableBody = document.querySelector("#partTable tbody");
    tableBody.innerHTML = '<tr><td colspan="8" class="text-center">Loading...</td></tr>';
    // Build URL with filters
    const url = new URL('/part-inventory-data', window.location.origin);
    url.searchParams.append('page', page);
    url.searchParams.append('per_page', itemsPerPage);
    if (searchName) url.searchParams.append('search_name', searchName);
    if (searchStock) url.searchParams.append('search_stock', searchStock);
    if (partType !== 'all') url.searchParams.append('part_type', partType);

    fetch(url)
        .then((response) => response.json())
        .then((data) => {
            tableBody.innerHTML = "";

            if (data.data && data.data.length > 0) {
                data.data.forEach((partInventory) => {
                    // Format price as Indonesian Rupiah
                    const formattedPrice = new Intl.NumberFormat('id-ID', {
                        style: 'currency',
                        currency: 'IDR',
                        minimumFractionDigits: 0
                    }).format(partInventory.price || 0);

                    // Use site_part_name if available, otherwise use the original part_name
                    const displayPartName = partInventory.site_part_name || partInventory.part.part_name;
                    const originalPartName = partInventory.part.part_name;

                    const row = `
                    <tr>
                        <td>${partInventory.part.part_code}</td>
                        <td>${displayPartName}</td>
                        <td>${partInventory.part.bin_location}</td>
                        <td>${partInventory.stock_quantity}</td>
                        <td>${partInventory.min_stock}</td>
                        <td>${partInventory.max_stock}</td>
                        <td>${formattedPrice}</td>
                        <td>
                            <button type="button" class="btn btn-sm btn-info part-detail-btn"
                                data-bs-toggle="tooltip"
                                data-bs-placement="top"
                                title="Nama Part di HO: ${originalPartName}">
                                <i class="mdi mdi-information-outline"></i>
                            </button>
                        </td>
                    </tr>
                    `;
                    tableBody.innerHTML += row;
                });

                // Render pagination
                renderPagination({
                    current_page: data.current_page,
                    per_page: data.per_page,
                    last_page: data.last_page,
                    total: data.total
                });

                // Initialize tooltips
                initializeTooltips();
            } else {
                tableBody.innerHTML =
                    '<tr><td colspan="8">Tidak ada part yang ditemukan.</td></tr>';
            }
        })
        .catch((error) => {
            console.error("Error:", error);
        });
}

// Function to load last activities
function loadLastActivities() {
    fetch("/last-activities")
        .then((response) => response.json())
        .then((data) => {
            const ul = document.getElementById("activityList");

            if (!ul) {
                return;
            }
            ul.innerHTML = "";
            data.forEach((activity) => {
                const li = document.createElement("li");
                const a = document.createElement("a");
                a.href = "#";
                a.textContent = activity.decription;
                li.appendChild(a);
                ul.appendChild(li);
            });
        })
        .catch((error) => {
            const ul = document.getElementById("activityList");
            if (ul) {
                ul.innerHTML = "<li>Error loading activities.</li>";
            }
        });
}

// Function to create pagination item
function createPaginationItem(page, text, isActive = false) {
    const li = document.createElement('li');
    li.className = `page-item ${isActive ? 'active' : ''}`;

    const a = document.createElement('a');
    a.className = 'page-link';
    a.href = '#';
    a.textContent = text;
    a.dataset.page = page;

    li.appendChild(a);
    return li;
}

// Function to render pagination
function renderPagination(data) {
    try {
        const paginationContainer = document.getElementById('part-pagination');
        if (!paginationContainer) {
            return;
        }

        paginationContainer.innerHTML = '';

        if (data.last_page > 1) {
            const pagination = document.createElement('ul');
            pagination.className = 'pagination pagination-rounded  justify-content-center';

            // Previous button
            if (data.current_page > 1) {
                pagination.appendChild(createPaginationItem(data.current_page - 1, '\u00ab'));
            }

            // Page numbers
            for (let i = 1; i <= data.last_page; i++) {
                pagination.appendChild(createPaginationItem(i, i, i === data.current_page));
            }

            // Next button
            if (data.current_page < data.last_page) {
                pagination.appendChild(createPaginationItem(data.current_page + 1, '\u00bb'));
            }

            paginationContainer.appendChild(pagination);

            // Add event listeners to pagination links
            paginationContainer.querySelectorAll('.page-link').forEach(link => {
                link.addEventListener('click', function(e) {
                    e.preventDefault();
                    const page = parseInt(this.dataset.page);
                    currentPage = page;
                    reloadData(page);
                });
            });
        }

        // Update global pagination data
        window.partPaginationData = data;
    } catch (error) {
        console.error('Error rendering pagination:', error);
    }
}

// Function to format number to Indonesian Rupiah
function formatRupiah(number) {
    return new Intl.NumberFormat('id-ID', {
        style: 'currency',
        currency: 'IDR',
        minimumFractionDigits: 0,
        maximumFractionDigits: 0
    }).format(number);
}

// Function to initialize tooltips
function initializeTooltips() {
    const tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
    tooltipTriggerList.map(function (tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl);
    });

    // Add click event for detail buttons to show modal with original part name
    document.querySelectorAll('.part-detail-btn').forEach(btn => {
        btn.addEventListener('click', function() {
            const originalPartName = this.getAttribute('title').replace('Nama Part di HO: ', '');
            Swal.fire({
                title: 'Detail Part',
                html: `<p><strong>Nama Part di HO:</strong> ${originalPartName}</p>`,
                icon: 'info',
                confirmButtonText: 'Tutup'
            });
        });
    });
}

// Function to parse Rupiah string to number
function parseRupiah(rupiahString) {
    if (!rupiahString) return 0;
    return parseInt(rupiahString.replace(/[^,\d]/g, ''));
}

document.addEventListener("DOMContentLoaded", function () {
    const partSearch = document.getElementById("partSearch");
    const suggestions = document.getElementById("suggestions");
    const addPartForm = document.getElementById("addPartForm");
    const selectedPartName = document.getElementById("selectedPartName");
    const selectedPartCode = document.getElementById("selectedPartCode");
    const priceDisplay = document.getElementById("price_display");
    const priceInput = document.getElementById("price");

    // Initialize price formatter
    if (priceDisplay && priceInput) {
        priceDisplay.addEventListener('input', function() {
            // Format the input as Rupiah
            const numValue = parseRupiah(this.value);
            priceInput.value = numValue;
            this.value = formatRupiah(numValue);
        });
    }

    // Filter elements
    const searchName = document.getElementById("searchName");
    const searchStock = document.getElementById("searchStock");
    const partType = document.getElementById("partType");
    const resetFilters = document.getElementById("resetFilters");

    function debounce(func, delay) {
        let timeout;
        return function (...args) {
            const context = this;
            clearTimeout(timeout);
            timeout = setTimeout(() => func.apply(context, args), delay);
        };
    }

    // Initial data load with page 1
    reloadData(1);

    // Add event listeners for filters
    if (searchName) {
        searchName.addEventListener('input', debounce(() => {
            reloadData(1); // Reset to page 1 when filter changes
        }, 500));
    }

    if (searchStock) {
        searchStock.addEventListener('input', debounce(() => {
            reloadData(1);
        }, 500));
    }

    if (partType) {
        partType.addEventListener('change', () => {
            reloadData(1);
        });
    }

    // Reset filters button
    if (resetFilters) {
        resetFilters.addEventListener('click', () => {
            if (searchName) searchName.value = '';
            if (searchStock) searchStock.value = '';
            if (partType) partType.value = 'all';
            reloadData(1);
        });
    }

    // Attach delete event listeners to the delete buttons
    function attachDeleteEventListeners() {
        document.querySelectorAll(".delete-part-btn").forEach((button) => {
            button.addEventListener("click", function (event) {
                event.preventDefault();
                const partInventoryId = this.dataset.partInventoryId;

                Swal.fire({
                    title: "Apakah Anda yakin?",
                    text: "Apakah Anda yakin ingin menghapus inventaris bagian ini?",
                    icon: "warning",
                    showCancelButton: true,
                    confirmButtonColor: "#3085d6",
                    cancelButtonColor: "#d33",
                    confirmButtonText: "Ya, Hapus!",
                    cancelButtonText: "Batal",
                }).then((result) => {
                    if (result.isConfirmed) {
                        fetch(`/partminmax/${partInventoryId}`, {
                            method: "DELETE",
                            headers: {
                                "Content-Type": "application/json",
                                "X-CSRF-TOKEN": document.querySelector(
                                    'meta[name="csrf-token"]'
                                ).content,
                                "X-Requested-With": "XMLHttpRequest",
                            },
                        })
                            .then((response) => response.json())
                            .then((data) => {
                                if (data.success === false) {
                                    Swal.fire({
                                        icon: "error",
                                        title: "Gagal!",
                                        text: data.message || "Gagal menghapus inventaris bagian."
                                    });
                                } else {
                                    Swal.fire({
                                        icon: "success",
                                        title: "Berhasil!",
                                        text: data.message || "Inventaris bagian berhasil dihapus!"
                                    });
                                    reloadData(1); // Reset to page 1 after deleting
                                }
                            })
                            .catch((error) => {
                                Swal.fire(
                                    "Error!",
                                    "Gagal menghapus inventaris bagian.",
                                    "error"
                                );
                            });
                    }
                });
            });
        });
    }

    // Autocomplete
    if (partSearch) {
        // Close suggestions when clicking outside
        document.addEventListener('click', function(e) {
            if (suggestions && !partSearch.contains(e.target) && !suggestions.contains(e.target)) {
                suggestions.style.display = 'none';
            }
        });

        partSearch.addEventListener(
            "input",
            debounce(function () {
                const searchTerm = this.value;
                if (searchTerm.length < 2) {
                    suggestions.style.display = "none";
                    return;
                }

                fetch(`/partminmax/autocomplete?search=${searchTerm}`)
                    .then((response) => response.json())
                    .then((parts) => {
                        suggestions.innerHTML = "";
                        if (parts.length > 0) {
                            // Limit the number of displayed results to 20 to prevent excessive scrolling
                            const maxResults = 20;
                            const displayParts = parts.slice(0, maxResults);

                            // Add a message if results were limited
                            if (parts.length > maxResults) {
                                const limitMessage = document.createElement("div");
                                limitMessage.textContent = `Menampilkan ${maxResults} dari ${parts.length} hasil. Silakan perjelas pencarian Anda.`;
                                limitMessage.classList.add("suggestion-limit-message");
                                suggestions.appendChild(limitMessage);
                            }

                            displayParts.forEach((part) => {
                                const suggestion = document.createElement("div");
                                suggestion.classList.add("suggestion-item");
                                suggestion.textContent = `${part.part_code} - ${part.part_name}`;
                                suggestion.dataset.partCode = part.part_code;
                                suggestion.dataset.partName = part.part_name;
                                suggestions.appendChild(suggestion);
                            });
                            suggestions.style.display = "block";
                        } else {
                            suggestions.style.display = "none";
                        }
                    })
                    .catch((error) =>
                        console.error(
                            "Error fetching autocomplete suggestions:",
                            error
                        )
                    );
            }, 300)
        );
    }

    // Suggestion Click
    if (suggestions) {
        suggestions.addEventListener("click", function (event) {
            if (event.target.classList.contains("suggestion-item")) {
                const partCode = event.target.dataset.partCode;
                const partName = event.target.dataset.partName;

                selectedPartCode.value = partCode;
                selectedPartName.value = `${partCode} - ${partName}`;
                suggestions.style.display = "none";
                partSearch.value = "";

                // Reset price to default
                if (priceInput && priceDisplay) {
                    priceInput.value = 0;
                    priceDisplay.value = formatRupiah(0);
                }
            }
        });
    }

    // Form Submission
    if (addPartForm) {
        addPartForm.addEventListener("submit", function (event) {
            event.preventDefault();

            const partCode = selectedPartCode.value;
            if (!partCode) {
                Swal.fire("Peringatan!", "Silakan pilih Part.", "warning");
                return;
            }

            const sitePartName = document.getElementById(`sitePartName`).value;
            const minStock = document.getElementById(`minStock`).value;
            const maxStock = document.getElementById(`maxStock`).value;
            const price = document.getElementById(`price`).value; // Using the hidden input with raw value

            const payload = {
                part_code: partCode,
                site_part_name: sitePartName,
                min_stock: minStock,
                max_stock: maxStock,
                price: price,
            };

            Swal.fire({
                title: "Konfirmasi",
                text: "Apakah Anda yakin mengubah data ini?",
                icon: "question",
                showCancelButton: true,
                confirmButtonColor: "#3085d6",
                cancelButtonColor: "#d33",
                confirmButtonText: "Ya, Simpan!",
                cancelButtonText: "Batal",
            }).then((result) => {
                if (result.isConfirmed) {
                    fetch("/partminmax", {
                        method: "POST",
                        headers: {
                            "Content-Type": "application/json",
                            "X-CSRF-TOKEN": document.querySelector(
                                'meta[name="csrf-token"]'
                            ).content,
                            "X-Requested-With": "XMLHttpRequest",
                        },
                        body: JSON.stringify(payload),
                    })
                        .then((response) => {
                            if (!response.ok) {
                                return response.json().then((error) => {
                                    throw new Error(
                                        error.message || "Gagal menyimpan data."
                                    );
                                });
                            }
                            return response.json();
                        })
                        .then((data) => {
                            Swal.fire(
                                "Berhasil!",
                                data.message || "Data berhasil disimpan!",
                                "success"
                            );
                            reloadData(1); // Reset to page 1 after adding
                        })
                        .catch((error) => {
                            Swal.fire(
                                "Error!",
                                error.message || "Gagal menyimpan data.",
                                "error"
                            );
                        });
                }
            });
        });
    }
    attachDeleteEventListeners();
});
