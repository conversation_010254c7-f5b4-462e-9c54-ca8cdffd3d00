@extends('sales.contentsales')
@section('title', 'Manual Invoices')
@section('resourcesales')
<style>
    .w-fit-content {
        width: fit-content;
    }
    .shadow-kit {
        border: 1px solid rgb(42, 105, 168);
        box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
        border-radius: 0.5rem;
        background-color: #fff;
    }
    .btn{
        font-size: 11px;
    }
    .btn-warning{
        background-color:rgb(242, 215, 132);
        color: #000;
    }
    .bg-warning{
        background-color:rgb(240, 250, 150);
        color: #0f0187;
    }

    /* Sortable headers styling */
    .sortable {
        cursor: pointer;
        position: relative;
        user-select: none;
    }

    .sortable:hover {
        background-color: rgba(0, 0, 0, 0.05);
    }

    .sortable.asc .sort-icon:before {
        content: "\F4CE"; /* mdi-sort-ascending */
    }

    .sortable.desc .sort-icon:before {
        content: "\F4CF"; /* mdi-sort-descending */
    }

    .sort-icon {
        font-size: 11px;
        margin-left: 5px;
        opacity: 0.5;
    }

    .sortable.asc .sort-icon {
        opacity: 1;
        transform: rotate(180deg);
    }

    .sortable.desc .sort-icon {
        opacity: 1;
        transform: rotate(0deg);
    }

    /* Search box styles */
    .search-box {
        position: relative;
        width: 200px;
    }
    .search-icon {
        position: absolute;
        right: 10px;
        top: 50%;
        transform: translateY(-50%);
        color: #aaa;
    }

    /* Date range filter styles */
    .date-range-filter {
        display: flex;
        align-items: center;
    }
    .date-range-filter label {
        margin-right: 5px;
        white-space: nowrap;
    }
</style>
@endsection

@section('contentsales')
@include('sales.partials.navigation')

<div class="content">
    <div class="container-fluid">
        <div class="row">
            <div class="col-md-12">
                <div class="bgwhite shadow-kit rounded-lg">
                    <div class="card-header bg-primary text-white d-flex justify-content-between align-items-center">
                        <div class="d-flex align-items-center">
                            <h5 class="mb-0 mr-3 font-bold text-uppercase text-white">Manual Invoices</h5>
                        </div>
                    </div>
                    <div class="card-body">
                        <div class="mb-3 d-flex justify-content-between align-items-center">
                            <div class="d-flex align-items-center">
                                <div class="date-range-filter mr-2">
                                    <div class="d-flex">
                                        <div class="mr-2">
                                            <label for="date-from" class="small mb-0">Dari Tanggal:</label>
                                            <input type="date" id="date-from" class="form-control form-control-sm">
                                        </div>
                                        <div>
                                            <label for="date-to" class="small mb-0">Sampai Tanggal:</label>
                                            <input type="date" id="date-to" class="form-control form-control-sm">
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="d-flex align-items-center">
                                <div class="search-box mr-2">
                                    <div class="position-relative">
                                        <input type="text" id="search-input" class="form-control form-control-sm" placeholder="Cari...">
                                        <i class="mdi mdi-magnify search-icon"></i>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="table-responsive">
                            <table class="table table-bordered w-100" id="manual-invoices-table" style="font-size: 11px;">
                                <thead class="bg-light">
                                    <tr>
                                        <th class="sortable" data-sort="tanggal_invoice">TANGGAL<i class="sort-icon mdi mdi-sort"></i></th>
                                        <th class="sortable" data-sort="no_invoice">INVOICE<i class="sort-icon mdi mdi-sort"></i></th>
                                        <th class="sortable" data-sort="customer">CUSTOMER<i class="sort-icon mdi mdi-sort"></i></th>
                                        <th class="sortable" data-sort="po_number">NO. PO<i class="sort-icon mdi mdi-sort"></i></th>
                                        <th class="sortable" data-sort="trouble">KATEGORI<i class="sort-icon mdi mdi-sort"></i></th>
                                        <th class="sortable" data-sort="direct_subtotal">NILAI INVOICE<i class="sort-icon mdi mdi-sort"></i></th>
                                        <th class="sortable" data-sort="payment_status">STATUS<i class="sort-icon mdi mdi-sort"></i></th>
                                        <th>PARTS</th>
                                        <th>AKSI</th>
                                    </tr>
                                </thead>
                                <tbody id="manual-invoices-table-body">
                                </tbody>
                            </table>
                        </div>
                        <div class="d-flex justify-content-between align-items-center mt-3">
                            <div>
                                <span id="showing-text">Menampilkan 0 dari 0 invoice</span>
                            </div>
                            <div>
                                <nav aria-label="Page navigation">
                                    <ul class="pagination pagination-sm" id="pagination">
                                        <!-- Pagination will be generated dynamically -->
                                    </ul>
                                </nav>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Invoice Details Modal -->
<div class="modal fade" id="invoice-details-modal" tabindex="-1" role="dialog" aria-labelledby="invoice-details-modal-label" aria-hidden="true">
    <div class="modal-dialog modal-lg" role="document">
        <div class="modal-content">
            <div class="modal-header bg-primary text-white">
                <h5 class="modal-title" id="invoice-details-modal-label">Detail Invoice</h5>
                <button type="button" class="close text-white" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="modal-body" id="invoice-details-content">
                <!-- Invoice details will be loaded dynamically -->
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-sm btn-secondary" data-dismiss="modal">Tutup</button>
                <button type="button" class="btn btn-sm btn-primary" id="print-invoice-btn">Print PDF</button>
            </div>
        </div>
    </div>
</div>

<!-- Parts Details Modal -->
<div class="modal fade" id="parts-details-modal" tabindex="-1" role="dialog" aria-labelledby="parts-details-modal-label" aria-hidden="true">
    <div class="modal-dialog modal-lg" role="document">
        <div class="modal-content">
            <div class="modal-header bg-info text-white">
                <h5 class="modal-title" id="parts-details-modal-label">Daftar Parts</h5>
                <button type="button" class="close text-white" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="modal-body" id="parts-details-content">
                <!-- Parts details will be loaded dynamically -->
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-sm btn-secondary" data-dismiss="modal">Tutup</button>
            </div>
        </div>
    </div>
</div>

<script>
    document.addEventListener('DOMContentLoaded', function() {
        let currentPage = 1;
        let currentSort = 'created_at';
        let currentOrder = 'desc';

        // Set default date range (current month)
        const today = new Date();
        const firstDay = new Date(today.getFullYear(), today.getMonth(), 1);
        document.getElementById('date-from').valueAsDate = firstDay;
        document.getElementById('date-to').valueAsDate = today;

        // Load invoices on page load
        loadManualInvoices();

        // Event listeners for filters
        document.getElementById('date-from').addEventListener('change', function() {
            currentPage = 1;
            loadManualInvoices();
        });

        document.getElementById('date-to').addEventListener('change', function() {
            currentPage = 1;
            loadManualInvoices();
        });

        document.getElementById('search-input').addEventListener('input', function() {
            currentPage = 1;
            loadManualInvoices();
        });

        // Sortable headers
        document.querySelectorAll('.sortable').forEach(header => {
            header.addEventListener('click', function() {
                const sortField = this.getAttribute('data-sort');
                
                if (currentSort === sortField) {
                    currentOrder = currentOrder === 'asc' ? 'desc' : 'asc';
                } else {
                    currentSort = sortField;
                    currentOrder = 'asc';
                }
                
                // Update header classes
                document.querySelectorAll('.sortable').forEach(h => {
                    h.classList.remove('asc', 'desc');
                });
                this.classList.add(currentOrder);
                
                currentPage = 1;
                loadManualInvoices();
            });
        });

        function loadManualInvoices() {
            const dateFrom = document.getElementById('date-from').value;
            const dateTo = document.getElementById('date-to').value;
            const search = document.getElementById('search-input').value;

            const params = new URLSearchParams({
                page: currentPage,
                per_page: 10,
                date_from: dateFrom,
                date_to: dateTo,
                search: search,
                sort: currentSort,
                order: currentOrder
            });

            fetch(`/sales/manual-invoices?${params}`)
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        displayInvoices(data.invoices);
                        updatePagination(data.invoices);
                    } else {
                        console.error('Error loading invoices:', data.message);
                    }
                })
                .catch(error => {
                    console.error('Error loading invoices:', error);
                });
        }

        function displayInvoices(invoicesData) {
            const tbody = document.getElementById('manual-invoices-table-body');
            tbody.innerHTML = '';

            if (invoicesData.data.length === 0) {
                tbody.innerHTML = '<tr><td colspan="9" class="text-center">Tidak ada data invoice</td></tr>';
                return;
            }

            invoicesData.data.forEach(invoice => {
                const row = document.createElement('tr');
                
                const partsCount = invoice.manual_invoice_parts ? invoice.manual_invoice_parts.length : 0;
                const partsText = partsCount > 0 ? `${partsCount} parts` : 'Tidak ada parts';
                
                row.innerHTML = `
                    <td>${formatDate(invoice.tanggal_invoice)}</td>
                    <td>${invoice.no_invoice}</td>
                    <td>${invoice.customer || '-'}</td>
                    <td>${invoice.po_number || '-'}</td>
                    <td>${invoice.trouble || '-'}</td>
                    <td>Rp ${formatNumber(invoice.direct_subtotal)}</td>
                    <td><span class="badge ${getStatusBadgeClass(invoice.payment_status)}">${invoice.payment_status}</span></td>
                    <td>
                        ${partsCount > 0 ? 
                            `<button class="btn btn-sm btn-info" onclick="showParts(${invoice.id})">
                                <i class="mdi mdi-format-list-bulleted"></i> ${partsText}
                            </button>` : 
                            `<span class="text-muted">${partsText}</span>`
                        }
                    </td>
                    <td>
                        <button class="btn btn-sm btn-primary" onclick="showInvoiceDetails(${invoice.id})">
                            <i class="mdi mdi-eye"></i> Detail
                        </button>
                    </td>
                `;
                tbody.appendChild(row);
            });

            // Update showing text
            const showingText = `Menampilkan ${invoicesData.from || 0} - ${invoicesData.to || 0} dari ${invoicesData.total || 0} invoice`;
            document.getElementById('showing-text').textContent = showingText;
        }

        function updatePagination(invoicesData) {
            const pagination = document.getElementById('pagination');
            pagination.innerHTML = '';

            if (invoicesData.last_page <= 1) return;

            // Previous button
            if (invoicesData.current_page > 1) {
                const prevLi = document.createElement('li');
                prevLi.className = 'page-item';
                prevLi.innerHTML = `<a class="page-link" href="#" onclick="changePage(${invoicesData.current_page - 1})">Previous</a>`;
                pagination.appendChild(prevLi);
            }

            // Page numbers
            for (let i = 1; i <= invoicesData.last_page; i++) {
                const li = document.createElement('li');
                li.className = `page-item ${i === invoicesData.current_page ? 'active' : ''}`;
                li.innerHTML = `<a class="page-link" href="#" onclick="changePage(${i})">${i}</a>`;
                pagination.appendChild(li);
            }

            // Next button
            if (invoicesData.current_page < invoicesData.last_page) {
                const nextLi = document.createElement('li');
                nextLi.className = 'page-item';
                nextLi.innerHTML = `<a class="page-link" href="#" onclick="changePage(${invoicesData.current_page + 1})">Next</a>`;
                pagination.appendChild(nextLi);
            }
        }

        window.changePage = function(page) {
            currentPage = page;
            loadManualInvoices();
        };

        window.showInvoiceDetails = function(invoiceId) {
            fetch(`/sales/manual-invoices/${invoiceId}`)
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        displayInvoiceDetails(data.invoice);
                        $('#invoice-details-modal').modal('show');
                    }
                })
                .catch(error => {
                    console.error('Error loading invoice details:', error);
                });
        };

        window.showParts = function(invoiceId) {
            fetch(`/sales/manual-invoices/${invoiceId}`)
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        displayPartsDetails(data.invoice);
                        $('#parts-details-modal').modal('show');
                    }
                })
                .catch(error => {
                    console.error('Error loading parts details:', error);
                });
        };

        function displayInvoiceDetails(invoice) {
            const content = document.getElementById('invoice-details-content');
            content.innerHTML = `
                <div class="row">
                    <div class="col-md-6">
                        <h6>Informasi Invoice</h6>
                        <table class="table table-sm">
                            <tr><td><strong>Nomor Invoice:</strong></td><td>${invoice.no_invoice}</td></tr>
                            <tr><td><strong>Tanggal:</strong></td><td>${formatDate(invoice.tanggal_invoice)}</td></tr>
                            <tr><td><strong>Customer:</strong></td><td>${invoice.customer || '-'}</td></tr>
                            <tr><td><strong>PO Number:</strong></td><td>${invoice.po_number || '-'}</td></tr>
                            <tr><td><strong>Kategori:</strong></td><td>${invoice.trouble || '-'}</td></tr>
                        </table>
                    </div>
                    <div class="col-md-6">
                        <h6>Informasi Pembayaran</h6>
                        <table class="table table-sm">
                            <tr><td><strong>Subtotal:</strong></td><td>Rp ${formatNumber(invoice.direct_subtotal)}</td></tr>
                            <tr><td><strong>PPN:</strong></td><td>${(invoice.ppn * 100).toFixed(0)}%</td></tr>
                            <tr><td><strong>Total:</strong></td><td>Rp ${formatNumber(invoice.direct_subtotal * (1 + invoice.ppn))}</td></tr>
                            <tr><td><strong>Status:</strong></td><td><span class="badge ${getStatusBadgeClass(invoice.payment_status)}">${invoice.payment_status}</span></td></tr>
                        </table>
                    </div>
                </div>
                ${invoice.notes ? `<div class="row"><div class="col-12"><h6>Catatan</h6><p>${invoice.notes}</p></div></div>` : ''}
            `;
        }

        function displayPartsDetails(invoice) {
            const content = document.getElementById('parts-details-content');
            
            if (!invoice.manual_invoice_parts || invoice.manual_invoice_parts.length === 0) {
                content.innerHTML = '<p class="text-center text-muted">Tidak ada parts untuk invoice ini</p>';
                return;
            }

            let totalParts = 0;
            let partsHtml = `
                <div class="table-responsive">
                    <table class="table table-bordered table-sm">
                        <thead class="bg-light">
                            <tr>
                                <th>Kode Part</th>
                                <th>Nama Part</th>
                                <th>Qty</th>
                                <th>Harga</th>
                                <th>Satuan</th>
                                <th>Total</th>
                            </tr>
                        </thead>
                        <tbody>
            `;

            invoice.manual_invoice_parts.forEach(part => {
                totalParts += part.total;
                partsHtml += `
                    <tr>
                        <td>${part.part_code}</td>
                        <td>${part.part_name}</td>
                        <td>${part.quantity}</td>
                        <td>Rp ${formatNumber(part.price)}</td>
                        <td>${part.eum}</td>
                        <td>Rp ${formatNumber(part.total)}</td>
                    </tr>
                `;
            });

            partsHtml += `
                        </tbody>
                        <tfoot class="bg-light">
                            <tr>
                                <th colspan="5" class="text-right">Total Parts:</th>
                                <th>Rp ${formatNumber(totalParts)}</th>
                            </tr>
                        </tfoot>
                    </table>
                </div>
            `;

            content.innerHTML = partsHtml;
        }

        function formatDate(dateString) {
            if (!dateString) return '-';
            const date = new Date(dateString);
            return date.toLocaleDateString('id-ID');
        }

        function formatNumber(num) {
            return new Intl.NumberFormat('id-ID').format(num);
        }

        function getStatusBadgeClass(status) {
            switch(status) {
                case 'Lunas': return 'badge-success';
                case 'Belum Lunas': return 'badge-warning';
                case 'Jatuh Tempo': return 'badge-danger';
                default: return 'badge-secondary';
            }
        }
    });
</script>
@endsection
