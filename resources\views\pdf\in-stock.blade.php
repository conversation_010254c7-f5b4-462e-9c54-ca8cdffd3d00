<!DOCTYPE html>
<html>
<head>
    <style>
        @page {
            margin: 100px 50px;
        }
        
        header {
            position: fixed;
            top: -80px;
            left: 0;
            right: 0;
            height: 70px;
            text-align: center;
        }
        
        footer {
            position: fixed;
            bottom: -60px;
            left: 0;
            right: 0;
            height: 50px;
            text-align: center;
            border-top: 1px solid #000;
        }
        
        table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 20px;
        }
        
        th, td {
            border: 1px solid #ddd;
            padding: 8px;
            text-align: left;
        }
        
        th {
            background-color: #f2f2f2;
        }
    </style>
</head>
<body>
    <header>
        <h2>Laporan Part {{ $type }} - {{ $site->site_name }}</h2>
        <p>Periode: {{ \Carbon\Carbon::parse($startDate)->translatedFormat('d F Y') }} s/d 
           {{ \Carbon\Carbon::parse($endDate)->translatedFormat('d F Y') }}</p>
    </header>

    <footer>
        <p>Dicetak pada: {{ now()->translatedFormat('d F Y H:i') }} | Halaman: <span class="pageNumber"></span></p>
    </footer>

    <table>
        <thead>
            <tr>
                <th>NO</th>
                <th>TANGGAL</th>
                <th>PART NUMBER</th>
                <th>NAMA PART</th>
                <th>QTY</th>
            </tr>
        </thead>
        <tbody>
            @foreach($data as $index => $item)
            <tr>
                <td>{{ $index + 1 }}</td>
                <td>
                    @if($type === 'IN')
                        {{ \Carbon\Carbon::parse($item->date_in)->translatedFormat('d F Y') }}
                    @else
                        {{ \Carbon\Carbon::parse($item->date_out)->translatedFormat('d F Y') }}
                    @endif
                </td>
                <td>{{ $item->partInventory->part->part_code }}</td>
                <td>{{ $item->partInventory->part->part_name }}</td>
                <td>{{ $item->quantity }}</td>
            </tr>
            @endforeach
        </tbody>
    </table>
</body>
</html>