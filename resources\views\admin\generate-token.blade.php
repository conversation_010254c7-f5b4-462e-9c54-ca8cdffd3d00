@extends('warehouse.content')
@section('contentho')
@section('title', 'Generate Reset Password Token')

<div class="row bgwhite page-title-box shadow-kit mb-1">
    <div class="col d-flex align-items-center" style="max-width: fit-content;">
        <button class="button-menu-mobile text-blue-500 btn-sm">
            <i style="font-size: 30px;" class="mdi mdi-menu"></i>
        </button>
        <div class="text">
            <p class="font-bold m-0">{{ session('name') }}</p>
        </div>
    </div>
    <div class="col">
        <ul class="list-unstyled topnav-menu float-right mb-0">
            <li class="dropdown notification-list">
                <a class="nav-link dropdown-toggle" href="#" data-bs-toggle="dropdown" aria-expanded="false">
                    <i class="mdi mdi-bell-outline noti-icon"></i>
                    <span class="badge badge-danger badge-pill float-right badgenotif"></span>
                </a>
                <div class="dropdown-menu dropdown-menu-right dropdown-lg">
                    <div class="dropdown-item noti-title">
                        <h5 class="m-0">Notifikasi</h5>
                    </div>
                    <div class="slimScrollDiv">
                        <div class="slimscroll noti-scroll" id="notificationContainer">
                            <!-- Notifications will be loaded here -->
                        </div>
                    </div>
                    <a href="javascript:void(0);" class="dropdown-item text-center text-primary notify-item notify-all">
                        Lihat Semua
                    </a>
                </div>
            </li>
        </ul>
    </div>
</div>

<div class="row">
    <div class="col-12">
        <div class="card shadow-kit bgwhite">
            <div class="card-header">
                <h4 class="card-title">Generate Reset Password Token</h4>
                <p class="card-subtitle">Buat token untuk user yang ingin mereset password</p>
            </div>
            <div class="card-body">
                @if(session('success'))
                <div class="alert alert-success">
                    {{ session('success') }}
                </div>
                @endif

                @if(session('token'))
                <div class="alert alert-info">
                    <strong>Token yang dihasilkan:</strong> {{ session('token') }}
                    <p class="mt-2 mb-0">Berikan token ini kepada user melalui WhatsApp.</p>
                </div>
                @endif

                @if($errors->any())
                <div class="alert alert-danger">
                    <ul class="mb-0">
                        @foreach($errors->all() as $error)
                        <li>{{ $error }}</li>
                        @endforeach
                    </ul>
                </div>
                @endif

                <div class="table-responsive">
                    <table id="users-table" class="table table-striped table-bordered dt-responsive nowrap">
                        <thead>
                            <tr>
                                <th>Nama</th>
                                <th>Username</th>
                                <th>Role</th>
                                <th>Site</th>
                                <th>Aksi</th>
                            </tr>
                        </thead>
                        <tbody>
                            @foreach($users as $user)
                            <tr>
                                <td>{{ $user->name }}</td>
                                <td>{{ $user->username }}</td>
                                <td>{{ $user->role }}</td>
                                <td>{{ $user->site ? $user->site->site_name : 'Warehouse' }}</td>
                                <td>
                                    <a href="{{ route('password.generate.token', $user->employee_id) }}" class="btn btn-primary btn-sm">
                                        <i class="mdi mdi-key"></i> Generate Token
                                    </a>
                                </td>
                            </tr>
                            @endforeach
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>

@endsection

@section('resource')
<script>
    $(document).ready(function() {
        $('#users-table').DataTable({
            responsive: true,
            pageLength: 15,
            lengthMenu: [15, 25, 50, 100],
            language: {
                search: "Cari:",
                lengthMenu: "Tampilkan _MENU_ data per halaman",
                zeroRecords: "Tidak ada data yang ditemukan",
                info: "Menampilkan halaman _PAGE_ dari _PAGES_",
                infoEmpty: "Tidak ada data yang tersedia",
                infoFiltered: "(difilter dari _MAX_ total data)",
                paginate: {
                    first: "Pertama",
                    last: "Terakhir",
                    next: "Selanjutnya",
                    previous: "Sebelumnya"
                }
            }
        });
    });
</script>
@endsection
