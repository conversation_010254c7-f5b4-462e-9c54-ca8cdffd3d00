// Core utilities and configurations
// resources/js/app.js
import $ from 'jquery';
import 'metismenu';
import 'jquery-slimscroll';
import 'peity';
import 'jquery-knob';
import counterup from 'counterup2';

// Import date utilities for consistent timezone handling
import './utils/dateUtils.js';

// Attach to jQuery
$.fn.counterUp = counterup;
$('[data-plugin="counterup"]').counterUp({ delay: 100, time: 1200 });

import { setupTableSearch } from './utils/tableSearch.js';
document.addEventListener("DOMContentLoaded", function () {
    setupTableSearch("searchInput", "myTable");
});