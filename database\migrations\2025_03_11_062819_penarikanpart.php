<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('part_withdrawals', function (Blueprint $table) {
            $table->id('withdrawal_id');
            $table->string('part_code', 50);
            $table->foreign('part_code')->references('part_code')->on('parts')->onUpdate('cascade');
            $table->string('from_site_id', 50);
            $table->foreign('from_site_id')->references('site_id')->on('sites')->onUpdate('cascade');
            $table->integer('requested_quantity');
            $table->integer('approved_quantity')->nullable(); 
            $table->enum('status', ['Pending', 'Approved', 'Rejected', 'In Transit', 'Completed'])->default('Pending');
            $table->string('withdrawal_reason', 255)->nullable();
            $table->text('notes')->nullable(); 
            $table->integer('previous_stock')->nullable(); 
            $table->integer('new_stock')->nullable(); 
            $table->string('updated_by')->nullable(); 
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('part_withdrawals');
    }
};
