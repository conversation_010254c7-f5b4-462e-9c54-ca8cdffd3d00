@extends('warehouse.content')
@section('contentho')
@section('title', 'Perlengkapan')
<style>
    /* Pagination styling */
    .pagination {
        display: flex;
        justify-content: center;
        list-style: none;
        padding: 0;
        margin-top: 15px;
    }
    .pagination .page-item {
        margin: 0 2px;
    }
    .pagination .page-item .page-link {
        color: #333;
        background-color: #fff;
        border: 1px solid #ddd;
        padding: 6px 12px;
        text-decoration: none;
        border-radius: 4px;
        cursor: pointer;
    }
    .pagination .page-item.active .page-link {
        color: #fff;
        background-color: #28a745;
        border-color: #28a745;
    }
    .pagination .page-item .page-link:hover {
        background-color: #f8f9fa;
    }
    #requisitions-pagination {
        margin-top: 15px;
    }
</style>
<div class="row bgwhite page-title-box shadow-kit mb-1">
    <div class="col d-flex align-items-center" style="max-width: fit-content;">
        <button class="button-menu-mobile text-blue-500 btn-sm">
            <i style="font-size: 30px;" class="mdi mdi-menu"></i>
        </button>
        <div class="text">
            <p class="font-bold m-0">{{ session('name') }}</p>
        </div>
    </div>
    <div class="col">
    <ul class="list-unstyled topnav-menu float-right mb-0">
        <li class="dropdown notification-list">
            <a class="nav-link dropdown-toggle  waves-effect waves-light" data-toggle="dropdown" href="#"
                role="button" aria-haspopup="false" aria-expanded="false">
                <i class="mdi mdi-bell-outline noti-icon"></i>
                <span class="badge badge-danger badge-pill float-right badgenotif"></span>
            </a>
            <div class="dropdown-menu dropdown-menu-right dropdown-lg">
                <div class="dropdown-item noti-title">
                    <h5 class="font-16 text-white m-0">
                        <span class="float-right">
                            <a href="javascript:void(0);" class="text-white" id="clear-all">
                                <small></small>
                            </a>
                        </span>Notifikasi
                    </h5>
                </div>
                <div class="noti-scroll" id="notification-list">
                </div>
               
                </a>
            </div>
        </li>
    </ul>
    </div>
</div>

<div id="alert-container"></div>
<div class="row pt-2 mr-2">
    <div class="col shadow-kit p-4">
        <div class="row pb-2 pl-1 pr-1">
            <div class="col-md-3 ">
                <select id="site-filter" class="form-control btn-primary mb-2 max200">
                    <option value="">Semua Site</option>
                    @foreach($datasites as $site)
                    <option value="{{ $site->site_id }}">{{ $site->site_name }}</option>
                    @endforeach
                </select>
            </div>
            <div class="col-md-6 offset-md-3">
                <input type="text" id="search" class="form-control" placeholder="Cari perlengkapan...">
            </div>
        </div>

        <div class="table-responsive">
            <div id="perlengkapan-table">
                @section('table')
                <table class="table table-striped">
                    <thead class="table-dark text-white">
                        <tr>
                            <th class="p-2">Nama</th>
                            <th class="p-2">Site</th>
                            <th class="p-2">Jumlah</th>
                            <th class="p-2">Tanggal Masuk</th>
                            <th class="p-2">Aksi</th>
                        </tr>
                    </thead>
                    <tbody>
                        @foreach($equipment as $item)
                        <tr>
                            <td>{{ $item->equipment_name }}</td>
                            <td>{{ $item->site->site_name }}</td>
                            <td>{{ $item->quantity }}</td>
                            <td>{{ \Carbon\Carbon::parse($item->date_in)->format('d-m-Y') }}</td>
                            <td>
                                <button class="btn btn-sm btn-danger delete-bton" data-id="{{ $item->equipment_id }}">Hapus</button>
                                <button class="btn btn-sm btn-primary edit-btn" data-id="{{ $item->equipment_id }}">Edit</button>
                            </td>
                        </tr>
                        @endforeach
                    </tbody>
                </table>
                @show
            </div>
            <div class="pagination-wrapper">
                {{ $equipment->links() }}
            </div>
        </div>
    </div>

    <div class="max400 col bgwhite shadow-kit p-4 ml-2">
        <form id="perlengkapan-form">
            @csrf
            <input type="hidden" id="equipment_id" name="equipment_id">

            <div class="form-group">
                <label>Nama Perlengkapan</label>
                <input type="text" id="equipment_name" name="equipment_name" class="form-control" list="suggestions">
                <datalist id="suggestions"></datalist>
            </div>
            <div class="form-group">
                <label>Site</label>
                <select id="site_id" name="site_id" class="form-control" autocomplete="off" required>
                    <option value="">Pilih Site</option>
                    @foreach($datasites as $site)
                    <option value="{{ $site->site_id }}">{{ $site->site_name }}</option>
                    @endforeach
                </select>
            </div>

            <div class="form-group">
                <label>Tanggal Masuk</label>
                <input value="{{ date('Y-m-d') }}" type="date" name="date_in" id="date_in" class="form-control">
            </div>

            <div class="form-group">
                <label>Jumlah</label>
                <input type="number" name="quantity" id="quantity" class="form-control">
            </div>

            <button type="submit" class="btn btn-primary">Simpan</button>
        </form>
    </div>
</div>
@endsection
@section('resource')
@vite(['resources/js/style.js','resources/js/adminho/crudperlengkapan.js'])
@endsection