import Chart from 'chart.js/auto';
import ChartDataLabels from 'chartjs-plugin-datalabels';

Chart.register(ChartDataLabels); // Registrasi global, !! Penting !! jangan panggil difile lain

document.addEventListener('DOMContentLoaded', function() {
    const siteColors = {};
    let currentChart = null;

    function getColorForSite(siteName) {
        if (!siteColors[siteName]) {
            const letters = '0123456789ABCDEF';
            let color = '#';
            for (let i = 0; i < 6; i++) {
                color += letters[Math.floor(Math.random() * 16)];
            }
            siteColors[siteName] = color;
        }
        return siteColors[siteName];
    }

    function createChart(canvasId, chartData, chartType) {
        const canvas = document.getElementById(canvasId);
        if (!canvas) return;

        const ctx = canvas.getContext('2d');

        // Destroy existing chart if it exists
        if (currentChart) {
            currentChart.destroy();
        }

        const datasets = [
            {
                label: 'In Stock',
                data: chartData.in_stock_data,
                borderColor: getColorForSite(chartData.site_name + '_'),
                backgroundColor: 'transparent',
                tension: 0.4,
                pointRadius: 3,
                pointHoverRadius: 5
            },
            {
                label: 'Out Stock',
                data: chartData.out_stock_data,
                borderColor: getColorForSite(chartData.site_name + '_out'),
                backgroundColor: 'transparent',
                tension: 0.4,
                pointRadius: 3,
                pointHoverRadius: 5
            }
        ];

        currentChart = new Chart(ctx, {
            type: 'line',
            data: {
                labels: chartData.labels,
                datasets: datasets
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                scales: {
                    y: {
                        beginAtZero: true,
                        title: {
                            display: true,
                            text: 'Quantity'
                        }
                    },
                    x: {
                        title: {
                            display: true,
                            text: window.groupBy.charAt(0).toUpperCase() + window.groupBy.slice(1)
                        }
                    }
                },
                plugins: {
                    title: {
                        display: true,
                        text: `In & Out Stock (${window.groupBy.charAt(0).toUpperCase() + window.groupBy.slice(1)})`,
                        font: { size: 16 }
                    },
                    legend: { position: 'top', align: 'left' }
                }
            }
        });

        return currentChart;
    }

    // Create the chart initially
    if (window.chartData) {
        createChart('inOutStockChart', window.chartData, 'In & Out');
    }

    // Listen for chart data update events
    document.addEventListener('chartDataUpdated', function() {
        if (window.chartData) {
            createChart('inOutStockChart', window.chartData, 'In & Out');
        }
    });
});