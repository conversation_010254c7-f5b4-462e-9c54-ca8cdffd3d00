<?php

namespace App\Http\Controllers;

use App\Models\LoginToken;
use App\Models\LogAktivitas;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Log;

class TokenAuthController extends Controller
{
    /**
     * Show the token login form
     *
     * @return \Illuminate\View\View
     */
    public function showLoginForm()
    {
        return view('auth.token-login');
    }

    /**
     * Handle a token login request
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\RedirectResponse
     */
    public function login(Request $request)
    {
        $request->validate([
            'token' => 'required|string',
        ]);

        $token = LoginToken::where('token', $request->token)->first();

        if (!$token) {
            return back()->withErrors(['token' => 'Token tidak valid atau sudah kadaluarsa.']);
        }

        $user = $token->user;

        if (!$user) {
            return back()->withErrors(['token' => 'User tidak ditemukan.']);
        }

        // Check if user is a superadmin
        if ($user->role !== 'superadmin') {
            return back()->withErrors(['token' => 'Token hanya dapat digunakan oleh Superadmin.']);
        }

        // Mark the token as used
        $token->markAsUsed();

        // Log in the user
        Auth::login($user);
        $request->session()->regenerate();

        // Set session variables
        session(['employee_id' => $user->employee_id]);
        session(['site_id' => $user->site_id]);
        session(['name' => $user->name]);
        session(['role' => $user->role]);

        // Log the login activity
        Log::info('User ' . $user->username . ' logged in successfully using token. employee_id: ' . $user->employee_id . ', site_id: ' . $user->site_id);

        LogAktivitas::create([
            'site_id' => $user->site_id,
            'name' => $user->name,
            'action' => 'Login',
            'decription' => "User {$user->name} berhasil login menggunakan token",
            'table' => 'Users',
            'ip_address' => $request->ip(),
        ]);

        return redirect()->route('superadmin.dashboard');
    }

    /**
     * Generate a token for a user (admin function)
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\RedirectResponse
     */
    public function generateToken(Request $request)
    {
        $request->validate([
            'employee_id' => 'required|exists:users,employee_id',
        ]);

        // Check if current user is admin or superadmin
        if (!in_array(Auth::user()->role, ['adminho', 'superadmin'])) {
            return back()->withErrors(['unauthorized' => 'Anda tidak memiliki akses untuk fungsi ini.']);
        }

        $user = User::findOrFail($request->employee_id);

        // Only generate tokens for superadmins
        if ($user->role !== 'superadmin') {
            return back()->withErrors(['employee_id' => 'Token hanya dapat dibuat untuk Superadmin.']);
        }

        // Generate permanent token
        $token = LoginToken::generateFor($user);

        // Log the token generation
        LogAktivitas::create([
            'site_id' => session('site_id'),
            'name' => session('name'),
            'action' => 'Generate Token',
            'decription' => "Token login dibuat untuk user {$user->name}",
            'table' => 'LoginTokens',
            'ip_address' => $request->ip(),
        ]);

        return back()->with([
            'success' => 'Token permanen berhasil dibuat.',
            'token' => $token->token,
        ]);
    }

    /**
     * Show the token generation form
     *
     * @return \Illuminate\View\View
     */
    public function showTokenGenerationForm()
    {
        // Check if current user is admin or superadmin
        if (!in_array(Auth::user()->role, ['adminho', 'superadmin'])) {
            return redirect()->route('login')->withErrors(['unauthorized' => 'Anda tidak memiliki akses untuk fungsi ini.']);
        }

        // Get only superadmin users
        $users = User::where('role', 'superadmin')->get();

        return view('auth.generate-token', compact('users'));
    }
}
