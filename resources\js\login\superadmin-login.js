document.addEventListener('DOMContentLoaded', function() {
    const loginForm = document.getElementById('login-form');
    const usernameInput = document.getElementById('username-input');
    const passwordInput = document.getElementById('password-input');

    // Add a hidden input for code
    const codeInput = document.createElement('input');
    codeInput.type = 'hidden';
    codeInput.name = 'code';
    codeInput.id = 'code-input';
    loginForm.appendChild(codeInput);

    // Listen for form submission
    loginForm.addEventListener('submit', function(event) {
        // Check if the password looks like a superadmin code (8 digits)
        const passwordValue = passwordInput.value;
        const isSuperadminCode = /^\d{8}$/.test(passwordValue);

        if (isSuperadminCode) {
            // If it looks like a code, set it as the code value
            codeInput.value = passwordValue;
        }
    });
});
