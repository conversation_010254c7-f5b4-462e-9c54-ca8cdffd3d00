@extends('warehouse.content')
@section('resource')

@vite(['resources/js/adminho/stockmonitoring.js', 'resources/js/style.js'])
@endsection
@section('contentho')
<style>
    .bg-danger {
        background-color:rgb(236, 107, 117) !important;
    }
</style>
<div class="row bgwhite page-title-box shadow-kit mb-1">
    <div class="col d-flex align-items-center" style="max-width: fit-content;">
        <button class="button-menu-mobile text-blue-500 btn-sm">
            <i style="font-size: 30px;" class="mdi mdi-menu"></i>
        </button>
        <div class="text">
            <p class="font-bold m-0">{{ session('name') }}</p>
        </div>
    </div>

    <div class="col">
        <ul class="list-unstyled topnav-menu float-right mb-0">
            <li class="dropdown notification-list">
                <a class="nav-link dropdown-toggle" href="#" data-bs-toggle="dropdown" aria-expanded="false">
                    <i class="mdi mdi-bell-outline noti-icon"></i>
                    <span class="badge badge-danger badge-pill float-right badgenotif"></span>
                </a>
                <div class="dropdown-menu dropdown-menu-end dropdown-lg">
                    <div class="dropdown-item noti-title bg-primary">
                        <h5 class="m-0 text-white d-flex justify-content-between align-items-center">
                            Notifikasi
                            <a href="javascript:void(0);" class="text-white" id="clear-all">
                                <small></small>
                            </a>
                        </h5>
                    </div>
                    <div class="noti-scroll" id="notification-list">
                        <!-- Notifications will be dynamically inserted here -->
                    </div>
                </div>
            </li>
        </ul>
    </div>
</div>

<div class="bgwhite m-4 p-4  shadow-kit">
    <h1 class="h3 font-bold text-center text-uppercase">Inventory Monitoring</h1>
    <div class="d-flex flex-wrap align-items-end gap-3">
        <div class="w-auto">
            <label for="site_id" class="form-label fw-bold">Pilih Site</label>
            <select class="form-control btn btn-primary" id="site_id" name="site_id" >
                @foreach($sites as $site)
                <option value="{{ $site->site_id }}" {{ $firstSite->site_id == $site->site_id ? 'selected' : '' }}>
                    {{ $site->site_name }}
                </option>
                @endforeach
            </select>
        </div>
        <div class="w-auto">
            <label for="start_date" class="form-label fw-bold">Tanggal awal</label>
            <input type="date" class="form-control" id="start_date" name="start_date" value="{{ $startDate }}" >
        </div>

        <div class="w-auto">
            <label for="end_date" class="form-label fw-bold">Tanggal Terakhir</label>
            <input type="date" class="form-control" id="end_date" name="end_date" value="{{ $endDate }}">
        </div>

        <div class="w-auto">
            <label for="search" class="form-label fw-bold">Cari Data</label>
            <input type="text" class="form-control" id="search" name="search" oninput="loadData()" placeholder="Cari Nama/Kode Barang">
        </div>
    </div>
    <hr class="m-3">
    <div class="table-responsive">
        <table class="table table-striped table-hover">
            <thead class="table-dark text-white">
                <tr>
                    <th class="p-2" onclick="sortTable(0)">Nama Barang <i class="fas fa-sort"></i></th>
                    <th class="p-2" onclick="sortTable(1)">Min Stock<i class="fas fa-sort"></i></th>
                    <th class="p-2" onclick="sortTable(2)">Max Stock<i class="fas fa-sort"></i></th>
                    <th class="p-2" onclick="sortTable(3)">Stok Saat Ini <i class="fas fa-sort"></i></th>
                    <th class="p-2" onclick="sortTable(4)">Jumlah Masuk (In) <i class="fas fa-sort"></i></th>
                    <th class="p-2" onclick="sortTable(5)">Jumlah Keluar (Out) <i class="fas fa-sort"></i></th>
                    <th class="p-2" onclick="sortTable(6)">Status <i class="fas fa-sort"></i></th>
                </tr>
            </thead>
            <tbody id="inventory-table-body">

            </tbody>
        </table>
    </div>
</div>

<!-- Modal Detail -->
<div class="modal fade" id="detailModal" tabindex="-1" aria-labelledby="detailModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-xl" style="max-width: 98%; margin: 10px auto;">
        <div class="modal-content p-4">
            <div class="modal-header pb-0">
                <h5 class="h3 font-bold text-center text-uppercase" id="detailModalLabel">Detail Pergerakan Barang</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <div class="row">
                    <div class="col-md-6">
                        <h4 class="font-bold h6">Track In</h4>
                        <div class="table-responsive">
                            <table class="table table-striped">
                                <thead class="bg-primary text-white">
                                    <tr>
                                        <th class="p-2">Tanggal Masuk</th>
                                        <th class="p-2">Jumlah</th>
                                        <th class="p-2">Nama Admin</th>
                                        <th class="p-2">Catatan</th>
                                    </tr>
                                </thead>
                                <tbody id="in-table-body">
                                    <!-- Data IN akan dimuat di sini -->
                                </tbody>
                            </table>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <h4 class="font-bold h6">Track Out</h4>
                        <div class="table-responsive">
                            <table class="table table-striped">
                                <thead class="bg-success text-white">
                                    <tr>
                                        <th class="p-2">Tanggal keluar</th>
                                        <th class="p-2">Jumlah</th>
                                        <th class="p-2">Nama Admin</th>
                                        <th class="p-2">Notes</th>
                                    </tr>
                                </thead>
                                <tbody id="out-table-body">
                                    <!-- Data OUT akan dimuat di sini -->
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection
