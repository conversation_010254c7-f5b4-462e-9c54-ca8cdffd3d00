<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Symfony\Component\HttpFoundation\Response;

class CheckRole
{
    public function handle(Request $request, Closure $next, $role): Response
    {
        if (!Auth::check()) {
            return redirect()->route('login')->with('error', 'Please log in.');
        }

        if (Auth::user()->role != $role) {
            return redirect()->route('login')->with('error', 'Unauthorized access. Your role does not permit access.');
        }

        return $next($request);
    }
}