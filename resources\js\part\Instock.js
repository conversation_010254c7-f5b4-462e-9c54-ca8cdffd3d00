document.addEventListener("DOMContentLoaded", function () {
    // Part Search
    const partSearch = document.getElementById("partSearch");
    const partResults = document.getElementById("partResults");
    const filterType = document.getElementById("filterType");
    const dailyPicker = document.getElementById("dailyPicker");
    const monthlyPicker = document.getElementById("monthlyPicker");
    const rangePicker = document.getElementById("rangePicker");
    const filterButton = document.getElementById("filterButton");

    partSearch.addEventListener("input", function (e) {
        if (this.value.length > 2) {
            fetch(`/site/instock/search?term=${this.value}`)
                .then((res) => res.json())
                .then((data) => {
                    partResults.innerHTML = "";
                    data.forEach((part) => {
                        const item = document.createElement("button");
                        item.className =
                            "list-group-item list-group-item-action pt-1 pb-1 pl-2 pr-4";
                        item.textContent = part.part_name;
                        item.type = "button";
                        item.onclick = () => {
                            document.querySelector(
                                'input[name="id_partinventory"]'
                            ).value = part.id_partinventory;
                            partSearch.value = part.part_name;
                            partResults.innerHTML = "";
                        };
                        partResults.appendChild(item);
                    });
                });
        }
    });

    // Supplier Search
    const supplierSearch = document.getElementById("supplierSearch");
    const supplierResults = document.getElementById("supplierResults");

    supplierSearch.addEventListener("input", function (e) {
        if (this.value.length > 2) {
            fetch(`/site/instock/search-supplier?term=${this.value}`)
                .then((res) => res.json())
                .then((data) => {
                    supplierResults.innerHTML = "";
                    data.forEach((supplier) => {
                        const item = document.createElement("button");
                        item.className =
                            "list-group-item list-group-item-action pt-1 pb-1 pl-2 pr-4";
                        item.textContent = supplier.nama_supplier;
                        item.type = "button";
                        item.onclick = () => {
                            document.querySelector(
                                'input[name="id_supplier"]'
                            ).value = supplier.id_supplier;
                            supplierSearch.value = supplier.nama_supplier;
                            supplierResults.innerHTML = "";
                        };
                        supplierResults.appendChild(item);
                    });
                });
        }
    });

    // Form Submit
    document.getElementById("addForm").addEventListener("submit", function (e) {
        e.preventDefault();
        const formData = new FormData(this);

        fetch("/site/instock", {
            method: "POST",
            headers: {
                "X-CSRF-TOKEN": document
                    .querySelector('meta[name="csrf-token"]')
                    .getAttribute("content"),
                Accept: "application/json",
            },
            body: formData,
        })
            .then((response) => response.json())
            .then((data) => {
                if (data.message) {
                    $("#addModal").modal("hide");
                    window.location.reload();
                }
            })
            .catch((error) => console.error("Error:", error));
    });

    // Delete Button
    document.querySelectorAll(".delete-btn").forEach((btn) => {
        btn.addEventListener("click", function () {
            if (confirm("Apakah Anda yakin ingin menghapus data ini?")) {
                fetch(`/site/instock/${this.dataset.id}`, {
                    method: "DELETE",
                    headers: {
                        "X-CSRF-TOKEN": document
                            .querySelector('meta[name="csrf-token"]')
                            .getAttribute("content"),
                    },
                })
                    .then((res) => res.json())
                    .then((data) => {
                        this.closest("tr").remove();
                    });
            }
        });
    });

    
    const dailyDate = document.getElementById("dailyDate").value;
    if (!dailyDate) {
        alert("Silakan pilih tanggal sebelum mencari data.");
        return;
    }

    // Tampilkan form input sesuai pilihan
    filterType.addEventListener("change", function () {
        const selectedType = this.value;

        // Sembunyikan semua form input
        dailyPicker.style.display = "none";
        monthlyPicker.style.display = "none";
        rangePicker.style.display = "none";

        // Tampilkan form input yang sesuai
        if (selectedType === "daily") {
            dailyPicker.style.display = "block";
        } else if (selectedType === "monthly") {
            monthlyPicker.style.display = "block";
        } else if (selectedType === "range") {
            rangePicker.style.display = "block";
        }
    });

    // Handle filter button click
    filterButton.addEventListener("click", function () {
        const selectedType = filterType.value;
        let url = "/site/instock?filter_type=" + selectedType;

        // Tambahkan parameter filter berdasarkan jenis yang dipilih
        if (selectedType === "daily") {
            const dailyDate = document.getElementById("dailyDate").value;
            url += "&date=" + dailyDate;
        } else if (selectedType === "monthly") {
            const monthlyDate = document.getElementById("monthlyDate").value;
            url += "&month=" + monthlyDate;
        } else if (selectedType === "range") {
            const startDate = document.getElementById("startDate").value;
            const endDate = document.getElementById("endDate").value;
            url += "&start_date=" + startDate + "&end_date=" + endDate;
        }

        // Redirect ke URL dengan parameter filter
        window.location.href = url;
    });

    // Set default form input saat halaman dimuat
    filterType.dispatchEvent(new Event("change"));
});
