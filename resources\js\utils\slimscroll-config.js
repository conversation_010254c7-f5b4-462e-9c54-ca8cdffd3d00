// Override jQuery slimScroll defaults to use passive event listeners
(function($) {
    const originalSlimScroll = $.fn.slimScroll;
    
    $.fn.slimScroll = function(options) {
        const defaultOptions = {
            touchScrollStep: 20,
            wheelStep: 5,
            events: {
                touchstart: { passive: true },
                touchmove: { passive: true },
                mousewheel: { passive: true }
            }
        };
        
        const mergedOptions = $.extend({}, defaultOptions, options);
        
        return originalSlimScroll.call(this, mergedOptions);
    };
})(jQuery);