@extends('sales.contentsales')
@section('title', 'Daftar Part')
@section('resourcesales')
@vite(['resources/js/sales/part-list.js'])
<style>
    .w-fit-content {
        width: fit-content;
    }
    .shadow-kit {
        border: 1px solid rgb(42, 105, 168);
        box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
        border-radius: 0.5rem;
        background-color: #fff;
    }
    .btn-warning {
        background-color: rgb(242, 215, 132);
        color: #000;
    }
    .bg-warning {
        background-color: rgb(240, 250, 150);
        color: #0f0187;
    }

    /* Table styling */
    .table {
        font-size: 11px;
    }
    .table th, .table td {
        font-size: 11px;
        padding: 0.3rem;
        vertical-align: middle;
    }

    /* Editable field styling */
    .editable-field {
        border: 1px solid #ddd;
        background: #fff;
        width: 100%;
        font-size: 11px;
        padding: 4px 6px;
        border-radius: 3px;
    }
    .editable-field:focus {
        background: #fff;
        border: 1px solid #007bff;
        outline: none;
        box-shadow: 0 0 3px rgba(0, 123, 255, 0.25);
    }
    .editable-cell {
        cursor: pointer;
        position: relative;
    }
    .editable-cell:hover {
        background-color: #f8f9fa;
    }
    .readonly-field {
        background-color: #f8f9fa;
        color: #6c757d;
        cursor: not-allowed;
    }

    /* Search box styles */
    .search-box {
        position: relative;
        width: 200px;
    }
    .search-icon {
        position: absolute;
        right: 10px;
        top: 50%;
        transform: translateY(-50%);
        color: #aaa;
    }

    /* Stock info styling */
    .stock-info {
        font-size: 10px;
        max-width: 150px;
    }
    .stock-item {
        display: block;
        margin-bottom: 2px;
    }
    .stock-site {
        font-weight: bold;
        color: #007bff;
    }
    .stock-quantity {
        color: #28a745;
    }

    /* Loading and skeleton styles */
    .skeleton {
        background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
        background-size: 200% 100%;
        animation: loading 1.5s infinite;
    }
    @keyframes loading {
        0% { background-position: 200% 0; }
        100% { background-position: -200% 0; }
    }

    /* Action buttons */
    .action-buttons {
        white-space: nowrap;
    }
    .btn-save {
        background-color: #28a745;
        color: white;
        border: none;
        padding: 2px 6px;
        font-size: 10px;
        border-radius: 3px;
        margin-right: 2px;
    }
    .btn-cancel {
        background-color: #dc3545;
        color: white;
        border: none;
        padding: 2px 6px;
        font-size: 10px;
        border-radius: 3px;
    }
    .btn-edit {
        background-color: #007bff;
        color: white;
        border: none;
        padding: 2px 6px;
        font-size: 10px;
        border-radius: 3px;
    }

    /* Currency formatting */
    .currency {
        text-align: right;
    }

    /* Modal styling */
    .modal-body {
        font-size: 11px;
    }
    .modal-body .form-label {
        font-size: 11px;
        font-weight: 600;
        margin-bottom: 4px;
    }
    .modal-body .form-control {
        font-size: 11px;
        padding: 6px 8px;
    }
    .modal-footer .btn {
        font-size: 11px;
        padding: 4px 12px;
    }

    /* Currency input styling */
    .currency-input {
        text-align: right;
    }
</style>
@endsection

@section('contentsales')
@include('sales.partials.navigation')

<div class="content">
    <div class="container-fluid">
        <div class="row">
            <div class="col-md-12">
                <div class="bgwhite shadow-kit rounded-lg">
                    <div class="card-header bg-primary text-white d-flex justify-content-between align-items-center">
                        <div class="d-flex align-items-center">
                            <h5 class="mb-0 mr-3 font-bold text-uppercase text-white">Daftar Part</h5>
                            <button id="add-new-part-btn" class="btn btn-sm btn-light ml-2">
                                <i class="mdi mdi-plus"></i> Tambah Part Baru
                            </button>
                        </div>
                    </div>
                    <div class="card-body">
                        <div class="mb-3 d-flex justify-content-between align-items-center">
                            <div class="d-flex align-items-center">
                                <span class="text-muted" style="font-size: 11px;">
                                    <i class="mdi mdi-information-outline"></i>
                                    Klik pada field untuk mengedit data part. Klik tombol simpan untuk menyimpan perubahan.
                                </span>
                            </div>
                            <div class="d-flex align-items-center">
                                <div class="search-box mr-2">
                                    <div class="position-relative">
                                        <input type="text" id="search-input" class="form-control form-control-sm" placeholder="Cari part..." style="font-size: 11px;">
                                        <i class="mdi mdi-magnify search-icon"></i>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="table-responsive">
                            <table class="table table-bordered w-100" id="parts-table">
                                <thead class="bg-light">
                                    <tr>
                                        <th style="width: 120px;">KODE PART</th>
                                        <th style="width: 250px;">NAMA PART</th>
                                        <th style="width: 150px;">TIPE</th>
                                        <th style="width: 120px;">HARGA BELI</th>
                                        <th style="width: 120px;">HARGA JUAL</th>
                                        <th style="width: 80px;">EUM</th>
                                        <th style="width: 120px;">AKSI</th>
                                    </tr>
                                </thead>
                                <tbody id="parts-table-body">
                                    <!-- Data will be loaded via AJAX -->
                                </tbody>
                            </table>
                        </div>
                        <div class="d-flex justify-content-between align-items-center mt-3">
                            <div>
                                <span id="showing-text" style="font-size: 11px;">Menampilkan 0 dari 0 part</span>
                            </div>
                            <div>
                                <nav aria-label="Page navigation">
                                    <ul class="pagination pagination-sm" id="pagination">
                                        <!-- Pagination will be generated dynamically -->
                                    </ul>
                                </nav>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Loading overlay -->
<div id="loading-overlay" class="d-none position-fixed w-100 h-100" style="top: 0; left: 0; background: rgba(0,0,0,0.5); z-index: 9999;">
    <div class="d-flex justify-content-center align-items-center h-100">
        <div class="spinner-border text-light" role="status">
            <span class="sr-only">Loading...</span>
        </div>
    </div>
</div>

<!-- Add Part Modal -->
<div class="modal fade" id="add-part-modal" tabindex="-1" role="dialog" aria-labelledby="add-part-modal-label" aria-hidden="true">
    <div class="modal-dialog modal-lg" role="document">
        <div class="modal-content">
            <div class="modal-header bg-primary text-white">
                <h5 class="modal-title" id="add-part-modal-label">Tambah Part Baru</h5>
                <button type="button" class="btn-close btn-close-white close-modal-btn" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <form id="add-part-form">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="add_part_code" class="form-label">Kode Part <span class="text-danger">*</span></label>
                                <input type="text" class="form-control" id="add_part_code" name="part_code" placeholder="Kode Part" required>
                            </div>
                            <div class="mb-3">
                                <label for="add_part_name" class="form-label">Nama Part <span class="text-danger">*</span></label>
                                <input type="text" class="form-control" id="add_part_name" name="part_name" placeholder="Nama Part" required>
                            </div>
                            <div class="mb-3">
                                <label for="add_part_type" class="form-label">Tipe Part <span class="text-danger">*</span></label>
                                <select class="form-control" id="add_part_type" name="part_type" required>
                                    <option value="">Pilih Tipe Part</option>
                                    <option value="AC">AC</option>
                                    <option value="TYRE">TYRE</option>
                                    <option value="FABRIKASI">FABRIKASI</option>
                                    <option value="PERLENGKAPAN AC">PERLENGKAPAN AC</option>
                                    <option value="PERSEDIAAN LAINNYA">PERSEDIAAN LAINNYA</option>
                                </select>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="add_price" class="form-label">Harga Jual</label>
                                <input type="text" class="form-control currency-input" id="add_price" name="price" placeholder="Rp 0">
                            </div>
                            <div class="mb-3">
                                <label for="add_purchase_price" class="form-label">Harga Beli</label>
                                <input type="text" class="form-control currency-input" id="add_purchase_price" name="purchase_price" placeholder="Rp 0">
                            </div>
                            <div class="mb-3">
                                <label for="add_eum" class="form-label">EUM</label>
                                <input type="text" class="form-control" id="add_eum" name="eum" placeholder="EA" value="EA" maxlength="5">
                            </div>
                        </div>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-sm btn-secondary close-modal-btn" data-bs-dismiss="modal">Batal</button>
                <button type="button" class="btn btn-sm btn-primary" id="save-part-btn">Simpan</button>
            </div>
        </div>
    </div>
</div>


@endsection
