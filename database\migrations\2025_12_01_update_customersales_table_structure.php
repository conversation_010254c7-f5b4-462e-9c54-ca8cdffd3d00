<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('customersales', function (Blueprint $table) {
            // Add code column if it doesn't exist
            if (!Schema::hasColumn('customersales', 'code')) {
                $table->string('code')->unique()->after('id');
            }
            
            // Copy data from kode to code if kode exists
            if (Schema::hasColumn('customersales', 'kode')) {
                // This will be handled in a separate data migration
            }
            
            // Drop total_pending column if it exists since we'll calculate from invoices
            if (Schema::hasColumn('customersales', 'total_pending')) {
                $table->dropColumn('total_pending');
            }
        });
        
        // Copy data from kode to code if both columns exist
        if (Schema::hasColumn('customersales', 'kode') && Schema::hasColumn('customersales', 'code')) {
            DB::statement('UPDATE customersales SET code = kode WHERE code IS NULL OR code = ""');
        }
        
        // Drop kode column after copying data
        Schema::table('customersales', function (Blueprint $table) {
            if (Schema::hasColumn('customersales', 'kode')) {
                $table->dropColumn('kode');
            }
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('customersales', function (Blueprint $table) {
            // Add back kode column
            if (!Schema::hasColumn('customersales', 'kode')) {
                $table->string('kode')->unique()->after('id');
            }
            
            // Add back total_pending column
            if (!Schema::hasColumn('customersales', 'total_pending')) {
                $table->decimal('total_pending', 15, 2)->default(0)->after('alamat');
            }
        });
        
        // Copy data from code to kode if both columns exist
        if (Schema::hasColumn('customersales', 'code') && Schema::hasColumn('customersales', 'kode')) {
            DB::statement('UPDATE customersales SET kode = code WHERE kode IS NULL OR kode = ""');
        }
        
        // Drop code column
        Schema::table('customersales', function (Blueprint $table) {
            if (Schema::hasColumn('customersales', 'code')) {
                $table->dropColumn('code');
            }
        });
    }
};
