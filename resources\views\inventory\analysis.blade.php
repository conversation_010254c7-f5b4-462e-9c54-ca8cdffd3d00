@extends('layouts.app')

@section('content')
<div class="container">
    <h2>Analisis Penggunaan Part</h2>
    
    <!-- Filter Section -->
    <div class="card mb-4">
        <div class="card-body">
            <form id="analysisFilter">
                <div class="row">
                    <div class="col-md-3">
                        <label>Periode Waktu</label>
                        <select name="time_period" class="form-select">
                            <option value="daily"><PERSON><PERSON></option>
                            <option value="weekly" selected>Mingguan</option>
                            <option value="monthly">Bulanan</option>
                            <option value="yearly"><PERSON><PERSON><PERSON></option>
                        </select>
                    </div>
                    <div class="col-md-3">
                        <label>Site</label>
                        <select name="site_id" class="form-select">
                            <option value="">Semua <PERSON></option>
                            @foreach($sites as $site)
                                <option value="{{ $site->site_id }}">{{ $site->site_name }}</option>
                            @endforeach
                        </select>
                    </div>
                    <div class="col-md-3">
                        <label>Kategori Part</label>
                        <select name="part_type" class="form-select">
                            <option value="">Semua Kategori</option>
                            <option value="AC">AC</option>
                            <option value="TYRE">TYRE</option>
                            <option value="FABRIKASI">FABRIKASI</option>
                            <option value="PERLENGKAPAN AC">PERLENGKAPAN AC</option>
                            <option value="PERSEDIAAN LAINNYA">PERSEDIAAN LAINNYA</option>
                        </select>
                    </div>
                </div>
            </form>
        </div>
    </div>

    <!-- Charts -->
    <div class="row">
        <div class="col-md-6">
            <div class="card">
                <div class="card-header">Top Usage by Quantity</div>
                <div class="card-body">
                    <canvas id="quantityChart"></canvas>
                </div>
            </div>
        </div>
        <div class="col-md-6">
            <div class="card">
                <div class="card-header">Top Usage by Frequency</div>
                <div class="card-body">
                    <canvas id="frequencyChart"></canvas>
                </div>
            </div>
        </div>
    </div>
</div>
<script>
    let quantityChart = null;
let frequencyChart = null;

function initializeCharts() {
    const chartOptions = {
        responsive: true,
        maintainAspectRatio: false,
        scales: {
            y: {
                beginAtZero: true
            }
        }
    };

    // Initial chart setup
    const quantityCtx = document.getElementById('quantityChart').getContext('2d');
    const frequencyCtx = document.getElementById('frequencyChart').getContext('2d');

    quantityChart = new Chart(quantityCtx, {
        type: 'bar',
        data: { labels: [], datasets: [{ label: 'Total Quantity', data: [] }] },
        options: chartOptions
    });

    frequencyChart = new Chart(frequencyCtx, {
        type: 'bar',
        data: { labels: [], datasets: [{ label: 'Transaction Count', data: [] }] },
        options: chartOptions
    });
}

function updateCharts(data) {
    // Update Quantity Chart
    quantityChart.data.labels = data.top_quantity.map(item => item.part_name);
    quantityChart.data.datasets[0].data = data.top_quantity.map(item => item.total_quantity);
    quantityChart.update();

    // Update Frequency Chart
    frequencyChart.data.labels = data.top_frequency.map(item => item.part_name);
    frequencyChart.data.datasets[0].data = data.top_frequency.map(item => item.transaction_count);
    frequencyChart.update();
}

async function fetchData() {
    const formData = new FormData(document.getElementById('analysisFilter'));
    const params = new URLSearchParams(formData);

    try {
        const response = await fetch(`/analysis?${params}`);
        const data = await response.json();
        updateCharts(data);
    } catch (error) {
        console.error('Error fetching data:', error);
    }
}

// Event Listeners
document.querySelectorAll('#analysisFilter select').forEach(select => {
    select.addEventListener('change', fetchData);
});

// Initial Load
document.addEventListener('DOMContentLoaded', () => {
    initializeCharts();
    fetchData();
});
</script>
@endsection

@push('scripts')
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script src="{{ asset('js/analysis.js') }}"></script>
@endpush