<?php

namespace App\Http\Controllers;

use Illuminate\Support\Facades\Hash;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class AuthenticationController extends Controller
{
    public function index()
    {
        return view('auth.login');
    }
    public function showLoginForm()
    {
        return view('auth.login'); // Sesuaikan dengan nama view Anda
    }

    public function login(Request $request)
    {
        $credentials = $request->validate([
            'username' => 'required',
            'password' => 'required',
        ]);

        $remember = $request->has('remember');
        $user = User::where('username', $credentials['username'])->first();

        if ($user && Hash::check($credentials['password'], $user->password)) {
            Auth::login($user, $remember);
            $request->session()->regenerate();
            session(['site_id' => $user->site_id]);
            return $this->redirectAfterLogin($user->role);
        }
        return back()->withErrors([
            'username' => 'Invalid username or password.',
        ])->withInput($request->only('username'));
    }

    protected function redirectAfterLogin($role)
    {
        switch ($role) {
            case 'adminho':
                return redirect()->route('admin.dashboard'); // Sesuaikan dengan route admin HO
            case 'adminsite':
                return redirect()->route('adminsite.dashboard'); // Sesuaikan dengan route admin site
            case 'sales':
                return redirect()->route('sales.dashboard'); // Sesuaikan dengan route sales
            case 'karyawan':
                return redirect()->route('karyawan.dashboard'); // Sesuaikan dengan route karyawan
            default:
                return redirect()->route('home'); // Route default jika role tidak dikenali
        }
    }

    public function logout(Request $request)
    {
        Auth::logout();

        $request->session()->invalidate();

        $request->session()->regenerateToken();

        return redirect('/login'); // Redirect ke halaman login
    }

    public function showCreateUserForm()
    {
        $roles = User::getRoles(); // Ambil daftar role dari model
        return view('warehouse.create_user', compact('roles'));
    }

    public function createUser(Request $request)
    {
        $request->validate([
            'employee_id' => 'required|string|max:50|unique:users',
            'site_id' => 'nullable|string|max:50',
            'username' => 'required|string|max:255|unique:users',
            'password' => 'required|string|min:8|confirmed', //Tambahkan confirmasi password
            'email' => 'nullable|email|max:50',
            'role' => 'required|in:' . implode(',', User::getRoles()), // Validasi role
        ]);

        User::create([
            'employee_id' => $request->employee_id,
            'site_id' => $request->site_id,
            'username' => $request->username,
            'password' => Hash::make($request->password),
            'email' => $request->email,
            'role' => $request->role,
        ]);

        return redirect()->route('dashboard')->with('success', 'User created successfully.');
    }
}