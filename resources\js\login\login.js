document.addEventListener('DOMContentLoaded', function() {
    const effects = document.querySelector('.background-effects');
    for (let i = 0; i < 5; i++) {
        const particle = document.createElement('div');
        particle.className = 'particle';
        particle.style.left = Math.random() * 100 + '%';
        particle.style.width = particle.style.height = Math.random() * 100 + 50 + 'px';
        particle.style.animationDelay = Math.random() * -20 + 's';
        effects.appendChild(particle);
    }
});