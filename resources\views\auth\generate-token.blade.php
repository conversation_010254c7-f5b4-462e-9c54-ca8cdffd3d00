@extends('warehouse.content')

@section('content')
<div class="content">
    <div class="container-fluid">
        <div class="row">
            <div class="col-12">
                <div class="card">
                    <div class="card-header bg-primary text-white">
                        <h4 class="mb-0">Generate Token Superadmin</h4>
                    </div>
                    <div class="card-body">
                        @if(session('success'))
                            <div class="alert alert-success">
                                <p>{{ session('success') }}</p>
                                <div class="mt-3">
                                    <p><strong>Token:</strong> <code>{{ session('token') }}</code></p>
                                    <p><strong>Status:</strong> Token permanen (tidak memiliki batas waktu)</p>
                                </div>
                                <div class="alert alert-warning mt-2">
                                    <p><strong>Perhatian!</strong> Simpan token ini dengan aman. Token hanya akan ditampilkan sekali.</p>
                                </div>
                            </div>
                        @endif

                        @if($errors->any())
                            <div class="alert alert-danger">
                                <ul class="mb-0">
                                    @foreach($errors->all() as $error)
                                        <li>{{ $error }}</li>
                                    @endforeach
                                </ul>
                            </div>
                        @endif

                        <form action="{{ route('token.generate') }}" method="post">
                            @csrf
                            <div class="mb-3">
                                <label for="employee_id" class="form-label">Pilih Superadmin</label>
                                <select class="form-select" id="employee_id" name="employee_id" required>
                                    <option value="">-- Pilih Superadmin --</option>
                                    @foreach($users as $user)
                                        <option value="{{ $user->employee_id }}">{{ $user->name }} ({{ $user->username }})</option>
                                    @endforeach
                                </select>
                            </div>

                            <div class="mb-3">
                                <div class="alert alert-info">
                                    <p class="mb-0"><strong>Catatan:</strong> Token yang dibuat akan berlaku permanen dan tidak memiliki batas waktu.</p>
                                </div>
                            </div>

                            <div class="text-end">
                                <button type="submit" class="btn btn-primary">Generate Token</button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection
