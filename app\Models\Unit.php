<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class Unit extends Model
{
    protected $fillable = [
        'site_id',
        'unit_code',
        'unit_type',
        'nopr',
        'noqtn',
        'pekerjaan',
        'HMKM',
        'SHIFT',
        'LOKASI',   
        'do_number',
        'noSPB',
    ];

    /**
     * Get the next DO number by incrementing the numeric part
     *
     * @return string|null
     */
    public function getNextDoNumber()
    {
        if (!$this->do_number) {
            return null;
        }

        // Extract the last numeric part from the DO number
        if (preg_match('/(.*?)(\d+)([^\d]*)$/', $this->do_number, $matches)) {
            $prefix = $matches[1]; // Everything before the last numeric part
            $numericPart = $matches[2]; // The last numeric part
            $suffix = $matches[3]; // Anything after the last numeric part (usually empty)

            // Increment the numeric part while preserving leading zeros
            $nextNumericPart = (int)$numericPart + 1;
            $digitCount = strlen($numericPart);
            $formattedNumericPart = str_pad($nextNumericPart, $digitCount, '0', STR_PAD_LEFT);

            return $prefix . $formattedNumericPart . $suffix;
        }

        // If no numeric part found, just return the original
        return $this->do_number;
    }

    /**
     * Get the next SPB number by incrementing the numeric part
     *
     * @return string|null
     */
    public function getNextSpbNumber()
    {
        if (!$this->noSPB) {
            return null;
        }

        // Extract the last numeric part from the SPB number
        if (preg_match('/(.*?)(\d+)([^\d]*)$/', $this->noSPB, $matches)) {
            $prefix = $matches[1]; // Everything before the last numeric part
            $numericPart = $matches[2]; // The last numeric part
            $suffix = $matches[3]; // Anything after the last numeric part (usually empty)

            // Increment the numeric part while preserving leading zeros
            $nextNumericPart = (int)$numericPart + 1;
            $digitCount = strlen($numericPart);
            $formattedNumericPart = str_pad($nextNumericPart, $digitCount, '0', STR_PAD_LEFT);

            return $prefix . $formattedNumericPart . $suffix;
        }

        // If no numeric part found, just return the original
        return $this->noSPB;
    }

    public function site()
    {
        return $this->belongsTo(Site::class, 'site_id', 'site_id');
    }

    public function parts()
    {
        return $this->hasMany(UnitPart::class);
    }

    public function transactions()
    {
        return $this->hasMany(UnitTransaction::class);
    }

    public function backlogs()
    {
        return $this->hasMany(Backlog::class, 'unit_code', 'unit_code');
    }

    public function openBacklogs()
    {
        return $this->hasMany(Backlog::class, 'unit_code', 'unit_code')->where('status', 'OPEN');
    }

    public function dailyReports()
    {
        return $this->hasMany(DailyReport::class, 'unit_id', 'id');
    }
}