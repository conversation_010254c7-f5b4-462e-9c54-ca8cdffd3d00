<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // First, get all existing document paths and convert them to strings
        $invoices = DB::table('invoices')->whereNotNull('document_path')->get();
        $updatedDocumentPaths = [];

        foreach ($invoices as $invoice) {
            try {
                // Try to decode the JSON
                $documentPaths = json_decode($invoice->document_path, true);
                
                // If it's an array and has at least one element, take the first one
                if (is_array($documentPaths) && count($documentPaths) > 0) {
                    $updatedDocumentPaths[$invoice->id] = $documentPaths[0];
                } else {
                    // If it's not a valid JSON array or is empty, keep the original value
                    $updatedDocumentPaths[$invoice->id] = $invoice->document_path;
                }
            } catch (\Exception $e) {
                // If there's an error decoding, keep the original value
                $updatedDocumentPaths[$invoice->id] = $invoice->document_path;
            }
        }

        // Change the column type from JSON to string
        Schema::table('invoices', function (Blueprint $table) {
            // Drop the JSON column
            $table->dropColumn('document_path');
        });

        Schema::table('invoices', function (Blueprint $table) {
            // Add a new string column
            $table->string('document_path', 255)->nullable()->after('signed_document_path');
        });

        // Update the invoices with the converted document paths
        foreach ($updatedDocumentPaths as $id => $path) {
            DB::table('invoices')
                ->where('id', $id)
                ->update(['document_path' => $path]);
        }
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // Get all existing document paths
        $invoices = DB::table('invoices')->whereNotNull('document_path')->get();
        $updatedDocumentPaths = [];

        foreach ($invoices as $invoice) {
            // Convert string to JSON array
            $updatedDocumentPaths[$invoice->id] = json_encode([$invoice->document_path]);
        }

        // Change the column type back to JSON
        Schema::table('invoices', function (Blueprint $table) {
            // Drop the string column
            $table->dropColumn('document_path');
        });

        Schema::table('invoices', function (Blueprint $table) {
            // Add a new JSON column
            $table->json('document_path')->nullable()->after('signed_document_path');
        });

        // Update the invoices with the converted document paths
        foreach ($updatedDocumentPaths as $id => $path) {
            DB::table('invoices')
                ->where('id', $id)
                ->update(['document_path' => $path]);
        }
    }
};
