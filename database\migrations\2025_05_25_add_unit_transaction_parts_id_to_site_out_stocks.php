<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('site_out_stocks', function (Blueprint $table) {
            $table->foreignId('unit_transaction_parts_id')->nullable()->after('notes');
            $table->foreign('unit_transaction_parts_id')
                ->references('id')
                ->on('unit_transaction_parts')
                ->onUpdate('cascade')
                ->onDelete('set null');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('site_out_stocks', function (Blueprint $table) {
            $table->dropForeign(['unit_transaction_parts_id']);
            $table->dropColumn('unit_transaction_parts_id');
        });
    }
};
