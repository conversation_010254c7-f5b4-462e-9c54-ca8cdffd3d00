@extends('warehouse.content')
@section('contentho')
@section('title', 'History In Part')
<div class="row bgwhite page-title-box shadow-kit mb-1">
    <div class="col d-flex align-items-center" style="max-width: fit-content;">
        <button class="button-menu-mobile text-blue-500 btn-sm">
            <i style="font-size: 30px;" class="mdi mdi-menu"></i>
        </button>
        <div class="text">
            <p class="font-bold m-0">{{ session('name') }}</p>
        </div>
    </div>
    <div class="col">
        <ul class="list-unstyled topnav-menu float-right mb-0">
            <li class="dropdown notification-list">
                <a class="nav-link dropdown-toggle" href="#" data-bs-toggle="dropdown" aria-expanded="false">
                    <i class="mdi mdi-bell-outline noti-icon"></i>
                    <span class="badge badge-danger badge-pill float-right badgenotif"></span>
                </a>
                <div class="dropdown-menu dropdown-menu-end dropdown-lg">
                    <div class="dropdown-item noti-title bg-primary">
                        <h5 class="m-0 text-white d-flex justify-content-between align-items-center">
                            Notifikasi
                            <a href="javascript:void(0);" class="text-white" id="clear-all">
                                <small></small>
                            </a>
                        </h5>
                    </div>
                    <div class="noti-scroll" id="notification-list">
                        <!-- Notifications will be dynamically inserted here -->
                    </div>
                </div>
            </li>
        </ul>
    </div>
</div>

<div class="row p-2">
    <div class="col bgwhite shadow-kit p-4 rounded-lg">
        <div class="shadow-kit p-4">
            <h1 class="text-xl font-bold mb-4">History In Part</h1>
            <div class="mb-3">
                <div class="d-flex align-items-center">
                    <div>
                        <label for="supplierFilter" class="form-label">Filter Supplier:</label>
                        <div class="d-flex">
                            <select class="form-control btn-primary mr-3" class="form-select" id="supplierFilter">
                                <option value="">All Suppliers</option>
                                @foreach($suppliers as $supplier)
                                <option value="{{ $supplier->supplier_id }}">{{ $supplier->supplier_name }}</option>
                                @endforeach
                            </select>
                        </div>
                    </div>
                    <div>
                        <label class="form-label">Filter berdasarkan Tanggal</label>
                        <div class="d-flex">
                            <input type="date" class="form-control me-2" id="startDate" name="start_date" value="<?php echo date('Y-m-d'); ?>">
                            <input type="date" class="form-control" id="endDate" name="end_date" value="<?php echo date('Y-m-d'); ?>">
                        </div>
                    </div>
                </div>
            </div>
            <hr>
            <table class="table">
                <h6 class="text-center h5 text-uppercase">Tabel history</h6>
                <thead class="table-dark text-white">
                    <tr>
                        <th class="p-2">Part Code</th>
                        <th class="p-2">Part Name</th>
                        <th class="p-2">Supplier</th>
                        <th class="p-2">Quantity</th>
                        <th class="p-2">Date In</th>
                        <th class="p-2">Notes</th>
                        <th class="p-2">Aksi</th>
                    </tr>
                </thead>
                <tbody id="inStockTableBody">
                </tbody>
            </table>
            <div id="instock-pagination" class="mt-3">
                <!-- Custom pagination will be rendered here by JavaScript -->
            </div>
        </div>
    </div>
    <div class="col max500">
        <div class="bgwhite shadow-kit rounded-lg">
            <div class="card-header h5 mt-0 font-bold">Tambahkan In Stock</div>
            <div class="card-body p-4 ">
                <form id="addInStockForm">
                    @csrf
                    <div class="mb-3">
                        <label for="partName" class="form-label">Nama Part</label>
                        <input type="text" class="form-control" id="partName" name="part_name" autocomplete="off">
                        <div id="partSuggestions" class="list-group"></div>
                        <input type="hidden" id="partCode" name="part_code">
                    </div>
                    <div class="mb-3">
                        <label for="supplierId" class="form-label">Supplier</label>
                        <select class="btn btn-sm btn-secondary" id="supplierId" name="supplier_id">
                            <option value="">Select Supplier</option>
                            @foreach($suppliers as $supplier)
                            <option value="{{ $supplier->supplier_id }}">{{ $supplier->supplier_name }}</option>
                            @endforeach
                        </select>
                    </div>
                    <div class="mb-3">
                        <label for="dateIn" class="form-label">Date In:</label>
                        <input value="{{ date('Y-m-d') }}" type="date" class="form-control" id="dateIn" name="date_in">
                    </div>
                    <div class="mb-3">
                        <label for="quantity" class="form-label">Quantity:</label>
                        <input type="number" class="form-control" id="quantity" name="quantity" step="0.1" min="0.1">
                    </div>
                    <div class="mb-3">
                        <label for="notes" class="form-label">Notes:</label>
                        <textarea class="form-control" id="notes" name="notes"></textarea>
                    </div>
                    <button type="submit" class="btn btn-primary">Add In Stock</button>
                </form>
            </div>
        </div>
        <div class="row">
            <div class="col mt-2 ml-2">
                <div class="card shadow-kit bgwhite p-3 mb-0">
                    <h5 class="h5 m-0 font-bold">Total In-Stocks</h5>
                    <p id="total" class="h1 font-bold">{{ $totalQuantity }}</p>
                </div>
            </div>
            <div class="col p-2 mr-2">
                <div class="card shadow-kit bgwhite p-3 mb-0">
                    <h5>Download Laporan</h5>
                    <div class="d-between">
                        <button class="btn btn-sm btn-primary report-btn" data-type="in">
                            <i class="mdi mdi-file-import mr-1"></i> Export In
                        </button>
                        <button class="btn btn-sm btn-success report-btn ml-2" data-type="out">
                            <i class="mdi mdi-file-export"></i> Export Out
                        </button>
                    </div>
                </div>
            </div>
        </div>
        <div class="col p-0">
            <div class="card shadow-kit bgwhite p-4 mt-2">
                <h3>Aktifitas Terakhir</h3>
                <hr class="mt-2">
                <ul id="activityList">
                </ul>
            </div>
        </div>
    </div>
</div>
<div id="exportModal" class="modal" tabindex="-1" role="dialog" aria-labelledby="exportModalLabel" aria-hidden="true">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="font-bold text-uppercase" id="title">Filter Laporan</h5>
                <span class="close" aria-label="Close">&times;</span>
            </div>
            <form id="exportForm" method="GET">
                <input type="hidden" name="report_type" id="reportType">
                <div class="modal-body">
                    <div class="form-grid">
                        <div class="d-flex gap-3">
                            <div class="form-group flex-grow-1">
                                <label for="start_date">Tanggal Mulai</label>
                                <input type="date" class="form-control" name="start_date">
                            </div>
                            <div class="form-group flex-grow-1">
                                <label for="end_date">Tanggal Selesai</label>
                                <input type="date" class="form-control" name="end_date">
                            </div>
                        </div>
                        <div class="d-flex gap-3">
                            <div class="form-group flex-grow-1">
                                <label for="sort_by">Urutkan Berdasarkan</label>
                                <select class="custom-select" name="sort_by">
                                    <option value="date">Tanggal</option>
                                    <option value="part_name">Nama Part</option>
                                </select>
                            </div>
                            <div class="form-group flex-grow-1">
                                <label for="site_id">Site</label>
                                <select class="custom-select" name="site_id">
                                    <option value="all" selected>Semua Site</option>
                                    @foreach($sites as $site)
                                    <option value="{{ $site->site_id }}">{{ $site->site_name }}</option>
                                    @endforeach
                                </select>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <!-- <button type="button" class="btn btn-secondary" id="closeModal">Tutup</button> -->
                    <button type="button" class="btn btn-pink btn-rounded waves-effect waves-light" id="exportPdf">
                        <i class="mdi mdi-file-pdf-box mr-1"></i> Export PDF
                    </button>
                    <button type="button" class="btn btn-pink btn-rounded waves-effect waves-light" id="exportExcel">
                        <i class="mdi mdi-file-excel mr-1"></i> Export Excel
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>
@endsection
@section('resource')
@vite(['resources/js/adminho/instockwho.js','resources/js/style.js'])
@vite('resources/js/laporan.js')
<style>
    /* Pagination styling */
    .pagination {
        display: flex;
        justify-content: center;
        list-style: none;
        padding: 0;
        margin-top: 15px;
    }
    .pagination .page-item {
        margin: 0 2px;
    }
    .pagination .page-item .page-link {
        color: #333;
        background-color: #fff;
        border: 1px solid #ddd;
        padding: 6px 12px;
        text-decoration: none;
        border-radius: 4px;
        cursor: pointer;
    }
    .pagination .page-item.active .page-link {
        color: #fff;
        background-color: #28a745;
        border-color: #28a745;
    }
    .pagination .page-item .page-link:hover {
        background-color: #f8f9fa;
    }
    #instock-pagination {
        margin-top: 15px;
    }
</style>
<script>
    // Initial pagination data
    window.instockPaginationData = {
        current_page: 1,
        per_page: 20,
        last_page: 1,
        total: 0
    };
</script>
@endsection