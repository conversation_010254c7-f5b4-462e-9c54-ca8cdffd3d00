import Swal from "sweetalert2";

document.addEventListener("DOMContentLoaded", function () {
    const form = document.getElementById("perlengkapan-form");
    const siteFilter = document.getElementById("site-filter");
    const searchInput = document.getElementById("search");

    // Fungsi untuk menampilkan SweetAlert messages
    function showAlert(icon, title, text) {
        Swal.fire({
            icon: icon,
            title: title,
            text: text,
            confirmButtonColor: icon === "success" ? "#3085d6" : "#d33",
        });
    }

    // Fungsi untuk mereset form
    function resetForm() {
        form.reset();
        document.getElementById("equipment_id").value = ""; // Hapus ID
    }

    // Fungsi untuk mengisi form saat edit
    function populateForm(data) {
        document.getElementById("equipment_id").value = data.equipment_id;
        document.getElementById("equipment_name").value = data.equipment_name;
        document.getElementById("site_id").value = data.site_id;
        document.getElementById("quantity").value = data.quantity;
        document.getElementById("date_in").value = data.date_in;
    }

    // Load data dengan filter
    function loadData() {
        const params = new URLSearchParams({
            site_id: document.getElementById("site-filter").value,
            search: document.getElementById("search").value,
        });

        fetch(`/perlengkapan?${params}`, {
            headers: {
                "X-Requested-With": "XMLHttpRequest",
            },
        })
            .then((response) => {
                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }
                return response.json();
            })
            .then((data) => {
                document.getElementById("perlengkapan-table").innerHTML =
                    data.tbody;
                document.querySelector(".pagination-wrapper").innerHTML =
                    data.pagination;
            })
            .catch((error) => {
                console.error("Error loading data:", error);
                showAlert("error", "Error!", "Failed to load data.");
            });
    }

    // Handle form submit
    form.addEventListener("submit", function (e) {
        e.preventDefault();

        Swal.fire({
            title: "Apakah Anda yakin?",
            text: "Pastikan data sudah benar! Data tidak bisa diubah setelah disimpan.",
            icon: "warning",
            showCancelButton: true,
            confirmButtonColor: "#3085d6",
            cancelButtonColor: "#d33",
            confirmButtonText: "Ya, simpan!",
            cancelButtonText: "Batal",
        }).then((result) => {
            if (result.isConfirmed) {
                const formData = new FormData(form);
                const id = document.getElementById("equipment_id").value;
                const url = id ? `/perlengkapan/${id}` : "/perlengkapan";
                const method = id ? "PUT" : "POST";
                const csrfToken = document
                    .querySelector('meta[name="csrf-token"]')
                    .getAttribute("content");
                const data = {
                    equipment_name:
                        document.getElementById("equipment_name").value,
                    site_id: document.getElementById("site_id").value,
                    date_in: document.getElementById("date_in").value,
                    quantity: document.getElementById("quantity").value,
                };

                fetch(url, {
                    method: method,
                    headers: {
                        "Content-Type": "application/json",
                        "X-CSRF-TOKEN": csrfToken,
                        "X-Requested-With": "XMLHttpRequest",
                    },
                    body: JSON.stringify(data),
                })
                    .then((response) => {
                        if (!response.ok) {
                            return response.json().then((err) => {
                                throw new Error(
                                    err.alert_message ||
                                        `HTTP error! status: ${response.status}`
                                );
                            });
                        }
                        return response.json();
                    })
                    .then((data) => {
                        showAlert(
                            data.alert_type,
                            data.alert_title,
                            data.alert_message
                        );

                        if (data.alert_type === "success") {
                            resetForm(); // Reset form setelah berhasil
                            loadData();
                        }
                    })
                    .catch((error) => {
                        console.error("Error submitting form:", error);
                        showAlert(
                            "error",
                            "Gagal!",
                            "Terjadi kesalahan, silakan coba lagi. " + error
                        );
                    });
            }
        });
    });

    // Autocomplete
    document
        .getElementById("equipment_name")
        .addEventListener("input", function () {
            const searchTerm = this.value;
            if (searchTerm.length < 2) return; // Minimum characters to search

            fetch(`/perlengkapan/autocomplete?term=${searchTerm}`)
                .then((response) => {
                    if (!response.ok) {
                        throw new Error(
                            `HTTP error! status: ${response.status}`
                        );
                    }
                    return response.json();
                })
                .then((data) => {
                    const datalist = document.getElementById("suggestions");
                    datalist.innerHTML = data
                        .map((item) => `<option value="${item}">`)
                        .join("");
                })
                .catch((error) => {
                    console.error("Autocomplete error:", error);
                    //Optionally display an error to the user.  But autocomplete failure isn't critical.
                });
        });

    // Edit data
    document.addEventListener("click", function (e) {
        if (e.target.classList.contains("edit-btn")) {
            const id = e.target.dataset.id;
            fetch(`/perlengkapan/${id}/edit`)
                .then((response) => {
                    if (!response.ok) {
                        throw new Error(
                            `HTTP error! status: ${response.status}`
                        );
                    }
                    return response.json();
                })
                .then((data) => {
                    populateForm(data); // Isi form dengan data
                })
                .catch((error) => {
                    console.error("Error fetching edit data:", error);
                    showAlert(
                        "error",
                        "Error!",
                        "Failed to fetch data for editing."
                    );
                });
        }
    });

    // Delete data
    document.addEventListener("click", function (e) {
        if (e.target.classList.contains("delete-bton")) {
            Swal.fire({
                title: "Apakah Anda yakin?",
                text: "Data yang dihapus tidak bisa dikembalikan!",
                icon: "warning",
                showCancelButton: true,
                confirmButtonColor: "#d33",
                cancelButtonColor: "#3085d6",
                confirmButtonText: "Ya, hapus!",
                cancelButtonText: "Batal",
            }).then((result) => {
                if (result.isConfirmed) {
                    const id = e.target.dataset.id;
                    const csrfToken = document
                        .querySelector('meta[name="csrf-token"]')
                        .getAttribute("content");

                    fetch(`/perlengkapan/${id}`, {
                        method: "DELETE",
                        headers: {
                            "X-CSRF-TOKEN": csrfToken,
                            "Content-Type": "application/json",
                        },
                    })
                        .then((response) => {
                            if (!response.ok) {
                                return response.json().then((err) => {
                                    throw new Error(
                                        err.alert_message ||
                                            `HTTP error! status: ${response.status}`
                                    );
                                });
                            }
                            return response.json();
                        })
                        .then((data) => {
                            showAlert(
                                data.alert_type,
                                data.alert_title,
                                data.alert_message
                            );

                            if (data.alert_type === "success") {
                                loadData();
                            }
                        })
                        .catch((error) => {
                            console.error("Error deleting data:", error);
                            showAlert(
                                "error",
                                "Gagal!",
                                "Terjadi kesalahan, silakan coba lagi. " + error
                            );
                        });
                }
            });
        }
    });

    // Filter and search
    [siteFilter, searchInput].forEach((element) => {
        element.addEventListener("change", loadData);
    });

    // Pagination
    document.addEventListener("click", function (e) {
        if (e.target.closest(".pagination a")) {
            e.preventDefault();
            const url = e.target.href;
            fetch(url)
                .then((response) => {
                    if (!response.ok) {
                        throw new Error(
                            `HTTP error! status: ${response.status}`
                        );
                    }
                    return response.json();
                })
                .then((data) => {
                    document.getElementById("perlengkapan-table").innerHTML =
                        data.tbody;
                    document.querySelector(".pagination-wrapper").innerHTML =
                        data.pagination;
                })
                .catch((error) => {
                    console.error("Error fetching pagination data:", error);
                    showAlert("error", "Error!", "Failed to load page.");
                });
        }
    });

    // Inisialisasi: Memuat data dan mereset form
    loadData();
    resetForm();
});
