// Import Bootstrap from our initialization file
import bootstrap from './bootstrap-init';

document.addEventListener('DOMContentLoaded', () => {
    const modalElement = document.getElementById('exportModal');
    let exportModal;

    // Initialize Bootstrap modal if it exists
    if (modalElement) {
        // Make sure we're using the proper Bootstrap instance
        exportModal = new bootstrap.Modal(modalElement, {
            backdrop: true,
            keyboard: true,
            focus: true
        });

        // Fix for modal display
        modalElement.classList.add('fade'); // Add fade class for animation

        // Show modal properly when needed
        const showModal = () => {
            modalElement.style.display = 'block';
            modalElement.classList.add('show');
            document.body.classList.add('modal-open');

            // Add backdrop if it doesn't exist
            if (!document.querySelector('.modal-backdrop')) {
                const backdrop = document.createElement('div');
                backdrop.classList.add('modal-backdrop', 'fade', 'show');
                document.body.appendChild(backdrop);
            }
        };

        // Hide modal properly
        const hideModal = () => {
            modalElement.style.display = 'none';
            modalElement.classList.remove('show');
            document.body.classList.remove('modal-open');

            // Remove backdrop
            const backdrop = document.querySelector('.modal-backdrop');
            if (backdrop) {
                backdrop.remove();
            }
        };

        // Handle close button click
        const closeButton = modalElement.querySelector('.close');
        if (closeButton) {
            closeButton.addEventListener('click', () => {
                hideModal();
            });
        }

        // Handle close button in footer
        const closeModalButton = document.getElementById('closeModal');
        if (closeModalButton) {
            closeModalButton.addEventListener('click', () => {
                hideModal();
            });
        }

        // Close modal when clicking outside
        modalElement.addEventListener('click', (e) => {
            if (e.target === modalElement) {
                hideModal();
            }
        });

        // Override Bootstrap's show/hide methods to use our custom functions
        exportModal.show = showModal;
        exportModal.hide = hideModal;
    }

    let currentReportType = '';

    // Handle report button clicks
    document.querySelectorAll('.report-btn').forEach(button => {
        button.addEventListener('click', (e) => {
            currentReportType = e.target.dataset.type;
            document.getElementById('reportType').value = currentReportType;
            document.getElementById('title').innerHTML = `Filter Laporan ${currentReportType.toUpperCase()}`;
            if (exportModal) {
                exportModal.show();
            }
        });
    });

    // Handle PDF export
    document.getElementById('exportPdf')?.addEventListener('click', () => {
        const form = document.getElementById('exportForm');
        form.action = getExportRoute(currentReportType, 'pdf');
        form.submit();
        if (exportModal) {
            exportModal.hide();
        }
    });

    // Handle Excel export
    document.getElementById('exportExcel')?.addEventListener('click', () => {
        const form = document.getElementById('exportForm');
        form.action = getExportRoute(currentReportType, 'excel');
        form.submit();
        if (exportModal) {
            exportModal.hide();
        }
    });

    function getExportRoute(type, format) {
        const routes = {
            in: {
                pdf: '/reports/export-in-pdf',
                excel: '/reports/export-in-excel'
            },
            out: {
                pdf: '/reports/export-out-pdf',
                excel: '/reports/export-out-excel'
            }
        };
        return routes[type][format];
    }
});
