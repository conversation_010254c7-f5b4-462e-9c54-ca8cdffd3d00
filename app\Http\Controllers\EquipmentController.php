<?php

namespace App\Http\Controllers;

use App\Models\Equipment;
use App\Models\EquipmentStock;
use App\Models\Site;
use App\Models\LogAktivitas;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Validator;

class EquipmentController extends Controller
{
    public function index(Request $request)
    {
        $sites = Site::all();
        $statuses = ['Baik', 'Cukup Baik', 'Kurang Baik', 'Rusak'];
        $equipments = Equipment::all();
        $equipmentStocks = EquipmentStock::with('equipment', 'site')->get();

        return view('equipment.index', compact('equipments', 'sites', 'statuses', 'equipmentStocks'));
    }

    public function getsitestockView(Request $request)
    {
        $siteId = session('site_id');
        $site = Site::findOrFail($siteId);
        $statuses = ['Baik', 'Cukup Baik', 'Kurang Baik', 'Rusak'];
        return view('sites.equipmentsite', compact('site', 'statuses'));
    }

    public function getsitestockData(Request $request)
    {
        $siteId = session('site_id');
        $page = $request->input('page', 1);
        $perPage = $request->input('per_page', 5); // Default to 5 items per page
        $startDate = $request->input('start_date');
        $endDate = $request->input('end_date');
        $useLastDate = $request->input('use_last_date', false);

        $baseQuery = EquipmentStock::where('site_id', $siteId);

        // Clone the query for potential last date lookup
        $query = clone $baseQuery;

        // Filter by status
        if ($status = $request->input('status')) {
            $query->where('status', $status);
        }

        // Filter by date range
        if ($startDate && $endDate) {
            $query->whereDate('received_at', '>=', $startDate)
                  ->whereDate('received_at', '<=', $endDate);
        }

        // Check if we have data for the selected date range
        $hasData = $query->exists();

        // If no data found and we should use the last date with data
        if (!$hasData && $useLastDate) {
            // Find the last date that has data
            $lastDateQuery = clone $baseQuery;
            if ($status) {
                $lastDateQuery->where('status', $status);
            }

            $lastDate = $lastDateQuery->latest('received_at')->value('received_at');

            if ($lastDate) {
                // Reset the query to use the last date
                $query = clone $baseQuery;
                if ($status) {
                    $query->where('status', $status);
                }

                $lastDateFormatted = date('Y-m-d', strtotime($lastDate));
                $query->whereDate('received_at', $lastDateFormatted);

                // Update the date range for the response
                $startDate = $lastDateFormatted;
                $endDate = $lastDateFormatted;
            }
        }

        // Get total count for pagination
        $total = $query->count();

        // Get paginated data
        $equipmentStocks = $query->with(['equipment', 'site'])
                               ->orderBy('received_at', 'desc')
                               ->skip(($page - 1) * $perPage)
                               ->take($perPage)
                               ->get();

        return response()->json([
            'data' => $equipmentStocks,
            'current_page' => (int)$page,
            'per_page' => (int)$perPage,
            'last_page' => ceil($total / $perPage),
            'total' => $total,
            'start_date' => $startDate,
            'end_date' => $endDate,
            'has_data' => $hasData,
            'used_last_date' => !$hasData && $useLastDate && $total > 0
        ]);
    }

    public function getEquipments(Request $request)
    {
        $searchTerm = $request->input('search');
        $all = $request->input('all', false); // Check if we want all records
        $perPage = $request->input('per_page', 5); // Default to 5 items per page
        $page = $request->input('page', 1);

        $query = Equipment::query()
            ->when($searchTerm, function ($query) use ($searchTerm) {
                return $query->where('name', 'like', '%' . $searchTerm . '%');
            });

        // If all=true, return all equipment without pagination
        if ($all) {
            $equipments = $query->get();
            return response()->json($equipments);
        }

        $total = $query->count();
        $equipments = $query->skip(($page - 1) * $perPage)
                          ->take($perPage)
                          ->get();

        return response()->json([
            'data' => $equipments,
            'current_page' => (int)$page,
            'per_page' => (int)$perPage,
            'last_page' => ceil($total / $perPage),
            'total' => $total
        ]);
    }


    public function getEquipmentStocks(Request $request)
    {
        $siteId = $request->input('site_id');
        $status = $request->input('status');
        $perPage = $request->input('per_page', 10); // Default to 10 items per page
        $page = $request->input('page', 1);

        $query = EquipmentStock::with('equipment', 'site')
            ->when($siteId, function ($query) use ($siteId) {
                return $query->where('site_id', $siteId);
            })
            ->when($status, function ($query) use ($status) {
                return $query->where('status', $status);
            });

        $total = $query->count();
        $equipmentStocks = $query->skip(($page - 1) * $perPage)
                               ->take($perPage)
                               ->get();

        return response()->json([
            'data' => $equipmentStocks,
            'current_page' => (int)$page,
            'per_page' => (int)$perPage,
            'last_page' => ceil($total / $perPage),
            'total' => $total
        ]);
    }

    public function store(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'name' => 'required|string|max:255',
            'description' => 'nullable|string',
        ]);

        if ($validator->fails()) {
            return response()->json(['errors' => $validator->errors()], 422);
        }
        $data = [
            'name' => strtoupper($request->input('name')),
            'description' => strtoupper($request->input('description')),
        ];
        $equipment = Equipment::create($data);
        if ($equipment) {
            // Log the equipment creation
            LogAktivitas::create([
                'site_id' => session('site_id'),
                'name' => session('name'),
                'action' => 'Menambahkan Equipment',
                'description' => "User " . session('name') . " menambahkan Equipment: " . $data['name'],
                'table' => "Equipment",
                'ip_address' => $request->ip(),
            ]);

            return response()->json(['success' => true, $equipment, 200]);
        } else {
            return response()->json(['success' => false, 400]);
        }
    }

    public function update(Request $request, Equipment $equipment)
    {
        try {
            $validator = Validator::make($request->all(), [
                'name' => 'required|string|max:255',
                'description' => 'nullable|string',
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'success' => false,
                    'errors' => $validator->errors(),
                    'message' => 'Validation failed'
                ], 422);
            }

            // Prepare data with uppercase values
            $data = [
                'name' => strtoupper($request->input('name')),
                'description' => $request->has('description') ? strtoupper($request->input('description')) : null,
            ];

            // Get original values for logging
            $originalName = $equipment->name;

            // Update the equipment
            $equipment->update($data);

            // Log the equipment update
            $changes = [];
            if ($originalName != $equipment->name) {
                $changes[] = "name: {$originalName} → {$equipment->name}";
            }

            LogAktivitas::create([
                'site_id' => session('site_id'),
                'name' => session('name'),
                'action' => 'Mengubah Equipment',
                'description' => "User " . session('name') . " mengubah Equipment: " . $equipment->name .
                                (!empty($changes) ? ' dengan perubahan ' . implode(', ', $changes) : ''),
                'table' => "Equipment",
                'ip_address' => $request->ip(),
            ]);

            // Return a consistent response format
            return response()->json([
                'success' => true,
                'message' => 'Equipment updated successfully!',
                'data' => $equipment
            ], 200);
        } catch (\Exception $e) {
            // Log the error
            \Log::error('Error updating equipment: ' . $e->getMessage());

            // Return error response
            return response()->json([
                'success' => false,
                'message' => 'Failed to update equipment',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    public function destroy(Request $request, Equipment $equipment)
    {
        // Log the equipment deletion before deleting
        LogAktivitas::create([
            'site_id' => session('site_id'),
            'name' => session('name'),
            'action' => 'Menghapus Equipment',
            'description' => "User " . session('name') . " menghapus Equipment: " . $equipment->name,
            'table' => "Equipment",
            'ip_address' => $request->ip(),
        ]);

        $equipment->delete();
        return response()->json(null, 204);
    }

    public function storeStock(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'equipment_id' => 'required|exists:equipment,id',
            'site_id' => 'required|exists:sites,site_id',
            'status' => 'required|in:Baik,Cukup Baik,Kurang Baik,Rusak',
            'received_at' => 'nullable|date',
        ]);

        if ($validator->fails()) {
            return response()->json(['errors' => $validator->errors()], 422);
        }

        $equipmentStock = EquipmentStock::create($request->all());
        if ($equipmentStock) {
            // Get equipment name for logging
            $equipment = Equipment::find($request->equipment_id);
            $site = Site::where('site_id', $request->site_id)->first();

            // Log the equipment stock creation
            LogAktivitas::create([
                'site_id' => session('site_id'),
                'name' => session('name'),
                'action' => 'Menambahkan Equipment Stock',
                'description' => "User " . session('name') . " menambahkan Equipment Stock: " .
                                $equipment->name . ' at ' . ($site ? $site->site_name : 'Unknown Site'),
                'table' => "Equipment Stocks",
                'ip_address' => $request->ip(),
            ]);

            // Add a message to the response
            return response()->json([
                'success' => true,
                'message' => 'Equipment stock created successfully!',
                'equipment_stock' => $equipmentStock,
            ], 201);
        } else {
            return response()->json([
                'success' => false,
                'message' => 'Equipment stock Faild!',
            ], 201);
        }
    }


    public function getEquipment(Equipment $equipment)
    {
        return response()->json($equipment);
    }

    public function destroystock(Request $request, $id)
    {
        $equipmentStock = EquipmentStock::with('equipment', 'site')->findOrFail($id);

        // Log the equipment stock deletion before deleting
        $equipmentName = $equipmentStock->equipment ? $equipmentStock->equipment->name : 'Unknown Equipment';
        $siteName = $equipmentStock->site ? $equipmentStock->site->site_name : 'Unknown Site';

        LogAktivitas::create([
            'site_id' => session('site_id'),
            'name' => session('name'),
            'action' => 'Menghapus Equipment Stock',
            'description' => "User " . session('name') . " menghapus Equipment Stock: " . $equipmentName . ' at ' . $siteName,
            'table' => "Equipment Stocks",
            'ip_address' => $request->ip(),
        ]);

        $equipmentStock->delete();

        return response()->json(['message' => 'Equipment stock deleted successfully.']);
    }
    public function getEquipmentStock($id)
    {
        $equipmentStock = EquipmentStock::with('equipment', 'site')->findOrFail($id);
        return response()->json($equipmentStock);
    }

    public function updateEquipmentStock(Request $request, $id)
    {
        $validator = Validator::make($request->all(), [
            'equipment_id' => 'required|exists:equipment,id',
            'site_id' => 'required|exists:sites,site_id',
            'quantity' => 'required|integer',
            'status' => 'required|in:Baik,Cukup Baik,Kurang Baik,Rusak',
            'received_at' => 'nullable|date',
        ]);
        if ($validator->fails()) {
            return response()->json(['success' => false, 'errors' => $validator->errors()], 422);
        }
        $equipmentStock = EquipmentStock::with('equipment', 'site')->findOrFail($id);

        // Get original values for logging
        $originalStatus = $equipmentStock->status;

        $equipmentStock->update($request->all());

        // Log the equipment stock update
        $equipmentName = $equipmentStock->equipment ? $equipmentStock->equipment->name : 'Unknown Equipment';
        $siteName = $equipmentStock->site ? $equipmentStock->site->site_name : 'Unknown Site';

        $changes = [];
        if ($originalStatus != $equipmentStock->status) {
            $changes[] = "status: {$originalStatus} → {$equipmentStock->status}";
        }

        LogAktivitas::create([
            'site_id' => session('site_id'),
            'name' => session('name'),
            'action' => 'Mengubah Equipment Stock',
            'description' => "User " . session('name') . " mengubah Equipment Stock: " . $equipmentName . ' at ' . $siteName .
                            (!empty($changes) ? ' dengan perubahan ' . implode(', ', $changes) : ''),
            'table' => "Equipment Stocks",
            'ip_address' => $request->ip(),
        ]);

        return response()->json(['success' => true, 'message' => 'Equipment stock updated successfully!', 'equipment_stock' => $equipmentStock]);
    }

    public function updateEquipmentStocksite(Request $request, $id)
    {
        $validator = Validator::make($request->all(), [
            'status' => 'required|in:Baik,Cukup Baik,Kurang Baik,Rusak',
            'received_at' => 'nullable|date',
        ]);
        if ($validator->fails()) {
            return response()->json(['errors' => $validator->errors()], 422);
        }
        $equipmentStock = EquipmentStock::findOrFail($id);
        $equipmentStock->update($request->only('status', 'received_at'));
        return response()->json(['message' => 'Equipment stock updated successfully!', 'equipment_stock' => $equipmentStock]);
    }
}
