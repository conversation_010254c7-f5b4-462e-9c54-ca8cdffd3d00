document.addEventListener('DOMContentLoaded', function () {
    const searchInput = document.getElementById('searchInput');
    const table = document.getElementById('myTable');
    const rows = table.getElementsByTagName('tr');

    searchInput.addEventListener('input', function () {
        const searchText = this.value.toLowerCase();

        for (let i = 1; i < rows.length; i++) { // <PERSON><PERSON> dari 1 untuk melewati header
            const row = rows[i];
            const cells = row.getElementsByTagName('td');
            let shouldDisplay = false;

            for (let j = 0; j < cells.length; j++) {
                const cellText = cells[j].textContent.toLowerCase();
                if (cellText.includes(searchText)) {
                    shouldDisplay = true;
                    break;
                }
            }

            if (shouldDisplay) {
                row.style.display = '';
            } else {
                row.style.display = 'none';
            }
        }
    });
});