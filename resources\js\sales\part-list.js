// Part List Management with Inline Editing
document.addEventListener('DOMContentLoaded', function() {
    let currentPage = 1;
    let currentSearch = '';
    let partTypes = [];
    let editingRow = null;

    // Initialize
    init();

    function init() {
        loadPartTypes();
        loadPartsData();
        setupEventListeners();
        setupCurrencyFormatting();
    }

    function setupEventListeners() {
        // Search functionality
        const searchInput = document.getElementById('search-input');
        if (searchInput) {
            let searchTimeout;
            searchInput.addEventListener('input', function() {
                clearTimeout(searchTimeout);
                searchTimeout = setTimeout(() => {
                    currentSearch = this.value;
                    currentPage = 1;
                    loadPartsData();
                }, 500);
            });
        }

        // Add new part button
        const addNewPartBtn = document.getElementById('add-new-part-btn');
        if (addNewPartBtn) {
            addNewPartBtn.addEventListener('click', function() {
                openAddPartModal();
            });
        }

        // Save part button
        const savePartBtn = document.getElementById('save-part-btn');
        if (savePartBtn) {
            savePartBtn.addEventListener('click', function() {
                saveNewPart();
            });
        }

        // Modal close buttons
        document.querySelectorAll('.close-modal-btn, [data-bs-dismiss="modal"]').forEach(button => {
            button.addEventListener('click', function() {
                closeModals();
            });
        });
    }

    function loadPartTypes() {
        fetch('/sales/part-list/part-types')
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    partTypes = data.data;
                }
            })
            .catch(error => {
                console.error('Error loading part types:', error);
            });
    }

    function loadPartsData() {
        showLoading(true);

        const params = new URLSearchParams({
            page: currentPage,
            search: currentSearch
        });

        fetch(`/sales/part-list/data?${params}`)
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    renderPartsTable(data.data);
                    renderPagination(data.pagination);
                    updateShowingText(data.pagination);
                } else {
                    showError('Gagal memuat data part');
                }
            })
            .catch(error => {
                console.error('Error loading parts:', error);
                showError('Terjadi kesalahan saat memuat data');
            })
            .finally(() => {
                showLoading(false);
            });
    }

    function renderPartsTable(parts) {
        const tbody = document.getElementById('parts-table-body');
        if (!tbody) return;

        if (parts.length === 0) {
            tbody.innerHTML = `
                <tr>
                    <td colspan="7" class="text-center text-muted">
                        <i class="mdi mdi-package-variant-closed"></i><br>
                        Tidak ada data part ditemukan
                    </td>
                </tr>
            `;
            return;
        }

        tbody.innerHTML = parts.map(part => `
            <tr data-part-code="${part.part_code}" data-original-part-code="${part.part_code}">
                <td class="editable-cell" data-field="part_code">${part.part_code}</td>
                <td class="editable-cell" data-field="part_name">${part.part_name || ''}</td>
                <td class="editable-cell" data-field="part_type">${part.part_type || ''}</td>
                <td class="editable-cell currency" data-field="purchase_price">${formatCurrency(part.purchase_price)}</td>
                <td class="editable-cell currency" data-field="price">${formatCurrency(part.price)}</td>
                <td class="editable-cell text-center" data-field="eum">${part.eum || ''}</td>
                <td class="action-buttons">
                    <button class="btn-edit" onclick="editRow('${part.part_code}')">
                        <i class="mdi mdi-pencil"></i> Edit
                    </button>
                </td>
            </tr>
        `).join('');
    }

    // Currency formatting function
    function formatCurrency(value) {
        if (!value || value === 0) return '-';
        return 'Rp ' + parseFloat(value).toLocaleString('id-ID');
    }

    // Currency input formatting
    function setupCurrencyFormatting() {
        document.addEventListener('input', function(e) {
            if (e.target.classList.contains('currency-input')) {
                formatCurrencyInput(e.target);
            }
        });

        // Handle dynamically created currency inputs
        document.addEventListener('focus', function(e) {
            if (e.target.classList.contains('currency-input')) {
                // Remove formatting for editing
                let value = e.target.value.replace(/[^\d]/g, '');
                if (value) {
                    e.target.value = value;
                }
            }
        });

        document.addEventListener('blur', function(e) {
            if (e.target.classList.contains('currency-input')) {
                formatCurrencyInput(e.target);
            }
        });
    }

    function formatCurrencyInput(input) {
        let value = input.value.replace(/[^\d]/g, '');
        if (value) {
            input.value = 'Rp ' + parseInt(value).toLocaleString('id-ID');
        }
    }

    function parseCurrencyValue(value) {
        if (!value) return null;
        return parseFloat(value.replace(/[^\d]/g, '')) || null;
    }

    function renderPagination(pagination) {
        const paginationContainer = document.getElementById('pagination');
        if (!paginationContainer) return;

        let paginationHTML = '';

        // Previous button
        if (pagination.current_page > 1) {
            paginationHTML += `
                <li class="page-item">
                    <a class="page-link" href="#" onclick="changePage(${pagination.current_page - 1})">
                        <i class="mdi mdi-chevron-left"></i>
                    </a>
                </li>
            `;
        }

        // Page numbers
        const startPage = Math.max(1, pagination.current_page - 2);
        const endPage = Math.min(pagination.last_page, pagination.current_page + 2);

        for (let i = startPage; i <= endPage; i++) {
            paginationHTML += `
                <li class="page-item ${i === pagination.current_page ? 'active' : ''}">
                    <a class="page-link" href="#" onclick="changePage(${i})">${i}</a>
                </li>
            `;
        }

        // Next button
        if (pagination.current_page < pagination.last_page) {
            paginationHTML += `
                <li class="page-item">
                    <a class="page-link" href="#" onclick="changePage(${pagination.current_page + 1})">
                        <i class="mdi mdi-chevron-right"></i>
                    </a>
                </li>
            `;
        }

        paginationContainer.innerHTML = paginationHTML;
    }

    function updateShowingText(pagination) {
        const showingText = document.getElementById('showing-text');
        if (showingText) {
            showingText.textContent = `Menampilkan ${pagination.from || 0} - ${pagination.to || 0} dari ${pagination.total} part`;
        }
    }

    // Global functions
    window.changePage = function(page) {
        currentPage = page;
        loadPartsData();
    };

    // Modal functions
    function openAddPartModal() {
        // Reset form
        document.getElementById('add-part-form').reset();
        document.getElementById('add_eum').value = 'EA';

        // Show modal
        const modal = document.getElementById('add-part-modal');
        modal.classList.add('show');
        modal.style.display = 'block';
        document.body.classList.add('modal-open');

        // Add backdrop
        if (!document.querySelector('.modal-backdrop')) {
            const backdrop = document.createElement('div');
            backdrop.className = 'modal-backdrop fade show';
            document.body.appendChild(backdrop);
        }
    }

    // Global function for inline editing
    window.editRow = function(partCode) {
        // Cancel any existing edit
        if (editingRow) {
            cancelEdit();
        }

        const row = document.querySelector(`tr[data-part-code="${partCode}"]`);
        if (!row) return;

        editingRow = row;
        const cells = row.querySelectorAll('.editable-cell');

        cells.forEach(cell => {
            const field = cell.dataset.field;
            const currentValue = cell.textContent.trim();
            let input;

            if (field === 'part_type') {
                // Create select dropdown for part type
                input = document.createElement('select');
                input.className = 'editable-field form-control';
                input.innerHTML = `
                    <option value="">Pilih Tipe</option>
                    <option value="AC">AC</option>
                    <option value="TYRE">TYRE</option>
                    <option value="FABRIKASI">FABRIKASI</option>
                    <option value="PERLENGKAPAN AC">PERLENGKAPAN AC</option>
                    <option value="PERSEDIAAN LAINNYA">PERSEDIAAN LAINNYA</option>
                `;
                input.value = currentValue;
            } else if (field === 'price' || field === 'purchase_price') {
                // Create currency input
                input = document.createElement('input');
                input.type = 'text';
                input.className = 'editable-field form-control currency-input';
                input.value = currentValue === '-' ? '' : currentValue;
            } else {
                // Create text input
                input = document.createElement('input');
                input.type = 'text';
                input.className = 'editable-field form-control';
                input.value = currentValue;
            }

            input.dataset.field = field;
            input.dataset.originalValue = currentValue;

            cell.innerHTML = '';
            cell.appendChild(input);
        });

        // Change action buttons
        const actionCell = row.querySelector('.action-buttons');
        actionCell.innerHTML = `
            <button class="btn-save" onclick="saveRow('${partCode}')">
                <i class="mdi mdi-check"></i> Simpan
            </button>
            <button class="btn-cancel" onclick="cancelEdit()">
                <i class="mdi mdi-close"></i> Batal
            </button>
        `;

        // Focus on first input
        const firstInput = cells[0].querySelector('input, select');
        if (firstInput) firstInput.focus();
    };

    function saveNewPart() {
        const form = document.getElementById('add-part-form');
        const formData = new FormData(form);

        // Convert currency values
        const price = parseCurrencyValue(formData.get('price'));
        const purchasePrice = parseCurrencyValue(formData.get('purchase_price'));

        const data = {
            part_code: formData.get('part_code'),
            part_name: formData.get('part_name'),
            part_type: formData.get('part_type'),
            price: price,
            purchase_price: purchasePrice,
            eum: formData.get('eum') || 'EA'
        };

        showLoading(true);

        fetch('/sales/part-list', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]')?.getAttribute('content') || ''
            },
            body: JSON.stringify(data)
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                showSuccess(data.message);
                closeModals();
                loadPartsData(); // Reload to show new data
            } else {
                if (data.errors) {
                    const errorMessages = Object.values(data.errors).flat().join('<br>');
                    showError(errorMessages);
                } else {
                    showError(data.message || 'Gagal menambahkan part');
                }
            }
        })
        .catch(error => {
            console.error('Error saving part:', error);
            showError('Terjadi kesalahan saat menambahkan part');
        })
        .finally(() => {
            showLoading(false);
        });
    }

    // Global function to save row
    window.saveRow = function(partCode) {
        if (!editingRow) return;

        const inputs = editingRow.querySelectorAll('.editable-field');
        const originalPartCode = editingRow.dataset.originalPartCode;
        const data = {};

        // Collect data from inputs
        inputs.forEach(input => {
            const field = input.dataset.field;
            let value = input.value.trim();

            if (field === 'price' || field === 'purchase_price') {
                value = parseCurrencyValue(value);
            }

            data[field] = value;
        });

        // Validate required fields
        if (!data.part_code || !data.part_name || !data.part_type) {
            showError('Kode part, nama part, dan tipe part harus diisi');
            return;
        }

        showLoading(true);

        fetch(`/sales/part-list/${originalPartCode}`, {
            method: 'PUT',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]')?.getAttribute('content') || ''
            },
            body: JSON.stringify(data)
        })
        .then(response => response.json())
        .then(result => {
            if (result.success) {
                showSuccess(result.message);
                editingRow = null;
                loadPartsData(); // Reload to show updated data
            } else {
                if (result.errors) {
                    const errorMessages = Object.values(result.errors).flat().join('<br>');
                    showError(errorMessages);
                } else {
                    showError(result.message || 'Gagal memperbarui part');
                }
            }
        })
        .catch(error => {
            console.error('Error updating part:', error);
            showError('Terjadi kesalahan saat memperbarui part');
        })
        .finally(() => {
            showLoading(false);
        });
    };

    // Global function to cancel edit
    window.cancelEdit = function() {
        if (!editingRow) return;

        const cells = editingRow.querySelectorAll('.editable-cell');
        cells.forEach(cell => {
            const input = cell.querySelector('.editable-field');
            if (input) {
                const originalValue = input.dataset.originalValue;
                cell.innerHTML = originalValue;
            }
        });

        // Restore action buttons
        const partCode = editingRow.dataset.originalPartCode;
        const actionCell = editingRow.querySelector('.action-buttons');
        actionCell.innerHTML = `
            <button class="btn-edit" onclick="editRow('${partCode}')">
                <i class="mdi mdi-pencil"></i> Edit
            </button>
        `;

        editingRow = null;
    };

    function closeModals() {
        // Close all modals
        document.querySelectorAll('.modal').forEach(modal => {
            modal.classList.remove('show');
            modal.style.display = 'none';
        });

        document.body.classList.remove('modal-open');

        // Remove backdrop
        const backdrop = document.querySelector('.modal-backdrop');
        if (backdrop) {
            backdrop.remove();
        }
    }

    function showLoading(show) {
        const overlay = document.getElementById('loading-overlay');
        if (overlay) {
            overlay.classList.toggle('d-none', !show);
        }
    }

    function showSuccess(message) {
        // Using SweetAlert if available, otherwise alert
        if (typeof Swal !== 'undefined') {
            Swal.fire({
                icon: 'success',
                title: 'Berhasil!',
                text: message,
                timer: 3000,
                showConfirmButton: false
            });
        } else {
            alert(message);
        }
    }

    function showError(message) {
        // Using SweetAlert if available, otherwise alert
        if (typeof Swal !== 'undefined') {
            Swal.fire({
                icon: 'error',
                title: 'Error!',
                html: message,
                confirmButtonText: 'OK'
            });
        } else {
            alert(message);
        }
    }
});
