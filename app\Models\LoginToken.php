<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Str;

class LoginToken extends Model
{
    use HasFactory;

    protected $fillable = [
        'token',
        'employee_id',
        'expires_at',
        'is_used',
    ];

    protected $casts = [
        'expires_at' => 'datetime',
        'is_used' => 'boolean',
    ];

    /**
     * Generate a new token for a user
     *
     * @param User $user
     * @param int $expiresInHours
     * @return LoginToken
     */
    public static function generateFor(User $user, int $expiresInHours = 24): self
    {
        // Invalidate any existing tokens for this user
        self::where('employee_id', $user->employee_id)
            ->update(['is_used' => true]);

        // Create a new token with a very far future expiration date (100 years)
        return self::create([
            'token' => Str::random(32),
            'employee_id' => $user->employee_id,
            'expires_at' => now()->addYears(100), // Set expiration to 100 years in the future
            'is_used' => false,
        ]);
    }

    /**
     * Check if the token is valid
     *
     * @return bool
     */
    public function isValid(): bool
    {
        // Only check if the token has been used, ignore expiration date
        return !$this->is_used;
    }

    /**
     * Mark the token as used
     *
     * @return void
     */
    public function markAsUsed(): void
    {
        $this->is_used = true;
        $this->save();
    }

    /**
     * Get the user associated with this token
     */
    public function user()
    {
        return $this->belongsTo(User::class, 'employee_id', 'employee_id');
    }
}
