@extends('sites.content')
@section('title', 'History In Part')
<style>
    /* Pagination styling */
    .pagination {
        display: flex;
        justify-content: center;
        list-style: none;
        padding: 0;
        margin-top: 15px;
    }

    .pagination .page-item {
        margin: 0 2px;
    }

    .pagination .page-item .page-link {
        color: #333;
        background-color: #fff;
        border: 1px solid #ddd;
        padding: 6px 12px;
        text-decoration: none;
        border-radius: 4px;
        cursor: pointer;
    }

    .pagination .page-item.active .page-link {
        color: #fff;
        background-color: #28a745;
        border-color: #28a745;
    }

    .pagination .page-item .page-link:hover {
        background-color: #f8f9fa;
    }

    /* Autocomplete styling */
    #suggestions,
    #combinedNameSuggestions {
        position: absolute;
        width: 100%;
        max-height: 250px;
        overflow-y: auto;
        background: white;
        border: 1px solid #ddd;
        border-radius: 4px;
        box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
        z-index: 1000;
        margin-top: 2px;
    }

    .suggestion-item {
        padding: 8px 12px;
        border-bottom: 1px solid #f0f0f0;
        cursor: pointer;
        display: flex;
        flex-direction: column;
    }

    .suggestion-item:last-child {
        border-bottom: none;
    }

    .suggestion-item-hover,
    .suggestion-item:hover {
        background-color: #f8f9fa;
    }

    .suggestion-main-text {
        font-weight: bold;
    }

    .suggestion-type {
        font-size: 0.8em;
        color: #6c757d;
    }

    .suggestion-stock {
        font-size: 0.8em;
        color: #28a745;
    }

    .suggestion-loading,
    .suggestion-error,
    .suggestion-no-results {
        padding: 10px;
        text-align: center;
    }

    .suggestion-loading {
        color: #6c757d;
    }

    .suggestion-error {
        color: #dc3545;
    }

    .suggestion-no-results {
        color: #6c757d;
        font-style: italic;
    }

    /* Form styling */
    #form-section {
        background-color: #f8f9fa;
        border-radius: 8px;
        padding: 20px;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    }

    #form-section h3 {
        margin-bottom: 20px;
        color: #343a40;
        border-bottom: 2px solid #28a745;
        padding-bottom: 10px;
    }

    .form-group {
        margin-bottom: 15px;
        position: relative;
    }

    #queue table {
        max-height: 300px;
        overflow-y: auto;
        margin-bottom: 15px;
    }
</style>
@section('contentsite')
<div class="row bgwhite page-title-box shadow-kit mb-1">
    <div class="col d-flex align-items-center" style="max-width: fit-content;">
        <button class="button-menu-mobile text-blue-500 btn-sm">
            <i style="font-size: 30px;" class="mdi mdi-menu"></i>
        </button>
        <div class="text">
            <p class="font-bold m-0">{{ session('name') }}</p>
        </div>
    </div>
    <div class="col">
        <ul class="list-unstyled topnav-menu float-right mb-0">
            <li class="dropdown notification-list">
                <a class="nav-link dropdown-toggle" href="#" data-bs-toggle="dropdown" aria-expanded="false">
                    <i class="mdi mdi-bell-outline noti-icon"></i>
                    <span class="badge badge-danger badge-pill float-right badgenotif"></span>
                </a>
                <div class="dropdown-menu dropdown-menu-end dropdown-lg">
                    <div class="dropdown-item noti-title bg-primary">
                        <h5 class="m-0 text-white d-flex justify-content-between align-items-center">
                            Notifikasi
                            <a href="javascript:void(0);" class="text-white" id="clear-all">
                                <small></small>
                            </a>
                        </h5>
                    </div>
                    <div class="noti-scroll" id="notification-list">
                        <!-- Notifications will be dynamically inserted here -->
                    </div>
                </div>
            </li>
        </ul>
    </div>
</div>
<div class="m-1">
    <div class="row">
        <div class="col bgwhite">
            <div class="p-4">
                <div class="row">
                    <div class="col bgwhite">
                        <div class="d-flex justify-content-between align-items-center mb-3">
                            <div>
                                <h4 class="mb-0 h4 font-bold">Daftar Part Merge</h4>
                                <p class="text-muted mb-0">Filter: <span id="current-filter-type">Semua Jenis Part</span></p>
                            </div>
                            <div class="d-flex align-items-center">
                                <select class="form-control" style="width: auto;" id="partTypeFilter">
                                    <option value="All">Semua Jenis Part</option>
                                    <option value="AC">AC</option>
                                    <option value="TYRE">TYRE</option>
                                    <option value="FABRIKASI">FABRIKASI</option>
                                    <option value="PERLENGKAPAN AC">PERLENGKAPAN AC</option>
                                    <option value="PERSEDIAAN LAINNYA">PERSEDIAAN LAINNYA</option>
                                </select>
                            </div>
                        </div>
                        <table id="datatable" class="table table-bordered dt-responsive nowrap"
                            style="border-collapse: collapse; border-spacing: 0; width: 100%;">
                            <thead class="table-primary">
                                <tr>
                                    <th>No</th>
                                    <th>Nama Part Gabungan</th>
                                    <th>Nama Sub Part</th>
                                    <th>Jumlah Sub Part</th>
                                    <th>Tanggal</th>
                                    <th>Catatan</th>
                                </tr>
                            </thead>
                            <tbody>
                            </tbody>
                        </table>
                        <div id="part-merge-pagination" class="mt-3"></div>
                    </div>
                    <div class="col-md bgwhite" style="max-width: fit-content; min-width: 500px;">
                        <div id="form-section" class="bgwhite rounded-2">
                            <h3 class="h4 font-bold">Form Part Merge</h3>
                            <div class="mb-3">
                                <div class="pb-0">
                                    <h5 class="card-title font-bold">1. Pilih Part yang Akan diMerge</h5>
                                    <div class="form-group">
                                        <label for="itemInput">Cari Part</label>
                                        <div class="input-group mb-2">
                                            <span class="input-group-text"><i class="mdi mdi-magnify"></i></span>
                                            <input class="form-control" type="text" id="itemInput"
                                                placeholder="Ketik nama atau kode part...">
                                        </div>
                                        <div class="hidden" id="suggestions"></div>
                                    </div>
                                    <div class="form-group">
                                        <label for="jumlah">Jumlah</label>
                                        <div class="input-group">
                                            <input class="form-control" type="number" id="jumlah" placeholder="Masukkan jumlah" min="1">
                                            <button class="btn btn-primary" onclick="addToQueue()">
                                                <i class="mdi mdi-plus"></i> Tambahkan
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div class="pt-0 mb-3">
                                <div class="pb-0">
                                    <h5 class="card-title font-bold">2. Daftar Part yang Akan DiMerge</h5>
                                </div>
                                <div class="card-body p-0" id="queue">
                                    <table class="table table-hover mb-0">
                                        <thead class="table-light">
                                            <tr>
                                                <th>Kode Part</th>
                                                <th>Nama Part</th>
                                                <th>Jumlah</th>
                                                <th>Aksi</th>
                                            </tr>
                                        </thead>
                                        <tbody id="queue-table-body">
                                            <tr>
                                                <td colspan="4" class="text-center text-muted">Belum ada part yang ditambahkan</td>
                                            </tr>
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                            <div class="mb-3">
                                <div class="pb-0">
                                    <h5 class="card-title font-bold">3. Tentukan Hasil Merge</h5>
                                </div>
                                <div class="">
                                    <div class="form-group">
                                        <label for="part_name">Nama Barang Merge <span class="text-danger">*</span></label>
                                        <div class="input-group mb-2">
                                            <span class="input-group-text"><i class="mdi mdi-package-variant"></i></span>
                                            <input class="form-control" type="text" id="part_name"
                                                placeholder="Cari atau masukkan nama barang gabungan...">
                                        </div>
                                        <input type="hidden" id="new_code_part">
                                        <div id="combinedNameSuggestions" class="hidden"></div>
                                        <small class="text-muted">Pilih part yang sudah ada atau masukkan nama baru</small>
                                    </div>
                                    <div class="form-group">
                                        <label for="notes">Catatan</label>
                                        <textarea class="form-control" id="notes" rows="3"
                                            placeholder="Tambahkan catatan jika diperlukan..."></textarea>
                                    </div>
                                </div>
                            </div>
                            <input type="hidden" id="user_role" value="{{ session('role') }}">
                            <input type="hidden" id="site_id" value="{{ session('site_id') }}">
                            <input type="hidden" id="employee_id" value="{{ session('employee_id') }}">
                            <button class="btn btn-success btn-lg w-100" onclick="submitCombinedItem()">
                                <i class="mdi mdi-content-save-outline mr-1"></i> Simpan Data
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection
@section('resourcesite')
@vite('resources/js/part_merge.js')
@endsection