<?php

require_once 'vendor/autoload.php';

use Illuminate\Http\Request;
use App\Http\Controllers\SuperadminController;
use Carbon\Carbon;

// Set up Laravel environment
$app = require_once 'bootstrap/app.php';
$app->make('Illuminate\Contracts\Console\Kernel')->bootstrap();

echo "Testing Division Modal Debug\n";
echo "============================\n\n";

// Test the API endpoint with AC division
echo "Testing API endpoint with AC division...\n";

$controller = new SuperadminController();
$request = new Request([
    'division' => 'AC',
    'start_date' => Carbon::now()->startOfMonth()->format('Y-m-d'),
    'end_date' => Carbon::now()->format('Y-m-d')
]);

try {
    $response = $controller->getDivisionPartsDetail($request);
    $data = json_decode($response->getContent(), true);
    
    echo "✅ API Response Status: " . $response->getStatusCode() . "\n";
    echo "✅ Success: " . ($data['success'] ? 'true' : 'false') . "\n";
    echo "✅ Data count: " . (isset($data['data']) ? count($data['data']) : 0) . "\n";
    
    if (isset($data['summary'])) {
        echo "✅ Summary data available\n";
        echo "   - Total Items: " . $data['summary']['total_items'] . "\n";
        echo "   - Total Value with PPN: Rp " . number_format($data['summary']['total_value_with_ppn'], 0, ',', '.') . "\n";
    }
    
    echo "\n";
    echo "🎯 The API is working correctly!\n";
    echo "📋 Now check the browser console for JavaScript debugging logs when you click on the AC division card.\n";
    echo "🔍 Look for logs starting with emojis like 🚀, 📋, 🎯, 🌐, 📊, etc.\n";
    
} catch (Exception $e) {
    echo "❌ Error: " . $e->getMessage() . "\n";
}

echo "\n";
echo "Instructions for debugging:\n";
echo "1. Open the superadmin parts page in your browser\n";
echo "2. Open browser developer tools (F12)\n";
echo "3. Go to the Console tab\n";
echo "4. Click on any division card (AC, TYRE, or FABRIKASI)\n";
echo "5. Watch the console for detailed logging messages\n";
echo "6. The logs will show you exactly where the issue is occurring\n";
