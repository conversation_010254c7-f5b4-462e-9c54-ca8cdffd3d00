<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Update the enum to include 'kasir'
        DB::statement("ALTER TABLE users MODIFY COLUMN role ENUM('adminho', 'adminsite', 'superadmin', 'sales', 'karyawan', 'kasir') DEFAULT 'karyawan'");
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // Remove 'kasir' from enum
        DB::statement("ALTER TABLE users MODIFY COLUMN role ENUM('adminho', 'adminsite', 'superadmin', 'sales', 'karyawan') DEFAULT 'karyawan'");
    }
};
