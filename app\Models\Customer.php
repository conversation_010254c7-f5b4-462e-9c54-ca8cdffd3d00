<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Customer extends Model
{
    use HasFactory;

    protected $table = 'customers';
    protected $primaryKey = 'customer_id';

    protected $fillable = [
        'customer_name',
        'address',
        'contact_person',
        'phone_number',
        'email',
    ];

   // Relationship with WarehouseOutStock (if needed, if customer is a destination)
   public function warehouseOutStocks()
   {
       return $this->hasMany(WarehouseOutStock::class, 'destination_id')->where('destination_type', 'customer');
   }
}