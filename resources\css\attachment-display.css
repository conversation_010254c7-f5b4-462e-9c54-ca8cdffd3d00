/* Attachment Display Styles */
.attachment-preview {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    min-height: 300px;
    padding: 20px;
    background-color: #f8f9fa;
    border-radius: 8px;
    border: 1px solid #e9ecef;
}

.attachment-placeholder {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    min-height: 300px;
    padding: 20px;
    background-color: #f8f9fa;
    border-radius: 8px;
    border: 1px dashed #dee2e6;
}

.attachment-preview img {
    max-width: 100%;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.attachment-preview iframe {
    width: 100%;
    height: 500px;
    border: 1px solid #dee2e6;
    border-radius: 4px;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.embed-responsive {
    position: relative;
    display: block;
    width: 100%;
    height: 100%;
    overflow: hidden;
}

.embed-responsive-16by9 {
    padding-bottom: 56.25%;
}

.embed-responsive-item {
    position: absolute;
    top: 0;
    bottom: 0;
    left: 0;
    width: 100%;
    height: 100%;
    border: 0;
}

#attachment-actions {
    margin-top: 15px;
}

#attachment-actions .btn {
    padding: 8px 20px;
    border-radius: 4px;
}

/* Make the attachment section have a fixed height to match the table */
#attachment-container {
    height: 100%;
    display: flex;
    flex-direction: column;
}

#attachment-content {
    flex-grow: 1;
    display: flex;
    flex-direction: column;
    justify-content: center;
}
