<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('invoices', function (Blueprint $table) {
            // Add status column if it doesn't exist
            if (!Schema::hasColumn('invoices', 'status')) {
                $table->enum('status', ['Draft', 'Proses', 'Selesai'])->default('Draft')->after('payment_status');
            }
            
            // Add document_path column for multiple documents if it doesn't exist
            if (!Schema::hasColumn('invoices', 'document_path')) {
                $table->json('document_path')->nullable()->after('signed_document_path');
            }
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('invoices', function (Blueprint $table) {
            if (Schema::hasColumn('invoices', 'status')) {
                $table->dropColumn('status');
            }
            if (Schema::hasColumn('invoices', 'document_path')) {
                $table->dropColumn('document_path');
            }
        });
    }
};
