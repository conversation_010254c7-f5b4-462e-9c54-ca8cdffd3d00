<!DOCTYPE html>
<html>

<head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
    <title>Berita Acara Pemakaian Part</title>
    <style>
        body {
            font-family: 'Times New Roman', Times, serif;
            font-size: 11px;
            line-height: 1.4;
            margin: 20px;
        }

        /* Header styles */
        .header-container {
            width: 100%;
            margin-bottom: 15px;
        }

        .logo-left {
            float: left;
            width: 50%;
        }

        .title-right {
            float: right;
            width: 40%;
            text-align: center;
        }

        .header-title {

            background-color: rgb(183, 176, 248);
            color: black;
            padding: 8px;
            font-size: 14px;
            font-weight: bold;
            text-align: center;
            margin-bottom: 5px;
        }

        .header-subtitle {
            background-color: rgb(183, 176, 248);
            color: black;
            padding: 4px;
            font-size: 12px;
            text-align: center;
        }

        /* Info tables */
        .info-container {
            width: 100%;
            margin-bottom: 15px;
        }

        .info-left {
            float: left;
            width: 50%;
        }

        .info-right {
            float: right;
            width: 50%;
        }

        .info-table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 15px;
        }

        .info-table td {
            padding: 1px;
            padding-left: 4px;
        }

        .info-table .label {
            width: 25%;
            font-weight: normal;
        }

        /* Main table styles */
        .main-table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 20px;
        }

        .main-table th,
        .main-table td {
            border: 1px solid #000;
            padding: 1px;
            text-align: center;
            font-size: 10px;
        }

        .main-table th {
            background-color: rgb(169, 150, 255);
            color: black;
            font-weight: bold;
        }

        .main-table tr:nth-child(even) {
            background-color: #f2f2f2;
        }

        /* Total section */
        .total-section {
            width: 30%;
            float: right;
            margin-bottom: 20px;
        }

        .total-table {
            width: 100%;
            border-collapse: collapse;
        }

        .total-table td {
            border: 1px solid #000;
            padding: 1px;
            text-align: left;
        }

        .total-table .total-label {
            font-weight: bold;
        }

        /* Signature section */
        .signature-table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 10px;
        }

        .signature-table td {
            width: 50%;
            text-align: center;
            vertical-align: bottom;
            font-weight: bold;
            font-size: 10px;
        }

        .signature-stamp {
            width: 100px;
            height: 100px;
            margin: 0 auto;
            position: relative;
        }

        .clearfix::after {
            content: "";
            clear: both;
            display: table;
        }

        /* Adjust for landscape orientation */
        @page {
            size: landscape;
        }
    </style>
</head>

<body>
    @php
    \Carbon\Carbon::setLocale('id');
    @endphp

    <!-- Header with logo and title -->
    <div class="header-container clearfix">
        <div class="logo-left">
            <img src="{{ public_path('assets/images/logo-small.png') }}" alt="PWB LOGO" style="width: 80px;">
            <p><strong>PT. PUTERA WIBOWO BORNEO</strong></p>
        </div>
        <div class="title-right">
            <div class="header-title">BUKTI SERAH TERIMA</div>
            <div class="header-subtitle">MATERIAL REQUEST / USAGE SLIP</div>
        </div>
    </div>

    <!-- Information Tables -->
    <div class="info-container clearfix">
        <div class="info-left">
            <table class="info-table">
                <tr>
                    <td class="label">SITE</td>
                    <td>: MURUNG RAYA</td>
                </tr>
                <tr>
                    <td class="label">WAREHOUSE</td>
                    <td>: PT INDO MURO KENCANA</td>
                </tr>
                <tr>
                    <td class="label">SUPPLIER</td>
                    <td>: PT PUTERA WIBOWO BORNEO</td>
                </tr>
                <tr>
                    <td class="label">UNIT NO. / AREA</td>
                    <td>: {{ $transactions[0]->unit->unit_code ?? 'ADT 101' }}</td>
                </tr>
            </table>
        </div>
        <div class="info-right">
            <table class="info-table">
                <tr>
                    <td class="label">NO.</td>
                    <td>: {{ $transactions[0]->noSPB ?? 'MR/PWB/0127' }}</td>
                </tr>
                <tr>
                    <td class="label">DATE</td>
                    <td>: {{ \Carbon\Carbon::parse($transactions[0]->mr_date ?? now())->translatedFormat('d F Y') }}</td>
                </tr>
                <tr>
                    <td class="label">NO PROJECT</td>
                    <td>: {{ $transactions[0]->wo_number ?? '' }}</td>
                </tr>
                <tr>
                    <td class="label">ISSUE NO</td>
                    <td>: {{ $transactions[0]->issue_nomor ?? '' }}</td>
                </tr>
            </table>
        </div>
    </div>

    <!-- Main Content Table -->
    <table class="main-table">
        <thead>
            <tr>
                <th style="width:5%;">NO.</th>
                <th style="width:15%;">ITEM CODE</th>
                <th style="width:15%;">MATERIAL NUMBER</th>
                <th style="width:25%;">DESCRIPTION</th>
                <th style="width:5%;">QTY</th>
                <th style="width:5%;">UOM</th>
                <th style="width:10%;">UNIT PRICE</th>
                <th style="width:12%;">UNIT PRICE (DISC 7.5%)</th>
                <th style="width:10%;">TOTAL PRICE</th>
            </tr>
        </thead>
        <tbody>
            @if(count($transactions) > 0)
            @php
            $no = 1;
            $totalAmount = 0;
            @endphp

            @foreach($transactions as $transaction)
            @php
            // Sort parts by ID
            $sortedParts = $transaction->parts->sortBy('id');
            @endphp
            @foreach($sortedParts as $part)
            @php
            $unitPrice = $part->price;
            $discountedPrice = $unitPrice * 0.925;
            $subtotal = $unitPrice * 0.925 * $part->quantity; // 7.5% discount
            $totalAmount += $subtotal;
            @endphp
            <tr>
                <td>{{ $no++ }}</td>
                <td>{{ $part->partInventory->item_code }}</td>
                <td>{{ $part->partInventory->part->part_code }}</td>
                <td style="text-align: left;">{{ $part->partInventory->part->part_name }}</td>
                <td>{{ $part->quantity }}</td>
                <td>{{ $part->eum ?? 'EA' }}</td>
                <td style="text-align: right;">Rp{{ number_format($unitPrice, 0, ',', '.') }}</td>
                <td style="text-align: right;">Rp{{ number_format($discountedPrice, 0, ',', '.') }}</td>
                <td style="text-align: right;">Rp{{ number_format($subtotal, 0, ',', '.') }}</td>
            </tr>
            @endforeach
            @endforeach

            @else
            <tr>
                <td></td>
                <td></td>
                <td></td>
                <td style="text-align: left;"></td>
                <td></td>
                <td></td>
                <td style="text-align: right;">Rp0</td>
                <td style="text-align: right;">Rp0</td>
                <td style="text-align: right;">Rp0</td>
            </tr>
            @endif
            <tr style="background-color: #ffff;">
                <td colspan="7" style="border: 0;"></td>
                <td style="text-align: left; font-weight: bold;">TOTAL</td>
                <td style="text-align: right;">Rp {{ number_format($totalAmount ?? 2590000, 0, ',', '.') }}</td>
            </tr>
        </tbody>
    </table>

    <!-- Signature Section -->
    <div class="clearfix">
        <table class="signature-table">
            <tr>
                <td>
                    <div>Issued By</div>
                </td>
                <td>
                    <div>Received By</div>
                </td>
            </tr>
            <tr>
                <td style="padding-top: 60px;">
                    <div>{{ SESSION('name') }}</div>

                    <div>Date: {{ \Carbon\Carbon::parse($transactions[0]->mr_date ?? now())->translatedFormat('d F Y') }}</div>

                </td>
                <td style="padding-top: 60px;">
                    <div>------------------------</div>
                    <div style="padding-right: 60px;">Date:</div>
                </td>
            </tr>
        </table>
    </div>
</body>

</html>