<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // First, we need to check if there are any duplicate entries that would violate the constraint
        $duplicates = DB::table('unit_transaction_parts')
            ->select('unit_transaction_id', 'part_inventory_id', DB::raw('COUNT(*) as count'))
            ->groupBy('unit_transaction_id', 'part_inventory_id')
            ->having('count', '>', 1)
            ->get();

        // If duplicates exist, we need to handle them before adding the constraint
        if ($duplicates->count() > 0) {
            foreach ($duplicates as $duplicate) {
                // Get all duplicate records
                $records = DB::table('unit_transaction_parts')
                    ->where('unit_transaction_id', $duplicate->unit_transaction_id)
                    ->where('part_inventory_id', $duplicate->part_inventory_id)
                    ->orderBy('id')
                    ->get();

                // Keep the first record and merge quantities for the rest
                $firstRecord = $records->shift();
                $totalQuantity = $firstRecord->quantity;
                $recordsToDelete = [];

                foreach ($records as $record) {
                    $totalQuantity += $record->quantity;
                    $recordsToDelete[] = $record->id;
                }

                // Update the first record with the total quantity
                DB::table('unit_transaction_parts')
                    ->where('id', $firstRecord->id)
                    ->update(['quantity' => $totalQuantity]);

                // Delete the duplicate records
                if (!empty($recordsToDelete)) {
                    DB::table('unit_transaction_parts')
                        ->whereIn('id', $recordsToDelete)
                        ->delete();
                }
            }
        }

        // Now add the unique constraint
        Schema::table('unit_transaction_parts', function (Blueprint $table) {
            $table->unique(['unit_transaction_id', 'part_inventory_id'], 'unit_transaction_part_unique');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('unit_transaction_parts', function (Blueprint $table) {
            $table->dropUnique('unit_transaction_part_unique');
        });
    }
};
