<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Timezone Test - Portal PWB</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .test-container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        .test-result {
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
            border-left: 4px solid;
        }
        .success {
            background-color: #d4edda;
            border-color: #28a745;
            color: #155724;
        }
        .error {
            background-color: #f8d7da;
            border-color: #dc3545;
            color: #721c24;
        }
        .info {
            background-color: #d1ecf1;
            border-color: #17a2b8;
            color: #0c5460;
        }
        input[type="date"] {
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
            margin: 5px;
        }
        button {
            padding: 8px 16px;
            background-color: #007bff;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background-color: #0056b3;
        }
        .code {
            background-color: #f8f9fa;
            padding: 10px;
            border-radius: 4px;
            font-family: monospace;
            margin: 10px 0;
        }
    </style>
</head>
<body>
    <h1>Portal PWB - Timezone Test</h1>
    <p>This page tests the timezone configuration and date utilities for Asia/Makassar timezone.</p>

    <div class="test-container">
        <h2>1. Date Utilities Test</h2>
        <div id="date-utils-test"></div>
    </div>

    <div class="test-container">
        <h2>2. Date Input Test</h2>
        <p>Test the timezone-safe date formatting for input fields:</p>
        <div>
            <label>Test Date Input: </label>
            <input type="date" id="test-date-input" />
            <button onclick="testDateInput()">Test with Sample Date</button>
        </div>
        <div id="date-input-results"></div>
    </div>

    <div class="test-container">
        <h2>3. Server Timezone Test</h2>
        <button onclick="testServerTimezone()">Check Server Timezone</button>
        <div id="server-timezone-results"></div>
    </div>

    <div class="test-container">
        <h2>4. Date Conversion Test</h2>
        <p>Test various date formats from server:</p>
        <button onclick="testDateConversions()">Test Date Conversions</button>
        <div id="date-conversion-results"></div>
    </div>

    <script src="/build/assets/app-C-lAshck.js"></script>
    <script>
        // Wait for DOM to be ready
        document.addEventListener('DOMContentLoaded', function() {
            // Test 1: Date Utilities
            testDateUtilities();
        });

        function testDateUtilities() {
            const resultsDiv = document.getElementById('date-utils-test');
            let html = '';

            // Check if DateUtils is available
            if (typeof window.DateUtils !== 'undefined') {
                html += '<div class="test-result success">✓ DateUtils is available globally</div>';
                
                // Test each function
                try {
                    const today = window.DateUtils.getTodayFormatted();
                    html += `<div class="test-result success">✓ getTodayFormatted(): ${today}</div>`;
                } catch (e) {
                    html += `<div class="test-result error">✗ getTodayFormatted() failed: ${e.message}</div>`;
                }

                try {
                    const tomorrow = window.DateUtils.getDateFromToday(1);
                    html += `<div class="test-result success">✓ getDateFromToday(1): ${tomorrow}</div>`;
                } catch (e) {
                    html += `<div class="test-result error">✗ getDateFromToday(1) failed: ${e.message}</div>`;
                }

                try {
                    const formatted = window.DateUtils.formatDateIndonesian('2025-01-15');
                    html += `<div class="test-result success">✓ formatDateIndonesian('2025-01-15'): ${formatted}</div>`;
                } catch (e) {
                    html += `<div class="test-result error">✗ formatDateIndonesian() failed: ${e.message}</div>`;
                }

                try {
                    const formatted = window.DateUtils.formatDateForInput('2025-01-15T10:30:00.000000Z');
                    html += `<div class="test-result success">✓ formatDateForInput('2025-01-15T10:30:00.000000Z'): ${formatted}</div>`;
                } catch (e) {
                    html += `<div class="test-result error">✗ formatDateForInput() failed: ${e.message}</div>`;
                }
            } else {
                html += '<div class="test-result error">✗ DateUtils is not available. Check if the utilities are loaded correctly.</div>';
            }

            resultsDiv.innerHTML = html;
        }

        function testDateInput() {
            const resultsDiv = document.getElementById('date-input-results');
            const dateInput = document.getElementById('test-date-input');
            
            // Test with a sample date that might have timezone issues
            const sampleDate = '2025-01-15T10:30:00.000000Z';
            
            let html = '<h4>Testing with sample date: ' + sampleDate + '</h4>';
            
            // Test old method (problematic)
            try {
                const oldMethod = new Date(sampleDate).toISOString().split('T')[0];
                html += `<div class="test-result info">Old method (new Date().toISOString()): ${oldMethod}</div>`;
            } catch (e) {
                html += `<div class="test-result error">Old method failed: ${e.message}</div>`;
            }
            
            // Test new method (timezone-safe)
            if (window.DateUtils) {
                try {
                    const newMethod = window.DateUtils.formatDateForInput(sampleDate);
                    html += `<div class="test-result success">New method (DateUtils.formatDateForInput): ${newMethod}</div>`;
                    
                    // Set the input value
                    dateInput.value = newMethod;
                } catch (e) {
                    html += `<div class="test-result error">New method failed: ${e.message}</div>`;
                }
            }
            
            resultsDiv.innerHTML = html;
        }

        function testServerTimezone() {
            const resultsDiv = document.getElementById('server-timezone-results');
            resultsDiv.innerHTML = '<div class="test-result info">Loading server timezone information...</div>';
            
            fetch('/debug-timezone')
                .then(response => response.json())
                .then(data => {
                    let html = '<h4>Server Timezone Configuration:</h4>';
                    html += '<div class="code">';
                    html += `App Timezone: ${data.app_timezone}<br>`;
                    html += `PHP Timezone: ${data.php_timezone}<br>`;
                    html += `Current Time: ${data.current_time}<br>`;
                    html += `Current Time ISO: ${data.current_time_iso}<br>`;
                    html += `Database Timezone: ${data.database_timezone}<br>`;
                    html += `ENV App Timezone: ${data.env_app_timezone}<br>`;
                    html += `ENV DB Timezone: ${data.env_db_timezone}`;
                    html += '</div>';
                    
                    // Check if timezone is correctly set
                    if (data.app_timezone === 'Asia/Makassar' && data.php_timezone === 'Asia/Makassar') {
                        html += '<div class="test-result success">✓ Timezone is correctly configured for Asia/Makassar</div>';
                    } else {
                        html += '<div class="test-result error">✗ Timezone configuration issue detected</div>';
                    }
                    
                    resultsDiv.innerHTML = html;
                })
                .catch(error => {
                    resultsDiv.innerHTML = `<div class="test-result error">Error fetching server timezone: ${error.message}</div>`;
                });
        }

        function testDateConversions() {
            const resultsDiv = document.getElementById('date-conversion-results');
            
            const testDates = [
                '2025-01-15',
                '2025-01-15T10:30:00.000000Z',
                '2025-01-15 10:30:00',
                '2025-12-31T23:59:59.000000Z'
            ];
            
            let html = '<h4>Testing various date formats:</h4>';
            
            testDates.forEach(testDate => {
                html += `<div class="code">Testing: ${testDate}</div>`;
                
                if (window.DateUtils) {
                    try {
                        const result = window.DateUtils.formatDateForInput(testDate);
                        html += `<div class="test-result success">✓ Result: ${result}</div>`;
                    } catch (e) {
                        html += `<div class="test-result error">✗ Failed: ${e.message}</div>`;
                    }
                } else {
                    html += '<div class="test-result error">✗ DateUtils not available</div>';
                }
                
                html += '<br>';
            });
            
            resultsDiv.innerHTML = html;
        }
    </script>
</body>
</html>
