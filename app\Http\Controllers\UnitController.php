<?php

namespace App\Http\Controllers;

use App\Models\Unit;
use App\Models\UnitPart;
use App\Models\PartInventory;

use App\Helpers\LogHelper;
use Illuminate\Http\Request;
use Illuminate\Validation\Rule;

class UnitController extends Controller
{
    public function index()
    {
        // Get site_id from session
        $site_id = session('site_id');
        $site_name = session('name');

        return view('units.index', compact('site_id', 'site_name'));
    }

    public function store(Request $request)
    {
        // Get site_id from session
        $site_id = session('site_id');

        $validated = $request->validate([
            'unit_code' => [
                'required',
                Rule::unique('units')->where(function ($query) use ($site_id) {
                    return $query->where('site_id', $site_id);
                }),
            ],
            'unit_type' => 'required',
            'nopr' => 'nullable|integer',
            'noqtn' => 'nullable|integer',
            'do_number' => 'nullable|string',
            'noSPB' => 'nullable|string',
            'pekerjaan' => 'nullable|string',
            'HMKM' => 'nullable|string',
            'SHIFT' => 'nullable|string',
            'LOKASI' => 'nullable|string',
        ]);

        // Add site_id from session
        $validated['site_id'] = $site_id;

        $unit = Unit::create($validated);

        // Log the unit creation
        LogHelper::logCreate('Unit', "{$unit->unit_type} ({$unit->unit_code})", 'Units', $request);

        return response()->json(['message' => 'Unit created successfully', 'unit' => $unit]);
    }

    public function update(Request $request, Unit $unit)
    {
        // Get site_id from session
        $site_id = session('site_id');

        // Check if the unit belongs to the user's site
        if ($unit->site_id !== $site_id) {
            return response()->json(['message' => 'You can only update units from your site'], 403);
        }

        $validated = $request->validate([
            'unit_code' => [
                'required',
                Rule::unique('units')->where(function ($query) use ($site_id) {
                    return $query->where('site_id', $site_id);
                })->ignore($unit->id)
            ],
            'unit_type' => 'required',
            'nopr' => 'nullable|integer',
            'noqtn' => 'nullable|integer',
            'do_number' => 'nullable|string',
            'noSPB' => 'nullable|string',
            'pekerjaan' => 'nullable|string',
            'HMKM' => 'nullable|string',
            'SHIFT' => 'nullable|string',
            'LOKASI' => 'nullable|string',
        ]);

        // Keep the original site_id
        $validated['site_id'] = $site_id;

        // Get original values for logging
        $originalUnitType = $unit->unit_type;
        $originalUnitCode = $unit->unit_code;

        $unit->update($validated);

        // Log the unit update
        $changes = [];
        if ($originalUnitType != $unit->unit_type) {
            $changes[] = "unit_type: {$originalUnitType} → {$unit->unit_type}";
        }
        if ($originalUnitCode != $unit->unit_code) {
            $changes[] = "unit_code: {$originalUnitCode} → {$unit->unit_code}";
        }

        LogHelper::logUpdate('Unit', "{$unit->unit_type} ({$unit->unit_code})", 'Units', $request, $changes);

        return response()->json(['message' => 'Unit updated successfully', 'unit' => $unit]);
    }

    public function destroy(Request $request, Unit $unit)
    {
        // Get site_id from session
        $site_id = session('site_id');

        // Check if the unit belongs to the user's site
        if ($unit->site_id !== $site_id) {
            return response()->json(['message' => 'You can only delete units from your site'], 403);
        }

        // Log the unit deletion before deleting
        LogHelper::logDelete('Unit', "{$unit->unit_type} ({$unit->unit_code})", 'Units', $request);

        $unit->delete();
        return response()->json(['message' => 'Unit deleted successfully']);
    }

    public function storePart(Request $request, Unit $unit)
    {
        $validated = $request->validate([
            'part_inventory_id' => 'required|exists:part_inventories,part_inventory_id',
            'quantity' => 'required|integer|min:1',
            'price' => 'required|numeric|min:0',
            'eum' => 'nullable|string',
        ]);

        $unitPart = $unit->parts()->create($validated);

        // Get part details for logging
        $partInventory = PartInventory::with('part')->find($validated['part_inventory_id']);
        $partName = $partInventory->part->part_name;

        // Log the unit part addition
        LogHelper::createLog(
            'Menambahkan Part ke Unit',
            'User ' . session('name') . ' menambahkan part ' . $partName . ' sebanyak ' . $validated['quantity'] . ' ke unit ' . $unit->unit_type . ' (' . $unit->unit_code . ')',
            'Unit Parts',
            $request
        );

        return response()->json([
            'message' => 'Unit part added successfully',
            'part' => $unitPart->load('partInventory.part')
        ]);
    }

    public function updatePart(Request $request, $id)
    {
        try {
            $unitPart = UnitPart::findOrFail($id);

            $validated = $request->validate([
                'quantity' => 'required|integer|min:1',
                'price' => 'required|numeric|min:0',
                'eum' => 'nullable|string',
            ]);

            // Get original values for logging
            $originalQuantity = $unitPart->quantity;
            $originalPrice = $unitPart->price;

            $unitPart->update($validated);

            // Get part and unit details for logging
            $partInventory = PartInventory::with('part')->find($unitPart->part_inventory_id);
            $unit = Unit::find($unitPart->unit_id);

            if ($partInventory && $unit) {
                $partName = $partInventory->part->part_name;

                // Prepare changes for logging
                $changes = [];
                if ($originalQuantity != $unitPart->quantity) {
                    $changes[] = "quantity: {$originalQuantity} → {$unitPart->quantity}";
                }
                if ($originalPrice != $unitPart->price) {
                    $changes[] = "price: {$originalPrice} → {$unitPart->price}";
                }

                // Log the unit part update
                LogHelper::createLog(
                    'Mengubah Part Unit',
                    "User " . session('name') . " mengubah part {$partName} pada unit {$unit->unit_type} ({$unit->unit_code}) dengan perubahan " . implode(', ', $changes),
                    'Unit Parts',
                    $request
                );
            }

            return response()->json([
                'message' => 'Unit part updated successfully',
                'part' => $unitPart->fresh()->load('partInventory.part')
            ]);
        } catch (\Exception $e) {
            \Illuminate\Support\Facades\Log::error('Error in updatePart: ' . $e->getMessage());
            return response()->json(['message' => 'Error updating part: ' . $e->getMessage()], 500);
        }
    }

    public function destroyPart(Request $request, $id)
    {
        try {
            $unitPart = UnitPart::findOrFail($id);

            // Get part and unit details for logging before deletion
            $partInventory = PartInventory::with('part')->find($unitPart->part_inventory_id);
            $unit = Unit::find($unitPart->unit_id);

            if ($partInventory && $unit) {
                $partName = $partInventory->part->part_name;

                // Log the unit part deletion
                LogHelper::createLog(
                    'Menghapus Part Unit',
                    "User " . session('name') . " menghapus part {$partName} sebanyak {$unitPart->quantity} dari unit {$unit->unit_type} ({$unit->unit_code})",
                    'Unit Parts',
                    $request
                );
            }

            $unitPart->delete();
            return response()->json(['message' => 'Unit part removed successfully']);
        } catch (\Exception $e) {
            \Illuminate\Support\Facades\Log::error('Error in destroyPart: ' . $e->getMessage());
            return response()->json(['message' => 'Error removing part: ' . $e->getMessage()], 500);
        }
    }

    public function getUnits(Request $request)
    {
        // Get site_id from session
        $site_id = session('site_id');

        $perPage = $request->input('per_page', 10); // Default to 10 items per page
        $query = Unit::with('site')->where('site_id', $site_id)->latest();

        // Apply search if provided
        if ($request->has('search') && !empty($request->search)) {
            $search = $request->search;
            $query->where(function($q) use ($search) {
                $q->where('unit_code', 'like', "%{$search}%")
                  ->orWhere('unit_type', 'like', "%{$search}%");
            });
        }

        $units = $query->paginate($perPage);

        // Preserve search parameter in pagination links
        if ($request->has('search')) {
            $units->appends(['search' => $request->search]);
        }

        return response()->json($units);
    }

    public function show(Unit $unit)
    {
        return response()->json($unit);
    }

    public function getParts(Request $request, Unit $unit)
    {
        $perPage = $request->input('per_page', 10); // Default to 10 items per page
        $parts = $unit->parts()->with('partInventory.part')->paginate($perPage);
        return response()->json($parts);
    }

    public function showPart($id)
    {
        try {
            $unitPart = UnitPart::with('partInventory.part')->findOrFail($id);
            return response()->json($unitPart);
        } catch (\Exception $e) {
            \Illuminate\Support\Facades\Log::error('Error in showPart: ' . $e->getMessage());
            return response()->json(['message' => 'Part not found'], 404);
        }
    }

    public function searchPartInventories(Request $request)
    {
        // Get site_id from session
        $site_id = session('site_id');

        $query = $request->input('query');
        $partInventories = PartInventory::with('part')
            ->where('site_id', $site_id) // Only show parts from the user's site
            ->whereHas('part', function($q) use ($query) {
                $q->where('part_name', 'like', "%{$query}%")
                  ->orWhere('part_code', 'like', "%{$query}%");
            })
            ->limit(10)
            ->get();

        return response()->json($partInventories);
    }

    /**
     * Store a new unit with a part in a single request
     */
    public function storeWithPart(Request $request)
    {
        try {
            // Get site_id from session
            $site_id = session('site_id');

            // Check if site_id exists
            if (!$site_id) {
                return response()->json(['message' => 'Site ID not found in session'], 400);
            }

            // Validate unit data
            $unitData = $request->input('unit');
            if (!$unitData) {
                return response()->json(['message' => 'Unit data is required'], 400);
            }

            $unitValidated = $this->validateUnitData($unitData);

            // Add site_id from session
            $unitValidated['site_id'] = $site_id;

            // Create the unit
            $unit = Unit::create($unitValidated);

            // Get parts data
            $partsData = $request->input('parts');
            if (!$partsData || !is_array($partsData) || count($partsData) === 0) {
                return response()->json(['message' => 'At least one part is required for new units'], 400);
            }

            $createdParts = [];

            // Process each part
            foreach ($partsData as $partData) {
                $partValidated = $this->validatePartData($partData);

                // Create the unit part
                $unitPart = $unit->parts()->create($partValidated);
                $createdParts[] = $unitPart;

                // Get part details for logging
                $partInventory = PartInventory::with('part')->find($partValidated['part_inventory_id']);
                if (!$partInventory) {
                    continue; // Skip logging if part inventory not found
                }

                $partName = $partInventory->part->part_name;

                // Log the unit part addition
                LogHelper::createLog(
                    'Menambahkan Part ke Unit',
                    'User ' . session('name') . ' menambahkan part ' . $partName . ' sebanyak ' . $partValidated['quantity'] . ' ke unit ' . $unit->unit_type . ' (' . $unit->unit_code . ')',
                    'Unit Parts',
                    $request
                );
            }

            // Log the unit creation
            LogHelper::logCreate('Unit', "{$unit->unit_type} ({$unit->unit_code})", 'Units', $request);

            return response()->json([
                'message' => 'Unit with parts created successfully',
                'unit' => $unit,
                'parts' => $createdParts
            ]);
        } catch (\Exception $e) {
            // Log the error
            \Illuminate\Support\Facades\Log::error('Error in storeWithPart: ' . $e->getMessage());
            \Illuminate\Support\Facades\Log::error($e->getTraceAsString());

            return response()->json([
                'message' => 'Error creating unit with part: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Update a unit with a part in a single request
     */
    public function updateWithPart(Request $request, Unit $unit)
    {
        try {
            // Get site_id from session
            $site_id = session('site_id');

            // Check if site_id exists
            if (!$site_id) {
                return response()->json(['message' => 'Site ID not found in session'], 400);
            }

            // Check if the unit belongs to the user's site
            if ($unit->site_id !== $site_id) {
                return response()->json(['message' => 'You can only update units from your site'], 403);
            }

            // Validate unit data
            $unitData = $request->input('unit');
            if (!$unitData) {
                return response()->json(['message' => 'Unit data is required'], 400);
            }

            $unitValidated = $this->validateUnitData($unitData, $unit->id);

            // Keep the original site_id
            $unitValidated['site_id'] = $site_id;

            // Get original values for logging
            $originalUnitType = $unit->unit_type;
            $originalUnitCode = $unit->unit_code;

            // Update the unit
            $unit->update($unitValidated);

            // Log the unit update
            $changes = [];
            if ($originalUnitType != $unit->unit_type) {
                $changes[] = "unit_type: {$originalUnitType} → {$unit->unit_type}";
            }
            if ($originalUnitCode != $unit->unit_code) {
                $changes[] = "unit_code: {$originalUnitCode} → {$unit->unit_code}";
            }

            LogHelper::logUpdate('Unit', "{$unit->unit_type} ({$unit->unit_code})", 'Units', $request, $changes);

            // Get parts data
            $partsData = $request->input('parts', []);

            $createdParts = [];

            // Process each part
            foreach ($partsData as $partData) {
                $partValidated = $this->validatePartData($partData);

                // Create the unit part
                $unitPart = $unit->parts()->create($partValidated);
                $createdParts[] = $unitPart;

                // Get part details for logging
                $partInventory = PartInventory::with('part')->find($partValidated['part_inventory_id']);
                if (!$partInventory) {
                    continue; // Skip logging if part inventory not found
                }

                $partName = $partInventory->part->part_name;

                // Log the unit part addition
                LogHelper::createLog(
                    'Menambahkan Part ke Unit',
                    'User ' . session('name') . ' menambahkan part ' . $partName . ' sebanyak ' . $partValidated['quantity'] . ' ke unit ' . $unit->unit_type . ' (' . $unit->unit_code . ')',
                    'Unit Parts',
                    $request
                );
            }

            return response()->json([
                'message' => 'Unit with parts updated successfully',
                'unit' => $unit,
                'parts' => $createdParts
            ]);
        } catch (\Exception $e) {
            // Log the error
            \Illuminate\Support\Facades\Log::error('Error in updateWithPart: ' . $e->getMessage());
            \Illuminate\Support\Facades\Log::error($e->getTraceAsString());

            return response()->json([
                'message' => 'Error updating unit with part: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Validate unit data
     */
    private function validateUnitData($data, $unitId = null)
    {
        // Get site_id from session
        $site_id = session('site_id');

        $rules = [
            'unit_code' => $unitId
                ? [
                    'required',
                    Rule::unique('units')->where(function ($query) use ($site_id) {
                        return $query->where('site_id', $site_id);
                    })->ignore($unitId)
                ]
                : [
                    'required',
                    Rule::unique('units')->where(function ($query) use ($site_id) {
                        return $query->where('site_id', $site_id);
                    })
                ],
            'unit_type' => 'required',
        ];

        return \Illuminate\Support\Facades\Validator::make($data, $rules)->validate();
    }

    /**
     * Validate part data
     */
    private function validatePartData($data)
    {
        $rules = [
            'part_inventory_id' => 'required|exists:part_inventories,part_inventory_id',
            'quantity' => 'required|integer|min:1',
            'price' => 'required|numeric|min:0',
            'eum' => 'nullable|string',
        ];

        return \Illuminate\Support\Facades\Validator::make($data, $rules)->validate();
    }
}