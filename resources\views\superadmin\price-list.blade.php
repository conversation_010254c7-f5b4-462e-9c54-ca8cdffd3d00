<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="utf-8" />
    <title>Portal PWB - Price List</title>
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta content="Portal PWB" name="description" />
    <meta content="PWB" name="author" />
    <meta http-equiv="X-UA-Compatible" content="IE=edge" />
    <meta name="csrf-token" content="{{ csrf_token() }}">
    <link rel="shortcut icon" href="{{ asset('assets/images/logo-small.png') }}">
    <!-- Styles -->
    @vite([
    'resources/assets/css/bootstrap.min.css',
    'resources/assets/css/icons.min.css',
    'resources/assets/css/app.min.css',
    'resources/css/app.css',
    'resources/css/superadmin-dashboard.css',
    'resources/css/superadmin-scaling.css'
    ])
    <!-- Scripts -->
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jQuery-slimScroll/1.3.8/jquery.slimscroll.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/metisMenu/3.0.7/metisMenu.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <!-- Vite JS Resources -->
    @vite([
    'resources/js/superadmin-price-list.js',
    'resources/js/superadmin-scaling.js',
    'resources/js/superadmin-mobile-menu.js'
    ])
    <style>
        :root {
            --primary-color: #2a69a8;
            --secondary-color: rgba(42, 105, 168, 0.5);
            --accent-color: rgb(40, 21, 211);
            --accent-hover-color: rgb(60, 41, 231);
            --highlight-color: rgb(251, 255, 0);
            --danger-color: #ff5d48;
            --success-color: #1bb99a;
            --info-color: #3db9dc;
            --warning-color: #f1734f;
            --text-color: #343a40;
            --text-muted: #6c757d;
            --border-color: rgba(0, 0, 0, 0.1);
            --card-bg-color: rgba(255, 255, 255, 0.95);
        }

        .btn:hover {
            border-radius: 50px;
            background: #e0e0e0;
            box-shadow: inset 20px 20px 60px var(--primary-color),
                inset -20px -20px 60px #ffffff;
        }

        body {
            margin: 0;
            padding: 0;
            min-height: 100vh;
            display: flex;
            justify-content: center;
            align-items: flex-start;
            background: url('{{ asset('assets/images/homewalpaper.jpg') }}');
            background-size: cover;
            background-position: center;
            background-attachment: fixed;
            font-family: 'Segoe UI', sans-serif;
            position: relative;
            overflow-x: hidden;
        }

        .dashboard-container {
            width: 100%;
            max-width: 1600px;
            padding: 0 15px;
            margin: 0 auto;
        }

        .login-theme-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 15px 20px;
            background-color: #fff;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
            position: sticky;
            top: 0;
            z-index: 100;
            margin-bottom: 20px;
            border-radius: 0 0 10px 10px;
        }

        .header-top {
            display: none; /* Hide on desktop, only show on mobile */
        }

        .company-logo {
            display: flex;
            align-items: center;
        }

        .company-logo img {
            height: 40px;
            margin-right: 10px;
        }

        .company-name {
            font-size: 1.2rem;
            font-weight: 700;
            margin: 0;
            color: var(--primary-color);
        }

        /* Mobile Menu Toggle Button */
        .mobile-menu-toggle {
            display: none;
            background: none;
            border: none;
            color: var(--primary-color);
            font-size: 1.5rem;
            cursor: pointer;
            padding: 5px;
            border-radius: 5px;
            transition: all 0.3s ease;
        }

        .mobile-menu-toggle:hover {
            background-color: rgba(42, 105, 168, 0.1);
        }

        /* Mobile Menu Close Button */
        .mobile-menu-close {
            display: none;
            background: none;
            border: none;
            color: var(--danger-color);
            font-size: 1.5rem;
            cursor: pointer;
            padding: 5px;
            border-radius: 5px;
            position: absolute;
            top: 10px;
            right: 10px;
            z-index: 1001;
            transition: all 0.3s ease;
        }

        .mobile-menu-close:hover {
            background-color: rgba(42, 105, 168, 0.1);
        }

        /* Mobile Menu Overlay */
        .mobile-menu-overlay {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 0, 0.5);
            z-index: 998;
        }

        /* Mobile Menu Header */
        .mobile-menu-header {
            margin-bottom: 15px;
            padding-bottom: 10px;
            padding-top: 30px; /* Add padding to top to avoid overlap with close button */
            border-bottom: 1px solid rgba(0, 0, 0, 0.1);
            text-align: center;
        }

        .menu-title {
            font-size: 1.1rem;
            font-weight: 600;
            color: var(--primary-color);
            margin: 0;
            padding: 0;
        }

        /* Navigation Links */
        .nav-links {
            display: flex;
            flex-wrap: wrap;
            margin-top: 10px;
        }

        .nav-link {
            display: flex;
            align-items: center;
            padding: 8px 15px;
            margin: 0 5px 5px 0;
            border-radius: 8px;
            text-decoration: none;
            color: var(--primary-color);
            background-color: rgba(42, 105, 168, 0.1);
            transition: all 0.3s ease;
            border: 1px solid var(--primary-color);
        }

        .nav-link i {
            margin-right: 8px;
            font-size: 1.1rem;
        }

        .nav-link:hover {
            background-color: var(--primary-color);
            color: #fff;
        }

        .nav-link.active {
            background-color: var(--primary-color);
            color: #fff;
        }

        .nav-link-danger {
            color: var(--danger-color) !important;
            background-color: rgba(255, 93, 72, 0.1) !important;
            border: 1px solid var(--danger-color) !important;
        }

        .nav-link-danger:hover {
            background-color: var(--danger-color) !important;
            color: #fff !important;
        }

        /* Header Right */
        .header-right {
            display: flex;
            align-items: center;
            justify-content: flex-end;
        }

        .filter-buttons {
            display: flex;
            flex-wrap: wrap;
            gap: 8px;
            margin-bottom: 15px;
        }

        .filter-buttons .btn {
            border-radius: 20px;
            padding: 6px 15px;
            font-size: 0.85rem;
            font-weight: 500;
            transition: all 0.2s;
        }

        .filter-buttons .btn.active {
            color: white;
            box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
        }

        /* Active button colors based on type */
        .filter-buttons .btn-outline-primary.active {
            background-color: var(--primary-color);
            border-color: var(--primary-color);
        }

        .filter-buttons .btn-outline-success.active {
            background-color: var(--success-color);
            border-color: var(--success-color);
        }

        .filter-buttons .btn-outline-info.active {
            background-color: var(--info-color);
            border-color: var(--info-color);
        }

        /* Custom purple button style */
        .btn-outline-purple {
            color: #6f42c1;
            border-color: #6f42c1;
        }

        .btn-outline-purple:hover {
            color: #fff;
            background-color: #6f42c1;
            border-color: #6f42c1;
        }

        .filter-buttons .btn-outline-purple.active {
            background-color: #6f42c1;
            border-color: #6f42c1;
            color: white;
        }

        /* Custom dark button style for Warehouse */
        .btn-outline-dark-brown {
            color: #5D4037;
            border-color: #5D4037;
        }

        .btn-outline-dark-brown:hover {
            color: #fff;
            background-color: #5D4037;
            border-color: #5D4037;
        }

        .filter-buttons .btn-outline-dark-brown.active {
            background-color: #5D4037;
            border-color: #5D4037;
            color: white;
        }

        /* Custom amber button style for other sites */
        .btn-outline-amber {
            color: #FF8F00;
            border-color: #FF8F00;
        }

        .btn-outline-amber:hover {
            color: #fff;
            background-color: #FF8F00;
            border-color: #FF8F00;
        }

        .filter-buttons .btn-outline-amber.active {
            background-color: #FF8F00;
            border-color: #FF8F00;
            color: white;
        }

        .search-box {
            position: relative;
            margin-bottom: 15px;
        }

        .search-box input {
            border-radius: 20px;
            padding-left: 40px;
            border: 1px solid var(--border-color);
        }

        .search-box i {
            position: absolute;
            left: 15px;
            top: 50%;
            transform: translateY(-50%);
            color: var(--text-muted);
        }

        .price-list-table th {
            background-color: var(--primary-color);
            color: white;
            font-weight: 500;
            border: none;
        }

        .price-list-table td {
            vertical-align: middle;
            padding: 2px 2px;
        }

        .site-price-badge {
            display: inline-block;
            padding: 4px 8px;
            border-radius: 4px;
            margin: 2px;
            font-size: 0.8rem;
            background-color: #f8f9fa;
            border: 1px solid #dee2e6;
        }

        .skeleton-row {
            height: 20px;
            background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
            background-size: 200% 100%;
            border-radius: 4px;
            margin-bottom: 8px;
        }

        @keyframes loading {
            0% {
                background-position: 200% 0;
            }

            100% {
                background-position: -200% 0;
            }
        }

        .pagination {
            justify-content: center;
            margin-top: 20px;
        }

        .pagination .page-link {
            color: var(--primary-color);
            border-radius: 4px;
            margin: 0 3px;
        }

        .pagination .page-item.active .page-link {
            background-color: var(--primary-color);
            border-color: var(--primary-color);
        }

        @media (max-width: 992px) {
            .login-theme-header {
                padding: 0;
                flex-direction: column;
                align-items: stretch;
            }

            /* Hide desktop logo on mobile */
            .login-theme-header > .company-logo {
                display: none;
            }

            /* Show header-top on mobile */
            .header-top {
                display: flex;
                justify-content: space-between;
                align-items: center;
                width: 100%;
                padding: 10px 15px;
            }

            /* Show mobile menu toggle button */
            .mobile-menu-toggle {
                display: block;
            }

            /* Mobile menu styling */
            .header-right {
                position: fixed;
                top: 0; /* Position at the top */
                right: -100%; /* Hide off-screen initially using percentage */
                width: 85%; /* Use percentage width to ensure it fits on all screens */
                max-width: 300px; /* Set a maximum width */
                height: auto; /* Auto height instead of 100% */
                max-height: 80vh; /* Maximum height of 80% of viewport height */
                background-color: #fff;
                box-shadow: -5px 0 15px rgba(0, 0, 0, 0.1);
                padding: 15px;
                z-index: 999;
                overflow-y: auto;
                transition: right 0.3s ease;
                flex-direction: column;
                border-radius: 0 0 0 10px; /* Rounded corners on bottom left */
            }

            /* Show mobile menu close button */
            .mobile-menu-close {
                display: block;
            }

            /* When mobile menu is active */
            .header-right.active {
                right: 0;
            }

            /* Show overlay when mobile menu is active */
            .mobile-menu-overlay.active {
                display: block;
            }

            /* Navigation links in mobile view */
            .nav-links {
                flex-direction: column;
                width: 100%;
                margin-top: 20px;
            }

            .nav-link {
                width: 100%;
                margin: 0 0 10px 0;
                padding: 12px 15px;
                justify-content: flex-start;
            }

            .nav-link i {
                width: 24px;
                text-align: center;
                margin-right: 10px;
            }

            .filter-buttons {
                justify-content: center;
            }
        }

        /* Mobile specific styles */
        @media (max-width: 576px) {
            .login-theme-header {
                padding: 8px 12px;
            }

            .company-logo {
                flex-direction: row;
                align-items: center;
            }

            .company-logo img {
                height: 28px;
                margin-right: 6px;
                margin-bottom: 0;
            }

            .company-name {
                font-size: 0.8rem;
                white-space: nowrap;
                overflow: hidden;
                text-overflow: ellipsis;
                max-width: 160px;
            }

            .mobile-menu-toggle {
                padding: 5px;
                font-size: 1.3rem;
            }

            .mobile-menu-close {
                padding: 5px;
                font-size: 1.3rem;
                top: 5px;
                right: 5px;
            }

            .header-right {
                width: 85%;
                max-width: 280px;
                padding: 10px;
            }

            .nav-links {
                margin-top: 15px;
            }

            .nav-link {
                padding: 8px 10px;
                font-size: 0.85rem;
                margin-bottom: 8px;
            }

            .nav-link i {
                font-size: 1rem;
                margin-right: 6px;
                width: 20px;
            }

            .filter-buttons .btn {
                font-size: 0.75rem;
                padding: 5px 10px;
                margin: 2px;
            }

            .search-box input {
                font-size: 0.85rem;
                height: 36px;
            }
        }
    </style>
</head>

<body>
    <!-- Dashboard Container -->
    <div class="dashboard-container">
        <!-- Login Theme Header -->
        <header class="login-theme-header">
            <!-- Company Logo (Visible on all devices) -->
            <div class="company-logo">
                <img src="{{ asset('assets/images/logo-small.png') }}" alt="PWB Logo">
                <h1 class="company-name">PT. PUTERA WIBOWO BORNEO</h1>
            </div>

            <!-- Mobile Header Top (Only visible on mobile) -->
            <div class="header-top">
                <div class="company-logo">
                    <img src="{{ asset('assets/images/logo-small.png') }}" alt="PWB Logo">
                    <h1 class="company-name">PT. PUTERA WIBOWO BORNEO</h1>
                </div>

                <!-- Mobile Menu Toggle Button -->
                <button type="button" class="mobile-menu-toggle" id="mobileMenuToggle">
                    <i class="mdi mdi-menu"></i>
                </button>
            </div>

            <!-- Mobile Menu Overlay -->
            <div class="mobile-menu-overlay" id="mobileMenuOverlay"></div>

            <!-- Header Right (Navigation) -->
            <div class="header-right" id="mobileMenu">
                <!-- Close Button for Mobile -->
                <button type="button" class="mobile-menu-close d-lg-none" id="mobileMenuClose">
                    <i class="mdi mdi-close"></i>
                </button>

                <div class="mobile-menu-header d-lg-none">
                    <h5 class="menu-title">Menu Navigasi</h5>
                </div>

                <div class="nav-links">
                    <a href="{{ route('superadmin.dashboard') }}" class="nav-link {{ request()->routeIs('superadmin.dashboard') ? 'active' : '' }}">
                        <i class="mdi mdi-view-dashboard"></i> <span>Dashboard</span>
                    </a>
                    <a href="{{ route('superadmin.invoices') }}" class="nav-link {{ request()->routeIs('superadmin.invoices') ? 'active' : '' }}">
                        <i class="mdi mdi-file-document-outline"></i> <span>acount receveable</span>
                    </a>
                    <a href="{{ route('superadmin.parts') }}" class="nav-link {{ request()->routeIs('superadmin.parts') ? 'active' : '' }}">
                        <i class="mdi mdi-package-variant"></i> <span>Part</span>
                    </a>
                    <a href="{{ route('superadmin.price-list') }}" class="nav-link {{ request()->routeIs('superadmin.price-list') ? 'active' : '' }}">
                        <i class="mdi mdi-tag-multiple"></i> <span>Price List</span>
                    </a>
                    <a href="{{ route('logout') }}" class="nav-link nav-link-danger">
                        <i class="mdi mdi-logout"></i> <span>Logout</span>
                    </a>
                </div>
            </div>
        </header>

        <!-- Main Content -->
        <div class="content-wrapper">
            <div class="card">
                <div class="card-body">
                    <h4 class="card-title mb-4">
                        <i class="mdi mdi-tag-multiple me-1"></i> Daftar Harga Part
                    </h4>

                    <!-- Part Type Filter Buttons -->
                    <div class="d-flex justify-content-between flex-wrap">
                        <div class="filter-buttons part-type-filters mb-3">
                            <button type="button" class="btn btn-outline-primary active" data-part-type="all">
                                Semua Tipe
                            </button>
                            <!-- Part type buttons will be added here by JavaScript -->
                        </div>

                        <!-- Search Box -->
                        <div class="search-box mb-3">
                            <i class="mdi mdi-magnify search-icon"></i>
                            <input type="text" id="searchInput" class="form-control" placeholder="Cari part...">
                        </div>
                    </div>

                    <!-- Site Filter Buttons -->
                    <div class="filter-buttons site-filters mb-3">
                        @foreach($sites as $site)
                        <button type="button" class="btn {{ $site->site_id == 'WHO' ? 'btn-outline-dark-brown' : 'btn-outline-amber' }} {{ $site->site_id == 'WHO' ? 'active' : '' }}" data-site-id="{{ $site->site_id }}">
                            {{ $site->site_name }}
                        </button>
                        @endforeach
                    </div>

                    <!-- Price List Table -->
                    <div class="table-responsive">
                        <table class="table price-list-table">
                            <thead>
                                <tr>
                                    <th>Kode Part</th>
                                    <th>Nama Part</th>
                                    <th>Tipe</th>
                                    <th>Harga</th>
                                </tr>
                            </thead>
                            <tbody id="priceListTableBody">
                                <!-- Table content will be loaded by JavaScript -->
                            </tbody>
                        </table>
                    </div>

                    <!-- Pagination -->
                    <div id="pagination-container" class="mt-3">
                        <!-- Pagination will be added by JavaScript -->
                    </div>
                </div>
            </div>
        </div>
    </div>
    <!-- App Scripts -->
    <script src="{{ asset('assets/js/vendor.min.js') }}"></script>
    <script src="{{ asset('assets/js/app.min.js') }}"></script>
</body>

</html>