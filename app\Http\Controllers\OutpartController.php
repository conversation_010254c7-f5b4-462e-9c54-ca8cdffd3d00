<?php

namespace App\Http\Controllers;

use App\Models\Customer;
use App\Models\LogAktivitas;
use App\Models\Notification;
use App\Models\Part;
use App\Models\WarehouseOutStock;
use App\Models\PartInventory;
use App\Models\Site;
use App\Models\StockTransaction;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;

class OutpartController extends Controller
{
    public function index()
    {
        $sites = Site::where('site_id', '!=', session('site_id'))->get();
        $customers = Customer::all();
        $warehouseOutStocks = WarehouseOutStock::with(['partInventory.part', 'customer', 'site', 'user'])->get();
        $totalQuantity = $warehouseOutStocks->sum('quantity');
        return view('warehouse.outpartwho', compact('warehouseOutStocks', 'sites', 'customers', 'totalQuantity'));
    }
    public function autocomplete(Request $request)
    {
        $term = $request->input('term');

        $partInventories = PartInventory::with('part', 'site')
            ->whereHas('part', function ($query) use ($term) {
                $query->where('part_name', 'like', '%' . $term . '%');
            })
            ->whereHas('site', function ($query) {
                $query->where('site_id', '=', session('site_id'));
            })
            ->limit(10)
            ->get();

        return response()->json($partInventories);
    }
    public function filter(Request $request)
    {
        $siteId = $request->input('site_id');
        $startDate = $request->input('start_date');
        $endDate = $request->input('end_date');
        $page = $request->input('page', 1);
        $perPage = $request->input('per_page', 15); // Default to 15 items per page

        $query = WarehouseOutStock::with(['partInventory.part', 'site', 'user', 'customer']);

        if ($siteId != 'all') {
            $query->where('destination_id', operator: $siteId);
        }

        if ($startDate && $endDate) {
            $query->whereBetween('date_out', [$startDate, $endDate]);
        } else if ($startDate) {
            $query->where('date_out', $startDate);
        }

        // Get total count for pagination
        $total = $query->count();

        // Get paginated data
        $warehouseOutStocks = $query->skip(($page - 1) * $perPage)
                                  ->take($perPage)
                                  ->get();

        return response()->json([
            'warehouseOutStocks' => $warehouseOutStocks,
            'current_page' => (int)$page,
            'per_page' => (int)$perPage,
            'last_page' => ceil($total / $perPage),
            'total' => $total
        ]);
    }

    public function store(Request $request)
    {
        try {
            $request->validate([
                'part_inventory_id' => 'required|exists:part_inventories,part_inventory_id',
                'site_id' => 'nullable|exists:sites,site_id',
                'date_out' => 'required|date',
                'quantity' => 'required|numeric|min:0.1',
                'notes' => 'nullable|string',
                'destination_type' => 'nullable|in:site,customer',
                'destination' => 'nullable',
            ]);

            $employeeId = session('employee_id');
            $status = ($request->input('site_id') <= session('site_id')) ? 'out' : 'On Delivery';

            $siteId = session('site_id');
            $quantityUsed = $request->input('quantity');

            $partInventory = PartInventory::where('part_inventory_id', $request->input('part_inventory_id'))
                ->where('site_id', $siteId)
                ->first();
            $codepart = $partInventory->part_code;
            $partname = Part::findOrFail($codepart)->part_name;

            $filePath = null;
            if ($request->hasFile('surat_jalan')) {
                $file = $request->file('surat_jalan');
                $fileName = time() . '_' . $file->getClientOriginalName();
                $file->move(public_path('storage/surat_jalan'), $fileName);
                $filePath = 'surat_jalan/' . $fileName;
            }

            if ($partInventory) {
                try {
                    if ($partInventory->stock_quantity < $quantityUsed) {
                        return response()->json([
                            'success' => false,
                            'message' => "Harap periksa quantity !!, stock sisa = {$partInventory->stock_quantity}"
                        ]);
                    }
                    $partInventory->stock_quantity -= $quantityUsed;
                    $partInventory->save();
                } catch (\Throwable $th) {
                    return response()->json([
                        'success' => false,
                        'message' => 'Gagal mengurangi stock, periksa data out part'
                    ]);
                }
            } else {
                return response()->json([
                    'success' => false,
                    'message' => 'Gagal Memuat Part'
                ]);
            }

            if ($request->input('destination') == '1') {
                $warehouseOutStock = WarehouseOutStock::create([
                    'part_inventory_id' => $request->input('part_inventory_id'),
                    'site_id' => $request->input('site_id') ?? session('site_id'),
                    'employee_id' => $employeeId,
                    'date_out' => $request->input('date_out'),
                    'quantity' => $request->input('quantity'),
                    'status' => $status,
                    'notes' => $request->input('note'),
                    'destination_type' => $request->input('destination_type'),
                    'destination_id' => $request->input('destination'),
                ]);
                $keteranganlog = "Admin HO " . session('name') . " Menambahkan Out Part : " . $partname . " sebanyak " . $request->input('quantity');
                $pesan = "Part Out Berhasil";

            } else {
                // Check if the part exists in the destination site's inventory
                $destinationSiteId = $request->input('destination');
                $destinationInventory = PartInventory::where('part_code', $codepart)
                    ->where('site_id', $destinationSiteId)
                    ->first();

                // If the part doesn't exist in the destination site's inventory, return an error
                if (!$destinationInventory) {
                    $sitename = Site::findOrFail($destinationSiteId)->site_name;

                    // Create notification for the site to register the part
                    Notification::create([
                        'title' => 'Part Tidak Terdaftar',
                        'message' => "Part {$partname} (kode: {$codepart}) belum terdaftar di inventory site. Harap daftarkan part terlebih dahulu.",
                        'type' => 'stock',
                        'routes' => 'partgroupsite.index',
                        'site_id' => $destinationSiteId,
                        'from_site' => session('site_id'),
                        'is_read' => false,
                    ]);

                    // Restore the stock quantity that was reduced earlier
                    $partInventory->stock_quantity += $quantityUsed;
                    $partInventory->save();

                    return response()->json([
                        'success' => false,
                        'message' => "Gagal mengirim part ke site {$sitename}. Part {$partname} belum terdaftar di inventory site tersebut. Notifikasi telah dikirim ke site untuk mendaftarkan part."
                    ]);
                }

                // jika ke site masukkan ke transaksi terlebih dahulu
                $warehouseOutStock = StockTransaction::create([
                    'part_code' => $codepart,
                    'transaction_type' => 'outbound',
                    'destination_siteid' => $destinationSiteId,
                    'quantity_sent' => $request->input('quantity'),
                    'status' => 'intransit',
                    'notes' => $request->input('note'),
                    'surat_jalan_path' => $filePath,
                ]);

                $sitename = Site::findOrFail($destinationSiteId)->site_name;
                $keteranganlog = "Admin HO " . session(key: 'name') . " Menambahkan Mengirim Part  : " . $partname . " Kesite " . $sitename . " sebanyak " . $request->input('quantity');
                $pesan = "Berhasil Mengirim Kesite : {$sitename} , Periksa pada menu Transaksi Out";
                Notification::create([
                    'title' => 'Pengiriman Part',
                    'message' => "Part {$partname} sedang dalam pengiriman sebanyak {$request->input('quantity')}",
                    'type' => 'stock',
                    'routes' => 'sites.instock.index',
                    'site_id' => $destinationSiteId,
                    'from_site' => session('site_id'),
                    'is_read' => false,
                ]);
            }

            if ($warehouseOutStock) {
                // Log Inventory
                LogAktivitas::create([
                    'site_id' => session('site_id'),
                    'name' => session('name'),
                    'action' => 'Menambahkan Out Part',
                    'decription' => $keteranganlog,
                    'ip_address' => $request->ip(),
                    'table' => "Out HO Stock",
                ]);
                return response()->json(['success' => true, 'message' => $pesan]);
            } else {
                return response()->json(['success' => false, 'message' => 'Gagal disimpan']);
            }
        } catch (\Throwable $th) {
            return response()->json(['success' => false, 'message' => $th->getMessage()]);
        }
    }

    public function destroy(Request $request, $id)
    {
        try {
            // Memulai Transaksi Database
            DB::beginTransaction();

            $warehouseOutStock = WarehouseOutStock::findOrFail($id);
            $partInventoryId = $warehouseOutStock->part_inventory_id;
            $quantity = $warehouseOutStock->quantity;

            $partInventory = PartInventory::findOrFail($partInventoryId);
            $partInventory->stock_quantity += $quantity;
            $partInventory->save();

            $part_code = PartInventory::findOrFail($partInventoryId)->part_code;
            $partName = Part::findOrFail($part_code)->part_name;

            $warehouseOutStock->delete();

            DB::commit();
            LogAktivitas::create([
                'site_id' => session('site_id'),
                'name' => session('name'),
                'action' => 'Menghapus Data Out HO',
                'decription' => "Admin HO " . session('name') . " Menghapus Data Out Stock " . $partName . " sebanyak " . $quantity, // Perbaiki pesan log dan tambahkan kuantitas
                'ip_address' => $request->ip(),
                'table' => "Out Stock HO",
            ]);
            return Response()->json(['success' => true, 'message' => 'Part Out deleted successfully']);
        } catch (\Exception $e) {
            DB::rollback();
            return Response()->json(['success' => false, 'message' => 'Gagal menghapus data out: ' . $e->getMessage()], 500);
        }
    }

    public function getPartInventories()
    {
        $partInventories = PartInventory::with('part')->get();
        return response()->json($partInventories);
    }
}


