<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use App\Models\LogAktivitas;

class AdminSiteModeController extends Controller
{
    public function showModeSelection()
    {
        if (session('role') !== 'adminsite') {
            return redirect('/login')->withErrors(['username' => '<PERSON><PERSON><PERSON> !!']);
        }
        return view('auth.mode-select');
    }
    public function selectMode(Request $request)
    {
        $request->validate([
            'mode' => 'required|in:inventory,daily_report',
        ]);
        session(['access_mode' => $request->mode]);
        if ($request->mode == 'inventory') {
            return redirect()->route('sites.dashboard');
        } else {
            return redirect()->route('daily-reports.index');
        }
    }
}
