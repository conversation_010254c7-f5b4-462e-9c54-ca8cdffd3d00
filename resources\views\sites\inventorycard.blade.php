@extends('sites.content')
@section('title', 'Inventory Card')
@section('resourcesite')
@vite(['resources/js/site/inventorycard.js'])
@endsection
@php
    $currentSiteId = session('site_id');
@endphp

@section('contentsite')
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="page-title-box">
                <div class="page-title-right">
                    <ol class="breadcrumb m-0">
                        <li class="breadcrumb-item"><a href="{{ route('sites.dashboard') }}">Dashboard</a></li>
                        <li class="breadcrumb-item active">Inventory Card</li>
                    </ol>
                </div>
                <h4 class="page-title">Inventory Card</h4>
            </div>
        </div>
    </div>

    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-body">
                    <div class="row mb-3">
                        <div class="col-md-4">
                            <div class="input-group">
                                <input type="text" id="search-input" class="form-control" placeholder="Cari Part...">
                                <button class="btn btn-primary" type="button" id="search-button">
                                    <i class="mdi mdi-magnify"></i> Cari
                                </button>
                            </div>
                        </div>
                        <div class="col-md-2">
                            <select id="part-type-filter" class="btn btn-primary">
                                <option value="">Semua Tipe</option>
                                @foreach(\App\Models\Part::PART_TYPES as $type)
                                    <option value="{{ $type }}">{{ $type }}</option>
                                @endforeach
                            </select>
                        </div>
                        <div class="col-md-2">
                            <select id="status-filter" class="btn btn-secondary">
                                <option value="">Semua Status</option>
                                <option value="Ready">Ready</option>
                                <option value="Not Ready">Not Ready</option>
                                <option value="Medium">Medium</option>
                                <option value="Lainnya">Lainnya</option>
                            </select>
                        </div>
                        <div class="col"></div>
                    </div>

                    <!-- Hidden input to store site ID -->
                    <input type="hidden" id="current-site-id" value="{{ $currentSiteId }}">

                    <div class="table-responsive">
                        <table class="table table-bordered table-hover w-100">
                            <thead class="table-dark text-white">
                                <tr>
                                    <th class="p-2 sortable" data-sort="part_code" style="cursor: pointer;">
                                        Part Code <i class="mdi mdi-sort sort-icon"></i>
                                    </th>
                                    @if($currentSiteId === 'IMK')
                                    <th class="p-2 sortable" data-sort="itemcode" style="cursor: pointer;">
                                        Item Code <i class="mdi mdi-sort sort-icon"></i>
                                    </th>
                                    @endif
                                    <th class="p-2 sortable" data-sort="part_name" style="cursor: pointer;">
                                        Nama Part <i class="mdi mdi-sort sort-icon"></i>
                                    </th>
                                    <th class="p-2 sortable" data-sort="stock_quantity" style="cursor: pointer;">
                                        Stock Saat Ini <i class="mdi mdi-sort sort-icon"></i>
                                    </th>
                                    <th class="p-2 sortable" data-sort="min_stock" style="cursor: pointer;">
                                        Min Stock <i class="mdi mdi-sort sort-icon"></i>
                                    </th>
                                    <th class="p-2 sortable" data-sort="max_stock" style="cursor: pointer;">
                                        Max Stock <i class="mdi mdi-sort sort-icon"></i>
                                    </th>
                                    <th class="p-2 sortable" data-sort="price" style="cursor: pointer;">
                                        Harga <i class="mdi mdi-sort sort-icon"></i>
                                    </th>
                                    <th class="p-2 sortable" data-sort="status" style="cursor: pointer;">
                                        Status <i class="mdi mdi-sort sort-icon"></i>
                                    </th>
                                </tr>
                            </thead>
                            <tbody id="inventory-table-body">
                                <!-- Data will be loaded here via AJAX -->
                                <tr>
                                    <td colspan="{{ $currentSiteId === 'IMK' ? 8 : 7 }}" class="text-center">Loading data...</td>
                                </tr>
                            </tbody>
                        </table>
                    </div>

                    <div class="mt-3">
                        <div id="pagination-info" class="text-center mb-2 text-muted small">
                            <!-- Page info will be displayed here -->
                        </div>
                        <div id="inventory-pagination" class="d-flex justify-content-center">
                            <!-- Custom pagination will be rendered here by JavaScript -->
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection
