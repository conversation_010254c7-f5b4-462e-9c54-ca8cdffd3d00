<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\Hash;
use App\Models\User;
use App\Models\Site;

class KasirUserSeeder extends Seeder
{
    /**
     * Run the database seeder.
     */
    public function run(): void
    {
        // Get first available site
        $site = Site::first();
        
        if (!$site) {
            $this->command->error('No sites found. Please create sites first.');
            return;
        }

        // Create kasir user
        $kasir = User::create([
            'employee_id' => 'KASIR001',
            'site_id' => $site->site_id,
            'name' => 'Kasir Test',
            'username' => 'kasir',
            'password' => Hash::make('kasir123'),
            'email' => '<EMAIL>',
            'role' => 'kasir'
        ]);

        $this->command->info('Kasir user created successfully:');
        $this->command->info('Username: kasir');
        $this->command->info('Password: kasir123');
        $this->command->info('Role: kasir');
        $this->command->info('Site: ' . $site->site_name);
    }
}
