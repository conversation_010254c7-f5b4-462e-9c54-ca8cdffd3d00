/* Enhanced UI Styles for Superadmin Dashboard */

/* Accessibility-Enhanced Colors with 4.5:1+ contrast ratio */
:root {
    --a11y-primary: #1458a0;     /* Darker blue for better contrast */
    --a11y-success: #02acd3;     /* Darker green for better contrast */
    --a11y-danger: #c62828;      /* Darker red for better contrast */
    --a11y-warning: #f57c00;     /* Darker orange for better contrast */
    --a11y-info: #3e8fbe;        /* Darker light blue for better contrast */
    --a11y-secondary: #424242;   /* Darker gray for better contrast */
}

.bg-a11y-primary {
    background-color: var(--a11y-primary) !important;
    color: white !important;
}

.bg-a11y-success {
    background-color: var(--a11y-success) !important;
    color: white !important;
}

.bg-a11y-danger {
    background-color: var(--a11y-danger) !important;
    color: white !important;
}

.bg-a11y-warning {
    background-color: var(--a11y-warning) !important;
    color: white !important;
}

.bg-a11y-info {
    background-color: var(--a11y-info) !important;
    color: white !important;
}

.bg-a11y-secondary {
    background-color: var(--a11y-secondary) !important;
    color: white !important;
}

.text-a11y-primary {
    color: var(--a11y-primary) !important;
}

.text-a11y-success {
    color: var(--a11y-success) !important;
}

.text-a11y-danger {
    color: var(--a11y-danger) !important;
}

.text-a11y-warning {
    color: var(--a11y-warning) !important;
}

.text-a11y-info {
    color: var(--a11y-info) !important;
}

.text-a11y-secondary {
    color: var(--a11y-secondary) !important;
}
.main-header {
    padding: 12px 20px;
    background: #ffffff;
    box-shadow: 0 2px 8px rgba(0,0,0,0.08);
    position: sticky;
    top: 0;
    z-index: 1000;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.header-right {
    display: flex;
    align-items: center;
}

.logo-text {
    font-size: 1.4rem;
    font-weight: 700;
    color: #2a69a8;
    margin-right: 25px;
}

.month-picker-container .form-label {
    font-size: 0.9rem;
    margin-bottom: 2px;
    color: #6c757d;
}

.content-wrapper {
    padding: 25px 20px;
    max-width: 1400px;
    margin: 0 auto;
}

.total-summary-card {
    background: linear-gradient(135deg, #2a69a8 0%, #154274 100%);
    border: none;
    border-radius: 10px;
    overflow: hidden;
    width: 100%;
}

.total-invoice-info {
    flex: 1;
    padding-right: 20px;
}

.total-summary-card h2.display-4 {
    font-size: 2.8rem;
    letter-spacing: -0.5px;
    margin-bottom: 0;
    font-weight: 700;
}

.total-summary-card .badge {
    font-size: 0.85rem;
    padding: 0.35rem 0.65rem;
    font-weight: 500;
}

.total-summary-card .badge i {
    font-size: 1rem;
    vertical-align: -2px;
    margin-right: 2px;
}

/* Site Invoice Scroller Styles */
.site-invoice-scroller {
    width: 320px;
    height: 220px;
    position: relative;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 12px;
    overflow: hidden;
    box-shadow: 0 6px 20px rgba(0, 0, 0, 0.15);
    border: 1px solid rgba(255, 255, 255, 0.2);
    display: flex;
    flex-direction: column;
}

.scroller-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 10px 15px;
    background: rgba(255, 255, 255, 0.1);
    border-bottom: 1px solid rgba(255, 255, 255, 0.15);
}

.scroller-title {
    color: white;
    font-weight: 600;
    margin: 0;
    font-size: 16px;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.scroller-controls {
    display: flex;
    gap: 5px;
}

.btn-control {
    width: 28px;
    height: 28px;
    padding: 0;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.9);
    color: #2a69a8;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
    border: none;
    transition: all 0.2s ease;
}

.btn-control:hover {
    background: white;
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.25);
}

.btn-control:active {
    transform: translateY(0);
    box-shadow: 0 2px 3px rgba(0, 0, 0, 0.2);
}

.scroller-container {
    flex: 1;
    overflow: hidden;
    position: relative;
}

.scroller-content {
    position: absolute;
    width: 100%;
    animation: scrollAnimation 25s linear infinite;
    transition: transform 0.3s ease;
}

.site-invoice-item {
    display: flex;
    align-items: center;
    padding: 15px;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    background: rgba(255, 255, 255, 0.05);
    transition: all 0.3s;
    position: relative;
    cursor: pointer;
}

.site-invoice-item:hover {
    background: rgba(255, 255, 255, 0.15);
    transform: translateX(5px);
}

.site-invoice-item:after {
    content: '\F142';
    font-family: 'Material Design Icons';
    position: absolute;
    right: 10px;
    top: 50%;
    transform: translateY(-50%);
    color: rgba(255, 255, 255, 0.5);
    font-size: 16px;
    opacity: 0;
    transition: opacity 0.3s;
}

.site-invoice-item:hover:after {
    opacity: 1;
}

.site-icon {
    width: 42px;
    height: 42px;
    background: rgba(255, 255, 255, 0.2);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 15px;
    color: white;
    font-size: 20px;
    box-shadow: 0 3px 6px rgba(0, 0, 0, 0.1);
}

.site-info {
    flex: 1;
}

.site-name {
    font-weight: 700;
    color: white;
    font-size: 16px;
    margin-bottom: 3px;
}

.site-amount {
    color: rgba(255, 255, 255, 0.95);
    font-size: 18px;
    font-weight: 800;
    margin-bottom: 3px;
}

.site-comparison {
    color: rgba(255, 255, 255, 0.8);
    font-size: 12px;
    margin-bottom: 5px;
}

.site-comparison .text-success {
    color: #4cd964 !important;
}

.site-comparison .text-danger {
    color: #ff3b30 !important;
}

.site-comparison .text-muted {
    color: rgba(255, 255, 255, 0.6) !important;
    font-size: 11px;
}

.site-jasa {
    color: rgba(255, 255, 255, 0.7);
    font-size: 11px;
    margin-top: 2px;
}

.site-details {
    display: flex;
    gap: 12px;
    margin-top: 5px;
}

.detail-item {
    display: flex;
    align-items: center;
    background: rgba(255, 255, 255, 0.1);
    padding: 3px 8px;
    border-radius: 4px;
}

.detail-label {
    color: rgba(255, 255, 255, 0.8);
    font-size: 12px;
    margin-right: 4px;
}

.detail-value {
    color: white;
    font-weight: 600;
    font-size: 12px;
}

.site-percentage {
    margin-left: 10px;
}

.percentage-circle {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 700;
    font-size: 14px;
    color: white;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
}

.percentage-circle.high {
    background: linear-gradient(135deg, #28a745, #20c997);
}

.percentage-circle.medium {
    background: linear-gradient(135deg, #fd7e14, #ffc107);
}

@keyframes scrollAnimation {
    0% {
        transform: translateY(0);
    }
    100% {
        transform: translateY(-50%);
    }
}

.paused {
    animation-play-state: paused !important;
}

.site-card {
    border: 1px solid #e9ecef;
    border-radius: 8px;
    transition: box-shadow 0.2s;
    margin-bottom: 1.5rem;
    cursor: default; /* Make site cards non-clickable */
}

/* Removed hover effect as site cards are no longer clickable */

.card-header {
    padding: 0.9rem 1.25rem;
    background: #f8f9fa !important;
    border-bottom: 2px solid #2a69a8;
}

.card-header h4 {
    color: #2a69a8 !important;
    font-size: 1.1rem;
    font-weight: 600;
    margin-bottom: 0;
}

.progress {
    height: 36px;
    border-radius: 8px;
    background-color: #f0f3f5;
    overflow: visible;
    margin-bottom: 10px;
    box-shadow: inset 0 1px 3px rgba(0, 0, 0, 0.1);
}

.achievement-progress {
    height: 45px;
    border-radius: 10px;
    background-color: #f0f3f5;
    margin-bottom: 15px;
    box-shadow: inset 0 2px 5px rgba(0, 0, 0, 0.15);
}

/* Highlight effect for target achievement section */
@keyframes highlight-pulse {
    0% { box-shadow: 0 0 0 0 rgba(42, 105, 168, 0.7); }
    70% { box-shadow: 0 0 0 10px rgba(42, 105, 168, 0); }
    100% { box-shadow: 0 0 0 0 rgba(42, 105, 168, 0); }
}

.highlight-section {
    animation: highlight-pulse 1s ease-in-out 2;
    background-color: rgba(42, 105, 168, 0.05);
    border-radius: 10px;
    padding: 10px;
    margin: -10px;
    transition: all 0.3s ease;
}

.progress-bar {
    font-weight: 700;
    font-size: 1rem;
    border-radius: 8px;
    position: relative;
    overflow: visible;
    line-height: 36px;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
    transition: width 0.6s ease, background-color 0.3s ease;
}

.achievement-progress .progress-bar {
    font-size: 1.2rem;
    font-weight: 800;
    line-height: 45px;
    border-radius: 10px;
    box-shadow: 0 3px 6px rgba(0, 0, 0, 0.15);
}

.badge i {
    font-size: 1rem;
    vertical-align: -2px;
    margin-left: 3px;
}

.status-indicators {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 10px;
    margin: 1.2rem 0;
}

.status-card {
    padding: 0.8rem;
    border-radius: 6px;
    text-align: center;
    transition: transform 0.2s;
    display: flex;
    flex-direction: column;
    justify-content: center;
}

.status-card:hover {
    transform: scale(1.03);
}

/* Ensure status cards have consistent height */
.status-card h6 {
    font-size: 0.8rem;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.status-card h4 {
    font-size: 1.4rem;
    margin-bottom: 0;
}

.status-card .small {
    font-size: 0.75rem;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.contribution-highlight {
    background: #f8f9fa;
    border-radius: 8px;
    padding: 1rem;
    text-align: center;
    margin-top: 1.2rem;
    border: 1px solid #e9ecef;
}

.chart-container {
    margin-top: 2.5rem;
    padding: 1.5rem;
    background: #fff;
    border-radius: 10px;
    box-shadow: 0 2px 8px rgba(0,0,0,0.06);
}

/* Ensure proper spacing for chart and toggle buttons */
.card-body canvas {
    width: 100% !important;
    height: 100% !important;
}

/* Site Details Styles */
.site-details {
    background-color: #f8f9fa;
    border-radius: 8px;
    padding: 15px;
    box-shadow: inset 0 0 5px rgba(0,0,0,0.1);
}

.detail-title {
    font-size: 0.9rem;
    border-radius: 4px;
    margin-bottom: 10px;
}

.detail-section {
    background-color: white;
    border-radius: 6px;
    padding: 10px;
    box-shadow: 0 1px 3px rgba(0,0,0,0.1);
}

.site-details .table {
    font-size: 0.8rem;
}

.site-details .stats-icon {
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
}

/* Best Part Sales Styles */
.best-part-sales .card {
    transition: transform 0.2s;
}

.best-part-sales .card:hover {
    transform: translateY(-3px);
}

.best-part-sales .card-header {
    padding: 8px 12px;
    border-bottom: 1px solid rgba(255,255,255,0.2);
}

.best-part-sales .card-body {
    padding: 10px;
}

.best-part-sales .table th {
    font-size: 0.75rem;
    font-weight: 600;
    color: #495057;
}

.best-part-sales .table td {
    font-size: 0.75rem;
    padding: 4px 8px;
}

/* Category Tabs Styles */
.category-tabs {
    background-color: #f8f9fa;
    margin-bottom: 15px; /* Add margin below tabs */
    border-bottom: 1px solid #dee2e6;
}

.category-tab {
    background-color: transparent;
    color: #495057;
    font-weight: 500;
    transition: all 0.2s;
    position: relative;
    padding: 10px 15px; /* Increase padding for better clickability */
}

.category-tab:hover {
    background-color: rgba(0, 123, 255, 0.1);
}

.category-tab.active {
    background-color: #fff;
    color: #0d6efd;
    font-weight: 600;
}

.category-tab.active::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    width: 100%;
    height: 2px;
    background-color: #0d6efd;
}

.category-pane {
    background-color: #fff;
    border-radius: 0 0 0.25rem 0.25rem;
    padding-top: 15px; /* Add padding at the top */
    margin-top: 5px; /* Add margin at the top */
}

/* Modal Styles */
.modal-fullscreen {
    width: 100vw;
    max-width: none;
    height: 100vh;
    margin: 0;
}

.modal-fullscreen .modal-content {
    height: 100%;
    border: 0;
    border-radius: 0;
}

.modal-fullscreen .modal-header {
    padding: 15px 20px;
    border-bottom: 2px solid rgba(255,255,255,0.1);
}

.modal-fullscreen .modal-body {
    overflow-y: auto;
}

.site-details-container {
    max-width: 1400px;
    margin: 0 auto;
}

.site-details-header {
    border-bottom: 1px solid #e9ecef;
    padding-bottom: 15px;
}

.part-type-section {
    background-color: #f8f9fa;
    transition: background-color 0.2s;
}

.part-type-section:hover {
    background-color: #f0f3f6;
}

/* Empty State Styles */
.empty-state {
    text-align: center;
    padding: 2rem;
    background-color: #f8f9fa;
    border-radius: 8px;
    margin-bottom: 1.5rem;
}

.empty-state-icon {
    font-size: 3rem;
    margin-bottom: 1rem;
    color: var(--a11y-secondary);
}

.empty-state-title {
    font-size: 1.25rem;
    margin-bottom: 0.5rem;
    color: #333;
}

.empty-state-text {
    color: #6c757d;
    margin-bottom: 1.5rem;
}

.empty-state-cta {
    margin-top: 1rem;
}

/* Revenue by Type Styles */
.revenue-by-type-container {
    display: flex;
    flex-direction: column;
}

.revenue-card {
    background-color: #ffffff;
    border-radius: 10px;
    padding: 15px;
    box-shadow: 0 2px 6px rgba(0,0,0,0.05);
    transition: all 0.3s ease;
    cursor: pointer;
}

.revenue-card:hover {
    transform: translateY(-3px);
    box-shadow: 0 4px 12px rgba(0,0,0,0.1);
}

.revenue-card .stats-icon {
    width: 48px;
    height: 48px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    margin-right: 15px;
}

.revenue-card h5 {
    font-size: 1rem;
    font-weight: 600;
    margin-bottom: 5px;
}

.revenue-card h4 {
    font-size: 1.2rem;
    font-weight: 700;
}

.cursor-pointer {
    cursor: pointer;
}

/* Loading Overlay Styles */
.loading-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(255, 255, 255, 0.8);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 10;
    border-radius: 0 0 8px 8px;
}

.spinner-container {
    text-align: center;
}

.spinner-container p {
    margin-top: 10px;
    color: #2a69a8;
    font-weight: 500;
}

/* Settings Panel Styles */
#bestPartsSettingsPanel {
    transition: all 0.3s ease;
}

#bestPartsSettingsPanel .card {
    border: 1px solid #e9ecef;
    box-shadow: 0 2px 8px rgba(0,0,0,0.05);
}

#bestPartsSettingsPanel .form-label {
    font-weight: 500;
    color: #495057;
}

#bestPartsSettingsPanel .form-text {
    font-size: 0.75rem;
}

/* Skeleton Loader Styles */
.skeleton-container {
    background-color: #ffffff;
    width: 100%;
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    z-index: 999;
}

.skeleton-card {
    background-color: #fff;
    border-radius: 10px;
    overflow: hidden;
    box-shadow: 0 2px 8px rgba(0,0,0,0.06);
    margin-bottom: 1rem;
    border: 1px solid rgba(0, 0, 0, 0.1);
}

.skeleton-header {
    height: 45px;
    background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
    background-size: 200% 100%;
    animation: shimmer 1.5s infinite;
}

.skeleton-body {
    padding: 15px;
}

.skeleton-line {
    height: 15px;
    margin-bottom: 10px;
    background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
    background-size: 200% 100%;
    animation: shimmer 1.5s infinite;
    border-radius: 4px;
}

.skeleton-title {
    height: 25px;
    width: 60%;
}

.skeleton-chart {
    height: 200px;
    background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
    background-size: 200% 100%;
    animation: shimmer 1.5s infinite;
    border-radius: 4px;
    margin-bottom: 15px;
}

.skeleton-tabs {
    height: 40px;
    margin-bottom: 15px;
    background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
    background-size: 200% 100%;
    animation: shimmer 1.5s infinite;
    border-radius: 4px;
}

.skeleton-table {
    height: 150px;
    background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
    background-size: 200% 100%;
    animation: shimmer 1.5s infinite;
    border-radius: 4px;
}

.skeleton-progress {
    height: 20px;
    margin: 15px 0;
    background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
    background-size: 200% 100%;
    animation: shimmer 1.5s infinite;
    border-radius: 4px;
}

.skeleton-indicators {
    height: 60px;
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 10px;
    margin-top: 15px;
}

.skeleton-indicators > div {
    background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
    background-size: 200% 100%;
    animation: shimmer 1.5s infinite;
    border-radius: 4px;
}

@keyframes shimmer {
    0% {
        background-position: -200% 0;
    }
    100% {
        background-position: 200% 0;
    }
}

/* Responsive Table Styles */
.table-responsive {
    overflow-x: auto;
    -webkit-overflow-scrolling: touch;
}

.table-responsive table {
    min-width: 500px;
}

/*Monthly ReportStatus Badges */
.status-badge {
    display: inline-block;
    padding: 0.25rem 0.5rem;
    border-radius: 0.25rem;
    font-weight: bold;
    min-width: 30px;
    text-align: center;
}

.status-submitted {
    background-color: #f8f9fa;
    color: #6c757d;
    border: 1px solid #dee2e6;
}

.status-approved {
    background-color: #e3f2fd;
    color: #0d6efd;
    border: 1px solid #b6d4fe;
}

.status-rejected {
    background-color: #f8d7da;
    color: #dc3545;
    border: 1px solid #f5c2c7;
}

.status-done {
    background-color: #d1e7dd;
    color: #198754;
    border: 1px solid #badbcc;
}

/* Container Query Polyfill */
.container-query {
    container-type: inline-size;
    container-name: dashboard-container;
}

@container dashboard-container (max-width: 768px) {
    .row-cols-auto {
        flex-direction: column;
    }

    .col-container-md {
        width: 100%;
    }
}

@media (max-width: 768px) {
    .main-header {
        flex-direction: column;
        align-items: flex-start;
        padding: 12px 15px;
    }

    .header-left {
        margin-bottom: 10px;
    }

    .month-picker-container form {
        flex-direction: column;
        align-items: flex-start;
    }

    .month-picker-container .d-flex {
        margin-bottom: 10px;
        width: 100%;
    }

    .month-picker-container button {
        padding: 0.25rem 0.5rem;
        font-size: 0.875rem;
    }

    /* Total invoice card mobile styles */
    .total-summary-card .card-body {
        flex-direction: column;
    }

    .total-invoice-info {
        width: 100%;
        padding-right: 0;
        margin-bottom: 20px;
    }

    .total-summary-card h2.display-4 {
        font-size: 2.2rem;
    }

    .site-invoice-scroller {
        width: 100%;
        height: 180px;
    }

    .site-invoice-item {
        padding: 12px;
    }

    .site-icon {
        width: 36px;
        height: 36px;
        font-size: 18px;
    }

    .site-name {
        font-size: 14px;
    }

    .site-amount {
        font-size: 16px;
    }

    .status-indicators {
        grid-template-columns: repeat(2, 1fr);
    }

    /* Adjust font sizes for mobile */
    .status-card h6 {
        font-size: 0.7rem;
    }

    .status-card h4 {
        font-size: 1.2rem;
    }

    /* Force single column layout */
    .row > [class*="col-"] {
        width: 100%;
        margin-bottom: 15px;
    }

    /* Adjust chart height for mobile */
    #incomeVsTargetChart {
        height: 250px !important;
    }

    /* Make buttons more touch-friendly */
    .btn {
        padding: 0.5rem 0.75rem;
    }

    /* Adjust card padding */
    .card-body {
        padding: 0.75rem;
    }
}
