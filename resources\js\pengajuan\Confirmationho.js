import Swal from "sweetalert2";

// Global variables for pagination
let currentPage = 1;
const itemsPerPage = 15; // 15 items per page for requisitions table

// Get initial pagination data if available from the server
let paginationData = typeof window.initialPaginationData !== 'undefined' ? window.initialPaginationData : {
    current_page: 1,
    per_page: 15,
    last_page: 1,
    total: 0
};

// Fungsi untuk mengambil token CSRF dari meta tag
function getCsrfToken() {
    return document
        .querySelector('meta[name="csrf-token"]')
        .getAttribute("content");
}

// Fungsi untuk menampilkan pesan error menggunakan SweetAlert2
function showErrorAlert(message) {
    Swal.fire({
        icon: "error",
        title: "Oops...",
        text: message,
        confirmButtonText: "OK",
    });
}

// Fungsi untuk menampilkan pesan sukses menggunakan SweetAlert2
function showSuccessAlert(message) {
    Swal.fire({
        icon: "success",
        title: "Berhasil!",
        text: message,
        confirmButtonText: "OK",
    });
}

function validateForm(formData) {
    const qusent = formData.get('qusent');
    const status = formData.get('status_details');
    const file = formData.get('surat_jalan');

    if (!qusent || isNaN(qusent) || parseInt(qusent) <= 0) {
        showErrorAlert("Jumlah pengiriman harus diisi dengan angka yang valid dan lebih dari 0");
        return false;
    }

    if (!status) {
        showErrorAlert("Status harus dipilih");
        return false;
    }

    if (file && file.size > 5 * 1024 * 1024) {
        showErrorAlert("Ukuran file lampiran terlalu besar (maks. 5MB)");
        return false;
    }

    if (file && !['application/pdf', 'image/jpeg', 'image/png'].includes(file.type)) {
        showErrorAlert("Format file tidak didukung. Gunakan PDF, JPG, atau PNG");
        return false;
    }

    return true;
}

let selectedRequisitionId = null;
async function loadRequisitionDetails(requisitionId) {
    try {
        const response = await fetch(`/requisitions/${requisitionId}/details`);

        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }

        const data = await response.json();
        const tbody = document.getElementById("detailList");
        tbody.innerHTML = "";
        data.forEach((detail) => {

            const row = document.createElement("tr");
            row.classList.add("mt-4");
            row.innerHTML = `
                <td>${detail.part_name}</td>
                <td>${detail.notes || ""}</td>
                <td class="text-center">${detail.quantity}</td>
                <td class="text-center">${detail.quantity_confirm}</td>
                <td class="text-center">${detail.quantity_send}</td>
                <td><input type="number" min="1" max="${detail.quantity}" class="form-control qusent" value="${detail.quantity}" ${detail.status_details === "selesai" ? "disabled" : ""}></td>
                <td>
                    <select class="form-control statusSelect" ${detail.status_details === "selesai" ? "disabled" : ""}>
                        <option value="intransit" ${detail.status_details === "intransit"? "selected": ""}>intransit</option>
                        <option value="pending" ${detail.status_details === "pending"? "selected": ""}>pending</option>
                        <option value="dikirim sebagian" ${detail.status_details === "dikirim sebagian"? "selected": ""}>dikirim sebagian</option>
                        <option value="selesai" ${detail.status_details === "selesai"? "selected": ""}>selesai</option>
                        <option value="ditolak" ${detail.status_details === "ditolak" ? "selected" : ""}>Tolak pengajuan</option>
                    </select>
                </td>
                <td>
                    <textarea style="height:40px" type="text" class="form-control notes_ho" ${detail.status_details === "selesai" ? "disabled" : ""}>${detail.notes_ho || ""}</textarea>
                </td>
                <td class="max100">
                    <input type="file" class="suratJalanFile" accept="*" ${detail.status_details === "selesai" ? "disabled" : ""}>
                </td>
                <td><button class="btn btn-sm btn-primary updateBtn" ${detail.status_details === "selesai" ? "disabled" : ""}>Update</button></td>
            `;
            tbody.appendChild(row);
        });
    } catch (error) {
        console.error("Gagal mengambil data detail:", error);
        showErrorAlert(
            "Gagal mengambil data detail. Periksa koneksi jaringan Anda dan coba lagi."
        );
    }
}

// Function to create pagination item
function createPaginationItem(page, text, isActive = false) {
    const li = document.createElement('li');
    li.className = `page-item ${isActive ? 'active' : ''}`;

    const a = document.createElement('a');
    a.className = 'page-link';
    a.href = '#';
    a.textContent = text;
    a.dataset.page = page;

    li.appendChild(a);
    return li;
}

// Function to render pagination
function renderPagination(data) {
    try {
        const paginationContainer = document.getElementById('requisitions-pagination');
        if (!paginationContainer) {
            console.error('Pagination container not found');
            return;
        }

        // Update global pagination data
        paginationData = data;

        paginationContainer.innerHTML = '';

        if (data.last_page > 1) {
            const pagination = document.createElement('ul');
            pagination.className = 'pagination pagination-rounded  justify-content-center';

            // Previous button
            if (data.current_page > 1) {
                pagination.appendChild(createPaginationItem(data.current_page - 1, '«'));
            }

            // Page numbers
            for (let i = 1; i <= data.last_page; i++) {
                pagination.appendChild(createPaginationItem(i, i, i === data.current_page));
            }

            // Next button
            if (data.current_page < data.last_page) {
                pagination.appendChild(createPaginationItem(data.current_page + 1, '»'));
            }

            paginationContainer.appendChild(pagination);

            // Add event listeners to pagination links
            paginationContainer.querySelectorAll('.page-link').forEach(link => {
                link.addEventListener('click', function(e) {
                    e.preventDefault();
                    const page = parseInt(this.dataset.page);
                    loadRequisitions(page);
                });
            });
        }
    } catch (error) {
        console.error('Error rendering pagination:', error);
    }
}

// Format date to a more readable format (DD MMMM YYYY)
function formatDate(dateString) {
    const options = { year: 'numeric', month: 'long', day: 'numeric' };
    return new Date(dateString).toLocaleDateString('id-ID', options);
}

// Function to load requisitions with pagination
async function loadRequisitions(page = 1) {
    try {
        // Show loading indicator
        const requisitionList = document.getElementById('requisitionList');
        requisitionList.innerHTML = '<tr><td colspan="5" class="text-center">Loading...</td></tr>';

        // Get filter values
        const startDate = document.getElementById('start_date')?.value || '';
        const endDate = document.getElementById('end_date')?.value || '';

        // Build query parameters
        const params = new URLSearchParams({
            page: page,
            per_page: itemsPerPage,
            use_last_date: true // Enable the feature to use last date with data
        });

        // Add date filters if provided
        if (startDate && endDate) {
            params.append('start_date', startDate);
            params.append('end_date', endDate);
        }

        // Fetch data from server
        const response = await fetch(`/requisitions/data?${params}`);

        if (!response.ok) {
            throw new Error('Network response was not ok');
        }

        const data = await response.json();

        // Check if we're showing data from a different date
        const lastDataAlert = document.getElementById('last-data-alert');
        if (data.usedLastDate && data.lastDate) {
            // Update the date inputs to reflect the actual date being shown
            if (document.getElementById('start_date')) {
                document.getElementById('start_date').value = data.lastDate;
            }
            if (document.getElementById('end_date')) {
                document.getElementById('end_date').value = data.lastDate;
            }

            // Show the alert
            if (lastDataAlert) {
                lastDataAlert.style.display = 'block';
                lastDataAlert.querySelector('span').textContent =
                    `Tidak ada data untuk hari ini. Menampilkan data dari tanggal terakhir yang tersedia: ${formatDate(data.lastDate)}`;
            }
        } else {
            // Hide the alert if we're showing data from the selected date range
            if (lastDataAlert) {
                lastDataAlert.style.display = 'none';
            }
        }

        // Clear the table
        requisitionList.innerHTML = '';

        // Add rows to the table
        if (data.requisitions && data.requisitions.length > 0) {
            data.requisitions.forEach(req => {
                const row = document.createElement('tr');
                row.dataset.id = req.requisition_id;
                row.className = req.status === 'diajukan' ? 'table-primary' : (req.status === 'pending' ? 'table-warning' : '');

                row.innerHTML = `
                    <td>${req.site_name}</td>
                    <td>${req.title}</td>
                    <td>${req.notes}</td>
                    <td>${req.requisition_date}</td>
                    <td>${req.status.charAt(0).toUpperCase() + req.status.slice(1)}</td>
                `;

                // Add click event listener
                row.addEventListener('click', async function() {
                    const requisitionId = this.dataset.id;
                    if (!requisitionId) {
                        console.warn("Requisition ID tidak ditemukan pada baris ini.");
                        return;
                    }

                    selectedRequisitionId = requisitionId;

                    // Remove 'selected' class from all rows
                    document.querySelectorAll("#requisitionList tr").forEach(r => r.classList.remove("selected"));
                    this.classList.add("selected");

                    // Show detail section
                    document.getElementById("detailSection").style.display = "block";

                    // Load details
                    await loadRequisitionDetails(requisitionId);
                });

                requisitionList.appendChild(row);
            });
        } else {
            requisitionList.innerHTML = '<tr><td colspan="5" class="text-center">No requisitions found</td></tr>';
        }

        // Render pagination
        renderPagination({
            current_page: data.current_page,
            last_page: data.last_page,
            total: data.total,
            per_page: data.per_page
        });

    } catch (error) {
        console.error('Error loading requisitions:', error);
        const requisitionList = document.getElementById('requisitionList');
        requisitionList.innerHTML = '<tr><td colspan="5" class="text-center text-danger">Failed to load data. Please try again.</td></tr>';

        showErrorAlert('Failed to load requisitions. Please try again.');
    }
}


// Initialize the page
document.addEventListener('DOMContentLoaded', function() {
    // Set default date values to today
    const today = new Date().toISOString().split('T')[0];
    if (document.getElementById('start_date')) {
        document.getElementById('start_date').value = today;
    }
    if (document.getElementById('end_date')) {
        document.getElementById('end_date').value = today;
    }

    // Add event listeners for filter buttons
    if (document.getElementById('filter-button')) {
        document.getElementById('filter-button').addEventListener('click', function() {
            currentPage = 1; // Reset to first page when applying filters
            loadRequisitions(1);
        });
    }

    if (document.getElementById('reset-filter-button')) {
        document.getElementById('reset-filter-button').addEventListener('click', function() {
            // Reset date inputs to today
            if (document.getElementById('start_date')) {
                document.getElementById('start_date').value = today;
            }
            if (document.getElementById('end_date')) {
                document.getElementById('end_date').value = today;
            }

            currentPage = 1; // Reset to first page
            loadRequisitions(1);
        });
    }

    // Initialize pagination with data from server
    if (paginationData && paginationData.current_page) {
        // If we have initial data, render pagination
        renderPagination(paginationData);

        // Add click handlers to existing rows
        document.querySelectorAll("#requisitionList tr").forEach((row) => {
            row.addEventListener("click", async function () {
                const requisitionId = this.dataset.id;
                if (!requisitionId) {
                    console.warn("Requisition ID tidak ditemukan pada baris ini.");
                    return;
                }

                selectedRequisitionId = requisitionId;

                // Remove 'selected' class from all rows
                document.querySelectorAll("#requisitionList tr").forEach(r => r.classList.remove("selected"));
                this.classList.add("selected");

                // Show detail section
                document.getElementById("detailSection").style.display = "block";

                // Load details
                await loadRequisitionDetails(requisitionId);
            });
        });
    } else {
        // If no initial data, load from server
        loadRequisitions(1);
    }
});

document
    .getElementById("btntutupapproveAll")
    .addEventListener("click", async (e) => {
        document.getElementById("detailSection").style.display = "none";
    });

    document.getElementById("detailList").addEventListener("click", async (e) => {
        if (e.target.classList.contains("updateBtn")) {
            const row = e.target.closest("tr");

            if (!selectedRequisitionId) {
                showErrorAlert("Pilih requisition terlebih dahulu!");
                return;
            }

            const qusent = row.querySelector(".qusent");
            const statusSelect = row.querySelector(".statusSelect");
            const notesTextarea = row.querySelector(".notes_ho");
            const suratJalanFile = row.querySelector(".suratJalanFile");

            if (!statusSelect || !notesTextarea || !suratJalanFile || !qusent) {
                const missingElements = [];
                if (!statusSelect) missingElements.push("statusSelect");
                if (!notesTextarea) missingElements.push("notesTextarea");
                if (!suratJalanFile) missingElements.push("suratJalanFile");
                if (!qusent) missingElements.push("qusent");
                showErrorAlert(
                    `Terjadi kesalahan: Elemen ${missingElements.join(
                        ","
                    )} tidak ditemukan di baris ini.`
                );
                return;
            }

            const file = suratJalanFile.files[0];
            const MAX_FILE_SIZE = 5 * 1024 * 1024; // 5MB
            if (file && file.size > MAX_FILE_SIZE) {
                showErrorAlert("Ukuran file terlalu besar (maks. 5MB).");
                return;
            }

            const formData = new FormData();
            formData.append("requisition_id", selectedRequisitionId);
            formData.append("part_name",row.querySelector("td:first-child").textContent);
            formData.append("qusent", qusent.value);
            formData.append("status_details", statusSelect.value);
            formData.append("notes_ho", notesTextarea.value);
            if (file) {
                formData.append("surat_jalan", file);
            }

            if (!validateForm(formData)) {
                return;
            }

            try {
                const response = await fetch("/requisitions/update-detail", {
                    method: "POST",
                    headers: {
                        "X-CSRF-TOKEN": getCsrfToken(),
                    },
                    body: formData,
                });

                if (!response.ok) {
                    const errorText = await response.text();
                    throw new Error(
                        `HTTP error! status: ${response.status}, message: ${errorText}`
                    );
                }

                const data = await response.json();

                if (data.success) {
                    await loadRequisitionDetails(selectedRequisitionId);
                    showSuccessAlert(data.message || "Detail berhasil diperbarui !");
                } else {
                    showErrorAlert(
                        data.message || "Gagal memperbarui detail. Coba lagi nanti !"
                    );
                }
            } catch (error) {
                try {
                    const errorMessage = error.message.split('message: ')[1];
                    const parsedError = JSON.parse(errorMessage);
                    showErrorAlert(parsedError.message);
                } catch (parseError) {
                    showErrorAlert("Terjadi kesalahan saat memproses permintaan");
                }
            }
        }
    });
//Event listener untuk tombol Approve all
document.getElementById("approveAll").addEventListener("click", async () => {
    if (!selectedRequisitionId) {
        showErrorAlert("Pilih requisition terlebih dahulu!");
        return;
    }

    try {
        const csrfToken = getCsrfToken();

        const response = await fetch(
            `/requisitions/${selectedRequisitionId}/approve-all`,
            {
                method: "POST",
                headers: {
                    "Content-Type": "application/json",
                    "X-CSRF-TOKEN": csrfToken,
                },
                body: JSON.stringify({}), // Kirim body kosong atau data lain yang diperlukan
            }
        );

        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }

        const data = await response.json();

        if (data.success) {
            document
                .querySelectorAll("#detailList .statusSelect")
                .forEach((select) => {
                    select.value = "setujui";
                });

            const requisitionRow = document.querySelector(
                `#requisitionList tr[data-id="${selectedRequisitionId}"]`
            );

            if (requisitionRow) {
                requisitionRow.className = "";
                const statusCell =
                    requisitionRow.querySelector("td:nth-child(5)");
                if (statusCell) {
                    statusCell.textContent = "Disetujui";
                }
            }
            showSuccessAlert(
                data.message || "Semua detail berhasil disetujui!"
            );
        } else {
            showErrorAlert(
                data.message ||
                    "Gagal menyetujui semua detail. Coba lagi nanti."
            );
        }
    } catch (error) {
        console.error("Gagal menyetujui semua detail:", error);
        showErrorAlert(
            "Gagal menyetujui semua detail. Periksa koneksi jaringan Anda dan coba lagi."
        );
    } finally {
        document.getElementById("detailSection").style.display = "none";
    }
});
