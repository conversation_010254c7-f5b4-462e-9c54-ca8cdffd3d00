<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('invoices', function (Blueprint $table) {
            $table->id();
            $table->foreignId('unit_transaction_id')->constrained('unit_transactions')->onUpdate('cascade')->onDelete('cascade');
            $table->string('customer')->nullable();
            $table->string('location')->nullable();
            $table->string('no_invoice')->nullable();
            $table->string('sn')->nullable();
            $table->text('trouble')->nullable();
            $table->string('lokasi')->nullable();
            $table->date('tanggal_invoice')->nullable();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('invoices');
    }
};
