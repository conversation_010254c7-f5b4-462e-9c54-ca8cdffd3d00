# Panduan Pencatatan Aktivitas (Activity Logging)

## Pendahuluan

Dokumen ini berisi panduan untuk melakukan pencatatan aktivitas (logging) yang konsisten di seluruh aplikasi. Pencatatan aktivitas yang baik sangat penting untuk audit trail dan pemantauan aktivitas pengguna.

## Cara Menggunakan LogHelper

Untuk memastikan konsistensi dalam pencatatan aktivitas, gunakan class `LogHelper` yang telah disediakan. Class ini menyediakan beberapa method yang dapat digunakan untuk mencatat berbagai jenis aktivitas.

### 1. Mencatat Aktivitas Langsung (Direct Logging)

Gunakan method `createDirectLog` untuk mencatat aktivitas secara langsung:

```php
use App\Helpers\LogHelper;

LogHelper::createDirectLog(
    'Membuat Data Part',                                      // action
    "Admin HO " . session('name') . " menambahkan Data Part: " . $partName,  // description
    "Data Part",                                              // table
    $request                                                  // request object
);
```

### 2. Mencatat Aktivitas Pembuatan (Create)

Gunakan method `logCreate` untuk mencatat aktivitas pembuatan data:

```php
use App\Helpers\LogHelper;

LogHelper::logCreate(
    'Part',                // itemType
    $part->part_name,      // itemName
    'Parts',               // table
    $request,              // request object
    ['harga' => $part->price]  // additionalInfo (optional)
);
```

### 3. Mencatat Aktivitas Perubahan (Update)

Gunakan method `logUpdate` untuk mencatat aktivitas perubahan data:

```php
use App\Helpers\LogHelper;

LogHelper::logUpdate(
    'Part',                // itemType
    $part->part_name,      // itemName
    'Parts',               // table
    $request,              // request object
    ['harga' => 'dari ' . $oldPrice . ' menjadi ' . $newPrice]  // changes (optional)
);
```

### 4. Mencatat Aktivitas Penghapusan (Delete)

Gunakan method `logDelete` untuk mencatat aktivitas penghapusan data:

```php
use App\Helpers\LogHelper;

LogHelper::logDelete(
    'Part',                // itemType
    $part->part_name,      // itemName
    'Parts',               // table
    $request               // request object
);
```

### 5. Mencatat Aktivitas Transaksi Unit

Gunakan method `logUnitTransaction` untuk mencatat aktivitas transaksi unit:

```php
use App\Helpers\LogHelper;

LogHelper::logUnitTransaction(
    'membuat',             // action (membuat, mengubah status, menghapus)
    $unit->unit_type,      // unitType
    $transaction->status,  // status
    $partsArray,           // parts array
    $request               // request object
);
```

## Praktik Terbaik

1. **Gunakan Deskripsi yang Spesifik**: Selalu berikan deskripsi yang spesifik dan informatif tentang aktivitas yang dilakukan.

2. **Gunakan Nama Kolom yang Benar**: Selalu gunakan nama kolom `description` (bukan `decription`) untuk mencatat deskripsi aktivitas.

3. **Gunakan Helper Method**: Gunakan method yang disediakan oleh `LogHelper` daripada membuat log secara langsung dengan `LogAktivitas::create()`.

4. **Sertakan Informasi yang Relevan**: Sertakan informasi yang relevan seperti nama item, ID, perubahan yang dilakukan, dll.

5. **Konsistensi Format**: Gunakan format yang konsisten untuk deskripsi aktivitas, misalnya:
   - Untuk pembuatan: "User [nama] menambahkan [jenis item]: [nama item]"
   - Untuk perubahan: "User [nama] mengubah [jenis item]: [nama item] dengan perubahan [detail perubahan]"
   - Untuk penghapusan: "User [nama] menghapus [jenis item]: [nama item]"

## Contoh Implementasi

### Contoh 1: Mencatat Pembuatan Part

```php
// Di PartController.php
public function store(Request $request)
{
    // ... kode validasi dan pembuatan part ...
    
    $part = new Part([
        'part_code' => $cleanPartCode,
        'part_name' => $cleanPartName,
        // ... properti lainnya ...
    ]);
    $part->save();
    
    // Mencatat aktivitas
    LogHelper::createDirectLog(
        'Membuat Data Part',
        "Admin HO " . session('name') . " menambahkan Data Part: " . $cleanPartName,
        "Data Part",
        $request
    );
    
    // ... kode lainnya ...
}
```

### Contoh 2: Mencatat Perubahan Status

```php
// Di OutstocksiteController.php
public function updateStatus(Request $request, $id)
{
    // ... kode untuk mengubah status ...
    
    $oldStatus = $siteOutStock->status;
    $newStatus = $request->input('status');
    
    $siteOutStock->status = $newStatus;
    $siteOutStock->save();
    
    // Mencatat aktivitas
    LogHelper::createDirectLog(
        'Mengubah Status Out Part',
        "User " . session('name') . " mengubah status " . $part->part_name . " dari " . $oldStatus . " menjadi " . $newStatus,
        "Out stock",
        $request
    );
    
    // ... kode lainnya ...
}
```

## Kesimpulan

Dengan mengikuti panduan ini, kita dapat memastikan bahwa semua aktivitas dicatat dengan konsisten dan informatif di seluruh aplikasi. Hal ini akan sangat membantu dalam proses audit dan pemantauan aktivitas pengguna.
