# Timezone Configuration - Asia/Makassar

This document outlines the timezone configuration for the Portal PWB application, which is set to use **Asia/Makassar** timezone (UTC+8) throughout the entire application.

## Configuration Files

### 1. Environment Configuration (.env)
```env
APP_TIMEZONE=Asia/Makassar
DB_TIMEZONE=+08:00
```

### 2. Application Configuration (config/app.php)
```php
'timezone' => env('APP_TIMEZONE', 'Asia/Makassar'),
```

### 3. Database Configuration (config/database.php)
```php
'mysql' => [
    // ... other configurations
    'timezone' => env('DB_TIMEZONE', '+08:00'),
],
```

### 4. Service Provider (app/Providers/AppServiceProvider.php)
```php
public function boot(): void
{
    // Set the default timezone for the application
    date_default_timezone_set(config('app.timezone'));
    
    // Set Carbon locale to Indonesian for date formatting
    \Carbon\Carbon::setLocale('id');
    
    // ... other configurations
}
```

## JavaScript Date Utilities

### Global Date Utility Functions (resources/js/utils/dateUtils.js)
The application includes a comprehensive set of JavaScript utility functions to handle dates consistently without timezone conversion issues:

- `formatDateForInput(dateString)` - Format dates for HTML input fields
- `getTodayFormatted()` - Get today's date in YYYY-MM-DD format
- `getDateFromToday(days)` - Get date N days from today
- `formatDateIndonesian(dateInput)` - Format dates in Indonesian style
- `formatDateDDMMYYYY(dateInput)` - Format dates in DD/MM/YYYY format
- `addDaysToDate(dateString, days)` - Add days to a date
- `getFirstDayOfMonth()` - Get first day of current month
- `getLastDayOfMonth()` - Get last day of current month

### Usage in JavaScript
```javascript
// These functions are available globally via window.DateUtils
const today = window.DateUtils.getTodayFormatted();
const formattedDate = window.DateUtils.formatDateIndonesian('2025-01-15');
```

## Timezone-Safe Date Handling

### Problem: Timezone Conversion Issues
JavaScript's `new Date().toISOString().split('T')[0]` method can cause timezone conversion issues, making dates appear one day earlier or later than expected.

### Solution: Direct String Manipulation
Instead of creating Date objects that can be affected by timezone conversion, the application uses direct string manipulation:

```javascript
// ❌ Problematic (can cause timezone issues)
new Date(dateString).toISOString().split('T')[0]

// ✅ Timezone-safe approach
function formatDateForInput(dateString) {
    if (!dateString) return '';
    
    // If it's already in YYYY-MM-DD format, return as is
    if (/^\d{4}-\d{2}-\d{2}$/.test(dateString)) {
        return dateString;
    }
    
    // Handle different date formats from server
    if (dateString.includes('T')) {
        return dateString.split('T')[0];
    } else if (dateString.includes(' ')) {
        return dateString.split(' ')[0];
    }
    
    return dateString;
}
```

## Testing Timezone Configuration

### Debug Route
A debug route is available to verify timezone configuration:

```
GET /debug-timezone
```

This returns:
```json
{
    "app_timezone": "Asia/Makassar",
    "php_timezone": "Asia/Makassar",
    "current_time": "2025-01-15 14:30:00 +08",
    "current_time_iso": "2025-01-15T06:30:00.000000Z",
    "database_timezone": "+08:00",
    "env_app_timezone": "Asia/Makassar",
    "env_db_timezone": "+08:00"
}
```

## Implementation in Forms

### Invoice Date Fields
The invoice edit forms have been updated to use timezone-safe date handling:

```javascript
// Set invoice date without timezone conversion
if (document.getElementById('invoice-tanggal')) {
    document.getElementById('invoice-tanggal').value = formatDateForInput(invoice.tanggal_invoice);
}
```

### Modal Scripts
Modal scripts now check if they're handling new or existing records:

```javascript
modal.addEventListener('show.bs.modal', function(event) {
    const invoiceIdField = document.getElementById('invoice-id');
    const isNewInvoice = !invoiceIdField || !invoiceIdField.value;
    
    if (isNewInvoice) {
        // Set default values only for new invoices
        invoiceDateField.value = window.DateUtils.getTodayFormatted();
    }
    // For existing invoices, values are set by the edit function
});
```

## Best Practices

### 1. Always Use Utility Functions
Use the provided date utility functions instead of creating Date objects directly.

### 2. Server-Side Date Handling
Laravel automatically handles timezone conversion based on the configuration. Use Carbon for date operations:

```php
// This will use Asia/Makassar timezone
$now = now();
$formatted = $now->format('Y-m-d H:i:s');
```

### 3. Database Storage
Dates are stored in the database using the configured timezone (+08:00).

### 4. Client-Side Display
Use the utility functions to format dates for display without timezone conversion issues.

## Troubleshooting

### Issue: Dates Showing One Day Earlier
**Cause**: JavaScript timezone conversion when using `new Date().toISOString()`
**Solution**: Use the `formatDateForInput()` utility function

### Issue: Inconsistent Date Formatting
**Cause**: Different parts of the application using different date formatting methods
**Solution**: Use the standardized utility functions from `dateUtils.js`

### Issue: Database Timezone Mismatch
**Cause**: Database not configured with correct timezone
**Solution**: Ensure `DB_TIMEZONE=+08:00` is set in `.env` file

## Migration Notes

When updating existing code:

1. Replace direct `new Date()` usage with utility functions
2. Update modal scripts to handle new vs. existing records
3. Test date input fields to ensure they show correct values
4. Verify that date calculations (like due dates) work correctly

## Related Files

- `resources/js/utils/dateUtils.js` - Date utility functions
- `resources/js/sales/dashboard.js` - Sales dashboard with timezone-safe date handling
- `resources/views/sales/invoice-form-modal.blade.php` - Invoice form modal
- `app/Providers/AppServiceProvider.php` - Application service provider
- `config/app.php` - Application configuration
- `config/database.php` - Database configuration
- `.env` - Environment variables
