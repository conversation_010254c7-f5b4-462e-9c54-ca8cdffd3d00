<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Fix typo in units table: 'perkerjaan' -> 'pekerjaan'
        if (Schema::hasColumn('units', 'perkerjaan')) {
            Schema::table('units', function (Blueprint $table) {
                $table->renameColumn('perkerjaan', 'pekerjaan');
            });
        }
        
        // Fix typo in unit_transactions table: 'perkerjaan' -> 'pekerjaan'
        if (Schema::hasColumn('unit_transactions', 'perkerjaan')) {
            Schema::table('unit_transactions', function (Blueprint $table) {
                $table->renameColumn('perkerjaan', 'pekerjaan');
            });
        }
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // Reverse the changes if needed
        if (Schema::hasColumn('units', 'pekerjaan')) {
            Schema::table('units', function (Blueprint $table) {
                $table->renameColumn('pekerjaan', 'perkerjaan');
            });
        }
        
        if (Schema::hasColumn('unit_transactions', 'pekerjaan')) {
            Schema::table('unit_transactions', function (Blueprint $table) {
                $table->renameColumn('pekerjaan', 'perkerjaan');
            });
        }
    }
};
