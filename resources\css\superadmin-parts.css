/* Superadmin Parts Page Styles */

/* Extra wide modal */
@media (min-width: 1200px) {
    .modal-fullscreen-xl-down {
        max-width: 95%;
        width: 95%;
    }
}

/* Dashboard Container */
.dashboard-container {
    min-height: 100vh;
    display: flex;
    flex-direction: column;
    background-color: #f8f9fa;
}

/* Login Theme Header */
.login-theme-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 15px 20px;
    background-color: #fff;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
    position: sticky;
    top: 0;
    z-index: 100;
}

.company-logo {
    display: flex;
    align-items: center;
}

.company-logo img {
    height: 40px;
    margin-right: 10px;
}

.company-name {
    font-size: 1.2rem;
    font-weight: 700;
    margin: 0;
    color: #2a69a8;
}

.header-right {
    display: flex;
    align-items: center;
}

.btn-logout {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    padding: 0.375rem 0.75rem;
    font-size: 0.9rem;
    font-weight: 400;
    line-height: 1.5;
    color: #dc3545;
    background-color: transparent;
    border: 1px solid #dc3545;
    border-radius: 0.25rem;
    text-decoration: none;
    transition: all 0.15s ease-in-out;
}

.btn-logout:hover {
    color: #fff;
    background-color: #dc3545;
    border-color: #dc3545;
}

/* Content Styles */
.content-wrapper {
    padding: 20px;
    flex: 1;
    background-color: #f8f9fa;
    color: #333;
    min-height: 80vh;
}

.container-query {
    max-width: 1400px;
    margin: 0 auto;
    background-color: transparent;
}

/* Card Styles */
.card {
    border-radius: 10px;
    overflow: hidden;
    transition: all 0.3s ease;
    border: none;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
}

.card:hover {
    transform: translateY(-3px);
    box-shadow: 0 8px 16px rgba(0, 0, 0, 0.1) !important;
}

.card-header {
    padding: 12px 15px;
    border-bottom: 1px solid rgba(0, 0, 0, 0.05);
    background-color: #fff;
}

.card-body {
    padding: 15px;
}

.card-title {
    color: #2a69a8;
    font-weight: 600;
}

/* Table Styles */
.table {
    margin-bottom: 0;
}

.table th {
    font-weight: 600;
    background-color: #f8f9fa;
    border-top: none;
}

.table td, .table th {
    padding: 12px 15px;
    vertical-align: middle;
}

/* Part Status Card Styles */
.part-status-card {
    padding: 12px;
    border-radius: 8px;
    text-align: center;
    margin-bottom: 10px;
    transition: all 0.2s ease;
}

.part-status-card:hover {
    transform: scale(1.03);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.part-status-card.ready {
    background-color: rgba(40, 167, 69, 0.1);
    border: 1px solid rgba(40, 167, 69, 0.2);
    color: #28a745;
}

.part-status-card.not-ready {
    background-color: rgba(220, 53, 69, 0.1);
    border: 1px solid rgba(220, 53, 69, 0.2);
    color: #dc3545;
}

.part-status-card h3 {
    font-weight: 700;
    font-size: 1.8rem;
}

.part-status-card h6 {
    font-size: 0.85rem;
    font-weight: 600;
    text-transform: uppercase;
}

/* Chart Container */
.chart-container {
    height: auto;
    margin-top: 15px;
}

/* Skeleton Loader */
.skeleton-container {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 9999;
    display: flex;
    justify-content: center;
    align-items: center;
}

.skeleton-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(255, 255, 255, 0.8);
}

.skeleton-content {
    position: relative;
    text-align: center;
    z-index: 1;
}

/* Responsive Styles */
@media (max-width: 768px) {
    .login-theme-header {
        flex-direction: column;
        padding: 10px;
    }

    .company-logo {
        margin-bottom: 10px;
    }

    .company-name {
        font-size: 1rem;
    }

    .header-right {
        width: 100%;
        justify-content: center;
        flex-direction: column;
    }

    .month-picker-container {
        margin-bottom: 10px;
        width: 100%;
    }

    .month-picker-container form {
        width: 100%;
        justify-content: center;
    }

    .card {
        margin-bottom: 20px;
    }

    .table-responsive {
        border: none;
    }

    .part-status-card {
        padding: 8px;
    }

    .part-status-card h3 {
        font-size: 1.5rem;
    }

    .part-status-card h6 {
        font-size: 0.75rem;
    }

    /* Force single column layout */
    .row > [class*="col-"] {
        width: 100%;
        margin-bottom: 15px;
    }
}
