@extends('warehouse.content')
@section('contentho')
@section('title', 'History Out Part')
<div class="row bgwhite page-title-box shadow-kit mb-1">
    <div class="col d-flex align-items-center" style="max-width: fit-content;">
        <button class="button-menu-mobile text-blue-500 btn-sm">
            <i style="font-size: 30px;" class="mdi mdi-menu"></i>
        </button>
        <div class="text">
            <p class="font-bold m-0">{{ session('name') }}</p>
        </div>
    </div>
    <div class="col">
        <ul class="list-unstyled topnav-menu float-right mb-0">
            <li class="dropdown notification-list">
                <a class="nav-link dropdown-toggle" href="#" data-bs-toggle="dropdown" aria-expanded="false">
                    <i class="mdi mdi-bell-outline noti-icon"></i>
                    <span class="badge badge-danger badge-pill float-right badgenotif"></span>
                </a>
                <div class="dropdown-menu dropdown-menu-end dropdown-lg">
                    <div class="dropdown-item noti-title bg-primary">
                        <h5 class="m-0 text-white d-flex justify-content-between align-items-center">
                            Notifikasi
                            <a href="javascript:void(0);" class="text-white" id="clear-all">
                                <small></small>
                            </a>
                        </h5>
                    </div>
                    <div class="noti-scroll" id="notification-list">
                        <!-- Notifications will be dynamically inserted here -->
                    </div>
                </div>
            </li>
        </ul>
    </div>
</div>
<div class="row p-2">
    <div class="col bgwhite shadow-kit p-4 rounded-lg">
        <div class="shadow-kit pl-4 pr-4 pt-1">
            <h1 class="text-xl font-bold mb-4 text-uppercase">History Out Part</h1>
            <form id="filterForm">
                <div class="d-flex align-items-center">
                    <div>
                        <label for="site_id" class="form-label">Filter Site</label>
                        <select style="min-width: 100%;" id="idfilter" class="btn btn-primary">
                            <option value="all">All Data</option>
                            @foreach ($sites as $site)
                            <option value="{{ $site->site_id }}" data-destination-type="site">{{ $site->site_name }}</option>
                            @endforeach
                            @foreach ($customers as $customer)
                            <option value="{{ $customer->customer_id }}" data-destination-type="customer">{{ $customer->customer_name }}</option>
                            @endforeach
                        </select>
                    </div>
                    <div class="col-md-3">
                        <label for="start_date" class="form-label">Filter berdasarkan Tanggal</label>
                        <div class="d-flex">
                            <input type="date" id="start_date" class="form-control ml-2">
                            <input type="date" id="end_date" class="form-control ml-2">
                            <!-- Tombol filter dihapus -->
                        </div>
                    </div>
                    <div class="col-md-3 d-flex justify-content-start">
                    </div>
                </div>
            </form>
            <hr class="m-3">
            <h6 class="text-center h5 text-uppercase">Tabel history</h6>
            <div class="table-responsive">
                <table class="table table-bordered">
                    <thead class="table-dark text-white">
                        <tr>
                            <th class="p-2">No</th>
                            <th class="p-2">Part Code</th>
                            <th class="p-2">Part Name</th>
                            <th class="p-2">Tujuan</th>
                            <th class="p-2">Quantity</th>
                            <th class="p-2">Date Out</th>
                            <!-- <th class="p-2">Status</th> -->
                            <th class="p-2">Note</th>
                            <th class="p-2">Aksi</th>
                        </tr>
                    </thead>
                    <tbody id="out_stock_table">
                    </tbody>
                </table>
                <div id="outstock-pagination" class="mt-3">
                    <!-- Custom pagination will be rendered here by JavaScript -->
                </div>
            </div>
        </div>
    </div>

    <div class="col max500">
        <div class="shadow-kit  bgwhite rounded-lg">
            <div class="card-header h5 m-0 font-bold">Tambahkan Out Stock</div>
            <form class="p-4">
                <div class="mb-3">
                    <label for="part_inventory_autocomplete" class="form-label">Part Inventory</label>
                    <input autocomplete="off" type="text" id="part_inventory_autocomplete" class="form-control" placeholder="Search Part and Site">
                    <input type="hidden" id="id_partinventory" name="id_partinventory">
                    <ul id="autocomplete_list" class="autocomplete-list"></ul>
                </div>

                <div class="mb-3">
                    <label for="destination" class="form-label">Tujuan Barang Out</label>
                    <select id="destination" class="btn btn-sm btn-secondary">
                        <option value="1">Tanpa Keterangan</option>
                        @foreach ($sites as $site)
                        <option value="{{ $site->site_id }}">{{ $site->site_name }}</option>
                        @endforeach
                        @foreach ($customers as $customer)
                        <option value="{{ $customer->customer_id }}" data-destination-type="customer">{{ $customer->customer_name }}</option>
                        @endforeach
                    </select>
                </div>

                <div class="mb-3">
                    <label for="date_out" class="form-label">Date Out</label>
                    <input value="{{ date('Y-m-d') }}" type="date" id="date_out" class="form-control">
                </div>

                <div class="mb-3">
                    <label for="quantity" class="form-label">Quantity</label>
                    <input type="number" id="quantity" class="form-control" step="0.1" min="0.1">
                </div>

                <div class="mb-3">
                    <label for="quantity" class="form-label"> Lampiran <span class="text-blue-300">*Opsional</span> </label>
                    <input type="file" id="surat_jalan" class="form-control">
                </div>

                <div class="mb-3">
                    <label for="note" class="form-label">Note</label>
                    <textarea type="text" id="note" class="form-control"></textarea>
                </div>
                <button id="add_button" class="btn btn-success">Add Part Out</button>
            </form>
        </div>
        <div class="row">
            <div class="col mt-2 ml-2">
                <div class="card shadow-kit bgwhite p-3 mb-0">
                    <h5 class="h5 m-0 font-bold">Total Out-Stocks</h5>
                    <p id="totalout" class="h1 font-bold">ini tidak tampil</p>
                </div>
            </div>
            <div class="col p-2 mr-2">
                <div class="card shadow-kit bgwhite p-3 mb-0">
                    <h5>Download Laporan</h5>
                    <div class="d-between">
                        <button class="btn btn-sm btn-primary report-btn" data-type="in">
                            <i class="mdi mdi-file-import mr-1"></i> Export In
                        </button>
                        <button class="btn btn-sm btn-success report-btn ml-2" data-type="out">
                            <i class="mdi mdi-file-export"></i> Export Out
                        </button>
                    </div>
                </div>
            </div>
        </div>
        <div class="col p-0">
            <div class="card shadow-kit bgwhite p-4 mt-2">
                <h3>Aktifitas Terakhir</h3>
                <hr class="mt-2">
                <ul id="activityList">
                </ul>
            </div>
        </div>
    </div>
</div>
<div id="exportModal" class="modal" tabindex="-1" role="dialog" aria-labelledby="exportModalLabel" aria-hidden="true">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="font-bold text-uppercase" id="title">Filter Laporan</h5>
                <span class="close" aria-label="Close">&times;</span>
            </div>
            <form id="exportForm" method="GET">
                <input type="hidden" name="report_type" id="reportType">
                <div class="modal-body">
                    <div class="form-grid">
                        <div class="d-flex gap-3">
                            <div class="form-group flex-grow-1">
                                <label for="start_date">Tanggal Mulai</label>
                                <input type="date" class="form-control" name="start_date">
                            </div>
                            <div class="form-group flex-grow-1">
                                <label for="end_date">Tanggal Selesai</label>
                                <input type="date" class="form-control" name="end_date">
                            </div>
                        </div>
                        <div class="d-flex gap-3">
                            <div class="form-group flex-grow-1">
                                <label for="sort_by">Urutkan Berdasarkan</label>
                                <select class="custom-select" name="sort_by">
                                    <option value="date">Tanggal</option>
                                    <option value="part_name">Nama Part</option>
                                </select>
                            </div>
                            <div class="form-group flex-grow-1">
                                <label for="site_id">Site</label>
                                <select class="custom-select" name="site_id">
                                    <option value="all" selected>Semua Site</option>
                                    @foreach($sites as $site)
                                    <option value="{{ $site->site_id }}">{{ $site->site_name }}</option>
                                    @endforeach
                                </select>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <!-- <button type="button" class="btn btn-secondary" id="closeModal">Tutup</button> -->
                    <button type="button" class="btn btn-pink btn-rounded waves-effect waves-light" id="exportPdf">
                        <i class="mdi mdi-file-pdf-box mr-1"></i> Export PDF
                    </button>
                    <button type="button" class="btn btn-pink btn-rounded waves-effect waves-light" id="exportExcel">
                        <i class="mdi mdi-file-excel mr-1"></i> Export Excel
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>
@endsection
@section('resource')
@vite(['resources/js/adminho/outstock.js','resources/js/style.js'])
@vite('resources/js/laporan.js')
<style>
    /* Pagination styling */
    .pagination {
        display: flex;
        justify-content: center;
        list-style: none;
        padding: 0;
        margin-top: 15px;
    }

    .pagination .page-item {
        margin: 0 2px;
    }

    .pagination .page-item .page-link {
        color: #333;
        background-color: #fff;
        border: 1px solid #ddd;
        padding: 6px 12px;
        text-decoration: none;
        border-radius: 4px;
        cursor: pointer;
    }

    .pagination .page-item.active .page-link {
        color: #fff;
        background-color: #28a745;
        border-color: #28a745;
    }

    .pagination .page-item .page-link:hover {
        background-color: #f8f9fa;
    }

    #outstock-pagination {
        margin-top: 15px;
    }
</style>
<script>
    // Initial pagination data
    window.outstockPaginationData = {
        current_page: 1,
        per_page: 15,
        last_page: 1,
        total: 0
    };
</script>
@endsection