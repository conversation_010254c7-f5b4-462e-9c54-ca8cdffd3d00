<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('penawarans', function (Blueprint $table) {
            // Add customer_code column if it doesn't exist
            if (!Schema::hasColumn('penawarans', 'customer_code')) {
                $table->string('customer_code')->nullable()->after('customer');
            }
        });

        // Migrate existing data: try to match customer names to customer codes
        $this->migrateExistingData();
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('penawarans', function (Blueprint $table) {
            if (Schema::hasColumn('penawarans', 'customer_code')) {
                $table->dropColumn('customer_code');
            }
        });
    }

    /**
     * Migrate existing penawaran data to use customer codes
     */
    private function migrateExistingData(): void
    {
        // Get all penawarans with customer names
        $penawarans = DB::table('penawarans')->whereNotNull('customer')->get();

        foreach ($penawarans as $penawaran) {
            // Try to find a matching customer by name
            $customer = DB::table('customersales')
                ->where('nama_customer', $penawaran->customer)
                ->first();

            if ($customer) {
                // Update the penawaran with the customer code
                DB::table('penawarans')
                    ->where('id', $penawaran->id)
                    ->update(['customer_code' => $customer->code]);
            }
        }
    }
};
