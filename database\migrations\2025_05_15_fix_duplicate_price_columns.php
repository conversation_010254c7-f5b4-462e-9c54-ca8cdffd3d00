<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Fix duplicate price columns in part_inventories table
        if (Schema::hasColumn('part_inventories', 'price')) {
            Schema::table('part_inventories', function (Blueprint $table) {
                $table->dropColumn('price');
            });
            
            Schema::table('part_inventories', function (Blueprint $table) {
                $table->decimal('price', 15, 2)->default(0)->nullable();
            });
        }
        
        // Fix duplicate price columns in site_out_stocks table
        if (Schema::hasColumn('site_out_stocks', 'price')) {
            Schema::table('site_out_stocks', function (Blueprint $table) {
                $table->dropColumn('price');
            });
            
            Schema::table('site_out_stocks', function (Blueprint $table) {
                $table->decimal('price', 15, 2)->default(0)->nullable();
            });
        }
        
        // Fix duplicate price columns in warehouse_out_stocks table
        if (Schema::hasColumn('warehouse_out_stocks', 'price')) {
            Schema::table('warehouse_out_stocks', function (Blueprint $table) {
                $table->dropColumn('price');
            });
            
            Schema::table('warehouse_out_stocks', function (Blueprint $table) {
                $table->decimal('price', 15, 2)->default(0)->nullable();
            });
        }
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // No need to reverse as we're just fixing duplicate columns
    }
};
