<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="utf-8" />
    <title>Reset Password</title>
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta content="A fully featured admin theme which can be used to build CRM, CMS, etc." name="description" />
    <meta content="Coderthemes" name="author" />
    <meta http-equiv="X-UA-Compatible" content="IE=edge" />
    <!-- App favicon -->
    <link rel="shortcut icon" href="{{ asset('assets/images/logo-small.png') }}">

    <!-- App css -->
    @vite("resources/assets/css/bootstrap.min.css")
    @vite("resources/assets/css/icons.min.css")
    @vite("resources/assets/css/app.min.css")
    @vite("resources/css/app.css")
</head>

<body class="authentication-bg">
    <div class="account-pages mt-5 mb-5">
        <div class="container">
            <div class="row justify-content-center">
                <div class="col-md-8 col-lg-6 col-xl-5">
                    <div class="card">
                        <div class="card-body p-4">
                            <div class="text-center mb-4">
                                <h4 class="text-uppercase mt-0">Reset Password</h4>
                            </div>

                            @if(session('success'))
                            <div class="alert alert-success">
                                {{ session('success') }}
                            </div>
                            @endif

                            @if($errors->any())
                            <div class="alert alert-danger">
                                <ul class="mb-0">
                                    @foreach($errors->all() as $error)
                                    <li>{{ $error }}</li>
                                    @endforeach
                                </ul>
                            </div>
                            @endif

                            <form action="{{ route('password.update') }}" method="POST">
                                @csrf

                                <div class="mb-3">
                                    <label for="current_password" class="form-label">Password Lama</label>
                                    <input class="form-control" type="password" id="current_password" name="current_password" required placeholder="Masukkan password lama">
                                </div>

                                <div class="mb-3">
                                    <label for="password" class="form-label">Password Baru</label>
                                    <input class="form-control" type="password" id="password" name="password" required placeholder="Masukkan password baru">
                                </div>

                                <div class="mb-3">
                                    <label for="password_confirmation" class="form-label">Konfirmasi Password Baru</label>
                                    <input class="form-control" type="password" id="password_confirmation" name="password_confirmation" required placeholder="Konfirmasi password baru">
                                </div>

                                <div class="mb-3">
                                    <label for="token" class="form-label">Token Reset</label>
                                    <input class="form-control" type="text" id="token" name="token" required placeholder="Masukkan token dari admin">
                                    <small class="text-muted">Token reset password akan diberikan oleh admin melalui WhatsApp.</small>
                                </div>

                                <div class="mb-3 d-grid text-center">
                                    <button class="btn btn-primary" type="submit">Reset Password</button>
                                </div>
                            </form>

                            <div class="row mt-3">
                                <div class="col-12 text-center">
                                    @if(Auth::user()->role == 'adminho')
                                    <a href="{{ route('adminho.dashboard') }}" class="text-muted ml-1"><i class="mdi mdi-arrow-left mr-1"></i>Kembali ke Dashboard</a>
                                    @elseif(Auth::user()->role == 'adminsite')
                                    <a href="{{ route('sites.dashboard') }}" class="text-muted ml-1"><i class="mdi mdi-arrow-left mr-1"></i>Kembali ke Dashboard</a>
                                    @elseif(Auth::user()->role == 'sales')
                                    <a href="{{ route('sales.dashboard') }}" class="text-muted ml-1"><i class="mdi mdi-arrow-left mr-1"></i>Kembali ke Dashboard</a>
                                    @endif
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- App js -->
    @vite("resources/assets/js/vendor.min.js")
    @vite("resources/assets/js/app.min.js")
</body>

</html>
