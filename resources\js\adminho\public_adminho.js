function loadNotReadySiteNames() {
    fetch("/notreadysite", {
        method: "GET",
        headers: {
            "X-CSRF-TOKEN": document.querySelector('meta[name="csrf-token"]')
                .content,
            "Content-Type": "application/json",
        },
    })
        .then((response) => response.json())
        .then((data) => {
            const jumlahSiteElement = document.getElementById("jumalhsite");

            if (data.sites && data.sites.length > 0) {
                const siteNames = data.sites.join(", ");
                jumlahSiteElement.textContent = "Not Ready : " + siteNames;
                jumlahSiteElement.style.display = "block"; // Pastikan elemen muncul
            } else {
                jumlahSiteElement.style.display = "none"; // Sembunyikan jika kosong
            }
        })
        .catch((error) => {
            console.error("Error fetching not ready site names:", error);
        });
}

function updateJumlahPengajuan() {
    fetch("/pengajuan/getcount", {
        method: "GET",
        headers: {
            "X-CSRF-TOKEN": document.querySelector('meta[name="csrf-token"]')
                .content,
            "Content-Type": "application/json",
        },
    })
        .then((response) => response.json())
        .then((data) => {
            const jumlahPengajuanElement =
                document.getElementById("jumlahpengajuan");
            if (data.jumlah > 0) {
                jumlahPengajuanElement.textContent = data.jumlah;
                jumlahPengajuanElement.style.display = "flex";
            } else {
                jumlahPengajuanElement.style.display = "none";
            }
        })
        .catch((error) => {
            console.error("Error fetching data:", error);
        });
}

setInterval(loadNotReadySiteNames, 3000);
document.addEventListener("DOMContentLoaded", loadNotReadySiteNames);
setInterval(updateJumlahPengajuan, 3000);
updateJumlahPengajuan();
