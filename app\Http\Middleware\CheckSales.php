<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Symfony\Component\HttpFoundation\Response;

class CheckSales
{
    public function handle(Request $request, Closure $next): Response
    {
        if (session('role') !== 'sales') {
            return redirect('/login')->withErrors(['username' => 'Akses <PERSON> !!']);
        }
        return $next($request);
    }
}
