<?php

namespace App\Http\Controllers\Sales;

use App\Http\Controllers\Controller;
use App\Models\Part;
use App\Models\PartInventory;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Validator;

class PartListController extends Controller
{
    /**
     * Display the part list page
     */
    public function index()
    {
        return view('sales.part-list');
    }

    /**
     * Get parts data for AJAX request
     */
    public function getData(Request $request)
    {
        try {
            $page = $request->get('page', 1);
            $perPage = 10;
            $search = $request->get('search', '');

            // Query only the parts table directly - no joins or relationships
            $query = Part::select([
                'part_code',
                'part_name',
                'part_type',
                'price',
                'purchase_price',
                'eum',
                'bin_location',
                'created_at',
                'updated_at'
            ])->orderBy('part_name', 'asc');

            // Apply search filter
            if (!empty($search)) {
                $query->where(function($q) use ($search) {
                    $q->where('part_name', 'LIKE', "%{$search}%")
                      ->orWhere('part_code', 'LIKE', "%{$search}%")
                      ->orWhere('part_type', 'LIKE', "%{$search}%");
                });
            }

            // Get paginated results - each part will appear only once
            $parts = $query->paginate($perPage, ['*'], 'page', $page);

            // Return data directly without additional formatting
            return response()->json([
                'success' => true,
                'data' => $parts->items(),
                'pagination' => [
                    'current_page' => $parts->currentPage(),
                    'last_page' => $parts->lastPage(),
                    'per_page' => $parts->perPage(),
                    'total' => $parts->total(),
                    'from' => $parts->firstItem(),
                    'to' => $parts->lastItem()
                ]
            ]);

        } catch (\Exception $e) {
            Log::error('Error fetching parts data: ' . $e->getMessage());
            return response()->json([
                'success' => false,
                'message' => 'Terjadi kesalahan saat mengambil data part'
            ], 500);
        }
    }

    /**
     * Store a new part
     */
    public function store(Request $request)
    {
        try {
            // Validation rules
            $validator = Validator::make($request->all(), [
                'part_code' => 'required|string|max:50|unique:parts,part_code',
                'part_name' => 'required|string|max:255',
                'part_type' => 'required|in:AC,TYRE,FABRIKASI,PERLENGKAPAN AC,PERSEDIAAN LAINNYA',
                'price' => 'nullable|numeric|min:0',
                'purchase_price' => 'nullable|numeric|min:0',
                'eum' => 'nullable|string|max:5'
            ], [
                'part_code.required' => 'Kode part harus diisi',
                'part_code.unique' => 'Kode part sudah digunakan',
                'part_code.max' => 'Kode part maksimal 50 karakter',
                'part_name.required' => 'Nama part harus diisi',
                'part_name.max' => 'Nama part maksimal 255 karakter',
                'part_type.required' => 'Tipe part harus dipilih',
                'part_type.in' => 'Tipe part tidak valid',
                'price.numeric' => 'Harga harus berupa angka',
                'price.min' => 'Harga tidak boleh negatif',
                'purchase_price.numeric' => 'Harga beli harus berupa angka',
                'purchase_price.min' => 'Harga beli tidak boleh negatif',
                'eum.max' => 'EUM maksimal 5 karakter'
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Data tidak valid',
                    'errors' => $validator->errors()
                ], 422);
            }

            DB::beginTransaction();

            // Create new part
            $part = Part::create([
                'part_code' => $request->part_code,
                'part_name' => $request->part_name,
                'part_type' => $request->part_type,
                'price' => $request->price,
                'purchase_price' => $request->purchase_price,
                'eum' => $request->eum ?: 'EA',
                'bin_location' => '-' // Default bin location
            ]);

            // Automatically create WHO inventory record for the new part
            // This ensures the part can be used in quotations without validation errors
            PartInventory::create([
                'part_code' => $request->part_code,
                'site_part_name' => $request->part_name,
                'site_id' => 'WHO',
                'price' => $request->price,
                'priority' => false,
                'min_stock' => 0,
                'max_stock' => 0,
                'stock_quantity' => 0,
            ]);

            DB::commit();

            Log::info("New part created: {$part->part_code} - {$part->part_name} with WHO inventory record");

            return response()->json([
                'success' => true,
                'message' => 'Part berhasil ditambahkan',
                'data' => $part
            ]);

        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('Error creating part: ' . $e->getMessage());
            return response()->json([
                'success' => false,
                'message' => 'Terjadi kesalahan saat menambahkan part'
            ], 500);
        }
    }

    /**
     * Update part data
     */
    public function update(Request $request, $partCode)
    {
        try {
            // Validation rules
            $validationRules = [
                'part_code' => 'required|string|max:50',
                'part_name' => 'required|string|max:255',
                'part_type' => 'required|in:AC,TYRE,FABRIKASI,PERLENGKAPAN AC,PERSEDIAAN LAINNYA',
                'price' => 'nullable|numeric|min:0',
                'purchase_price' => 'nullable|numeric|min:0',
                'eum' => 'nullable|string|max:5'
            ];

            // If part_code is being changed, add unique validation
            if ($request->part_code !== $partCode) {
                $validationRules['part_code'] .= '|unique:parts,part_code';
            }

            $validator = Validator::make($request->all(), $validationRules, [
                'part_code.required' => 'Kode part harus diisi',
                'part_code.max' => 'Kode part maksimal 50 karakter',
                'part_code.unique' => 'Kode part sudah digunakan',
                'part_name.required' => 'Nama part harus diisi',
                'part_name.max' => 'Nama part maksimal 255 karakter',
                'part_type.required' => 'Tipe part harus dipilih',
                'part_type.in' => 'Tipe part tidak valid',
                'price.numeric' => 'Harga harus berupa angka',
                'price.min' => 'Harga tidak boleh negatif',
                'purchase_price.numeric' => 'Harga beli harus berupa angka',
                'purchase_price.min' => 'Harga beli tidak boleh negatif',
                'eum.max' => 'EUM maksimal 5 karakter'
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Data tidak valid',
                    'errors' => $validator->errors()
                ], 422);
            }

            DB::beginTransaction();

            // Find the part
            $part = Part::where('part_code', $partCode)->first();
            if (!$part) {
                return response()->json([
                    'success' => false,
                    'message' => 'Part tidak ditemukan'
                ], 404);
            }

            // Store old values for comparison
            $oldPartCode = $part->part_code;
            $oldPartName = $part->part_name;
            $oldPrice = $part->price;

            $newPartCode = $request->part_code;
            $newPartName = $request->part_name;
            $newPrice = $request->price;

            // Update part data first (this will trigger CASCADE updates for foreign keys)
            $part->update([
                'part_code' => $newPartCode,
                'part_name' => $newPartName,
                'part_type' => $request->part_type,
                'price' => $request->price,
                'purchase_price' => $request->purchase_price,
                'eum' => $request->eum
            ]);

            // Handle additional updates for part_code change if needed
            if ($oldPartCode !== $newPartCode) {
                // The foreign key constraints with ON UPDATE CASCADE automatically handle:
                // - part_inventories ✓ (has CASCADE)
                // - part_withdrawals ✓ (has CASCADE)
                // - stock_transactions ✓ (has CASCADE)
                // - manual_invoice_parts ✓ (has CASCADE)
                // - requisition_details ✓ (now has CASCADE after migration)

                // Update part_merges table (both old and new part codes) - has CASCADE but need both fields
                if (DB::getSchemaBuilder()->hasTable('part_merges')) {
                    DB::table('part_merges')->where('part_code_old', $oldPartCode)
                        ->update(['part_code_old' => $newPartCode]);

                    DB::table('part_merges')->where('part_code_new', $oldPartCode)
                        ->update(['part_code_new' => $newPartCode]);
                }

                Log::info("Part code updated from '{$oldPartCode}' to '{$newPartCode}'. All related records synchronized via CASCADE.");
            }

            // Critical Business Rule: Update site inventories if part name changed
            if ($oldPartName !== $newPartName) {
                PartInventory::where('part_code', $newPartCode)
                    ->whereNull('site_part_name') // Only update if no custom site name
                    ->update(['site_part_name' => $newPartName]);

                Log::info("Part name updated from '{$oldPartName}' to '{$newPartName}' for part {$newPartCode}. Site inventories synchronized.");
            }

            // Update WHO inventory price if price changed
            if ($oldPrice != $newPrice) {
                PartInventory::where('part_code', $newPartCode)
                    ->where('site_id', 'WHO')
                    ->update(['price' => $newPrice]);

                Log::info("Part price updated from '{$oldPrice}' to '{$newPrice}' for part {$newPartCode}. WHO inventory price synchronized.");
            }

            DB::commit();

            return response()->json([
                'success' => true,
                'message' => 'Data part berhasil diperbarui',
                'data' => $part->fresh()
            ]);

        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('Error updating part: ' . $e->getMessage());
            return response()->json([
                'success' => false,
                'message' => 'Terjadi kesalahan saat memperbarui data part'
            ], 500);
        }
    }

    /**
     * Get part types for dropdown
     */
    public function getPartTypes()
    {
        $partTypes = ['AC', 'TYRE', 'FABRIKASI', 'PERLENGKAPAN AC', 'PERSEDIAAN LAINNYA'];

        return response()->json([
            'success' => true,
            'data' => $partTypes
        ]);
    }
}
