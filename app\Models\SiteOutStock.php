<?php
namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use App\Models\UnitTransactionPart;

class SiteOutStock extends Model
{
    use HasFactory;

    protected $table = 'site_out_stocks';
    protected $primaryKey = 'site_out_stock_id';

    protected $fillable = [
        'part_inventory_id',
        'site_id',
        'employee_id',
        'unit_id',
        'price',
        'date_out',
        'quantity',
        'status',
        'po_number',
        'wo_number',
        'notes',
        'unit_transaction_parts_id',
    ];

    protected $casts = [
        'date_out' => 'datetime',
        'quantity' => 'float',
    ];

    public function partInventory()
    {
        return $this->belongsTo(PartInventory::class, 'part_inventory_id', 'part_inventory_id');
    }

    public function site()
    {
        return $this->belongsTo(Site::class, 'site_id', 'site_id');
    }

    public function user()
    {
        return $this->belongsTo(User::class, 'employee_id', 'employee_id');
    }

    public function unitTransactionPart()
    {
        return $this->belongsTo(UnitTransactionPart::class, 'unit_transaction_parts_id', 'id');
    }

    public function unit()
    {
        return $this->belongsTo(Unit::class, 'unit_id');
    }
}