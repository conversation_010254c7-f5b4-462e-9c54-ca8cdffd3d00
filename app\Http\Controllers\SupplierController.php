<?php

namespace App\Http\Controllers;

use Illuminate\Support\Str;
use Illuminate\Http\Request;
use App\Models\Supplier;
use Illuminate\Support\Facades\Validator;
use App\Helpers\LogHelper;

class SupplierController extends Controller
{
    public function index()
    {
        return view('warehouse.suppliermanagemen'); // Ubah nama view agar lebih deskriptif
    }

    public function getSuppliers(Request $request)
    {
        $perPage = $request->input('per_page', 10); // Default to 10 items per page
        $page = $request->input('page', 1);

        // Get total count
        $total = Supplier::count();

        // Get paginated data
        $suppliers = Supplier::skip(($page - 1) * $perPage)
                          ->take($perPage)
                          ->get();

        return response()->json([
            'data' => $suppliers,
            'current_page' => (int)$page,
            'per_page' => (int)$perPage,
            'last_page' => ceil($total / $perPage),
            'total' => $total
        ]);
    }

    public function store(Request $request)
    {
        try {
            $validator = Validator::make($request->all(), [
                'supplier_name' => 'required|string|max:255',
                'address' => 'nullable|string|max:255',
                'contact_person' => 'nullable|string|max:255',
            ]);

            $supplierId = 'SUP' . Str::random(20);
            while (Supplier::where('supplier_id', $supplierId)->exists()) {
                $supplierId = 'SID' . Str::random(5);
            }

            if ($validator->fails()) {
                return response()->json(['errors' => $validator->errors()], 422);
            }

            $data = [
                'supplier_name' => strtoupper($request->input('supplier_name')),
                'address' => strtoupper($request->input('address')),
                'contact_person' => strtoupper($request->input('contact_person')),
            ];

            $data['supplier_id'] = $supplierId;
            $supplier = Supplier::create($data);

            // Log the supplier creation
            LogHelper::logCreate('Supplier', $data['supplier_name'], 'Suppliers', $request);

            return response()->json($supplier, 201);
        } catch (\Throwable $th) {
            return response()->json(['errors' => $th->getMessage()], 500);
        }
    }

    public function update(Request $request, $supplierId)
    {
        $validator = Validator::make($request->all(), [
            'supplier_id' => 'required|string|max:50|unique:suppliers,supplier_id,' . $supplierId . ',supplier_id', // Validasi: harus ada, string, panjang maks 50, unik (kecuali ID saat ini)
            'supplier_name' => 'required|string|max:255',
            'address' => 'nullable|string|max:255',
            'contact_person' => 'nullable|string|max:255',
        ]);

        if ($validator->fails()) {
            return response()->json(['errors' => $validator->errors()], 422);
        }

        $supplier = Supplier::find($supplierId);
        if (!$supplier) {
            return response()->json(['error' => 'Supplier not found'], 404);
        }

        // Get original values for logging
        $originalName = $supplier->supplier_name;

        $supplier->update($request->all());

        // Log the supplier update
        $changes = [];
        if ($originalName != $supplier->supplier_name) {
            $changes[] = "supplier_name: {$originalName} → {$supplier->supplier_name}";
        }

        LogHelper::logUpdate('Supplier', $supplier->supplier_name, 'Suppliers', $request, $changes);

        return response()->json($supplier);
    }

    public function edit($supplierId) // Ubah nama variabel
    {
        $supplier = Supplier::find($supplierId);
        if (!$supplier) {
            return response()->json(['error' => 'Supplier not found'], 404);
        }
        return response()->json($supplier);
    }

    public function destroy(Request $request, $supplierId) // Ubah nama variabel
    {
        $supplier = Supplier::find($supplierId);
        if (!$supplier) {
            return response()->json(['error' => 'Supplier not found'], 404);
        }

        // Log the supplier deletion before deleting
        LogHelper::logDelete('Supplier', $supplier->supplier_name, 'Suppliers', $request);

        $supplier->delete();
        return response()->json(null, 204);
    }
}
