@extends('sites.content')
@section('title', 'Unit Recap')
@section('resourcesite')
@vite(['resources/js/unit-recap.js'])
@vite(['resources/css/unit-recap.css'])
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script src="https://cdn.jsdelivr.net/npm/chartjs-plugin-datalabels@2"></script>
<script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
@endsection

@section('contentsite')
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="page-title-box">
                <div class="page-title-right">
                    <ol class="breadcrumb m-0">
                        <li class="breadcrumb-item"><a href="{{ route('sites.dashboard') }}">Dashboard</a></li>
                        <li class="breadcrumb-item"><a href="{{ route('daily-reports.index') }}">Daily Reports</a></li>
                        <li class="breadcrumb-item active">Unit Recap</li>
                    </ol>
                </div>
                <h4 class="page-title">
                    Unit Recap Dashboard
                    <a href="{{ route('daily-reports.index') }}" class="btn btn-sm btn-secondary ms-2">
                        <i class="mdi mdi-arrow-left"></i> Back to Daily Reports
                    </a>
                </h4>
            </div>
        </div>
    </div>

    <div class="row">
        <!-- Chart Section -->
        <div class="col-md-8">
            <div class="card">
                <div class="card-body">
                    <div class="row mb-3">
                        <div class="col-md-8">
                            <h4 class="header-title">Problem Component Analysis</h4>
                        </div>
                        <div class="col-md-4">
                            <div class="row">
                                <div class="col-6">
                                    <input type="date" class="form-control form-control-sm" id="chart-start-date" placeholder="Start Date">
                                </div>
                                <div class="col-6">
                                    <input type="date" class="form-control form-control-sm" id="chart-end-date" placeholder="End Date">
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Loading Skeleton for Chart -->
                    <div id="chart-loading-skeleton" class="d-none">
                        <div class="skeleton-loader">
                            <div class="skeleton-chart"></div>
                        </div>
                    </div>

                    <!-- Chart Container -->
                    <div class="chart-container" style="position: relative; height: 400px;">
                        <canvas id="problemComponentChart"></canvas>
                    </div>
                </div>
            </div>
        </div>

        <!-- Unit Selection Section -->
        <div class="col-md-4">
            <div class="card">
                <div class="card-body">
                    <h4 class="header-title mb-3">Unit Search</h4>

                    <!-- Unit Search -->
                    <div class="mb-1">
                        <label for="unit-search" class="form-label">Search Unit</label>
                        <div class="position-relative">
                            <input type="text" class="form-control" id="unit-search" placeholder="Type unit code or type..." autocomplete="off">
                            <div id="unit-dropdown" class="dropdown-menu w-100" style="display: none; max-height: 200px; overflow-y: auto;">
                                <!-- Search results will appear here -->
                            </div>
                        </div>
                    </div>

                    <!-- Predicted Next Service Date Section -->
                    <div id="service-prediction-section">
                        <div class="row p-2">
                            <div class="col"><small class="fw-bold">Predict Next Service:</small></div>
                            <div class="col"><div class="fw-bold" id="predicted-service-date">-</div></div>
                            <div class="col"><small class="text-muted">(<span id="days-until-service">-</span> hari lagi)</small></div>
                        </div>
                        <hr class="mb-2">
                        <div class="card border-info mb-3" style="display: none;">
                            <div class="card-header bg-info text-white">
                                <h6 class="mb-0">
                                    <i class="mdi mdi-calendar-clock"></i> Prediksi Service Berikutnya
                                </h6>
                            </div>
                            <div class="card-body">
                                <!-- Loading indicator -->
                                <div id="prediction-loading" class="text-center py-2 d-none">
                                    <div class="spinner-border spinner-border-sm text-primary" role="status">
                                        <span class="visually-hidden">...</span>
                                    </div>
                                    <p class="mt-1 text-muted small">Menghitung prediksi...</p>
                                </div>

                                <!-- Prediction content -->
                                <div id="prediction-content" class="d-none">
                                    <div class="row">
                                        <div class="col-6">
                                            <small class="text-muted">HM Saat Ini:</small>
                                            <div class="fw-bold" id="current-hm">-</div>
                                        </div>
                                        <div class="col-6">
                                            <small class="text-muted">Tanggal Terakhir:</small>
                                            <div class="fw-bold" id="last-service-date">-</div>
                                        </div>
                                    </div>
                                    <hr class="my-2">
                                    <div class="row">
                                        <div class="col-6">
                                            <small class="text-muted">Service Pada HM:</small>
                                            <div class="fw-bold text-warning" id="next-service-hm">-</div>
                                        </div>
                                        <div class="col-6">
                                            <small class="text-muted">Sisa HM:</small>
                                            <div class="fw-bold text-warning" id="remaining-hm">-</div>
                                        </div>
                                    </div>
                                    <hr class="my-2">
                                    <div class="text-center">
                                        <small class="text-muted">Prediksi Tanggal Service:</small>
                                        <div class="fw-bold text-primary fs-5" id="predicted-service-date">-</div>
                                        <small class="text-muted">(<span id="days-until-service">-</span> hari lagi)</small>
                                    </div>
                                </div>

                                <!-- No data message -->
                                <div id="prediction-no-data" class="text-center py-2 d-none">
                                    <i class="mdi mdi-information-outline text-muted" style="font-size: 24px;"></i>
                                    <p class="text-muted mt-1 mb-0 small">Tidak ada data HM untuk unit ini</p>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Open Backlogs Section -->
                    <div id="unit-backlogs-section" class="d-none">
                        <h5 class="text-primary fw-bold">
                            <i class="mdi mdi-alert-circle"></i> Open Backlogs
                        </h5>
                        <div id="unit-backlogs-list">
                            <!-- Backlog items will be loaded here -->
                        </div>
                    </div>

                    <!-- Report Dates List -->
                    <div id="report-dates-container" class="d-none">
                        <div class="d-flex justify-content-between align-items-center mb-2">
                            <h6 class="mb-0">Tanggal Service Unit</h6>
                            <button type="button" class="btn btn-primary btn-sm" id="btn-show-all-reports">
                                <i class="mdi mdi-calendar-multiple"></i> All
                            </button>
                        </div>
                        <div id="report-dates-list" class="list-group" style="max-height: 300px; overflow-y: auto;">
                            <!-- Dates will be loaded here -->
                        </div>
                    </div>

                    <!-- Loading indicator -->
                    <div id="dates-loading" class="text-center py-3 d-none">
                        <div class="spinner-border spinner-border-sm text-primary" role="status">
                            <span class="visually-hidden">...</span>
                        </div>
                        <p class="mt-2 text-muted small">...</p>
                    </div>

                    <!-- Empty state -->
                    <div id="no-dates-message" class="text-center py-4 d-none">
                        <i class="mdi mdi-calendar-blank" style="font-size: 48px; color: #ccc;"></i>
                        <p class="text-muted mt-2">No reports found for this unit</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Problem Component Details Modal -->
<div class="modal fade" id="problem-component-modal" tabindex="-1" aria-labelledby="problem-component-modal-label" aria-hidden="true">
    <div class="modal-dialog modal-fullscreen">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="problem-component-modal-label">Problem Component Details</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <!-- Loading spinner -->
                <div id="modal-loading" class="text-center py-4">
                    <div class="spinner-border text-primary" role="status">
                        <span class="visually-hidden">...</span>
                    </div>
                    <p class="mt-2 text-muted">...</p>
                </div>

                <!-- Content will be loaded here -->
                <div id="modal-content" class="d-none">
                    <div class="table-responsive">
                        <table class="table table-striped table-hover">
                            <thead class="table-dark">
                                <tr>
                                    <th>Date</th>
                                    <th>Unit</th>
                                    <th>HM</th>
                                    <th>Problem</th>
                                    <th>Problem Description</th>
                                    <th>Job Description</th>
                                    <th>Start</th>
                                    <th>Finish</th>
                                    <th>Shift</th>
                                    <th>Down Time</th>
                                    <th>Technicians</th>
                                </tr>
                            </thead>
                            <tbody id="modal-table-body">
                                <!-- Data will be loaded here -->
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
            </div>
        </div>
    </div>
</div>

<!-- Unit Date Reports Modal -->
<div class="modal fade" id="unit-date-modal" tabindex="-1" aria-labelledby="unit-date-modal-label" aria-hidden="true">
    <div class="modal-dialog modal-fullscreen">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="unit-date-modal-label">Unit Reports</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <!-- Loading spinner -->
                <div id="unit-modal-loading" class="text-center py-4">
                    <div class="spinner-border text-primary" role="status">
                        <span class="visually-hidden">...</span>
                    </div>
                    <p class="mt-2 text-muted">...</p>
                </div>

                <!-- Content will be loaded here -->
                <div id="unit-modal-content" class="d-none">
                    <div class="table-responsive">
                        <table class="table table-striped table-hover">
                            <thead class="table-dark">
                                <tr>
                                    <th>Date</th>
                                    <th>Unit</th>
                                    <th>HM</th>
                                    <th>Problem</th>
                                    <th>Problem Component</th>
                                    <th>Problem Description</th>
                                    <th>Job Description</th>
                                    <th>Start</th>
                                    <th>Finish</th>
                                    <th>Shift</th>
                                    <th>Down Time</th>
                                    <th>Technicians</th>
                                </tr>
                            </thead>
                            <tbody id="unit-modal-table-body">
                                <!-- Data will be loaded here -->
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
            </div>
        </div>
    </div>
</div>
@endsection