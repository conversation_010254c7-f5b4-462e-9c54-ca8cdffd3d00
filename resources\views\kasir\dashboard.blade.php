@extends('kasir.contentkasir')

@section('title', 'Dashboard Kasir - Portal PWB')

@section('resourcekasir')
@vite(['resources/js/kasir/dashboard.js'])
<style>
    .w-fit-content {
        width: fit-content;
    }
    .btn{
        font-size: 11px;
    }
    .btn-warning{
        background-color:rgb(242, 215, 132);
        color: #000;
    }
    .bg-warning{
        background-color:rgb(240, 250, 150);
        color: #0f0187;
    }

    /* Sortable table styles */
    .sortable {
        cursor: pointer;
        position: relative;
    }

    .sortable:hover {
        background-color: rgba(0, 0, 0, 0.05);
    }

    .sort-icon {
        font-size: 11px;
        margin-left: 5px;
        opacity: 0.5;
    }

    .sortable.asc .sort-icon {
        opacity: 1;
        transform: rotate(180deg);
    }

    .sortable.desc .sort-icon {
        opacity: 1;
        transform: rotate(0deg);
    }
</style>
@endsection

@section('contentkasir')
<div class="content">
    <div class="container-fluid">
        <div class="row">
            <div class="col-md-12">
                <div class="bgwhite shadow-kit rounded-lg mb-4">
                    <div class="card-header bg-primary text-white d-flex justify-content-between align-items-center">
                        <div class="d-flex align-items-center">
                            <h5 class="mb-0 font-bold text-uppercase text-white me-3">Dashboard Kasir</h5>
                        </div>
                        <div class="d-flex">
                            <input type="date" id="start-date" class="form-control form-control-sm mr-2" value="{{ $startDate }}">
                            <input type="date" id="end-date" class="form-control form-control-sm mr-2" value="{{ $endDate }}">
                            <button type="button" id="filter-btn" class="btn btn-sm btn-light">
                                <i class="mdi mdi-filter"></i> Filter
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Summary Cards -->
        <div class="row">
            <div class="col-md-6 mb-3">
                <div class="bgwhite shadow-kit rounded-lg h-100">
                    <div class="card-header bg-danger text-white">
                        <h5 class="mb-0 font-bold"><i class="mdi mdi-arrow-up-bold"></i> Total Pengeluaran</h5>
                    </div>
                    <div class="card-body text-center">
                        <h3 class="text-danger mb-0" id="total-pengeluaran">
                            Rp {{ number_format($totalPengeluaran, 0, ',', '.') }}
                        </h3>
                        <small class="text-muted">Periode: {{ \Carbon\Carbon::parse($startDate)->format('d/m/Y') }} - {{ \Carbon\Carbon::parse($endDate)->format('d/m/Y') }}</small>
                    </div>
                </div>
            </div>

            <div class="col-md-6 mb-3">
                <div class="bgwhite shadow-kit rounded-lg h-100">
                    <div class="card-header bg-success text-white">
                        <h5 class="mb-0 font-bold"><i class="mdi mdi-arrow-down-bold"></i> Total Penerimaan</h5>
                    </div>
                    <div class="card-body text-center">
                        <h3 class="text-success mb-0" id="total-penerimaan">
                            Rp {{ number_format($totalPenerimaan, 0, ',', '.') }}
                        </h3>
                        <small class="text-muted">Periode: {{ \Carbon\Carbon::parse($startDate)->format('d/m/Y') }} - {{ \Carbon\Carbon::parse($endDate)->format('d/m/Y') }}</small>
                    </div>
                </div>
            </div>
        </div>

        <div class="row">
            <!-- Daftar Transaksi -->
            <div class="col-md-8 mb-3">
                <div class="bgwhite shadow-kit rounded-lg">
                    <div class="card-header bg-info text-white d-flex justify-content-between align-items-center">
                        <h5 class="mb-0 font-bold"><i class="mdi mdi-format-list-bulleted"></i> Daftar Transaksi Terakhir</h5>
                        <div class="d-flex">
                            <select id="type-filter" class="btn btn-primary me-2" style="width: auto;">
                                <option value="">Semua Jenis</option>
                                <option value="pengeluaran">Pengeluaran</option>
                                <option value="penerimaan">Penerimaan</option>
                            </select>
                            <input type="text" id="search-input" class="form-control form-control-sm" placeholder="Cari..." style="width: 200px;">
                        </div>
                    </div>
                    <div class="card-body">
                        <!-- Skeleton Loader -->
                        <div id="skeleton-loader" class="d-none">
                            @for($i = 0; $i < 5; $i++)
                            <div class="skeleton skeleton-text mb-2"></div>
                            @endfor
                        </div>

                        <!-- Transactions Table -->
                        <div class="table-responsive" id="transactions-container">
                            <table class="table table-bordered table-hover" id="transactions-table">
                                <thead class="bg-light">
                                    <tr>
                                        <th width="5%">No</th>
                                        <th width="10%">Jenis</th>
                                        <th width="25%">Deskripsi</th>
                                        <th width="15%">Jumlah</th>
                                        <th width="15%">Tanggal</th>
                                        <th width="10%">Lampiran</th>
                                        <th width="10%">Aksi</th>
                                    </tr>
                                </thead>
                                <tbody id="transactions-table-body">
                                    <!-- Data will be loaded via AJAX -->
                                </tbody>
                            </table>
                        </div>

                        <!-- Empty State -->
                        <div id="empty-state" class="empty-state d-none">
                            <img src="{{ asset('assets/images/empty-transactions.svg') }}" alt="Tidak ada transaksi" onerror="this.style.display='none'">
                            <h5>Belum ada transaksi</h5>
                            <p>Mulai dengan menambahkan transaksi pertama Anda</p>
                        </div>

                        <!-- Pagination -->
                        <div class="d-flex justify-content-between align-items-center mt-3">
                            <div>
                                <span id="entries-info">Menampilkan 0 dari 0 transaksi</span>
                            </div>
                            <div id="pagination-container">
                                <!-- Pagination will be loaded dynamically -->
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Form Input Transaksi -->
            <div class="col-md-4 mb-3">
                <div class="bgwhite shadow-kit rounded-lg">
                    <div class="card-header bg-primary text-white d-flex justify-content-between align-items-center">
                        <h5 class="mb-0 font-bold"><i class="mdi mdi-plus-circle"></i> Input Transaksi</h5>
                        <button type="button" id="toggle-form-btn" class="btn btn-sm btn-light">
                            <i class="mdi mdi-eye-off"></i>
                        </button>
                    </div>
                    <div class="card-body" id="transaction-form-container">
                        <form id="transaction-form" enctype="multipart/form-data">
                            <div class="mb-3">
                                <label for="type" class="form-label">Jenis Transaksi <span class="text-danger">*</span></label>
                                <select id="type" name="type" class="form-select" required>
                                    <option value="">Pilih Jenis</option>
                                    <option value="pengeluaran">Pengeluaran</option>
                                    <option value="penerimaan">Penerimaan</option>
                                </select>
                            </div>

                            <div class="mb-3">
                                <label for="description" class="form-label">Deskripsi <span class="text-danger">*</span></label>
                                <textarea id="description" name="description" class="form-control" rows="3" placeholder="Masukkan deskripsi transaksi..." required></textarea>
                            </div>

                            <div class="mb-3">
                                <label for="amount" class="form-label">Jumlah (Rp) <span class="text-danger">*</span></label>
                                <input type="text" id="amount" name="amount" class="form-control currency-input" placeholder="0" required>
                                <small class="text-muted">Masukkan angka, akan otomatis diformat</small>
                            </div>

                            <div class="mb-3">
                                <label for="transaction_date" class="form-label">Tanggal Transaksi <span class="text-danger">*</span></label>
                                <input type="datetime-local" id="transaction_date" name="transaction_date" class="form-control" value="{{ now()->setTimezone('Asia/Makassar')->format('Y-m-d\TH:i') }}" required>
                            </div>

                            <div class="mb-3">
                                <label for="attachment" class="form-label">Lampiran</label>
                                <input type="file" id="attachment" name="attachment" class="form-control" accept=".pdf,.jpg,.jpeg,.png,.doc,.docx">
                                <small class="text-muted">Format: PDF, JPG, JPEG, PNG, DOC, DOCX. Maksimal 5MB</small>
                            </div>

                            <div class="d-grid">
                                <button type="submit" class="btn btn-primary" id="submit-btn">
                                    <i class="mdi mdi-content-save"></i> Simpan Transaksi
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Attachment Preview Modal -->
<div class="modal fade" id="attachment-modal" tabindex="-1" aria-labelledby="attachment-modal-label" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header bg-primary text-white">
                <h5 class="modal-title font-bold" id="attachment-modal-label">Preview Lampiran</h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body text-center" id="attachment-preview">
                <!-- Attachment preview will be loaded here -->
            </div>
            <div class="modal-footer">
                <a href="#" id="download-attachment" class="btn btn-primary" target="_blank">
                    <i class="mdi mdi-download"></i> Download
                </a>
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Tutup</button>
            </div>
        </div>
    </div>
</div>
@endsection
