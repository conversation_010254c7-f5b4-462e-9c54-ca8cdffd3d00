{"version": 3, "sources": ["custom/fonts/_fonts.scss", "custom/structure/_general.scss", "custom/structure/_left-menu.scss", "app-rtl.css", "../../node_modules/bootstrap/scss/mixins/_breakpoints.scss", "custom/structure/_topbar.scss", "custom/structure/_page-head.scss", "custom/structure/_footer.scss", "custom/structure/_right-sidebar.scss", "custom/structure/_layouts.scss", "custom/components/_helper.scss", "custom/components/_social.scss", "custom/components/_widgets.scss", "custom/components/_custom-checkbox.scss", "custom/components/_custom-radio.scss", "custom/components/_print.scss", "custom/plugins/_waves.scss", "custom/plugins/_slimscroll.scss", "custom/plugins/_toastr.scss", "custom/plugins/_sweetalert.scss", "custom/plugins/_ion-rangeslider.scss", "custom/plugins/_rating.scss", "custom/plugins/_jstree.scss", "custom/plugins/_hopscotch.scss", "custom/plugins/_calendar.scss", "custom/plugins/_bootstrap-tagsinput.scss", "custom/plugins/_multiple-select.scss", "custom/plugins/_select2.scss", "custom/plugins/_autocomplete.scss", "custom/plugins/_parsley.scss", "custom/plugins/_timepicker.scss", "custom/plugins/_datepicker.scss", "custom/plugins/_daterange.scss", "custom/plugins/_clockpicker.scss", "custom/plugins/_form-wizard.scss", "custom/plugins/_summernote.scss", "custom/plugins/_dropify.scss", "custom/plugins/_x-editable.scss", "custom/plugins/_datatable.scss", "custom/plugins/_responsive-table.scss", "custom/plugins/_tablesaw.scss", "custom/plugins/_flot.scss", "custom/plugins/_morris.scss", "custom/plugins/_chartist.scss", "custom/plugins/_chartjs.scss", "custom/plugins/_c3.scss", "custom/plugins/_sparkline-chart.scss", "custom/pages/_components-demo.scss", "custom/pages/_authentication.scss", "custom/pages/_extras-pages.scss", "custom/pages/_timeline.scss", "custom/pages/_error.scss", "custom/pages/_gallery.scss", "custom/pages/_coming-soon.scss", "custom/rtl/_general-rtl.scss", "custom/rtl/_bootstrap-rtl.scss", "../../node_modules/bootstrap/scss/mixins/_border-radius.scss", "custom/rtl/_spacing-rtl.scss", "custom/rtl/_float-rtl.scss", "custom/rtl/_text-rtl.scss", "../../node_modules/bootstrap/scss/mixins/_text-truncate.scss", "../../node_modules/bootstrap/scss/mixins/_text-emphasis.scss", "../../node_modules/bootstrap/scss/mixins/_hover.scss", "../../node_modules/bootstrap/scss/mixins/_text-hide.scss", "custom/rtl/_structure-rtl.scss", "custom/rtl/_components-rtl.scss", "custom/rtl/_plugins-rtl.scss", "custom/rtl/_pages-rtl.scss"], "names": [], "mappings": "AAIA,yFCAA,KACE,SAAA,SACA,WAAA,KAGF,KACE,eAAA,KACA,WAAA,OCLF,WACI,QAAA,EADJ,cAIQ,WAAA,KAJR,cAOQ,QAAA,EAPR,iBASY,MAAA,KATZ,sCAcQ,QAAA,KAdR,0BAiBQ,SAAA,SACA,OAAA,EACA,SAAA,OACA,mCAAA,KAAA,2BAAA,KACA,4BAAA,KAAA,oBAAA,KACA,4BAAA,MAAA,CAAA,WAAA,oBAAA,MAAA,CAAA,WAIR,uBCOA,sBDHY,QAAA,IAAA,KACA,MAAA,QACA,QAAA,MACA,SAAA,SACA,mBAAA,IAAA,IAAA,WAAA,IAAA,IARZ,6BAAA,6BCeE,4BACA,4BDLc,MAAA,QAMhB,iCCGA,gCDCY,MAAA,QAMZ,SACI,OAAA,KACA,SAAA,OACA,MAAA,KAIJ,cACI,YAAA,MACA,SAAA,OACA,QAAA,EAAA,KAAA,IAAA,KACA,WAAA,KACA,WAAA,KAIJ,gBACI,MAAA,MACA,WAAA,KACA,OAAA,EACA,QAAA,KAAA,EACA,SAAA,MACA,mBAAA,IAAA,IAAA,SAAA,WAAA,IAAA,IAAA,SACA,IAAA,KACA,mBAAA,EAAA,EAAA,KAAA,EAAA,mBAAA,WAAA,EAAA,EAAA,KAAA,EAAA,mBACA,QAAA,EAIJ,sBAIgB,MAAA,QACA,QAAA,MACA,QAAA,KAAA,KACA,SAAA,SACA,mBAAA,IAAA,IAAA,WAAA,IAAA,IACA,UAAA,OAThB,6BAAA,4BAAA,4BAcoB,MAAA,QACA,gBAAA,KAfpB,wBAmBoB,QAAA,aACA,YAAA,UACA,OAAA,EAAA,KAAA,EAAA,IACA,WAAA,OACA,MAAA,KACA,UAAA,KAxBpB,kCA2BoB,MAAA,MA3BpB,oCA6BwB,aAAA,EA7BxB,6BAkCgB,iBAAA,QACA,MAAA,QAnChB,uBAuCgB,aAAA,KAvChB,0BA0CoB,aAAA,KA1CpB,0BAgDQ,mBAAA,kBAAA,KAAA,WAAA,kBAAA,KAAA,WAAA,UAAA,KAAA,WAAA,UAAA,IAAA,CAAA,kBAAA,KACA,SAAA,SACA,MAAA,KACA,QAAA,aACA,YAAA,wBACA,eAAA,KACA,YAAA,OACA,UAAA,OACA,kBAAA,eAAA,UAAA,eAxDR,iCA0DY,QAAA,QA1DZ,qBA8DQ,WAAA,IA9DR,6CAoEgB,kBAAA,cAAA,UAAA,cApEhB,0BA0EQ,QAAA,KAAA,KACA,eAAA,MACA,eAAA,KACA,OAAA,QACA,UAAA,SACA,eAAA,UACA,MAAA,QACA,YAAA,IAKR,oBAGQ,MAAA,eAHR,6BAQY,QAAA,KARZ,6BAWY,QAAA,MAXZ,0BAgBQ,SAAA,SACA,YAAA,EACA,MAAA,eACA,QAAA,EAnBR,yCCbE,2CDoCU,SAAA,kBACA,OAAA,eAxBZ,yCA2BY,WAAA,OChCV,+CADA,+CADA,oDDOF,oDAoCgB,QAAA,eApChB,8CAyCoB,SAAA,SACA,YAAA,OA1CpB,gDA6CwB,QAAA,KAAA,KACA,WAAA,KACA,mBAAA,KAAA,WAAA,KA/CxB,uDAAA,sDAAA,sDAoD4B,MAAA,QApD5B,kDAuD4B,UAAA,SACA,aAAA,KAxD5B,qDA4D4B,QAAA,KACA,aAAA,KA7D5B,sDAmE4B,SAAA,SACA,MAAA,mBACA,iBAAA,QACA,mBAAA,KAAA,WAAA,KAtE5B,2DAyEgC,QAAA,OAzEhC,uEAAA,kEA+EgC,QAAA,KA/EhC,uDAoF4B,QAAA,MACA,KAAA,KACA,SAAA,SACA,MAAA,MACA,OAAA,eACA,mBAAA,IAAA,IAAA,KAAA,EAAA,kBAAA,WAAA,IAAA,IAAA,KAAA,EAAA,kBAzF5B,0DA4FgC,mBAAA,IAAA,IAAA,KAAA,EAAA,kBAAA,WAAA,IAAA,IAAA,KAAA,EAAA,kBA5FhC,yDA+FgC,mBAAA,KAAA,WAAA,KACA,QAAA,IAAA,KACA,SAAA,SACA,MAAA,MACA,QAAA,EAnGhC,+DAqGoC,MAAA,QArGpC,8CA6GoB,QAAA,IAAA,EACA,QAAA,KACA,QAAA,KACA,iBAAA,KAhHpB,0DAqHgC,QAAA,MACA,KAAA,MACA,WAAA,MACA,OAAA,eACA,SAAA,SACA,MAAA,MA1HhC,mEAgIgC,SAAA,SACA,MAAA,KACA,IAAA,KACA,kBAAA,eAAA,UAAA,eAnIhC,0DAyI4B,MAAA,QAzI5B,wBAmJQ,YAAA,eAnJR,kBAwJQ,KAAA,eAxJR,oBA6JQ,QAAA,KAKR,cACI,WAAA,OExQA,4BF4QA,KACI,WAAA,OACA,eAAA,KAEJ,gBACI,QAAA,KACA,QAAA,aAEJ,gCAEQ,QAAA,MAGR,cAAA,wBACI,YAAA,YACA,QAAA,EAAA,KAEJ,UACI,QAAA,MAKR,+BAEQ,iBAAA,QAFR,2CAKY,QAAA,MALZ,0CASY,QAAA,KATZ,qCAcQ,iBAAA,QAdR,2DAsBwB,MAAA,QAtBxB,kEAAA,iEAAA,iEA2B4B,MAAA,QA3B5B,kEA+BwB,MAAA,KACA,iBAAA,QACA,mBAAA,KAjCxB,4DCjFE,2DD8HkB,MAAA,QA7CpB,kEAAA,kEC9EI,iEACA,iED6HoB,iBAAA,YACA,MAAA,QAjDxB,mECzEE,kED+HkB,MAAA,KAOpB,sDAGQ,mBAAA,KAAA,IAAA,EAAA,EAAA,QAAA,WAAA,KAAA,IAAA,EAAA,EAAA,QAHR,mFAY4B,iBAAA,QAZ5B,2EAiBoB,iBAAA,QAjBpB,kGAAA,kGAAA,mGCvHA,iGACA,iGAFA,kGDgJwB,MAAA,QAYxB,6BAEQ,MAAA,MAFR,mCAKQ,MAAA,MACA,WAAA,OANR,2DAa4B,QAAA,MACA,UAAA,KACA,YAAA,KACA,MAAA,KACA,OAAA,EAjB5B,uDAsBoB,aAAA,EAtBpB,yDAwBwB,QAAA,KAAA,KC3JtB,0CDmIF,+CA+BY,QAAA,eA/BZ,iDAkCY,YAAA,MAlCZ,yDAqCY,KAAA,MAKZ,qDAGY,WAAA,KAHZ,+DAS4B,QAAA,aACA,UAAA,KACA,YAAA,KACA,YAAA,IACA,aAAA,KACA,eAAA,OACA,MAAA,KGjgB5B,MACI,QAAA,MACA,YAAA,KAFJ,mBAIQ,QAAA,MAJR,mBAOQ,QAAA,KAPR,yBAUQ,MAAA,QACA,YAAA,IACA,UAAA,KACA,eAAA,UAbR,0BAgBQ,MAAA,KACA,YAAA,IACA,UAAA,KACA,eAAA,UAIR,UACI,iBAAA,KACA,OAAA,KACA,MAAA,MACA,MAAA,KAGJ,YACI,QAAA,KAGJ,WACI,QAAA,MAGJ,eACI,iBAAA,QACA,QAAA,EAAA,KAAA,EAAA,EACA,SAAA,MACA,KAAA,EACA,MAAA,EACA,OAAA,KACA,QAAA,IAPJ,+BAWY,MAAA,KAXZ,sCAcY,QAAA,EAAA,KACA,MAAA,QACA,UAAA,KACA,QAAA,MACA,YAAA,KACA,WAAA,OACA,WAAA,KApBZ,wCA0BY,iBAAA,sBA1BZ,2BAiCQ,SAAA,OACA,OAAA,KACA,QAAA,MACA,UAAA,MACA,aAAA,KArCR,2CAwCY,QAAA,WACA,eAAA,OAzCZ,4EA4CgB,UAAA,SACA,MAAA,QA7ChB,yCAiDY,OAAA,KACA,OAAA,KACA,aAAA,KACA,cAAA,EACA,MAAA,QACA,iBAAA,QACA,mBAAA,KAAA,WAAA,KACA,cAAA,KAAA,EAAA,EAAA,KAxDZ,+CA2DY,YAAA,EACA,QAAA,EA5DZ,gCAgEY,iBAAA,QACA,MAAA,QACA,aAAA,YACA,cAAA,EAAA,KAAA,KAAA,EACA,mBAAA,eAAA,WAAA,eApEZ,mCAyEQ,OAAA,KACA,MAAA,QACA,QAAA,aACA,OAAA,KACA,YAAA,KACA,MAAA,KACA,iBAAA,YACA,UAAA,KACA,OAAA,QAjFR,+CAqFQ,QAAA,KAMR,aACI,WAAA,MAGJ,mBACI,YAAA,EADJ,+BAIQ,iBAAA,QACA,QAAA,KAAA,KACA,cAAA,OAAA,OAAA,EAAA,EACA,WAAA,KAPR,8BAWQ,UAAA,KAXR,oCAeQ,QAAA,aACA,OAAA,KACA,MAAA,KACA,iBAAA,QACA,cAAA,IACA,OAAA,IAAA,MAAA,QACA,SAAA,SACA,IAAA,KACA,MAAA,KAvBR,gCA2BQ,QAAA,KAAA,KA3BR,2CA+BY,iBAAA,QACA,cAAA,KAhCZ,6CAoCY,MAAA,KACA,OAAA,KACA,MAAA,KACA,UAAA,KACA,YAAA,KACA,WAAA,OACA,WAAA,IACA,aAAA,KACA,cAAA,IACA,MAAA,KA7CZ,gDAiDY,cAAA,IACA,SAAA,OACA,YAAA,KACA,cAAA,SACA,YAAA,OACA,MAAA,QACA,YAAA,IAvDZ,kDA0DgB,YAAA,IA1DhB,sDA6DgB,QAAA,MA7DhB,qDAgEgB,QAAA,MACA,SAAA,OACA,cAAA,SACA,YAAA,OACA,UAAA,KApEhB,0CA0EY,YAAA,KACA,YAAA,OACA,YAAA,KA5EZ,6CAkFY,QAAA,KAAA,KAlFZ,mDAqFgB,iBAAA,QArFhB,kDA4FY,QAAA,IAAA,KAKZ,kBACI,MAAA,MADJ,oBAGQ,aAAA,IACA,UAAA,KAIR,UACI,QAAA,EAAA,eADJ,cAGQ,OAAA,KACA,MAAA,KAMR,0BACI,mCAEQ,YAAA,KAOZ,6BAEQ,iBAAA,KACA,mBAAA,MAAA,IAAA,EAAA,EAAA,QAAA,WAAA,MAAA,IAAA,EAAA,EAAA,QAHR,oDAOgB,MAAA,QAPhB,iEAagB,aAAA,KAbhB,iDAkBY,MAAA,QAlBZ,0EAwBgB,MAAA,kBAxBhB,uDA2BgB,MAAA,QACA,iBAAA,QACA,aAAA,QA7BhB,8CAgCgB,iBAAA,QACA,MAAA,QCpShB,gBACI,SAAA,SACA,iBAAA,KACA,QAAA,EAAA,KACA,OAAA,EAAA,MAAA,KAAA,MACA,mBAAA,EAAA,EAAA,KAAA,EAAA,mBAAA,WAAA,EAAA,EAAA,KAAA,EAAA,mBALJ,4BAQQ,UAAA,KACA,OAAA,EACA,YAAA,KAVR,kCAcQ,MAAA,MACA,WAAA,IAfR,4BAkBQ,iBAAA,YACA,QAAA,OAAA,EFkDJ,4BE5CA,4BAEQ,QAAA,MACA,YAAA,OACA,cAAA,SACA,SAAA,OACA,YAAA,KANR,kCASQ,QAAA,MClCZ,QACI,OAAA,EACA,QAAA,KAAA,KAAA,KACA,SAAA,SACA,MAAA,EACA,MAAA,QACA,KAAA,MACA,iBAAA,QH8DA,4BG1DA,QACI,KAAA,YACA,WAAA,QCbR,WACI,iBAAA,KACA,mBAAA,EAAA,EAAA,KAAA,EAAA,eAAA,CAAA,EAAA,IAAA,EAAA,EAAA,gBAAA,WAAA,EAAA,EAAA,KAAA,EAAA,eAAA,CAAA,EAAA,IAAA,EAAA,EAAA,gBACA,QAAA,MACA,SAAA,MACA,mBAAA,IAAA,IAAA,SAAA,WAAA,IAAA,IAAA,SACA,MAAA,MACA,QAAA,KACA,MAAA,gBACA,MAAA,OACA,IAAA,EACA,OAAA,EAXJ,2BAcQ,iBAAA,QACA,QAAA,OACA,MAAA,KAhBR,6BAmBQ,iBAAA,QACA,OAAA,KACA,MAAA,KACA,YAAA,KACA,MAAA,QACA,WAAA,OACA,cAAA,IACA,WAAA,KA1BR,mCA6BY,iBAAA,QA7BZ,qBAiCQ,QAAA,KACA,WAAA,OAlCR,+BAoCY,SAAA,SACA,OAAA,KACA,MAAA,KACA,OAAA,EAAA,KAAA,KAAA,KAvCZ,0CAyCgB,SAAA,SACA,MAAA,KACA,OAAA,EACA,OAAA,KACA,MAAA,KACA,iBAAA,KACA,YAAA,KACA,cAAA,IACA,mBAAA,EAAA,KAAA,KAAA,iBAAA,WAAA,EAAA,KAAA,KAAA,iBAjDhB,wBAqDY,cAAA,IArDZ,0BAuDgB,MAAA,QAOhB,kBACI,iBAAA,kBACA,SAAA,SACA,KAAA,EACA,MAAA,EACA,IAAA,EACA,OAAA,EACA,QAAA,KACA,QAAA,KACA,mBAAA,IAAA,IAAA,SAAA,WAAA,IAAA,IAAA,SAGJ,8BAEQ,MAAA,EAFR,qCAKQ,QAAA,MJVJ,4BIeA,WACI,SAAA,KADJ,4BAGQ,OAAA,gBAKZ,gCAEQ,SAAA,SACA,YAAA,IAAA,OAAA,QACA,aAAA,KACA,eAAA,KALR,uCAOY,QAAA,GACA,SAAA,SACA,KAAA,KACA,IAAA,IACA,MAAA,KACA,OAAA,KACA,iBAAA,KACA,OAAA,IAAA,MAAA,QACA,cAAA,IC3GZ,kBAEQ,iBAAA,QAFR,2BAIY,iBAAA,QACA,UAAA,OACA,OAAA,EAAA,KACA,mBAAA,EAAA,EAAA,KAAA,EAAA,mBAAA,WAAA,EAAA,EAAA,KAAA,EAAA,mBAPZ,iCAWY,UAAA,OACA,OAAA,EAAA,KAZZ,0BAgBY,OAAA,EAAA,KACA,UAAA,qBAjBZ,mCAsBgB,UAAA,oBAMhB,yBAEI,iCAAA,gCAEQ,SAAA,UC9BZ,UACE,UAAA,KAGF,UACE,UAAA,KAGF,UACE,UAAA,MAGF,UACE,UAAA,MAGF,UACE,UAAA,MAKF,uBACE,YAAA,MAAA,CAAA,WAIF,WACE,OAAA,OACA,MAAA,OAGF,WACE,OAAA,QACA,MAAA,QAGF,WACE,OAAA,OACA,MAAA,OAGF,WACE,OAAA,OACA,MAAA,OAGF,WACE,OAAA,KACA,MAAA,KAGF,YACE,OAAA,OACA,MAAA,OAGF,cACE,kBAAA,OAAA,eAAA,OAAA,YAAA,OACA,MAAA,KACA,QAAA,YAAA,QAAA,YAAA,QAAA,KACA,OAAA,KACA,iBAAA,OAAA,cAAA,OAAA,gBAAA,OACA,MAAA,KAGF,cACE,aAAA,KADF,iCAGI,OAAA,EAAA,EAAA,KAAA,MACA,QAAA,aACA,OAAA,IAAA,MAAA,KACA,cAAA,IAOJ,oBACE,YAAA,IAGF,sBACE,YAAA,IAMF,WPwqBA,WACA,WACA,WOtqBE,SAAA,OACA,cAAA,SACA,QAAA,YACA,mBAAA,SAGF,WACE,mBAAA,EAGF,WACE,mBAAA,EAIF,WACE,mBAAA,EAGF,WACE,mBAAA,EAKF,SACE,YAAA,SACA,aAAA,SC1HF,kBACI,OAAA,KACA,MAAA,KACA,YAAA,iBACA,QAAA,MACA,OAAA,IAAA,MAAA,QACA,cAAA,IACA,MAAA,QCLJ,eAEQ,UAAA,KACA,QAAA,GACA,WAAA,KAIR,eACI,SAAA,SACA,IAAA,EACA,OAAA,EACA,MAAA,KACA,UAAA,KACA,WAAA,OACA,MAAA,EACA,KAAA,KACA,QAAA,YAAA,QAAA,YAAA,QAAA,KACA,kBAAA,OAAA,eAAA,OAAA,YAAA,OACA,iBAAA,OAAA,cAAA,OAAA,gBAAA,OACA,mBAAA,WAAA,sBAAA,OAAA,mBAAA,IAAA,eAAA,IAZJ,oBAeM,kBAAA,cAAA,UAAA,cAKN,0BAEQ,SAAA,OACA,QAAA,KAAA,EACA,SAAA,SAJR,0CAOY,QAAA,MACA,MAAA,KACA,aAAA,KACA,WAAA,IAVZ,8CAagB,MAAA,KAbhB,6CAkBY,QAAA,MACA,cAAA,EACA,MAAA,QApBZ,2CAwBY,MAAA,QACA,QAAA,MACA,OAAA,EACA,SAAA,OA3BZ,2CA+BY,MAAA,QACA,UAAA,SACA,SAAA,SACA,MAAA,IACA,IAAA,KCjEZ,gBAEQ,QAAA,aACA,aAAA,IACA,SAAA,SACA,YAAA,IALR,wBAOY,iBAAA,KACA,cAAA,IACA,OAAA,IAAA,MAAA,QACA,QAAA,GACA,QAAA,aACA,OAAA,KACA,KAAA,EACA,YAAA,MACA,SAAA,SACA,mBAAA,IAAA,YAAA,WAAA,IAAA,YACA,MAAA,KACA,QAAA,YACA,IAAA,IAnBZ,uBAsBY,MAAA,QACA,QAAA,aACA,UAAA,KACA,OAAA,KACA,KAAA,EACA,YAAA,MACA,aAAA,IACA,YAAA,IACA,SAAA,SACA,IAAA,EACA,MAAA,KAhCZ,+BAoCQ,OAAA,QACA,QAAA,EACA,QAAA,EACA,QAAA,YAvCR,8CAyCY,QAAA,IAzCZ,mDA8CY,eAAA,KACA,QAAA,EA/CZ,oDAoDY,QAAA,GACA,SAAA,SACA,IAAA,IACA,KAAA,IACA,QAAA,MACA,MAAA,IACA,OAAA,IACA,OAAA,IAAA,MAAA,QACA,iBAAA,EACA,kBAAA,EACA,kBAAA,cAGA,UAAA,cAjEZ,sDAsEY,iBAAA,QACA,OAAA,YAKZ,wCAGY,cAAA,IAKZ,0BACI,WAAA,EAGJ,gCAEQ,OAAA,KACA,MAAA,KACA,SAAA,SAJR,gCAOQ,OAAA,KACA,MAAA,KARR,uCAUY,YAAA,EAVZ,sCAaY,YAAA,EAOR,6DAGY,iBAAA,QACA,aAAA,QAJZ,4DAOY,aAAA,KAPZ,+DAGY,iBAAA,QACA,aAAA,QAJZ,8DAOY,aAAA,KAPZ,6DAGY,iBAAA,QACA,aAAA,QAJZ,4DAOY,aAAA,KAPZ,0DAGY,iBAAA,QACA,aAAA,QAJZ,yDAOY,aAAA,KAPZ,6DAGY,iBAAA,QACA,aAAA,QAJZ,4DAOY,aAAA,KAPZ,4DAGY,iBAAA,QACA,aAAA,QAJZ,2DAOY,aAAA,KAPZ,2DAGY,iBAAA,QACA,aAAA,QAJZ,0DAOY,aAAA,KAPZ,0DAGY,iBAAA,QACA,aAAA,QAJZ,yDAOY,aAAA,KAPZ,4DAGY,iBAAA,QACA,aAAA,QAJZ,2DAOY,aAAA,KAPZ,0DAGY,iBAAA,QACA,aAAA,QAJZ,yDAOY,aAAA,KCpHhB,aAEQ,QAAA,aACA,aAAA,IACA,SAAA,SACA,YAAA,IALR,qBAOY,cAAA,OAAA,IAAA,YACA,mBAAA,OAAA,IAAA,YACA,iBAAA,KACA,cAAA,IACA,OAAA,IAAA,MAAA,QACA,QAAA,GACA,QAAA,aACA,OAAA,KACA,KAAA,EACA,YAAA,MACA,SAAA,SACA,WAAA,OAAA,IAAA,YACA,MAAA,KACA,QAAA,YApBZ,oBAuBY,gBAAA,eAAA,IAAA,8BACA,cAAA,WACA,aAAA,WACA,cAAA,aAAA,IAAA,8BACA,kBAAA,WACA,mBAAA,kBAAA,IAAA,8BACA,iBAAA,QACA,cAAA,IACA,QAAA,IACA,QAAA,aACA,OAAA,KACA,KAAA,IACA,YAAA,MACA,SAAA,SACA,IAAA,IACA,UAAA,WACA,WAAA,kBAAA,IAAA,8BAAA,WAAA,UAAA,IAAA,8BAAA,WAAA,UAAA,IAAA,6BAAA,CAAA,kBAAA,IAAA,8BACA,MAAA,KAxCZ,yBA4CQ,OAAA,QACA,QAAA,EACA,QAAA,EACA,QAAA,YA/CR,wCAiDY,QAAA,IAjDZ,6CAsDY,eAAA,KACA,QAAA,IAAA,KAAA,yBACA,QAAA,KAAA,OAxDZ,8CA+DY,kBAAA,WACA,UAAA,WAhEZ,gDAqEY,OAAA,YArEZ,oBA0EQ,WAAA,EA1ER,0BA+EY,OAAA,KACA,MAAA,KAQR,8CAGY,iBAAA,QAHZ,uDAQY,aAAA,QARZ,sDAWY,iBAAA,QAXZ,gDAGY,iBAAA,QAHZ,yDAQY,aAAA,QARZ,wDAWY,iBAAA,QAXZ,8CAGY,iBAAA,QAHZ,uDAQY,aAAA,QARZ,sDAWY,iBAAA,QAXZ,2CAGY,iBAAA,QAHZ,oDAQY,aAAA,QARZ,mDAWY,iBAAA,QAXZ,8CAGY,iBAAA,QAHZ,uDAQY,aAAA,QARZ,sDAWY,iBAAA,QAXZ,6CAGY,iBAAA,QAHZ,sDAQY,aAAA,QARZ,qDAWY,iBAAA,QAXZ,4CAGY,iBAAA,QAHZ,qDAQY,aAAA,QARZ,oDAWY,iBAAA,QAXZ,2CAGY,iBAAA,QAHZ,oDAQY,aAAA,QARZ,mDAWY,iBAAA,QAXZ,6CAGY,iBAAA,QAHZ,sDAQY,aAAA,QARZ,qDAWY,iBAAA,QAXZ,2CAGY,iBAAA,QAHZ,oDAQY,aAAA,QARZ,mDAWY,iBAAA,QClGhB,aZ0pCE,QYzpCE,gBZwpCF,eADA,gBADA,WYjpCM,QAAA,KAEJ,WZupCF,SAFA,cACA,WAEA,KYnpCM,QAAA,EACA,OAAA,GCnBR;;;;;;AAOC,cACC,SAAA,SACA,OAAA,QACA,QAAA,aACA,SAAA,OACA,oBAAA,KACA,iBAAA,KACA,gBAAA,KACA,YAAA,KACA,4BAAA,YAEF,4BACE,SAAA,SACA,cAAA,IACA,MAAA,MACA,OAAA,MACA,WAAA,MACA,YAAA,MACA,QAAA,EACA,WAAA,eAIA,WAAA,mHACA,mBAAA,IAAA,IAAA,SAGA,WAAA,IAAA,IAAA,SACA,4BAAA,iBAAA,CAAA,QAGA,4BAAA,OAAA,CAAA,kBAAA,oBAAA,OAAA,CAAA,kBAAA,oBAAA,SAAA,CAAA,QAAA,oBAAA,SAAA,CAAA,OAAA,CAAA,kBACA,kBAAA,SAAA,eAIA,UAAA,SAAA,eACA,eAAA,KAEF,wCACE,WAAA,qBAIA,WAAA,2IAEF,0CACE,WAAA,eAEF,sDACE,WAAA,qBAEF,oBACE,mBAAA,eAGA,WAAA,eAEF,cb8pCA,ca5pCE,kBAAA,cAIA,UAAA,cACA,mBAAA,oDAEF,cb6pCA,oBAFA,oBACA,sBaxpCE,YAAA,OACA,eAAA,OACA,OAAA,QACA,OAAA,KACA,QAAA,EACA,MAAA,QACA,iBAAA,cACA,UAAA,IACA,YAAA,IACA,WAAA,OACA,gBAAA,KACA,QAAA,EAEF,cACE,QAAA,MAAA,MACA,cAAA,KAEF,oBACE,OAAA,EACA,QAAA,MAAA,MAEF,qBACE,cAAA,KACA,eAAA,OAEF,kCACE,QAAA,EAEF,yCACE,SAAA,SACA,IAAA,EACA,KAAA,EACA,QAAA,EAEF,cACE,WAAA,OACA,MAAA,MACA,OAAA,MACA,YAAA,MACA,cAAA,IAEF,aACE,mBAAA,KACA,mBAAA,EAAA,IAAA,MAAA,IAAA,gBACA,WAAA,EAAA,IAAA,MAAA,IAAA,gBACA,mBAAA,IAAA,IAGA,WAAA,IAAA,IAEF,oBACE,mBAAA,EAAA,IAAA,KAAA,IAAA,eACA,WAAA,EAAA,IAAA,KAAA,IAAA,eAEF,aACE,QAAA,MCjIF,eACI,OAAA,eCGJ,qBAEQ,mBAAA,EAAA,EAAA,KAAA,EAAA,mBAAA,WAAA,EAAA,EAAA,KAAA,EAAA,mBACA,QAAA,EAHR,2BAKY,mBAAA,EAAA,EAAA,KAAA,EAAA,mBAAA,WAAA,EAAA,EAAA,KAAA,EAAA,mBACA,QAAA,GAOR,eACI,OAAA,IAAA,MAAA,kBACA,iBAAA,+BAFJ,iBACI,OAAA,IAAA,MAAA,kBACA,iBAAA,+BAFJ,eACI,OAAA,IAAA,MAAA,kBACA,iBAAA,8BAFJ,YACI,OAAA,IAAA,MAAA,kBACA,iBAAA,8BAFJ,eACI,OAAA,IAAA,MAAA,kBACA,iBAAA,8BAFJ,cACI,OAAA,IAAA,MAAA,kBACA,iBAAA,6BAFJ,aACI,OAAA,IAAA,MAAA,kBACA,iBAAA,+BAFJ,YACI,OAAA,IAAA,MAAA,kBACA,iBAAA,4BAFJ,cACI,OAAA,IAAA,MAAA,kBACA,iBAAA,8BAFJ,YACI,OAAA,IAAA,MAAA,kBACA,iBAAA,+BAOR,aACI,iBAAA,mBACA,OAAA,IAAA,MAAA,QC3BJ,aACE,YAAA,aAAA,CAAA,kBAAA,CAAA,UAAA,CAAA,MAAA,CAAA,gBAAA,CAAA,KAAA,CAAA,WAAA,CAAA,UAAA,CAAA,mBAAA,CAAA,gBAAA,CAAA,iBAAA,CAAA,mBACA,mBAAA,EAAA,KAAA,KAAA,eAAA,WAAA,EAAA,KAAA,KAAA,eAFF,0BAKI,UAAA,KALJ,4BAQI,UAAA,KARJ,2BAWI,OAAA,KAAA,EAXJ,yBAAA,0BAAA,6BAcI,OAAA,IAAA,MAAA,QACA,UAAA,KACA,mBAAA,KAAA,WAAA,KAhBJ,wCAmBI,iBAAA,kBACA,UAAA,MApBJ,sCAwBI,iBAAA,kBACA,UAAA,MAzBJ,iCA6BI,mBAAA,eAAA,WAAA,eAIJ,2BACE,MAAA,QACA,aAAA,QAGF,0BACE,aAAA,QADF,gChB+0CE,sDgB/0CF,mEAKI,iBAAA,QALJ,uCAAA,8CASI,aAAA,QAKJ,0BACE,MAAA,QACA,aAAA,QAGF,wBACE,aAAA,QADF,8BAGI,iBAAA,QAGJ,+BAAA,gCAAA,mCACE,QAAA,EACA,OAAA,IAAA,MAAA,QAGF,6BACE,iBAAA,kBCvEF,sBAAA,uBAAA,yBAAA,qBAEI,WAAA,kBAFJ,8BAAA,gCAAA,4BAMM,iBAAA,QANN,uBAUI,WAAA,QACA,aAAA,QAXJ,sBAAA,sBAeI,MAAA,QACA,WAAA,QAhBJ,4BAmBI,UAAA,KACA,MAAA,QApBJ,wCAyBQ,MAAA,IACA,OAAA,ICvBR,eAEI,MAAA,QAIJ,aAEI,UAAA,KAIJ,aAEI,UAAA,KlB85CJ,6BmB96CA,6BAGQ,iBAAA,kCAHR,6BAMQ,oBAAA,OAAA,KACA,kBAAA,SAPR,yCAWQ,iBAAA,YACA,iBAAA,KACA,oBAAA,EAAA,EAbR,+BAiBQ,YAAA,KACA,OAAA,KAlBR,iEAsBQ,WAAA,mCAAA,OAAA,OAAA,UAtBR,mCA0BQ,MAAA,KACA,OAAA,KACA,YAAA,KACA,UAAA,KACA,MAAA,QA9BR,gCnBy8CA,yCmBt6CQ,WAAA,qBACA,mBAAA,KAAA,WAAA,KApCR,gCnB+8CA,yCmBt6CQ,WAAA,sBACA,mBAAA,KAAA,WAAA,KA1CR,6BA8CQ,WAAA,IA9CR,iCAkDQ,OAAA,KClDR,qBACI,OAAA,IAAA,MAAA,QACA,cAAA,IAFJ,qCpB89CE,qCoBz9CM,iBAAA,kBACA,iBAAA,eACA,aAAA,kBACA,YAAA,eACA,OAAA,EAAA,EAAA,EAAA,cACA,YAAA,aAAA,CAAA,kBAAA,CAAA,UAAA,CAAA,MAAA,CAAA,gBAAA,CAAA,KAAA,CAAA,WAAA,CAAA,UAAA,CAAA,mBAAA,CAAA,gBAAA,CAAA,iBAAA,CAAA,mBACA,MAAA,eAXR,8CAeQ,WAAA,QACA,QAAA,EACA,cAAA,IAjBR,2FAuBgB,aAAA,KAAA,MAAA,QAvBhB,oFA2BgB,OAAA,KA3BhB,qFAiCgB,YAAA,KAAA,MAAA,QACA,KAAA,KAlChB,4FAsCgB,YAAA,EAAA,MAAA,QAtChB,kFA2CgB,cAAA,KAAA,MAAA,QACA,IAAA,EA5ChB,yFAgDgB,cAAA,EAAA,MAAA,eAhDhB,oFAqDgB,WAAA,KAAA,MAAA,QACA,IAAA,KAtDhB,2FAyDgB,WAAA,EAAA,MAAA,eAzDhB,wBA+DQ,YAAA,MAAA,CAAA,WACA,cAAA,KACA,YAAA,IAjER,wCAoEQ,YAAA,aAAA,CAAA,kBAAA,CAAA,UAAA,CAAA,MAAA,CAAA,gBAAA,CAAA,KAAA,CAAA,WAAA,CAAA,UAAA,CAAA,mBAAA,CAAA,gBAAA,CAAA,iBAAA,CAAA,mBCpER,UACI,MAAA,KACA,cAAA,EAGJ,SACI,WAAA,KAGJ,2BAEQ,WAAA,KAIR,eAEQ,UAAA,QACA,YAAA,SACA,eAAA,UAIR,4BAEQ,YAAA,IAIR,QACI,WAAA,IAGJ,6BrBy/CA,6BAGA,4BAFA,yBACA,yBqBr/CQ,QAAA,EAIR,wBAEQ,WAAA,QACA,UAAA,KACA,YAAA,KACA,QAAA,KAAA,EACA,eAAA,UACA,YAAA,IrB0/CR,yBAEA,yBADA,qBAFA,mBAFA,gBqBn/CA,gBrBo/CA,mBqB5+CQ,aAAA,QARR,yBAAA,yBAYQ,WAAA,kBAKR,WACI,WAAA,QACA,OAAA,KACA,MAAA,QACA,eAAA,WACA,mBAAA,KAAA,WAAA,KACA,cAAA,cACA,OAAA,EAAA,cACA,QAAA,IAAA,eACA,OAAA,eAGJ,eACI,YAAA,QACA,UAAA,KAGJ,gBACI,WAAA,QAGJ,oBACI,WAAA,QrB8+CJ,iBACA,mBqB5+CA,eAGI,iBAAA,QACA,MAAA,KACA,YAAA,KAGJ,iBACI,WAAA,QAGJ,yBAEQ,WAAA,KAIR,UACI,cAAA,IACA,OAAA,KACA,OAAA,KACA,UAAA,SACA,OAAA,IAAA,IACA,QAAA,IAAA,IACA,WAAA,OAGJ,gBACI,OAAA,KACA,OAAA,KAAA,EACA,QAAA,IAAA,KACA,MAAA,KACA,cAAA,IAGJ,sCAGY,cAAA,IAHZ,gCAOQ,cAAA,IAPR,2BAUQ,MAAA,KAIR,gCAEQ,MAAA,KpB/EJ,4BoBoFA,uBAAA,qBAAA,sBAEQ,MAAA,KACA,QAAA,MACA,MAAA,KACA,OAAA,KAAA,EAGR,oBAIgB,MAAA,KAKhB,iBACI,QAAA,MCzKR,qBACE,mBAAA,KAAA,WAAA,KACA,QAAA,IAAA,IAAA,IACA,MAAA,KACA,iBAAA,KACA,aAAA,QALF,2BAQI,MAAA,QARJ,iCAYI,iBAAA,QACA,QAAA,aACA,UAAA,KACA,OAAA,IAAA,IACA,QAAA,EAAA,IACA,cAAA,IACA,YAAA,ICpBJ,cACI,WAAA,YAAA,0CAAA,UAAA,IAAA,IACA,MAAA,KACA,UAAA,MAHJ,uBAMQ,mBAAA,KAAA,WAAA,KACA,OAAA,IAAA,MAAA,QAPR,gCAUY,mBAAA,KAAA,WAAA,KACA,OAAA,IAAA,MAAA,QAXZ,6BAeQ,iBAAA,KAfR,mDAkBgB,OAAA,KACA,QAAA,IAAA,KACA,MAAA,QApBhB,yCAuBgB,iBAAA,QACA,MAAA,KAxBhB,4BA6BQ,iBAAA,KA7BR,iDAgCgB,OAAA,KACA,QAAA,IAAA,KACA,MAAA,QAlChB,wCAqCgB,iBAAA,QACA,MAAA,KAOhB,cACI,cAAA,KAGJ,eACI,mBAAA,KAAA,WAAA,KACA,QAAA,YAGJ,mBACI,YAAA,IACA,YAAA,MAAA,CAAA,WACA,MAAA,kBACA,UAAA,KC1DJ,8CAEQ,iBAAA,KACA,OAAA,IAAA,MAAA,QACA,OAAA,KACA,QAAA,EALR,2EAOY,YAAA,KACA,aAAA,KACA,MAAA,QATZ,wEAYY,OAAA,KACA,MAAA,KACA,MAAA,IAdZ,0EAgBgB,aAAA,QAAA,YAAA,YAAA,YACA,aAAA,IAAA,IAAA,EAAA,IAMhB,gFAIgB,aAAA,YAAA,YAAA,QAAA,sBACA,aAAA,EAAA,IAAA,IAAA,cAMhB,yBACI,QAAA,IAAA,KAGJ,kBACI,OAAA,gBACA,iBAAA,KACA,mBAAA,EAAA,EAAA,KAAA,EAAA,mBAAA,WAAA,EAAA,EAAA,KAAA,EAAA,mBAGJ,sDAEQ,QAAA,KACA,iBAAA,KAHR,6EAKY,OAAA,IAAA,MAAA,QACA,iBAAA,KACA,MAAA,QACA,QAAA,EARZ,oDAYQ,YAAA,IAZR,iFAeQ,iBAAA,QAfR,yEAkBQ,iBAAA,QACA,MAAA,QAnBR,+EAqBY,iBAAA,QACA,MAAA,KAKZ,gDAEQ,WAAA,KACA,iBAAA,KACA,OAAA,IAAA,MAAA,kBAJR,6EAMY,QAAA,IAAA,KANZ,uEASY,OAAA,EACA,MAAA,QAVZ,kGAYgB,MAAA,QAZhB,yFAYgB,MAAA,QAZhB,6FAYgB,MAAA,QAZhB,8FAYgB,MAAA,QAZhB,oFAYgB,MAAA,QAZhB,2EAgBY,iBAAA,QACA,OAAA,KACA,MAAA,KACA,cAAA,IACA,QAAA,EAAA,IACA,WAAA,IArBZ,mFAwBY,MAAA,KACA,aAAA,IAzBZ,yFA2BgB,MAAA,KClGhB,0BACI,OAAA,IAAA,MAAA,QACA,WAAA,QACA,OAAA,QACA,SAAA,KACA,WAAA,gBACA,mBAAA,EAAA,EAAA,KAAA,EAAA,mBAAA,WAAA,EAAA,EAAA,KAAA,EAAA,mBANJ,iCAQM,YAAA,IACA,MAAA,QAIN,yBACE,QAAA,IAAA,KACA,YAAA,OACA,SAAA,OAGF,4BACE,QAAA,IAGF,uBACE,WAAA,QACA,OAAA,QAIF,oBACE,QAAA,IACA,YAAA,IACA,YAAA,MAAA,CAAA,WAHF,2BAKI,YAAA,IACA,UAAA,KACA,MAAA,QACA,QAAA,MCpCJ,eACE,aAAA,kBAEF,qBACE,OAAA,EACA,QAAA,EAFF,wBAII,UAAA,KACA,WAAA,KACA,MAAA,QACA,WAAA,ICTJ,4CAIgB,OAAA,IAAA,MAAA,kBACA,MAAA,KACA,iBAAA,KACA,MAAA,QAPhB,wCAWgB,MAAA,QAXhB,8CAaoB,iBAAA,QACA,aAAA,QAdpB,iDAoBQ,oBAAA,QApBR,4DAuBQ,iBAAA,QC1BR,YACI,QAAA,eADJ,e5By1DE,e4Br1DM,MAAA,KACA,OAAA,KACA,QAAA,IANR,eAUQ,YAAA,I5Bs1DN,yDACA,mDACA,uCACA,sCACA,6CACA,+CACA,6CACA,4CACA,gDACA,0CACA,2CACA,6CACA,oDACA,sDACA,oDACA,mDACA,0DACA,4DACA,0DACA,yDACA,6DACA,uDACA,2CACA,0CACA,iDACA,mDACA,iDACA,gDACA,oDACA,8C4B73DF,sCAAA,wCAAA,+CAAA,iDAAA,+CAAA,8CAAA,qDAAA,uDAAA,qDAAA,oD5B63D2M,iCAA1J,8BAA+B,uCAAwC,6CAA8C,oC4Bh0DlJ,iBAAA,kBACA,iBAAA,eACA,MAAA,K5Bm0DlB,qCACA,mC4Bn4DF,oCAAA,kCAsEoB,WAAA,Q5Bg0DlB,iCACA,iC4Bv4DF,4BAAA,4BA6EoB,MAAA,QACA,QAAA,GA9EpB,8BAAA,uCAAA,6CAAA,oCAkFoB,iBAAA,QAlFpB,qC5B64DE,wBACA,wBACA,8B4BnzDM,WAAA,QA5FR,qCAgGY,WAAA,IAKZ,mBACI,OAAA,IAAA,MAAA,kBAGJ,2BAGQ,cAAA,IAAA,MAAA,KAHR,4BAMQ,oBAAA,gBANR,kDAUY,WAAA,IAAA,MAAA,gBAVZ,iDAaY,WAAA,IAAA,MAAA,KCrHZ,iBACI,YAAA,aAAA,CAAA,kBAAA,CAAA,UAAA,CAAA,MAAA,CAAA,gBAAA,CAAA,KAAA,CAAA,WAAA,CAAA,UAAA,CAAA,mBAAA,CAAA,gBAAA,CAAA,iBAAA,CAAA,mBACA,iBAAA,KACA,aAAA,QAHJ,iCAMQ,iBAAA,KACA,aAAA,QAPR,4CAAA,4CAUgB,aAAA,QAVhB,oBAAA,oBAgBQ,QAAA,IAhBR,yBAAA,yBAkBY,MAAA,QAlBZ,oCAAA,oCAsBgB,iBAAA,QAtBhB,mCAAA,2BAAA,iCA4BQ,iBAAA,QA5BR,kCAgCQ,iBAAA,QAhCR,uCAAA,+BAqCY,iBAAA,KACA,aAAA,QACA,MAAA,QAvCZ,wBAAA,iCAAA,iCAAA,mCA8CY,iBAAA,YACA,MAAA,qBA/CZ,6BAmDY,iBAAA,QACA,MAAA,QApDZ,oDA0DY,kBAAA,QA1DZ,8BAgEQ,iBAAA,QAhER,mCAkEY,YAAA,IClEZ,oCAEQ,UAAA,KACA,YAAA,IACA,iBAAA,KAJR,sCAQQ,iBAAA,QARR,wCAYQ,iBAAA,KACA,aAAA,QAbR,uCAiBQ,MAAA,QAjBR,kCAqBQ,iBAAA,QACA,MAAA,KCrBR,eAKM,SAAA,SACA,QAAA,MACA,MAAA,KAPN,qBAUc,MAAA,IAVd,iBAeQ,UAAA,KACA,OAAA,EAAA,KAAA,KAhBR,iBAAA,wBAAA,uBAoBU,QAAA,MACA,MAAA,KACA,QAAA,IAAA,IACA,gBAAA,KACA,cAAA,IAxBV,2BA6BY,WAAA,QACA,OAAA,IAAA,MAAA,QACA,MAAA,QACE,OAAA,QAhCd,kCAAA,iCAkCc,WAAA,QAlCd,0BAAA,iCAAA,gCA0Cc,WAAA,QACA,MAAA,KACA,OAAA,QA5Cd,uBAAA,8BAAA,6BAkDc,WAAA,QACA,MAAA,QAnDd,uBAAA,qBA6Dc,MAAA,KACA,SAAA,SA9Dd,iBAsEM,QAAA,MACA,OAAA,KACA,WAAA,MACA,SAAA,OACA,SAAA,SACA,MAAA,KACA,QAAA,KACA,OAAA,IAAA,MAAA,QA7EN,uBA+EU,QAAA,EACA,SAAA,SAhFV,0BAmFc,WAAA,eAnFd,6BAqFkB,QAAA,MACA,YAAA,KAtFlB,8BA2Fc,OAAA,EAAA,KACA,MAAA,KACA,OAAA,KA7Fd,6BAiGc,QAAA,MACA,aAAA,QAlGd,mCAqGkB,aAAA,QArGlB,4CAwGkB,QAAA,aAxGlB,mCA4GkB,WAAA,mBACA,OAAA,IAAA,MAAA,QACA,MAAA,QA9GlB,6BAmHc,QAAA,aACA,cAAA,KACA,WAAA,KArHd,mCAuHkB,MAAA,QACA,UAAA,KAxHlB,iBAiIM,SAAA,SACA,QAAA,MACA,WAAA,MACA,MAAA,KACA,WAAA,KArIN,oBAuIU,QAAA,aACA,WAAA,MAxIV,uBA0Ic,OAAA,EAAA,KA1Id,mBAAA,0BAAA,yBA+IU,WAAA,QACA,MAAA,KACA,QAAA,MACA,QAAA,KAAA,IACA,gBAAA,KACA,cAAA,IApJV,6BAAA,oCAAA,mCAyJc,WAAA,QACA,MAAA,QA1Jd,wBAmKU,QAAA,OACA,MAAA,KACA,MAAA,IArKV,8BAwKkB,MAAA,KACA,MAAA,KAzKlB,0BA+KQ,MAAA,IACA,OAAA,EAAA,KAAA,KACA,QAAA,OACA,MAAA,KAlLR,0BAsLU,QAAA,OACA,MAAA,MACA,MAAA,IACA,OAAA,EAAA,KACA,WAAA,eA1LV,gCA6LkB,OAAA,EAAA,EAAA,EAAA,IAalB,YAAA,QACE,QAAA,MACA,MAAA,KACA,SAAA,OAHF,cAAA,UAKM,QAAA,EALN,eAAA,WASM,WAAA,eACA,QAAA,EACA,OAAA,EAXN,kBAAA,cAcU,QAAA,MACA,QAAA,EAfV,iCAAA,6BAuBU,SAAA,SACA,KAAA,OAxBV,4BAAA,wBA8BU,SAAA,SACA,KAAA,O9BrKN,4B8B2KF,0BAAA,wBAAA,qBACI,MAAA,MCjPN,WACE,YAAA,WACA,WAAA,OACA,YAAA,IACA,IAAA,6BACA,IAAA,oCAAA,2BAAA,CAAA,+BAAA,cAAA,CAAA,8BAAA,mBAGF,wBAEI,OAAA,IAAA,MAAA,QACA,mBAAA,KAAA,WAAA,KACA,OAAA,EAJJ,wCAOM,iBAAA,QACA,WAAA,IAAA,MAAA,QARN,uCAYQ,OAAA,KAKR,oBACE,QAAA,KAGF,8BAKM,cAAA,EhCmmEN,qDgC9lEA,0DAEI,UAAA,MAGJ,cACG,QAAA,EC7CH,WACI,YAAA,QACA,IAAA,0BACA,IAAA,gCAAA,2BAAA,CAAA,2BAAA,cAAA,CAAA,0BAAA,kBAAA,CAAA,kCAAA,cACA,YAAA,IACA,WAAA,OAGJ,iBACI,OAAA,IAAA,OAAA,QACA,iBAAA,YACA,cAAA,IACA,MAAA,QAJJ,uBAMQ,iBAAA,wHANR,kCAUQ,iBAAA,QClBR,kBACI,WAAA,iCAAA,OAAA,OAAA,UAGJ,sBACI,WAAA,mCAAA,OAAA,OAAA,UAGJ,0BACI,QAAA,MATJ,kBACI,WAAA,iCAAA,OAAA,OAAA,UAGJ,sBACI,WAAA,mCAAA,OAAA,OAAA,UAGJ,0BACI,QAAA,MCTJ,oCACI,QAAA,EAGJ,gBACI,gBAAA,mBACA,cAAA,eAFJ,kCAAA,mCAQY,iBAAA,QARZ,qCAAA,sCAWgB,aAAA,QAXhB,+BAgBgB,QAAA,YAhBhB,+BAAA,+BAsBgB,QAAA,IAAA,MAAA,kBACA,eAAA,KACA,MAAA,QACA,iBAAA,sBAMhB,iBACI,YAAA,IAIJ,8EAAA,8EAMwB,mBAAA,EAAA,KAAA,KAAA,iBAAA,WAAA,EAAA,KAAA,KAAA,iBACA,iBAAA,QACA,IAAA,OARxB,2EAAA,2EAiBwB,iBAAA,QACA,IAAA,OASxB,mBAAA,YACI,MAAA,KAGJ,mBACI,iBAAA,QACA,OAAA,KACA,MAAA,KACA,mBAAA,KAAA,WAAA,KACA,cAAA,IACA,WAAA,OACA,QAAA,GAPJ,sBAUQ,cAAA,KACA,iBAAA,qBACA,MAAA,KlCdJ,4BkCmBA,wBAAA,4BACI,QAAA,aACA,UAAA,OAGJ,mBACI,QAAA,KAEJ,wBAEQ,WAAA,OACA,QAAA,MACA,OAAA,KAAA,EAAA,YAGR,eACI,QAAA,aACA,cAAA,MAKR,4BAEQ,iBAAA,QAKR,WACI,MAAA,KACA,aAAA,KAGJ,qBAAA,2BAGQ,MAAA,eACA,cAAA,IACA,QAAA,YACA,mBAAA,eAAA,WAAA,eACA,MAAA,eACA,WAAA,kBACA,OAAA,IAAA,MAAA,kBAIR,gCACI,iBAAA,YAGJ,qBACI,QAAA,KAAA,EAAA,EAAA,EACA,iBAAA,KACA,mBAAA,EAAA,EAAA,KAAA,EAAA,mBAAA,WAAA,EAAA,EAAA,KAAA,EAAA,mBACA,OAAA,KAJJ,wBAMQ,WAAA,cACA,QAAA,IAAA,eACA,OAAA,eACA,mBAAA,eAAA,WAAA,eAIR,uBACI,MAAA,KCzJJ,wDAEQ,QAAA,IAAA,KACA,MAAA,QAHR,8DAAA,8DAKY,iBAAA,QACA,MAAA,QANZ,2CAUQ,OAAA,KACA,cAAA,EAXR,sCAcQ,QAAA,MAdR,kCAkBY,UAAA,KACA,YAAA,IAnBZ,uCAuBQ,aAAA,KAvBR,6CAyBY,QAAA,aACA,aAAA,IACA,SAAA,SACA,cAAA,EA5BZ,qDA8BgB,iBAAA,QACA,cAAA,IACA,OAAA,IAAA,MAAA,QACA,QAAA,GACA,QAAA,aACA,OAAA,KACA,KAAA,EACA,YAAA,MACA,SAAA,SACA,mBAAA,IAAA,YAAA,WAAA,IAAA,YACA,MAAA,KACA,QAAA,EAzChB,oDA4CgB,MAAA,QACA,QAAA,aACA,UAAA,KACA,OAAA,KACA,KAAA,EACA,YAAA,MACA,aAAA,IACA,YAAA,IACA,SAAA,SACA,IAAA,KACA,MAAA,KAtDhB,4DA0DY,OAAA,QACA,QAAA,EACA,QAAA,EACA,QAAA,EA7DZ,2EA+DgB,QAAA,IA/DhB,gFAoEgB,eAAA,KACA,QAAA,EArEhB,iFA0EgB,QAAA,QACA,YAAA,sBACA,YAAA,IA5EhB,mFAiFgB,iBAAA,QACA,OAAA,YAlFhB,kFAuFgB,iBAAA,QACA,aAAA,QAxFhB,iFA2FgB,MAAA,QpCkwEhB,8CADA,4DoC51EA,4DAkGQ,WAAA,QACA,aAAA,QACA,MAAA,KpC+vEN,oDADA,kEoCl2EF,kEAuGY,MAAA,KAvGZ,8DA4GY,IAAA,eA5GZ,sCAgHQ,iBAAA,QACA,MAAA,QACA,OAAA,IAAA,MAAA,QAlHR,kDAoHY,iBAAA,QACA,aAAA,QACA,MAAA,KACA,mBAAA,EAAA,EAAA,EAAA,IAAA,qBAAA,WAAA,EAAA,EAAA,EAAA,IAAA,qBAvHZ,+CA4HY,MAAA,MA5HZ,8DA8HgB,KAAA,KACA,MAAA,EC/HhB,gBAEQ,WAAA,QACA,iBAAA,KACA,OAAA,KAJR,mBAMY,YAAA,KANZ,kCASY,OAAA,KACA,YAAA,IACA,YAAA,MAAA,CAAA,WAXZ,aAeQ,WAAA,IAAA,MAAA,kBAfR,mBAAA,aAmBQ,UAAA,QACA,YAAA,QACA,QAAA,eAKR,mBAAA,yBAGY,cAAA,KAKZ,2CAAA,2CAIgB,UAAA,IACA,cAAA,KAMhB,uCAEQ,mBAAA,KAAA,WAAA,KACA,aAAA,QAHR,4CAMQ,aAAA,IAAA,MAAA,QAIR,4BAEQ,MAAA,QAKR,sCAGY,YAAA,KACA,iBAAA,KACA,eAAA,KACA,OAAA,IAAA,MAAA,QACA,QAAA,IAAA,KACA,MAAA,QARZ,4CAWgB,QAAA,KAXhB,uDAgBoB,WAAA,KAhBpB,6CAAA,4CAAA,4CAqBgB,MAAA,kBACA,iBAAA,QACA,QAAA,YACA,mBAAA,eAAA,WAAA,eACA,iBAAA,KAMhB,wCAEQ,QAAA,MAIR,uBACI,OAAA,QAGJ,4BACI,MAAA,eCxGJ,SACI,QAAA,IAAA,KACA,iBAAA,kBACA,QAAA,IACA,MAAA,QACA,QAAA,EACA,cAAA,IAGJ,WAEQ,OAAA,KACA,YAAA,MAAA,CAAA,WAIR,aACI,aAAA,IACA,YAAA,KACA,cAAA,KACA,UAAA,KACA,YAAA,IACA,MAAA,QAGJ,oBAEQ,cAAA,IAFR,wBAIY,cAAA,IrCwCR,4BqCjCA,aACI,QAAA,MCrCR,mBAEQ,YAAA,MAAA,CAAA,qBACA,YAAA,cACA,KAAA,QAGR,cACI,SAAA,SACA,QAAA,GAFJ,mCAKQ,UAAA,KACA,WAAA,OACA,cAAA,IACA,QAAA,KAAA,KACA,WAAA,qBACA,MAAA,QACA,OAAA,IAAA,MAAA,QACA,YAAA,aAAA,CAAA,kBAAA,CAAA,UAAA,CAAA,MAAA,CAAA,gBAAA,CAAA,KAAA,CAAA,WAAA,CAAA,UAAA,CAAA,mBAAA,CAAA,gBAAA,CAAA,iBAAA,CAAA,mBAZR,2DAeY,YAAA,IACA,OAAA,MAAA,EACA,YAAA,MAAA,CAAA,WAjBZ,uDAqBY,YAAA,OACA,OAAA,KAAA,EACA,MAAA,KC9BZ,0BACI,MAAA,KAGJ,UACI,WAAA,MADJ,oBAGQ,KAAA,QACA,MAAA,QACA,UAAA,KACA,YAAA,EAIR,8CAEQ,MAAA,KACA,KAAA,KACA,UAAA,KAIR,SACI,OAAA,kBAGJ,yCxC4+EA,0CACA,2CACA,iDwCv+EgB,OAAA,QAPhB,yCxCk/EA,0CACA,2CACA,iDwCr+EgB,OAAA,QAfhB,yCxCw/EA,0CACA,2CACA,iDwCn+EgB,OAAA,QAvBhB,yCxC8/EA,0CACA,2CACA,iDwCj+EgB,OAAA,QA/BhB,yCxCogFA,0CACA,2CACA,iDwC/9EgB,OAAA,QAvChB,yCxC0gFA,0CACA,2CACA,iDwC79EgB,OAAA,QA/ChB,yCxCghFA,0CACA,2CACA,iDwC39EgB,OAAA,QAOhB,sBxCw9EA,2BwCr9EQ,KAAA,QAIR,sBxCq9EA,2BwCl9EQ,KAAA,QAIR,sBxCk9EA,2BwC/8EQ,KAAA,QAIR,sBxC+8EA,2BwC58EQ,KAAA,QAIR,SACI,aAAA,IAGJ,kBACI,SAAA,SACA,QAAA,aACA,QAAA,EACA,UAAA,KACA,QAAA,IAAA,KACA,cAAA,IACA,WAAA,QACA,MAAA,QACA,WAAA,OACA,eAAA,KACA,QAAA,EACA,mBAAA,QAAA,IAAA,OAAA,WAAA,QAAA,IAAA,OAZJ,+BAcQ,QAAA,ECtIR,eACI,OAAA,KACA,SAAA,SACA,MAAA,KAGJ,uBACI,OAAA,MCPJ,YACI,mBAAA,EAAA,KAAA,KAAA,iBAAA,WAAA,EAAA,KAAA,KAAA,iBACA,QAAA,EAFJ,eAIQ,YAAA,KACA,YAAA,MAAA,CAAA,WALR,oBAOY,WAAA,QAPZ,eAYQ,OAAA,eAZR,eAeQ,iBAAA,QACA,MAAA,QAIR,qBACI,UAAA,KACA,YAAA,IAGJ,SAEQ,YAAA,aAAA,CAAA,kBAAA,CAAA,UAAA,CAAA,MAAA,CAAA,gBAAA,CAAA,KAAA,CAAA,WAAA,CAAA,UAAA,CAAA,mBAAA,CAAA,gBAAA,CAAA,iBAAA,CAAA,mBACA,KAAA,QAHR,SAAA,SAMQ,OAAA,QAKR,+BAGY,OAAA,KCvCZ,YACE,mBAAA,YAAA,WAAA,YACA,MAAA,eACA,OAAA,eACA,iBAAA,kBACA,mBAAA,EAAA,KAAA,KAAA,iBAAA,WAAA,EAAA,KAAA,KAAA,iBACA,QAAA,IAAA,eACA,cAAA,IACA,aAAA,kBAGF,UACE,MAAA,kBACA,UAAA,eACA,YAAA,eACA,YAAA,aAAA,CAAA,kBAAA,CAAA,UAAA,CAAA,MAAA,CAAA,gBAAA,CAAA,KAAA,CAAA,WAAA,CAAA,UAAA,CAAA,mBAAA,CAAA,gBAAA,CAAA,iBAAA,CAAA,6BACA,YAAA,cCfF,aACI,YAAA,KACA,cAAA,MAFJ,kBAKQ,cAAA,KACA,YAAA,IAMR,qBAEQ,OAAA,QACA,YAAA,KACA,YAAA,OACA,cAAA,SACA,QAAA,MACA,SAAA,OAPR,uBASY,cAAA,EACA,YAAA,QAVZ,mBAcQ,WAAA,OACA,eAAA,OACA,UAAA,KACA,MAAA,KACA,OAAA,KACA,YAAA,KACA,aAAA,KACA,MAAA,QACA,OAAA,IAAA,MAAA,QACA,cAAA,IACA,QAAA,aACA,mBAAA,IAAA,IAAA,WAAA,IAAA,IAzBR,2BA4BQ,gBAAA,YACA,WAAA,KA7BR,mCA+BY,MAAA,QAQZ,gCAEQ,iBAAA,QACA,WAAA,KACA,UAAA,MACA,YAAA,IACA,QAAA,KAAA,KAKR,gBACI,SAAA,MACA,IAAA,IACA,MAAA,EACA,qBAAA,YAAA,iBAAA,MAAA,aAAA,YACA,YAAA,IACA,iBAAA,QACA,MAAA,eACA,YAAA,KACA,QAAA,KAAA,IACA,cAAA,IAAA,EAAA,EAAA,IACA,kBAAA,iBAAA,UAAA,iBACA,eAAA,UAGJ,yBACI,gBACI,QAAA,MChFR,mBACE,iBAAA,QAGF,kBACE,iBAAA,KACA,QAAA,IACA,cAAA,IAHF,wBAMI,OAAA,IAAA,MAAA,QCPJ,cACI,mBAAA,EAAA,OAAA,KAAA,mBAAA,WAAA,EAAA,OAAA,KAAA,mBADJ,wCAIY,QAAA,KAJZ,0BAQQ,UAAA,KACA,YAAA,IATR,qBAYQ,OAAA,EAAA,MACA,mBAAA,EAAA,EAAA,KAAA,EAAA,mBAAA,WAAA,EAAA,EAAA,KAAA,EAAA,mBAOR,kBACI,OAAA,MACA,MAAA,MACA,OAAA,EAAA,KAGJ,OACI,QAAA,EACA,kBAAA,WAAA,KAAA,GAAA,GAAA,CAAA,QAAA,KAAA,GAAA,GAAA,SAAA,UAAA,WAAA,KAAA,GAAA,GAAA,CAAA,QAAA,KAAA,GAAA,GAAA,SAGJ,OACI,QAAA,EACA,kBAAA,WAAA,KAAA,GAAA,GAAA,CAAA,QAAA,KAAA,GAAA,GAAA,SAAA,UAAA,WAAA,KAAA,GAAA,GAAA,CAAA,QAAA,KAAA,GAAA,GAAA,SAGJ,OACI,QAAA,EACA,kBAAA,WAAA,KAAA,GAAA,GAAA,CAAA,QAAA,KAAA,GAAA,GAAA,SAAA,UAAA,WAAA,KAAA,GAAA,GAAA,CAAA,QAAA,KAAA,GAAA,GAAA,SAGJ,OACI,QAAA,EACA,kBAAA,WAAA,KAAA,GAAA,EAAA,CAAA,QAAA,KAAA,GAAA,GAAA,SAAA,UAAA,WAAA,KAAA,GAAA,EAAA,CAAA,QAAA,KAAA,GAAA,GAAA,SAGJ,OACI,QAAA,EACA,kBAAA,WAAA,KAAA,GAAA,IAAA,CAAA,QAAA,KAAA,GAAA,GAAA,SAAA,UAAA,WAAA,KAAA,GAAA,IAAA,CAAA,QAAA,KAAA,GAAA,GAAA,SAGJ,OACI,QAAA,EACA,kBAAA,WAAA,KAAA,GAAA,IAAA,CAAA,QAAA,KAAA,GAAA,GAAA,SAAA,UAAA,WAAA,KAAA,GAAA,IAAA,CAAA,QAAA,KAAA,GAAA,GAAA,SAGJ,OACI,QAAA,EACA,kBAAA,WAAA,KAAA,GAAA,IAAA,CAAA,QAAA,KAAA,GAAA,GAAA,SAAA,UAAA,WAAA,KAAA,GAAA,IAAA,CAAA,QAAA,KAAA,GAAA,GAAA,SAGJ,2BACI,GACE,kBAAA,eAAA,UAAA,eACA,QAAA,EAEF,IACE,kBAAA,mBAAA,UAAA,mBACA,QAAA,EAEF,IACE,kBAAA,kBAAA,UAAA,kBAEF,IACE,kBAAA,kBAAA,UAAA,kBACA,QAAA,EAEF,IACE,kBAAA,kBAAA,UAAA,kBAEF,IACE,kBAAA,kBAAA,UAAA,kBAEF,IACE,kBAAA,kBAAA,UAAA,kBAEF,IACE,kBAAA,iBAAA,UAAA,iBAEF,KACE,kBAAA,eAAA,UAAA,eACA,QAAA,GA9BN,mBACI,GACE,kBAAA,eAAA,UAAA,eACA,QAAA,EAEF,IACE,kBAAA,mBAAA,UAAA,mBACA,QAAA,EAEF,IACE,kBAAA,kBAAA,UAAA,kBAEF,IACE,kBAAA,kBAAA,UAAA,kBACA,QAAA,EAEF,IACE,kBAAA,kBAAA,UAAA,kBAEF,IACE,kBAAA,kBAAA,UAAA,kBAEF,IACE,kBAAA,kBAAA,UAAA,kBAEF,IACE,kBAAA,iBAAA,UAAA,iBAEF,KACE,kBAAA,eAAA,UAAA,eACA,QAAA,GAIN,2BACI,GACE,kBAAA,eAAA,UAAA,eACA,QAAA,EAEF,IACE,kBAAA,kBAAA,UAAA,kBACA,QAAA,EAEF,IACE,kBAAA,mBAAA,UAAA,mBACA,QAAA,EAEF,IACE,kBAAA,kBAAA,UAAA,kBAEF,IACE,kBAAA,kBAAA,UAAA,kBACA,QAAA,EAEF,IACE,kBAAA,kBAAA,UAAA,kBAEF,IACE,kBAAA,kBAAA,UAAA,kBAEF,IACE,kBAAA,iBAAA,UAAA,iBAEF,KACE,kBAAA,eAAA,UAAA,eACA,QAAA,GA/BN,mBACI,GACE,kBAAA,eAAA,UAAA,eACA,QAAA,EAEF,IACE,kBAAA,kBAAA,UAAA,kBACA,QAAA,EAEF,IACE,kBAAA,mBAAA,UAAA,mBACA,QAAA,EAEF,IACE,kBAAA,kBAAA,UAAA,kBAEF,IACE,kBAAA,kBAAA,UAAA,kBACA,QAAA,EAEF,IACE,kBAAA,kBAAA,UAAA,kBAEF,IACE,kBAAA,kBAAA,UAAA,kBAEF,IACE,kBAAA,iBAAA,UAAA,iBAEF,KACE,kBAAA,eAAA,UAAA,eACA,QAAA,GAIN,2BACI,GACE,kBAAA,eAAA,UAAA,eACA,QAAA,EAEF,IACE,kBAAA,kBAAA,UAAA,kBAEF,IACE,kBAAA,mBAAA,UAAA,mBACA,QAAA,EAEF,IACE,kBAAA,mBAAA,UAAA,mBACA,QAAA,EAEF,IACE,kBAAA,kBAAA,UAAA,kBAEF,IACE,kBAAA,kBAAA,UAAA,kBACA,QAAA,EAEF,IACE,kBAAA,kBAAA,UAAA,kBAEF,IACE,kBAAA,iBAAA,UAAA,iBAEF,KACE,kBAAA,eAAA,UAAA,eACA,QAAA,GA/BN,mBACI,GACE,kBAAA,eAAA,UAAA,eACA,QAAA,EAEF,IACE,kBAAA,kBAAA,UAAA,kBAEF,IACE,kBAAA,mBAAA,UAAA,mBACA,QAAA,EAEF,IACE,kBAAA,mBAAA,UAAA,mBACA,QAAA,EAEF,IACE,kBAAA,kBAAA,UAAA,kBAEF,IACE,kBAAA,kBAAA,UAAA,kBACA,QAAA,EAEF,IACE,kBAAA,kBAAA,UAAA,kBAEF,IACE,kBAAA,iBAAA,UAAA,iBAEF,KACE,kBAAA,eAAA,UAAA,eACA,QAAA,GAIN,2BACI,GACE,kBAAA,eAAA,UAAA,eACA,QAAA,EAEF,IACE,kBAAA,kBAAA,UAAA,kBAEF,IACE,kBAAA,mBAAA,UAAA,mBAEF,IACE,kBAAA,mBAAA,UAAA,mBACA,QAAA,EAEF,IACE,kBAAA,mBAAA,UAAA,mBACA,QAAA,EAEF,IACE,kBAAA,kBAAA,UAAA,kBAEF,IACE,kBAAA,kBAAA,UAAA,kBACA,QAAA,EAEF,IACE,kBAAA,iBAAA,UAAA,iBAEF,KACE,kBAAA,eAAA,UAAA,eACA,QAAA,GA/BN,mBACI,GACE,kBAAA,eAAA,UAAA,eACA,QAAA,EAEF,IACE,kBAAA,kBAAA,UAAA,kBAEF,IACE,kBAAA,mBAAA,UAAA,mBAEF,IACE,kBAAA,mBAAA,UAAA,mBACA,QAAA,EAEF,IACE,kBAAA,mBAAA,UAAA,mBACA,QAAA,EAEF,IACE,kBAAA,kBAAA,UAAA,kBAEF,IACE,kBAAA,kBAAA,UAAA,kBACA,QAAA,EAEF,IACE,kBAAA,iBAAA,UAAA,iBAEF,KACE,kBAAA,eAAA,UAAA,eACA,QAAA,GAIN,2BACI,GACE,kBAAA,eAAA,UAAA,eACA,QAAA,EAEF,IACE,kBAAA,kBAAA,UAAA,kBAEF,IACE,kBAAA,mBAAA,UAAA,mBAEF,IACE,kBAAA,mBAAA,UAAA,mBAEF,IACE,kBAAA,mBAAA,UAAA,mBACA,QAAA,EAEF,IACE,kBAAA,mBAAA,UAAA,mBACA,QAAA,EAEF,IACE,kBAAA,kBAAA,UAAA,kBAEF,IACE,kBAAA,iBAAA,UAAA,iBACA,QAAA,EAEF,KACE,kBAAA,eAAA,UAAA,eACA,QAAA,GA/BN,mBACI,GACE,kBAAA,eAAA,UAAA,eACA,QAAA,EAEF,IACE,kBAAA,kBAAA,UAAA,kBAEF,IACE,kBAAA,mBAAA,UAAA,mBAEF,IACE,kBAAA,mBAAA,UAAA,mBAEF,IACE,kBAAA,mBAAA,UAAA,mBACA,QAAA,EAEF,IACE,kBAAA,mBAAA,UAAA,mBACA,QAAA,EAEF,IACE,kBAAA,kBAAA,UAAA,kBAEF,IACE,kBAAA,iBAAA,UAAA,iBACA,QAAA,EAEF,KACE,kBAAA,eAAA,UAAA,eACA,QAAA,GAIN,2BACI,GACE,kBAAA,eAAA,UAAA,eACA,QAAA,EAEF,IACE,kBAAA,kBAAA,UAAA,kBAEF,IACE,kBAAA,mBAAA,UAAA,mBAEF,IACE,kBAAA,mBAAA,UAAA,mBAEF,IACE,kBAAA,mBAAA,UAAA,mBAEF,IACE,kBAAA,mBAAA,UAAA,mBACA,QAAA,EAEF,IACE,kBAAA,mBAAA,UAAA,mBACA,QAAA,EAEF,IACE,kBAAA,kBAAA,UAAA,kBAEF,KACE,kBAAA,eAAA,UAAA,eACA,QAAA,GA9BN,mBACI,GACE,kBAAA,eAAA,UAAA,eACA,QAAA,EAEF,IACE,kBAAA,kBAAA,UAAA,kBAEF,IACE,kBAAA,mBAAA,UAAA,mBAEF,IACE,kBAAA,mBAAA,UAAA,mBAEF,IACE,kBAAA,mBAAA,UAAA,mBAEF,IACE,kBAAA,mBAAA,UAAA,mBACA,QAAA,EAEF,IACE,kBAAA,mBAAA,UAAA,mBACA,QAAA,EAEF,IACE,kBAAA,kBAAA,UAAA,kBAEF,KACE,kBAAA,eAAA,UAAA,eACA,QAAA,GAMN,SACE,OAAA,KAAA,KACA,OAAA,KACA,MAAA,MAHF,eAMI,QAAA,GACA,SAAA,SACA,OAAA,KACA,MAAA,KACA,WAAA,kBACA,cAAA,IACA,WAAA,EACA,YAAA,KACA,QAAA,GAdJ,cAiBI,OAAA,KACA,YAAA,KAAA,MAAA,YACA,aAAA,KAAA,MAAA,YACA,cAAA,KAAA,MAAA,QApBJ,oBAsBM,QAAA,GACA,SAAA,SACA,OAAA,KACA,MAAA,KACA,WAAA,IACA,YAAA,KACA,WAAA,QAEA,kBAAA,kBACA,UAAA,YA/BN,qBAkCM,QAAA,GACA,SAAA,SACA,OAAA,KACA,MAAA,KACA,WAAA,IACA,YAAA,MACA,WAAA,QACA,kBAAA,aAAA,UAAA,aAzCN,cA6CI,OAAA,KACA,YAAA,KAAA,MAAA,YACA,aAAA,KAAA,MAAA,YACA,WAAA,KAAA,MAAA,QAhDJ,qBAkDM,QAAA,GACA,SAAA,SACA,OAAA,KACA,WAAA,MACA,YAAA,MACA,YAAA,KAAA,MAAA,YACA,aAAA,KAAA,MAAA,YACA,WAAA,KAAA,MAAA,QC3UN,UACI,gBAAA,SACA,eAAA,EACA,QAAA,MACA,cAAA,KACA,SAAA,SACA,aAAA,MACA,MAAA,KAPJ,qBAUM,cAAA,KACA,aAAA,MACA,WAAA,KACA,SAAA,SACA,WAAA,MAdN,uBAgBQ,MAAA,KAhBR,iBAoBM,iBAAA,qBACA,OAAA,EACA,QAAA,GACA,KAAA,IACA,SAAA,SACA,IAAA,KACA,MAAA,IACA,QAAA,EA3BN,yBA8BM,WAAA,QACA,cAAA,IACA,MAAA,KACA,QAAA,MACA,OAAA,KACA,KAAA,MACA,WAAA,MACA,SAAA,SACA,WAAA,OACA,IAAA,IACA,MAAA,KAxCN,2BA0CQ,MAAA,QA1CR,4BA+CQ,UAAA,KACA,WAAA,IAMN,kBAEI,MAAA,QACA,UAAA,KACA,YAAA,IACA,OAAA,EAAA,EAAA,IACA,eAAA,UAIJ,eACE,QAAA,UADF,sBAGI,QAAA,GACA,QAAA,MACA,MAAA,IALJ,qCASM,cAAA,IAAA,MAAA,YACA,aAAA,IAAA,MAAA,eACA,WAAA,IAAA,MAAA,YACA,QAAA,MACA,OAAA,EACA,KAAA,KACA,WAAA,MACA,SAAA,SACA,IAAA,IACA,MAAA,EAKN,yBAEI,QAAA,GACA,QAAA,MACA,MAAA,IAJJ,6CAQM,cAAA,IAAA,MAAA,YACA,YAAA,IAAA,MAAA,eACA,WAAA,IAAA,MAAA,YACA,QAAA,MACA,OAAA,EACA,KAAA,KACA,WAAA,MACA,SAAA,SACA,MAAA,KACA,IAAA,IACA,MAAA,EAlBN,yCAqBM,MAAA,MACA,WAAA,KAtBN,2CAwBQ,MAAA,MACA,YAAA,IAzBR,kCA8BI,KAAA,KACA,MAAA,MA/BJ,0BAkCI,QAAA,KAlCJ,0BAqCI,YAAA,EACA,aAAA,KAtCJ,0CAwCM,WAAA,eAxCN,sBA4CI,WAAA,MA5CJ,qBA+CI,WAAA,MA/CJ,kCAkDI,WAAA,MAIJ,eACE,QAAA,WACA,eAAA,IACA,MAAA,IAHF,kBAKI,UAAA,KACA,YAAA,IACA,OAAA,EAPJ,sBAUI,WAAA,KACA,QAAA,MACA,cAAA,IACA,YAAA,KACA,SAAA,SACA,WAAA,KACA,QAAA,KACA,cAAA,IACA,mBAAA,EAAA,OAAA,KAAA,mBAAA,WAAA,EAAA,OAAA,KAAA,mBAlBJ,uBAsBM,MAAA,QACA,QAAA,MACA,UAAA,KACA,cAAA,IAzBN,iBA6BI,MAAA,QACA,UAAA,KACA,cAAA,EA/BJ,sBAkCI,WAAA,KAlCJ,wBAoCM,MAAA,KACA,aAAA,IArCN,0BAyCM,OAAA,KACA,MAAA,KACA,cAAA,IA3CN,6BA+CI,WAAA,KAAA,OAAA,OAAA,EAAA,EAAA,KACA,WAAA,KACA,QAAA,IC7LN,YACI,MAAA,KACA,UAAA,KACA,YAAA,MAHJ,wBAKQ,YAAA,qBAAA,IAAA,GAAA,CAAA,qBAAA,KAAA,GAAA,CAAA,qBAAA,IAAA,ICNR,mBAEQ,mBAAA,IAAA,IAAA,SAAA,WAAA,IAAA,IAAA,SACA,MAAA,QACA,cAAA,IACA,QAAA,IAAA,KACA,QAAA,aACA,UAAA,KACA,YAAA,IACA,eAAA,UATR,yBAWY,MAAA,QAXZ,2BAeQ,iBAAA,QACA,MAAA,KAKR,aACI,iBAAA,KACA,WAAA,KACA,cAAA,IACA,SAAA,OAJJ,eAMQ,QAAA,MACA,iBAAA,QACA,SAAA,OARR,8BAaU,SAAA,SACA,kBAAA,YAAA,UAAA,YACA,QAAA,GAKV,WACI,SAAA,OACA,MAAA,KACA,mBAAA,IAAA,IAAA,SAAA,WAAA,IAAA,IAAA,SC5CJ,gBACI,UAAA,KACA,YAAA,IACA,WAAA,OACA,MAAA,QAJJ,qBAMQ,UAAA,KACA,YAAA,IACA,QAAA,MACA,eAAA,UACA,YAAA,IACA,MAAA,QAIR,YACI,MAAA,KACA,MAAA,ICjBJ,KACI,UAAA,IAGJ,KACI,WAAA,MCHJ,eACI,WAAA,MADJ,oBAGQ,WAAA,MACA,KAAA,eACA,MAAA,EACA,OAAA,KANR,iCAUQ,KAAA,eACA,MAAA,eAXR,+CAaY,kBAAA,WAAA,UAAA,WAKZ,qBACI,MAAA,eACA,KAAA,YAFJ,0BAIQ,KAAA,YAIR,kBACI,MAAA,MACA,MAAA,MAGJ,8BAAA,+BAGY,MAAA,KAOZ,GACI,cAAA,EAMJ,WACI,OAAA,QAAA,OAAA,QAAA,MAGJ,iBACI,OAAA,QAAA,MAAA,QAAA,OAGJ,WpDm6GA,oBoDj6GI,UAAA,IAKJ,8CAIgB,aAAA,EACA,uBAAA,EACA,0BAAA,ECtDZ,wBAAA,OACA,2BAAA,OD+CJ,6CAYgB,wBAAA,EACA,2BAAA,EC/CZ,uBAAA,OACA,0BAAA,ODwDJ,oBACI,WAAA,eAKJ,GACI,aAAA,EAKJ,qBAEQ,OAAA,MAAA,KAAA,MAAA,MAIR,iCAEQ,aAAA,OACA,YAAA,EAHR,gCAOQ,YAAA,OACA,aAAA,EAOR,mBACI,aAAA,QACA,cAAA,QAFJ,0BAKQ,KAAA,EACA,MAAA,KAOR,kCAEQ,cAAA,MACA,aAAA,EAHR,0CAKY,aAAA,MACA,cAAA,EAOZ,mBACI,YAAA,OACA,aAAA,EAGJ,uBACI,YAAA,KACA,aAAA,EAGJ,gBACI,cAAA,OACA,aAAA,EAGJ,8BAEQ,KAAA,KACA,MAAA,QAHR,6BAQQ,KAAA,KACA,MAAA,QAIR,eACI,cAAA,QACA,aAAA,EAFJ,6CAMY,MAAA,SACA,KAAA,KAPZ,4CAWY,MAAA,qBACA,KAAA,KAZZ,0EAkBY,kBAAA,oBAAA,UAAA,oBAKZ,0BAEQ,MAAA,KACA,KAAA,EACA,aAAA,QAQR,qBACI,YAAA,KACA,aAAA,EAGJ,oBACI,aAAA,KACA,YAAA,EpDu2GJ,6CACA,4CAHA,wFACA,+EAHA,uDACA,oEoDj2GA,uCpD+1GA,oDoDv1GI,wBAAA,OACA,2BAAA,OACA,uBAAA,EACA,0BAAA,EpDs2GJ,8CACA,6CoDp2GA,sCpD81GA,mDAGA,qEACA,kFAHA,yDACA,sEoDx1GI,uBAAA,OACA,0BAAA,OACA,wBAAA,EACA,2BAAA,EAGJ,mCAEQ,YAAA,IACA,aAAA,EErPA,KAAgC,OAAA,YAChC,MtDqlHR,MsDnlHU,WAAA,YAEF,MACE,YAAA,YACA,aAAA,YAEF,MtDqlHR,MsDnlHU,cAAA,YAEF,MACE,aAAA,YACA,YAAA,YAfF,KAAgC,OAAA,iBAChC,MtDwmHR,MsDtmHU,WAAA,iBAEF,MACE,YAAA,iBACA,aAAA,YAEF,MtDwmHR,MsDtmHU,cAAA,iBAEF,MACE,aAAA,iBACA,YAAA,YAfF,KAAgC,OAAA,gBAChC,MtD2nHR,MsDznHU,WAAA,gBAEF,MACE,YAAA,gBACA,aAAA,YAEF,MtD2nHR,MsDznHU,cAAA,gBAEF,MACE,aAAA,gBACA,YAAA,YAfF,KAAgC,OAAA,eAChC,MtD8oHR,MsD5oHU,WAAA,eAEF,MACE,YAAA,eACA,aAAA,YAEF,MtD8oHR,MsD5oHU,cAAA,eAEF,MACE,aAAA,eACA,YAAA,YAfF,KAAgC,OAAA,iBAChC,MtDiqHR,MsD/pHU,WAAA,iBAEF,MACE,YAAA,iBACA,aAAA,YAEF,MtDiqHR,MsD/pHU,cAAA,iBAEF,MACE,aAAA,iBACA,YAAA,YAfF,KAAgC,OAAA,eAChC,MtDorHR,MsDlrHU,WAAA,eAEF,MACE,YAAA,eACA,aAAA,YAEF,MtDorHR,MsDlrHU,cAAA,eAEF,MACE,aAAA,eACA,YAAA,YAfF,KAAgC,QAAA,YAChC,MtDusHR,MsDrsHU,YAAA,YAEF,MACE,aAAA,YACA,cAAA,YAEF,MtDusHR,MsDrsHU,eAAA,YAEF,MACE,cAAA,YACA,aAAA,YAfF,KAAgC,QAAA,iBAChC,MtD0tHR,MsDxtHU,YAAA,iBAEF,MACE,aAAA,iBACA,cAAA,YAEF,MtD0tHR,MsDxtHU,eAAA,iBAEF,MACE,cAAA,iBACA,aAAA,YAfF,KAAgC,QAAA,gBAChC,MtD6uHR,MsD3uHU,YAAA,gBAEF,MACE,aAAA,gBACA,cAAA,YAEF,MtD6uHR,MsD3uHU,eAAA,gBAEF,MACE,cAAA,gBACA,aAAA,YAfF,KAAgC,QAAA,eAChC,MtDgwHR,MsD9vHU,YAAA,eAEF,MACE,aAAA,eACA,cAAA,YAEF,MtDgwHR,MsD9vHU,eAAA,eAEF,MACE,cAAA,eACA,aAAA,YAfF,KAAgC,QAAA,iBAChC,MtDmxHR,MsDjxHU,YAAA,iBAEF,MACE,aAAA,iBACA,cAAA,YAEF,MtDmxHR,MsDjxHU,eAAA,iBAEF,MACE,cAAA,iBACA,aAAA,YAfF,KAAgC,QAAA,eAChC,MtDsyHR,MsDpyHU,YAAA,eAEF,MACE,aAAA,eACA,cAAA,YAEF,MtDsyHR,MsDpyHU,eAAA,eAEF,MACE,cAAA,eACA,aAAA,YAQF,MAAwB,OAAA,kBACxB,OtDkyHR,OsDhyHU,WAAA,kBAEF,OtDkyHR,OsDhyHU,aAAA,kBAEF,OtDkyHR,OsDhyHU,cAAA,kBAEF,OtDkyHR,OsDhyHU,YAAA,kBAfF,MAAwB,OAAA,iBACxB,OtDqzHR,OsDnzHU,WAAA,iBAEF,OtDqzHR,OsDnzHU,aAAA,iBAEF,OtDqzHR,OsDnzHU,cAAA,iBAEF,OtDqzHR,OsDnzHU,YAAA,iBAfF,MAAwB,OAAA,gBACxB,OtDw0HR,OsDt0HU,WAAA,gBAEF,OtDw0HR,OsDt0HU,aAAA,gBAEF,OtDw0HR,OsDt0HU,cAAA,gBAEF,OtDw0HR,OsDt0HU,YAAA,gBAfF,MAAwB,OAAA,kBACxB,OtD21HR,OsDz1HU,WAAA,kBAEF,OtD21HR,OsDz1HU,aAAA,kBAEF,OtD21HR,OsDz1HU,cAAA,kBAEF,OtD21HR,OsDz1HU,YAAA,kBAfF,MAAwB,OAAA,gBACxB,OtD82HR,OsD52HU,WAAA,gBAEF,OtD82HR,OsD52HU,aAAA,gBAEF,OtD82HR,OsD52HU,cAAA,gBAEF,OtD82HR,OsD52HU,YAAA,gBAMN,QAAmB,OAAA,eACnB,StD42HJ,SsD12HM,WAAA,eAEF,StD42HJ,SsD12HM,YAAA,eACA,aAAA,kBAEF,StD42HJ,SsD12HM,cAAA,eAEF,StD42HJ,SsD12HM,aAAA,eACA,YAAA,erDXF,yBqDlDI,QAAgC,OAAA,YAChC,StD66HN,SsD36HQ,WAAA,YAEF,SACE,YAAA,YACA,aAAA,YAEF,StD26HN,SsDz6HQ,cAAA,YAEF,SACE,aAAA,YACA,YAAA,YAfF,QAAgC,OAAA,iBAChC,StD27HN,SsDz7HQ,WAAA,iBAEF,SACE,YAAA,iBACA,aAAA,YAEF,StDy7HN,SsDv7HQ,cAAA,iBAEF,SACE,aAAA,iBACA,YAAA,YAfF,QAAgC,OAAA,gBAChC,StDy8HN,SsDv8HQ,WAAA,gBAEF,SACE,YAAA,gBACA,aAAA,YAEF,StDu8HN,SsDr8HQ,cAAA,gBAEF,SACE,aAAA,gBACA,YAAA,YAfF,QAAgC,OAAA,eAChC,StDu9HN,SsDr9HQ,WAAA,eAEF,SACE,YAAA,eACA,aAAA,YAEF,StDq9HN,SsDn9HQ,cAAA,eAEF,SACE,aAAA,eACA,YAAA,YAfF,QAAgC,OAAA,iBAChC,StDq+HN,SsDn+HQ,WAAA,iBAEF,SACE,YAAA,iBACA,aAAA,YAEF,StDm+HN,SsDj+HQ,cAAA,iBAEF,SACE,aAAA,iBACA,YAAA,YAfF,QAAgC,OAAA,eAChC,StDm/HN,SsDj/HQ,WAAA,eAEF,SACE,YAAA,eACA,aAAA,YAEF,StDi/HN,SsD/+HQ,cAAA,eAEF,SACE,aAAA,eACA,YAAA,YAfF,QAAgC,QAAA,YAChC,StDigIN,SsD//HQ,YAAA,YAEF,SACE,aAAA,YACA,cAAA,YAEF,StD+/HN,SsD7/HQ,eAAA,YAEF,SACE,cAAA,YACA,aAAA,YAfF,QAAgC,QAAA,iBAChC,StD+gIN,SsD7gIQ,YAAA,iBAEF,SACE,aAAA,iBACA,cAAA,YAEF,StD6gIN,SsD3gIQ,eAAA,iBAEF,SACE,cAAA,iBACA,aAAA,YAfF,QAAgC,QAAA,gBAChC,StD6hIN,SsD3hIQ,YAAA,gBAEF,SACE,aAAA,gBACA,cAAA,YAEF,StD2hIN,SsDzhIQ,eAAA,gBAEF,SACE,cAAA,gBACA,aAAA,YAfF,QAAgC,QAAA,eAChC,StD2iIN,SsDziIQ,YAAA,eAEF,SACE,aAAA,eACA,cAAA,YAEF,StDyiIN,SsDviIQ,eAAA,eAEF,SACE,cAAA,eACA,aAAA,YAfF,QAAgC,QAAA,iBAChC,StDyjIN,SsDvjIQ,YAAA,iBAEF,SACE,aAAA,iBACA,cAAA,YAEF,StDujIN,SsDrjIQ,eAAA,iBAEF,SACE,cAAA,iBACA,aAAA,YAfF,QAAgC,QAAA,eAChC,StDukIN,SsDrkIQ,YAAA,eAEF,SACE,aAAA,eACA,cAAA,YAEF,StDqkIN,SsDnkIQ,eAAA,eAEF,SACE,cAAA,eACA,aAAA,YAQF,SAAwB,OAAA,kBACxB,UtD8jIN,UsD5jIQ,WAAA,kBAEF,UtD6jIN,UsD3jIQ,aAAA,kBAEF,UtD4jIN,UsD1jIQ,cAAA,kBAEF,UtD2jIN,UsDzjIQ,YAAA,kBAfF,SAAwB,OAAA,iBACxB,UtD4kIN,UsD1kIQ,WAAA,iBAEF,UtD2kIN,UsDzkIQ,aAAA,iBAEF,UtD0kIN,UsDxkIQ,cAAA,iBAEF,UtDykIN,UsDvkIQ,YAAA,iBAfF,SAAwB,OAAA,gBACxB,UtD0lIN,UsDxlIQ,WAAA,gBAEF,UtDylIN,UsDvlIQ,aAAA,gBAEF,UtDwlIN,UsDtlIQ,cAAA,gBAEF,UtDulIN,UsDrlIQ,YAAA,gBAfF,SAAwB,OAAA,kBACxB,UtDwmIN,UsDtmIQ,WAAA,kBAEF,UtDumIN,UsDrmIQ,aAAA,kBAEF,UtDsmIN,UsDpmIQ,cAAA,kBAEF,UtDqmIN,UsDnmIQ,YAAA,kBAfF,SAAwB,OAAA,gBACxB,UtDsnIN,UsDpnIQ,WAAA,gBAEF,UtDqnIN,UsDnnIQ,aAAA,gBAEF,UtDonIN,UsDlnIQ,cAAA,gBAEF,UtDmnIN,UsDjnIQ,YAAA,gBAMN,WAAmB,OAAA,eACnB,YtD+mIF,YsD7mII,WAAA,eAEF,YtD8mIF,YsD5mII,YAAA,eACA,aAAA,kBAEF,YtD6mIF,YsD3mII,cAAA,eAEF,YtD4mIF,YsD1mII,aAAA,eACA,YAAA,gBrDXF,yBqDlDI,QAAgC,OAAA,YAChC,StD6qIN,SsD3qIQ,WAAA,YAEF,SACE,YAAA,YACA,aAAA,YAEF,StD2qIN,SsDzqIQ,cAAA,YAEF,SACE,aAAA,YACA,YAAA,YAfF,QAAgC,OAAA,iBAChC,StD2rIN,SsDzrIQ,WAAA,iBAEF,SACE,YAAA,iBACA,aAAA,YAEF,StDyrIN,SsDvrIQ,cAAA,iBAEF,SACE,aAAA,iBACA,YAAA,YAfF,QAAgC,OAAA,gBAChC,StDysIN,SsDvsIQ,WAAA,gBAEF,SACE,YAAA,gBACA,aAAA,YAEF,StDusIN,SsDrsIQ,cAAA,gBAEF,SACE,aAAA,gBACA,YAAA,YAfF,QAAgC,OAAA,eAChC,StDutIN,SsDrtIQ,WAAA,eAEF,SACE,YAAA,eACA,aAAA,YAEF,StDqtIN,SsDntIQ,cAAA,eAEF,SACE,aAAA,eACA,YAAA,YAfF,QAAgC,OAAA,iBAChC,StDquIN,SsDnuIQ,WAAA,iBAEF,SACE,YAAA,iBACA,aAAA,YAEF,StDmuIN,SsDjuIQ,cAAA,iBAEF,SACE,aAAA,iBACA,YAAA,YAfF,QAAgC,OAAA,eAChC,StDmvIN,SsDjvIQ,WAAA,eAEF,SACE,YAAA,eACA,aAAA,YAEF,StDivIN,SsD/uIQ,cAAA,eAEF,SACE,aAAA,eACA,YAAA,YAfF,QAAgC,QAAA,YAChC,StDiwIN,SsD/vIQ,YAAA,YAEF,SACE,aAAA,YACA,cAAA,YAEF,StD+vIN,SsD7vIQ,eAAA,YAEF,SACE,cAAA,YACA,aAAA,YAfF,QAAgC,QAAA,iBAChC,StD+wIN,SsD7wIQ,YAAA,iBAEF,SACE,aAAA,iBACA,cAAA,YAEF,StD6wIN,SsD3wIQ,eAAA,iBAEF,SACE,cAAA,iBACA,aAAA,YAfF,QAAgC,QAAA,gBAChC,StD6xIN,SsD3xIQ,YAAA,gBAEF,SACE,aAAA,gBACA,cAAA,YAEF,StD2xIN,SsDzxIQ,eAAA,gBAEF,SACE,cAAA,gBACA,aAAA,YAfF,QAAgC,QAAA,eAChC,StD2yIN,SsDzyIQ,YAAA,eAEF,SACE,aAAA,eACA,cAAA,YAEF,StDyyIN,SsDvyIQ,eAAA,eAEF,SACE,cAAA,eACA,aAAA,YAfF,QAAgC,QAAA,iBAChC,StDyzIN,SsDvzIQ,YAAA,iBAEF,SACE,aAAA,iBACA,cAAA,YAEF,StDuzIN,SsDrzIQ,eAAA,iBAEF,SACE,cAAA,iBACA,aAAA,YAfF,QAAgC,QAAA,eAChC,StDu0IN,SsDr0IQ,YAAA,eAEF,SACE,aAAA,eACA,cAAA,YAEF,StDq0IN,SsDn0IQ,eAAA,eAEF,SACE,cAAA,eACA,aAAA,YAQF,SAAwB,OAAA,kBACxB,UtD8zIN,UsD5zIQ,WAAA,kBAEF,UtD6zIN,UsD3zIQ,aAAA,kBAEF,UtD4zIN,UsD1zIQ,cAAA,kBAEF,UtD2zIN,UsDzzIQ,YAAA,kBAfF,SAAwB,OAAA,iBACxB,UtD40IN,UsD10IQ,WAAA,iBAEF,UtD20IN,UsDz0IQ,aAAA,iBAEF,UtD00IN,UsDx0IQ,cAAA,iBAEF,UtDy0IN,UsDv0IQ,YAAA,iBAfF,SAAwB,OAAA,gBACxB,UtD01IN,UsDx1IQ,WAAA,gBAEF,UtDy1IN,UsDv1IQ,aAAA,gBAEF,UtDw1IN,UsDt1IQ,cAAA,gBAEF,UtDu1IN,UsDr1IQ,YAAA,gBAfF,SAAwB,OAAA,kBACxB,UtDw2IN,UsDt2IQ,WAAA,kBAEF,UtDu2IN,UsDr2IQ,aAAA,kBAEF,UtDs2IN,UsDp2IQ,cAAA,kBAEF,UtDq2IN,UsDn2IQ,YAAA,kBAfF,SAAwB,OAAA,gBACxB,UtDs3IN,UsDp3IQ,WAAA,gBAEF,UtDq3IN,UsDn3IQ,aAAA,gBAEF,UtDo3IN,UsDl3IQ,cAAA,gBAEF,UtDm3IN,UsDj3IQ,YAAA,gBAMN,WAAmB,OAAA,eACnB,YtD+2IF,YsD72II,WAAA,eAEF,YtD82IF,YsD52II,YAAA,eACA,aAAA,kBAEF,YtD62IF,YsD32II,cAAA,eAEF,YtD42IF,YsD12II,aAAA,eACA,YAAA,gBrDXF,yBqDlDI,QAAgC,OAAA,YAChC,StD66IN,SsD36IQ,WAAA,YAEF,SACE,YAAA,YACA,aAAA,YAEF,StD26IN,SsDz6IQ,cAAA,YAEF,SACE,aAAA,YACA,YAAA,YAfF,QAAgC,OAAA,iBAChC,StD27IN,SsDz7IQ,WAAA,iBAEF,SACE,YAAA,iBACA,aAAA,YAEF,StDy7IN,SsDv7IQ,cAAA,iBAEF,SACE,aAAA,iBACA,YAAA,YAfF,QAAgC,OAAA,gBAChC,StDy8IN,SsDv8IQ,WAAA,gBAEF,SACE,YAAA,gBACA,aAAA,YAEF,StDu8IN,SsDr8IQ,cAAA,gBAEF,SACE,aAAA,gBACA,YAAA,YAfF,QAAgC,OAAA,eAChC,StDu9IN,SsDr9IQ,WAAA,eAEF,SACE,YAAA,eACA,aAAA,YAEF,StDq9IN,SsDn9IQ,cAAA,eAEF,SACE,aAAA,eACA,YAAA,YAfF,QAAgC,OAAA,iBAChC,StDq+IN,SsDn+IQ,WAAA,iBAEF,SACE,YAAA,iBACA,aAAA,YAEF,StDm+IN,SsDj+IQ,cAAA,iBAEF,SACE,aAAA,iBACA,YAAA,YAfF,QAAgC,OAAA,eAChC,StDm/IN,SsDj/IQ,WAAA,eAEF,SACE,YAAA,eACA,aAAA,YAEF,StDi/IN,SsD/+IQ,cAAA,eAEF,SACE,aAAA,eACA,YAAA,YAfF,QAAgC,QAAA,YAChC,StDigJN,SsD//IQ,YAAA,YAEF,SACE,aAAA,YACA,cAAA,YAEF,StD+/IN,SsD7/IQ,eAAA,YAEF,SACE,cAAA,YACA,aAAA,YAfF,QAAgC,QAAA,iBAChC,StD+gJN,SsD7gJQ,YAAA,iBAEF,SACE,aAAA,iBACA,cAAA,YAEF,StD6gJN,SsD3gJQ,eAAA,iBAEF,SACE,cAAA,iBACA,aAAA,YAfF,QAAgC,QAAA,gBAChC,StD6hJN,SsD3hJQ,YAAA,gBAEF,SACE,aAAA,gBACA,cAAA,YAEF,StD2hJN,SsDzhJQ,eAAA,gBAEF,SACE,cAAA,gBACA,aAAA,YAfF,QAAgC,QAAA,eAChC,StD2iJN,SsDziJQ,YAAA,eAEF,SACE,aAAA,eACA,cAAA,YAEF,StDyiJN,SsDviJQ,eAAA,eAEF,SACE,cAAA,eACA,aAAA,YAfF,QAAgC,QAAA,iBAChC,StDyjJN,SsDvjJQ,YAAA,iBAEF,SACE,aAAA,iBACA,cAAA,YAEF,StDujJN,SsDrjJQ,eAAA,iBAEF,SACE,cAAA,iBACA,aAAA,YAfF,QAAgC,QAAA,eAChC,StDukJN,SsDrkJQ,YAAA,eAEF,SACE,aAAA,eACA,cAAA,YAEF,StDqkJN,SsDnkJQ,eAAA,eAEF,SACE,cAAA,eACA,aAAA,YAQF,SAAwB,OAAA,kBACxB,UtD8jJN,UsD5jJQ,WAAA,kBAEF,UtD6jJN,UsD3jJQ,aAAA,kBAEF,UtD4jJN,UsD1jJQ,cAAA,kBAEF,UtD2jJN,UsDzjJQ,YAAA,kBAfF,SAAwB,OAAA,iBACxB,UtD4kJN,UsD1kJQ,WAAA,iBAEF,UtD2kJN,UsDzkJQ,aAAA,iBAEF,UtD0kJN,UsDxkJQ,cAAA,iBAEF,UtDykJN,UsDvkJQ,YAAA,iBAfF,SAAwB,OAAA,gBACxB,UtD0lJN,UsDxlJQ,WAAA,gBAEF,UtDylJN,UsDvlJQ,aAAA,gBAEF,UtDwlJN,UsDtlJQ,cAAA,gBAEF,UtDulJN,UsDrlJQ,YAAA,gBAfF,SAAwB,OAAA,kBACxB,UtDwmJN,UsDtmJQ,WAAA,kBAEF,UtDumJN,UsDrmJQ,aAAA,kBAEF,UtDsmJN,UsDpmJQ,cAAA,kBAEF,UtDqmJN,UsDnmJQ,YAAA,kBAfF,SAAwB,OAAA,gBACxB,UtDsnJN,UsDpnJQ,WAAA,gBAEF,UtDqnJN,UsDnnJQ,aAAA,gBAEF,UtDonJN,UsDlnJQ,cAAA,gBAEF,UtDmnJN,UsDjnJQ,YAAA,gBAMN,WAAmB,OAAA,eACnB,YtD+mJF,YsD7mJI,WAAA,eAEF,YtD8mJF,YsD5mJI,YAAA,eACA,aAAA,kBAEF,YtD6mJF,YsD3mJI,cAAA,eAEF,YtD4mJF,YsD1mJI,aAAA,eACA,YAAA,gBrDXF,0BqDlDI,QAAgC,OAAA,YAChC,StD6qJN,SsD3qJQ,WAAA,YAEF,SACE,YAAA,YACA,aAAA,YAEF,StD2qJN,SsDzqJQ,cAAA,YAEF,SACE,aAAA,YACA,YAAA,YAfF,QAAgC,OAAA,iBAChC,StD2rJN,SsDzrJQ,WAAA,iBAEF,SACE,YAAA,iBACA,aAAA,YAEF,StDyrJN,SsDvrJQ,cAAA,iBAEF,SACE,aAAA,iBACA,YAAA,YAfF,QAAgC,OAAA,gBAChC,StDysJN,SsDvsJQ,WAAA,gBAEF,SACE,YAAA,gBACA,aAAA,YAEF,StDusJN,SsDrsJQ,cAAA,gBAEF,SACE,aAAA,gBACA,YAAA,YAfF,QAAgC,OAAA,eAChC,StDutJN,SsDrtJQ,WAAA,eAEF,SACE,YAAA,eACA,aAAA,YAEF,StDqtJN,SsDntJQ,cAAA,eAEF,SACE,aAAA,eACA,YAAA,YAfF,QAAgC,OAAA,iBAChC,StDquJN,SsDnuJQ,WAAA,iBAEF,SACE,YAAA,iBACA,aAAA,YAEF,StDmuJN,SsDjuJQ,cAAA,iBAEF,SACE,aAAA,iBACA,YAAA,YAfF,QAAgC,OAAA,eAChC,StDmvJN,SsDjvJQ,WAAA,eAEF,SACE,YAAA,eACA,aAAA,YAEF,StDivJN,SsD/uJQ,cAAA,eAEF,SACE,aAAA,eACA,YAAA,YAfF,QAAgC,QAAA,YAChC,StDiwJN,SsD/vJQ,YAAA,YAEF,SACE,aAAA,YACA,cAAA,YAEF,StD+vJN,SsD7vJQ,eAAA,YAEF,SACE,cAAA,YACA,aAAA,YAfF,QAAgC,QAAA,iBAChC,StD+wJN,SsD7wJQ,YAAA,iBAEF,SACE,aAAA,iBACA,cAAA,YAEF,StD6wJN,SsD3wJQ,eAAA,iBAEF,SACE,cAAA,iBACA,aAAA,YAfF,QAAgC,QAAA,gBAChC,StD6xJN,SsD3xJQ,YAAA,gBAEF,SACE,aAAA,gBACA,cAAA,YAEF,StD2xJN,SsDzxJQ,eAAA,gBAEF,SACE,cAAA,gBACA,aAAA,YAfF,QAAgC,QAAA,eAChC,StD2yJN,SsDzyJQ,YAAA,eAEF,SACE,aAAA,eACA,cAAA,YAEF,StDyyJN,SsDvyJQ,eAAA,eAEF,SACE,cAAA,eACA,aAAA,YAfF,QAAgC,QAAA,iBAChC,StDyzJN,SsDvzJQ,YAAA,iBAEF,SACE,aAAA,iBACA,cAAA,YAEF,StDuzJN,SsDrzJQ,eAAA,iBAEF,SACE,cAAA,iBACA,aAAA,YAfF,QAAgC,QAAA,eAChC,StDu0JN,SsDr0JQ,YAAA,eAEF,SACE,aAAA,eACA,cAAA,YAEF,StDq0JN,SsDn0JQ,eAAA,eAEF,SACE,cAAA,eACA,aAAA,YAQF,SAAwB,OAAA,kBACxB,UtD8zJN,UsD5zJQ,WAAA,kBAEF,UtD6zJN,UsD3zJQ,aAAA,kBAEF,UtD4zJN,UsD1zJQ,cAAA,kBAEF,UtD2zJN,UsDzzJQ,YAAA,kBAfF,SAAwB,OAAA,iBACxB,UtD40JN,UsD10JQ,WAAA,iBAEF,UtD20JN,UsDz0JQ,aAAA,iBAEF,UtD00JN,UsDx0JQ,cAAA,iBAEF,UtDy0JN,UsDv0JQ,YAAA,iBAfF,SAAwB,OAAA,gBACxB,UtD01JN,UsDx1JQ,WAAA,gBAEF,UtDy1JN,UsDv1JQ,aAAA,gBAEF,UtDw1JN,UsDt1JQ,cAAA,gBAEF,UtDu1JN,UsDr1JQ,YAAA,gBAfF,SAAwB,OAAA,kBACxB,UtDw2JN,UsDt2JQ,WAAA,kBAEF,UtDu2JN,UsDr2JQ,aAAA,kBAEF,UtDs2JN,UsDp2JQ,cAAA,kBAEF,UtDq2JN,UsDn2JQ,YAAA,kBAfF,SAAwB,OAAA,gBACxB,UtDs3JN,UsDp3JQ,WAAA,gBAEF,UtDq3JN,UsDn3JQ,aAAA,gBAEF,UtDo3JN,UsDl3JQ,cAAA,gBAEF,UtDm3JN,UsDj3JQ,YAAA,gBAMN,WAAmB,OAAA,eACnB,YtD+2JF,YsD72JI,WAAA,eAEF,YtD82JF,YsD52JI,YAAA,eACA,aAAA,kBAEF,YtD62JF,YsD32JI,cAAA,eAEF,YtD42JF,YsD12JI,aAAA,eACA,YAAA,gBCjEF,YAAwB,MAAA,gBACxB,aAAwB,MAAA,eACxB,YAAwB,MAAA,etDoDxB,yBsDtDA,eAAwB,MAAA,gBACxB,gBAAwB,MAAA,eACxB,eAAwB,MAAA,gBtDoDxB,yBsDtDA,eAAwB,MAAA,gBACxB,gBAAwB,MAAA,eACxB,eAAwB,MAAA,gBtDoDxB,yBsDtDA,eAAwB,MAAA,gBACxB,gBAAwB,MAAA,eACxB,eAAwB,MAAA,gBtDoDxB,0BsDtDA,eAAwB,MAAA,gBACxB,gBAAwB,MAAA,eACxB,eAAwB,MAAA,gBCF5B,gBAAkB,YAAA,cAAA,CAAA,KAAA,CAAA,MAAA,CAAA,QAAA,CAAA,iBAAA,CAAA,aAAA,CAAA,oBAIlB,cAAiB,WAAA,kBACjB,WAAiB,YAAA,iBACjB,aAAiB,YAAA,iBACjB,eCTE,SAAA,OACA,cAAA,SACA,YAAA,ODeE,WAAwB,WAAA,gBACxB,YAAwB,WAAA,eACxB,aAAwB,WAAA,iBvDqCxB,yBuDvCA,cAAwB,WAAA,gBACxB,eAAwB,WAAA,eACxB,gBAAwB,WAAA,kBvDqCxB,yBuDvCA,cAAwB,WAAA,gBACxB,eAAwB,WAAA,eACxB,gBAAwB,WAAA,kBvDqCxB,yBuDvCA,cAAwB,WAAA,gBACxB,eAAwB,WAAA,eACxB,gBAAwB,WAAA,kBvDqCxB,0BuDvCA,cAAwB,WAAA,gBACxB,eAAwB,WAAA,eACxB,gBAAwB,WAAA,kBAM5B,gBAAmB,eAAA,oBACnB,gBAAmB,eAAA,oBACnB,iBAAmB,eAAA,qBAInB,mBAAuB,YAAA,cACvB,qBAAuB,YAAA,kBACvB,oBAAuB,YAAA,cACvB,kBAAuB,YAAA,cACvB,oBAAuB,YAAA,iBACvB,aAAuB,WAAA,iBAIvB,YAAc,MAAA,eEvCZ,cACE,MAAA,kBCUF,qBAAA,qBDLM,MAAA,kBANN,gBACE,MAAA,kBCUF,uBAAA,uBDLM,MAAA,kBANN,cACE,MAAA,kBCUF,qBAAA,qBDLM,MAAA,kBANN,WACE,MAAA,kBCUF,kBAAA,kBDLM,MAAA,kBANN,cACE,MAAA,kBCUF,qBAAA,qBDLM,MAAA,kBANN,aACE,MAAA,kBCUF,oBAAA,oBDLM,MAAA,kBANN,YACE,MAAA,kBCUF,mBAAA,mBDLM,MAAA,kBANN,WACE,MAAA,kBCUF,kBAAA,kBDLM,MAAA,kBANN,aACE,MAAA,kBCUF,oBAAA,oBDLM,MAAA,kBANN,WACE,MAAA,kBCUF,kBAAA,kBDLM,MAAA,kBFuCR,WAAa,MAAA,kBACb,YAAc,MAAA,kBAEd,eAAiB,MAAA,yBACjB,eAAiB,MAAA,+BAIjB,WIvDE,KAAA,CAAA,CAAA,EAAA,EACA,MAAA,YACA,YAAA,KACA,iBAAA,YACA,OAAA,EJuDF,sBAAwB,gBAAA,eAExB,YACE,WAAA,qBACA,cAAA,qBAKF,YAAc,MAAA,kBK9Dd,UACI,MAAA,MAGJ,eACI,QAAA,EAAA,EAAA,EAAA,KADJ,+BAKY,MAAA,MALZ,sCASY,UAAA,IATZ,2BAcQ,YAAA,KAdR,yCAgBY,cAAA,KACA,aAAA,EACA,cAAA,EAAA,KAAA,KAAA,EAlBZ,+CAsBY,aAAA,EAtBZ,gCAyBY,cAAA,KAAA,EAAA,EAAA,KAOZ,oCAGQ,KAAA,KACA,MAAA,KAJR,6CAUY,MAAA,MACA,YAAA,KACA,aAAA,EAZZ,gDAgBY,YAAA,EACA,aAAA,KAjBZ,uCAuBY,YAAA,IACA,aAAA,EACA,MAAA,MAOZ,kCAEQ,MAAA,KAOR,cACI,aAAA,MACA,YAAA,EAIJ,wBAKoB,OAAA,EAAA,IAAA,EAAA,KALpB,kCASoB,MAAA,KATpB,oCAYwB,YAAA,EAZxB,uBAkBgB,cAAA,KACA,aAAA,EAnBhB,0BAsBoB,cAAA,KACA,aAAA,EAvBpB,0BA8BQ,KAAA,KACA,MAAA,KA/BR,iCAkCY,QAAA,QAlCZ,6CAyCgB,kBAAA,eAAA,UAAA,eAOhB,kDAa4B,YAAA,KACA,aAAA,IAd5B,qDAkB4B,cAAA,KACA,aAAA,EAnB5B,uDA0B4B,MAAA,KACA,KAAA,KA3B5B,0DAqCgC,MAAA,MACA,WAAA,MAtChC,mEA4CgC,KAAA,KACA,MAAA,EA7ChC,yBAyDQ,MAAA,EAzDR,wBA8DQ,aAAA,eACA,YAAA,YA/DR,kBAoEQ,KAAA,YACA,MAAA,e5DxIJ,4B4D8IA,c7D+hKF,wB6D7hKM,aAAA,aAIR,0BACI,mCAEQ,YAAA,EACA,aAAA,KASZ,uDAMoB,cAAA,EANpB,iDAYY,aAAA,MACA,YAAA,EAbZ,yDAiBY,KAAA,KACA,MAAA,MAKZ,qDAGY,WAAA,MAHZ,+DAS4B,aAAA,IACA,YAAA,KAW5B,QACI,KAAA,EACA,MAAA,M5D/MA,4B4DmNA,QACI,MAAA,aAOR,WACI,MAAA,eACA,KAAA,OACA,MAAA,KAGJ,8BAEQ,KAAA,EACA,MAAA,KAIR,gCAEQ,YAAA,EACA,aAAA,IAAA,OAAA,QACA,cAAA,KACA,aAAA,EALR,uCAOY,MAAA,KAOZ,6BAEQ,mBAAA,OAAA,IAAA,EAAA,EAAA,QAAA,WAAA,OAAA,IAAA,EAAA,EAAA,QAIR,sDAGY,mBAAA,MAAA,IAAA,EAAA,EAAA,QAAA,WAAA,MAAA,IAAA,EAAA,EAAA,QCjUZ,0CAGM,MAAA,MACA,YAAA,KACA,aAAA,EALN,2CASM,MAAA,KACA,KAAA,IAON,eACE,KAAA,EACA,MAAA,KAMF,mBAEI,YAAA,KACA,aAAA,EAOJ,gBAEI,cAAA,IACA,aAAA,EAHJ,wBAMM,KAAA,KACA,MAAA,EACA,YAAA,EACA,aAAA,MATN,uBAaM,KAAA,KACA,MAAA,EACA,aAAA,MACA,YAAA,EACA,aAAA,EACA,cAAA,IAlBN,oDAwBM,KAAA,KACA,MAAA,IACA,kBAAA,cAAA,UAAA,cAKN,uCAGM,aAAA,EAHN,sCAOM,aAAA,EAQN,aAEI,aAAA,EACA,cAAA,IAHJ,qBAMM,KAAA,KACA,MAAA,EACA,YAAA,EACA,aAAA,MATN,oBAaM,KAAA,EACA,MAAA,IACA,YAAA,EACA,aAAA,MAON,a9DiwKE,S8DhwKA,c9DiwKA,K8D9vKI,aAAA,GAKN,gBACE,KAAA,EACA,MAAA,KACA,cAAA,EAAA,IAAA,IAAA,ECrHF,oBACI,WAAA,MAGJ,mBAEQ,KAAA,KACA,MAAA,KAMR,aACI,aAAA,KACA,cAAA,IAKJ,2EAGY,cAAA,KAHZ,wEAOY,KAAA,IACA,MAAA,KARZ,2EAcY,MAAA,MACA,YAAA,IACA,aAAA,EAhBZ,2CAqBQ,MAAA,MAMR,iCAEQ,QAAA,IAAA,IAAA,EAAA,EAMR,kBACI,YAAA,EACA,aAAA,IAFJ,mCAKQ,YAAA,EACA,aAAA,IAOR,uCAEQ,WAAA,eAFR,6CAIY,YAAA,YACA,aAAA,KAKZ,uBAAA,mBAAA,YACI,MAAA,MAGJ,WAEQ,aAAA,EACA,YAAA,KAIR,qBACI,aAAA,EACA,aAAA,IAGJ,6BAGY,aAAA,EACA,cAAA,KAOZ,+CAEQ,MAAA,KAFR,mDAOgB,YAAA,MACA,IAAA,KAQhB,6DAIgB,aAAA,EACA,YAAA,KAMhB,kDAGY,YAAA,EACA,aAAA,KCxIZ,qBAGM,YAAA,EACA,aAAA,MACA,kBAAA,YAAA,UAAA,YALN,oBASM,YAAA,EACA,aAAA,KACA,kBAAA,aAAA,UAAA,aAXN,qBAiBM,YAAA,EACA,aAAA,MAlBN,eAuBI,YAAA,EACA,aAAA", "file": "app-rtl.min.css", "sourcesContent": ["//\r\n// Google font - Roboto\r\n//\r\n\r\n@import url('https://fonts.googleapis.com/css?family=Roboto:300,400,500,700&display=swap');", "// \r\n// general.scss\r\n//\r\n\r\nhtml {\r\n  position: relative;\r\n  min-height: 100%;\r\n}\r\n\r\nbody {\r\n  padding-bottom: 60px;\r\n  overflow-x: hidden;\r\n}", "// \r\n// menu.scss\r\n//\r\n\r\n// Metis Menu Overwrite\r\n\r\n.metismenu {\r\n    padding: 0;\r\n\r\n    li {\r\n        list-style: none;\r\n    }\r\n    ul {\r\n        padding: 0;\r\n        li {\r\n            width: 100%;\r\n        }\r\n    }\r\n\r\n    .mm-collapse:not(.mm-show) {\r\n        display: none;\r\n    }\r\n    .mm-collapsing {\r\n        position: relative;\r\n        height: 0;\r\n        overflow: hidden;\r\n        transition-timing-function: ease;\r\n        transition-duration: .35s;\r\n        transition-property: height, visibility;\r\n    }\r\n}\r\n\r\n.nav-second-level,\r\n.nav-thrid-level {\r\n    li {\r\n        a {\r\n            padding: 8px 20px;\r\n            color: $menu-item;\r\n            display: block;\r\n            position: relative;\r\n            transition: all 0.4s;\r\n            &:focus,\r\n            &:hover {\r\n                color: $menu-item-hover;\r\n            }\r\n        }\r\n    }\r\n}\r\n\r\n.nav-second-level,\r\n.nav-third-level {\r\n    li.mm-active {\r\n        >a {\r\n            color: $menu-item-active;\r\n        }\r\n    }\r\n}\r\n\r\n// Wrapper\r\n#wrapper {\r\n    height: 100%;\r\n    overflow: hidden;\r\n    width: 100%;\r\n}\r\n\r\n//Content Page\r\n.content-page {\r\n    margin-left: $leftbar-width;\r\n    overflow: hidden;\r\n    padding: 0 15px 5px 15px;\r\n    min-height: 80vh;\r\n    margin-top: $topbar-height;\r\n}\r\n\r\n// Sidemenu\r\n.left-side-menu {\r\n    width: $leftbar-width;\r\n    background: $bg-leftbar-light;\r\n    bottom: 0;\r\n    padding: 20px 0;\r\n    position: fixed;\r\n    transition: all .2s ease-out;\r\n    top: $topbar-height;\r\n    box-shadow: $box-shadow;\r\n    z-index: 1;\r\n}\r\n\r\n// Sidebar\r\n#sidebar-menu {\r\n    >ul {\r\n        >li {\r\n            >a {\r\n                color: $menu-item;\r\n                display: block;\r\n                padding: 13px 20px;\r\n                position: relative;\r\n                transition: all 0.4s;\r\n                font-size: 15.5px;\r\n\r\n                &:hover,\r\n                &:focus,\r\n                &:active {\r\n                    color: $menu-item-hover;\r\n                    text-decoration: none;\r\n                }\r\n\r\n                i {\r\n                    display: inline-block;\r\n                    line-height: 1.0625rem;\r\n                    margin: 0 10px 0 3px;\r\n                    text-align: center;\r\n                    width: 20px;\r\n                    font-size: 18px;\r\n                }\r\n                .drop-arrow {\r\n                    float: right;\r\n                    i {\r\n                        margin-right: 0;\r\n                    }\r\n                }\r\n            }\r\n            > a.active {\r\n                background-color: $menu-item-active-bg;\r\n                color: $menu-item-active;\r\n            }\r\n\r\n            > ul {\r\n                padding-left: 38px;\r\n\r\n                ul {\r\n                    padding-left: 20px;\r\n                }\r\n            }\r\n        }\r\n    }\r\n    .menu-arrow {\r\n        transition: transform .15s;\r\n        position: absolute;\r\n        right: 20px;\r\n        display: inline-block;\r\n        font-family: 'Material Design Icons';\r\n        text-rendering: auto;\r\n        line-height: 1.5rem;\r\n        font-size: 1.1rem;\r\n        transform: translate(0, 0);\r\n        &:before {\r\n            content: \"\\F142\";\r\n        }\r\n    }\r\n    .badge{\r\n        margin-top: 4px;\r\n    }\r\n\r\n    li.mm-active {\r\n        > a {\r\n            > span.menu-arrow {\r\n                transform: rotate(90deg);\r\n            }\r\n        }\r\n    }\r\n\r\n    .menu-title {\r\n        padding: 10px 20px;\r\n        letter-spacing: .05em;\r\n        pointer-events: none;\r\n        cursor: default;\r\n        font-size: 0.6875rem;\r\n        text-transform: uppercase;\r\n        color: $menu-item;\r\n        font-weight: $font-weight-semibold;\r\n    }\r\n}\r\n\r\n// Enlarge menu\r\n.enlarged {\r\n\r\n    .logo-box {\r\n        width: $leftbar-width-collapsed !important;\r\n    }\r\n\r\n    .logo {\r\n        span.logo-lg {\r\n            display: none;\r\n        }\r\n        span.logo-sm {\r\n            display: block;\r\n        }\r\n    }\r\n    // Side menu\r\n    .left-side-menu {\r\n        position: absolute;\r\n        padding-top: 0;\r\n        width: $leftbar-width-collapsed !important;\r\n        z-index: 5;\r\n\r\n        .slimScrollDiv,\r\n        .slimscroll-menu {\r\n            overflow: inherit !important;\r\n            height: auto !important;\r\n        }\r\n        .slimScrollBar {\r\n            visibility: hidden;\r\n        }\r\n\r\n        // Sidebar Menu\r\n        #sidebar-menu {\r\n            .menu-title,\r\n            .menu-arrow,\r\n            .label,\r\n            .badge{\r\n                display: none !important;\r\n            }\r\n\r\n            > ul {\r\n                > li {\r\n                    position: relative;\r\n                    white-space: nowrap;\r\n\r\n                    > a {\r\n                        padding: 15px 20px;\r\n                        min-height: 54px;\r\n                        transition: none;\r\n            \r\n                        &:hover,\r\n                        &:active,\r\n                        &:focus {\r\n                            color: $menu-item-hover;\r\n                        }\r\n                        i {\r\n                            font-size: 1.125rem;\r\n                            margin-right: 20px;\r\n                        }\r\n\r\n                        span {\r\n                            display: none;\r\n                            padding-left: 25px;\r\n                        }\r\n                    }\r\n                    \r\n                    &:hover  {\r\n                        > a {\r\n                            position: relative;\r\n                            width: calc(190px + #{$leftbar-width-collapsed});\r\n                            background-color: $menu-item-active-bg;\r\n                            transition: none;\r\n\r\n                            span {\r\n                                display: inline;\r\n                            }\r\n                        }\r\n\r\n                        a.open,a.mm-active {\r\n                            :after {\r\n                                display: none;\r\n                            }\r\n                        }\r\n\r\n                        > ul {\r\n                            display: block;\r\n                            left: $leftbar-width-collapsed;\r\n                            position: absolute;\r\n                            width: 190px;\r\n                            height: auto !important;\r\n                            box-shadow: 3px 5px 10px 0 rgba(54,61,71,.1);\r\n\r\n                            ul {\r\n                                box-shadow: 3px 5px 10px 0 rgba(54,61,71,.1);\r\n                            }\r\n                            a {\r\n                                box-shadow: none;\r\n                                padding: 8px 20px;\r\n                                position: relative;\r\n                                width: 190px;\r\n                                z-index: 6;\r\n                                &:hover {\r\n                                    color: $menu-item-hover;\r\n                                }\r\n                            }\r\n                        }\r\n                    }\r\n                }\r\n\r\n                ul {\r\n                    padding: 5px 0;\r\n                    z-index: 9999;\r\n                    display: none;\r\n                    background-color: $bg-leftbar-light;\r\n\r\n                    li {\r\n                        &:hover {\r\n                            > ul {\r\n                                display: block;\r\n                                left: 190px;\r\n                                margin-top: -36px;\r\n                                height: auto !important;\r\n                                position: absolute;\r\n                                width: 190px;\r\n                            }\r\n                        }\r\n\r\n                        > a {\r\n                            span.pull-right {\r\n                                position: absolute;\r\n                                right: 20px;\r\n                                top: 12px;\r\n                                transform: rotate(270deg);\r\n                            }\r\n                        }\r\n                    }\r\n                    li.active {\r\n                        a {\r\n                            color: $menu-item-active;\r\n                        }\r\n                    }\r\n                }\r\n            }\r\n        }\r\n    }\r\n\r\n    // Content Page\r\n    .content-page {\r\n        margin-left: $leftbar-width-collapsed !important;\r\n    }\r\n\r\n    //Footer\r\n    .footer {\r\n        left: $leftbar-width-collapsed !important;\r\n    }\r\n\r\n    //User box\r\n    .user-box {\r\n        display: none;\r\n    }\r\n}\r\n\r\n// Body min-height set\r\nbody.enlarged {\r\n    min-height: 1200px;\r\n}\r\n\r\n@include media-breakpoint-down(sm) {\r\n    body {\r\n        overflow-x: hidden;\r\n        padding-bottom: 80px;\r\n    }\r\n    .left-side-menu {\r\n        display: none;\r\n        z-index: 10 !important;\r\n    }\r\n    .sidebar-enable {\r\n        .left-side-menu {\r\n            display: block;\r\n        }\r\n    }\r\n    .content-page,.enlarged .content-page {\r\n        margin-left: 0 !important;\r\n        padding: 0 10px;\r\n    }\r\n    .logo-box {\r\n        display: none;\r\n    }\r\n}\r\n\r\n// Leftbar-dark\r\n.left-side-menu-dark {\r\n    .logo-box{\r\n        background-color: $bg-leftbar-dark;\r\n\r\n        .logo-light {\r\n            display: $logo-dark-display;\r\n        }\r\n        \r\n        .logo-dark {\r\n            display: $logo-light-display;\r\n        }\r\n    }\r\n\r\n    .left-side-menu {\r\n        background-color: $bg-leftbar-dark;\r\n\r\n\r\n\r\n        #sidebar-menu {\r\n            > ul {\r\n                > li{\r\n                    > a {\r\n                        color: $menu-item-color-dark;\r\n        \r\n                        &:hover,\r\n                        &:focus,\r\n                        &:active {\r\n                            color: $menu-item-hover-color-dark;\r\n                        }\r\n                    }\r\n                    > a.active {\r\n                        color: $menu-item-active-color-dark;\r\n                        background-color: $menu-item-active-bg-dark;\r\n                        border-right-color: $menu-item-active-color-dark;\r\n                    }\r\n                }\r\n            }\r\n\r\n\r\n        }\r\n\r\n        .nav-second-level,\r\n        .nav-thrid-level {\r\n            li {\r\n                a {\r\n                    color: $menu-item-color-dark;\r\n                    &:focus,\r\n                    &:hover {\r\n                        background-color: transparent;\r\n                        color: $menu-item-hover-color-dark;\r\n                    }\r\n                }\r\n\r\n                >a.active {\r\n                    color: $menu-item-active-color-dark;\r\n                }\r\n            }\r\n        }\r\n    }\r\n}\r\n\r\n.enlarged.left-side-menu-dark #wrapper {\r\n\r\n    .navbar-custom{\r\n        box-shadow: $leftbar-width-collapsed 1px 0 0 $gray-200;\r\n    }\r\n\r\n    .left-side-menu {\r\n        #sidebar-menu {\r\n            > ul {\r\n                > li {\r\n                    &:hover  {\r\n                        > a {\r\n                            background-color: $menu-item-active-bg-dark;\r\n                        }\r\n                    }\r\n                }\r\n                ul{\r\n                    background-color: $bg-leftbar-dark;\r\n                }\r\n            }\r\n            .nav-second-level,\r\n            .nav-third-level {\r\n                li {\r\n                    >a.active,  a:focus, a:hover {\r\n                        color: $primary;\r\n                    }\r\n                }\r\n            }\r\n        }\r\n    }\r\n}\r\n\r\n/* =============\r\n  Small Menu\r\n============= */\r\n\r\n.left-side-menu-sm {\r\n    .logo-box {\r\n        width: $leftbar-width-sm;\r\n    }\r\n    .left-side-menu {\r\n        width: $leftbar-width-sm;\r\n        text-align: center;\r\n        #sidebar-menu {\r\n            > ul {\r\n\r\n                > li {\r\n                    > a {\r\n                        > i {\r\n                            display: block;\r\n                            font-size: 18px;\r\n                            line-height: 24px;\r\n                            width: 100%;\r\n                            margin: 0;\r\n                        }\r\n                    }\r\n                }\r\n                ul  {\r\n                    padding-left: 0;\r\n                    a {\r\n                        padding: 10px 20px;\r\n                    }\r\n                }\r\n            }\r\n        }\r\n        .menu-arrow,\r\n        .badge {\r\n            display: none !important;\r\n        }\r\n        &+.content-page {\r\n            margin-left: $leftbar-width-sm;\r\n        }\r\n        + .content-page .footer {\r\n            left: $leftbar-width-sm;\r\n        }\r\n    }\r\n}\r\n\r\n.enlarged.left-side-menu-sm {\r\n    #wrapper {\r\n        .left-side-menu {\r\n            text-align: left;\r\n\r\n            ul {\r\n                li {\r\n                    a {\r\n                        i {\r\n                            display: inline-block;\r\n                            font-size: 18px;\r\n                            line-height: 17px;\r\n                            margin-left: 3px;\r\n                            margin-right: 15px;\r\n                            vertical-align: middle;\r\n                            width: 20px;\r\n                        }\r\n                    }\r\n                }\r\n            }\r\n        }\r\n    }\r\n}", "/*\r\nTemplate Name: Uplon - Responsive Bootstrap 4 Admin Dashboard\r\nAuthor: CoderThemes\r\nVersion: 2.0.0\r\nWebsite: https://coderthemes.com/\r\nContact: <EMAIL>\r\nFile: Main Css File\r\n*/\n@import url(\"https://fonts.googleapis.com/css?family=Roboto:300,400,500,700&display=swap\");\nhtml {\n  position: relative;\n  min-height: 100%; }\n\nbody {\n  padding-bottom: 60px;\n  overflow-x: hidden; }\n\n.metismenu {\n  padding: 0; }\n  .metismenu li {\n    list-style: none; }\n  .metismenu ul {\n    padding: 0; }\n    .metismenu ul li {\n      width: 100%; }\n  .metismenu .mm-collapse:not(.mm-show) {\n    display: none; }\n  .metismenu .mm-collapsing {\n    position: relative;\n    height: 0;\n    overflow: hidden;\n    transition-timing-function: ease;\n    transition-duration: .35s;\n    transition-property: height, visibility; }\n\n.nav-second-level li a,\n.nav-thrid-level li a {\n  padding: 8px 20px;\n  color: #6e768e;\n  display: block;\n  position: relative;\n  transition: all 0.4s; }\n  .nav-second-level li a:focus, .nav-second-level li a:hover,\n  .nav-thrid-level li a:focus,\n  .nav-thrid-level li a:hover {\n    color: #141a21; }\n\n.nav-second-level li.mm-active > a,\n.nav-third-level li.mm-active > a {\n  color: #64b0f2; }\n\n#wrapper {\n  height: 100%;\n  overflow: hidden;\n  width: 100%; }\n\n.content-page {\n  margin-left: 240px;\n  overflow: hidden;\n  padding: 0 15px 5px 15px;\n  min-height: 80vh;\n  margin-top: 70px; }\n\n.left-side-menu {\n  width: 240px;\n  background: #ffffff;\n  bottom: 0;\n  padding: 20px 0;\n  position: fixed;\n  transition: all .2s ease-out;\n  top: 70px;\n  box-shadow: 0px 0px 35px 0px rgba(73, 80, 87, 0.15);\n  z-index: 1; }\n\n#sidebar-menu > ul > li > a {\n  color: #6e768e;\n  display: block;\n  padding: 13px 20px;\n  position: relative;\n  transition: all 0.4s;\n  font-size: 15.5px; }\n  #sidebar-menu > ul > li > a:hover, #sidebar-menu > ul > li > a:focus, #sidebar-menu > ul > li > a:active {\n    color: #141a21;\n    text-decoration: none; }\n  #sidebar-menu > ul > li > a i {\n    display: inline-block;\n    line-height: 1.0625rem;\n    margin: 0 10px 0 3px;\n    text-align: center;\n    width: 20px;\n    font-size: 18px; }\n  #sidebar-menu > ul > li > a .drop-arrow {\n    float: right; }\n    #sidebar-menu > ul > li > a .drop-arrow i {\n      margin-right: 0; }\n\n#sidebar-menu > ul > li > a.active {\n  background-color: #f5f8fb;\n  color: #64b0f2; }\n\n#sidebar-menu > ul > li > ul {\n  padding-left: 38px; }\n  #sidebar-menu > ul > li > ul ul {\n    padding-left: 20px; }\n\n#sidebar-menu .menu-arrow {\n  transition: transform .15s;\n  position: absolute;\n  right: 20px;\n  display: inline-block;\n  font-family: 'Material Design Icons';\n  text-rendering: auto;\n  line-height: 1.5rem;\n  font-size: 1.1rem;\n  transform: translate(0, 0); }\n  #sidebar-menu .menu-arrow:before {\n    content: \"\\F142\"; }\n\n#sidebar-menu .badge {\n  margin-top: 4px; }\n\n#sidebar-menu li.mm-active > a > span.menu-arrow {\n  transform: rotate(90deg); }\n\n#sidebar-menu .menu-title {\n  padding: 10px 20px;\n  letter-spacing: .05em;\n  pointer-events: none;\n  cursor: default;\n  font-size: 0.6875rem;\n  text-transform: uppercase;\n  color: #6e768e;\n  font-weight: 600; }\n\n.enlarged .logo-box {\n  width: 70px !important; }\n\n.enlarged .logo span.logo-lg {\n  display: none; }\n\n.enlarged .logo span.logo-sm {\n  display: block; }\n\n.enlarged .left-side-menu {\n  position: absolute;\n  padding-top: 0;\n  width: 70px !important;\n  z-index: 5; }\n  .enlarged .left-side-menu .slimScrollDiv,\n  .enlarged .left-side-menu .slimscroll-menu {\n    overflow: inherit !important;\n    height: auto !important; }\n  .enlarged .left-side-menu .slimScrollBar {\n    visibility: hidden; }\n  .enlarged .left-side-menu #sidebar-menu .menu-title,\n  .enlarged .left-side-menu #sidebar-menu .menu-arrow,\n  .enlarged .left-side-menu #sidebar-menu .label,\n  .enlarged .left-side-menu #sidebar-menu .badge {\n    display: none !important; }\n  .enlarged .left-side-menu #sidebar-menu > ul > li {\n    position: relative;\n    white-space: nowrap; }\n    .enlarged .left-side-menu #sidebar-menu > ul > li > a {\n      padding: 15px 20px;\n      min-height: 54px;\n      transition: none; }\n      .enlarged .left-side-menu #sidebar-menu > ul > li > a:hover, .enlarged .left-side-menu #sidebar-menu > ul > li > a:active, .enlarged .left-side-menu #sidebar-menu > ul > li > a:focus {\n        color: #141a21; }\n      .enlarged .left-side-menu #sidebar-menu > ul > li > a i {\n        font-size: 1.125rem;\n        margin-right: 20px; }\n      .enlarged .left-side-menu #sidebar-menu > ul > li > a span {\n        display: none;\n        padding-left: 25px; }\n    .enlarged .left-side-menu #sidebar-menu > ul > li:hover > a {\n      position: relative;\n      width: calc(190px + 70px);\n      background-color: #f5f8fb;\n      transition: none; }\n      .enlarged .left-side-menu #sidebar-menu > ul > li:hover > a span {\n        display: inline; }\n    .enlarged .left-side-menu #sidebar-menu > ul > li:hover a.open :after, .enlarged .left-side-menu #sidebar-menu > ul > li:hover a.mm-active :after {\n      display: none; }\n    .enlarged .left-side-menu #sidebar-menu > ul > li:hover > ul {\n      display: block;\n      left: 70px;\n      position: absolute;\n      width: 190px;\n      height: auto !important;\n      box-shadow: 3px 5px 10px 0 rgba(54, 61, 71, 0.1); }\n      .enlarged .left-side-menu #sidebar-menu > ul > li:hover > ul ul {\n        box-shadow: 3px 5px 10px 0 rgba(54, 61, 71, 0.1); }\n      .enlarged .left-side-menu #sidebar-menu > ul > li:hover > ul a {\n        box-shadow: none;\n        padding: 8px 20px;\n        position: relative;\n        width: 190px;\n        z-index: 6; }\n        .enlarged .left-side-menu #sidebar-menu > ul > li:hover > ul a:hover {\n          color: #141a21; }\n  .enlarged .left-side-menu #sidebar-menu > ul ul {\n    padding: 5px 0;\n    z-index: 9999;\n    display: none;\n    background-color: #ffffff; }\n    .enlarged .left-side-menu #sidebar-menu > ul ul li:hover > ul {\n      display: block;\n      left: 190px;\n      margin-top: -36px;\n      height: auto !important;\n      position: absolute;\n      width: 190px; }\n    .enlarged .left-side-menu #sidebar-menu > ul ul li > a span.pull-right {\n      position: absolute;\n      right: 20px;\n      top: 12px;\n      transform: rotate(270deg); }\n    .enlarged .left-side-menu #sidebar-menu > ul ul li.active a {\n      color: #64b0f2; }\n\n.enlarged .content-page {\n  margin-left: 70px !important; }\n\n.enlarged .footer {\n  left: 70px !important; }\n\n.enlarged .user-box {\n  display: none; }\n\nbody.enlarged {\n  min-height: 1200px; }\n\n@media (max-width: 767.98px) {\n  body {\n    overflow-x: hidden;\n    padding-bottom: 80px; }\n  .left-side-menu {\n    display: none;\n    z-index: 10 !important; }\n  .sidebar-enable .left-side-menu {\n    display: block; }\n  .content-page, .enlarged .content-page {\n    margin-left: 0 !important;\n    padding: 0 10px; }\n  .logo-box {\n    display: none; } }\n\n.left-side-menu-dark .logo-box {\n  background-color: #363c4a; }\n  .left-side-menu-dark .logo-box .logo-light {\n    display: block; }\n  .left-side-menu-dark .logo-box .logo-dark {\n    display: none; }\n\n.left-side-menu-dark .left-side-menu {\n  background-color: #363c4a; }\n  .left-side-menu-dark .left-side-menu #sidebar-menu > ul > li > a {\n    color: #929fbf; }\n    .left-side-menu-dark .left-side-menu #sidebar-menu > ul > li > a:hover, .left-side-menu-dark .left-side-menu #sidebar-menu > ul > li > a:focus, .left-side-menu-dark .left-side-menu #sidebar-menu > ul > li > a:active {\n      color: #c8cddc; }\n  .left-side-menu-dark .left-side-menu #sidebar-menu > ul > li > a.active {\n    color: #ffffff;\n    background-color: #3c4352;\n    border-right-color: #ffffff; }\n  .left-side-menu-dark .left-side-menu .nav-second-level li a,\n  .left-side-menu-dark .left-side-menu .nav-thrid-level li a {\n    color: #929fbf; }\n    .left-side-menu-dark .left-side-menu .nav-second-level li a:focus, .left-side-menu-dark .left-side-menu .nav-second-level li a:hover,\n    .left-side-menu-dark .left-side-menu .nav-thrid-level li a:focus,\n    .left-side-menu-dark .left-side-menu .nav-thrid-level li a:hover {\n      background-color: transparent;\n      color: #c8cddc; }\n  .left-side-menu-dark .left-side-menu .nav-second-level li > a.active,\n  .left-side-menu-dark .left-side-menu .nav-thrid-level li > a.active {\n    color: #ffffff; }\n\n.enlarged.left-side-menu-dark #wrapper .navbar-custom {\n  box-shadow: 70px 1px 0 0 #e9ecef; }\n\n.enlarged.left-side-menu-dark #wrapper .left-side-menu #sidebar-menu > ul > li:hover > a {\n  background-color: #3c4352; }\n\n.enlarged.left-side-menu-dark #wrapper .left-side-menu #sidebar-menu > ul ul {\n  background-color: #363c4a; }\n\n.enlarged.left-side-menu-dark #wrapper .left-side-menu #sidebar-menu .nav-second-level li > a.active, .enlarged.left-side-menu-dark #wrapper .left-side-menu #sidebar-menu .nav-second-level li a:focus, .enlarged.left-side-menu-dark #wrapper .left-side-menu #sidebar-menu .nav-second-level li a:hover,\n.enlarged.left-side-menu-dark #wrapper .left-side-menu #sidebar-menu .nav-third-level li > a.active,\n.enlarged.left-side-menu-dark #wrapper .left-side-menu #sidebar-menu .nav-third-level li a:focus,\n.enlarged.left-side-menu-dark #wrapper .left-side-menu #sidebar-menu .nav-third-level li a:hover {\n  color: #64b0f2; }\n\n/* =============\r\n  Small Menu\r\n============= */\n.left-side-menu-sm .logo-box {\n  width: 160px; }\n\n.left-side-menu-sm .left-side-menu {\n  width: 160px;\n  text-align: center; }\n  .left-side-menu-sm .left-side-menu #sidebar-menu > ul > li > a > i {\n    display: block;\n    font-size: 18px;\n    line-height: 24px;\n    width: 100%;\n    margin: 0; }\n  .left-side-menu-sm .left-side-menu #sidebar-menu > ul ul {\n    padding-left: 0; }\n    .left-side-menu-sm .left-side-menu #sidebar-menu > ul ul a {\n      padding: 10px 20px; }\n  .left-side-menu-sm .left-side-menu .menu-arrow,\n  .left-side-menu-sm .left-side-menu .badge {\n    display: none !important; }\n  .left-side-menu-sm .left-side-menu + .content-page {\n    margin-left: 160px; }\n  .left-side-menu-sm .left-side-menu + .content-page .footer {\n    left: 160px; }\n\n.enlarged.left-side-menu-sm #wrapper .left-side-menu {\n  text-align: left; }\n  .enlarged.left-side-menu-sm #wrapper .left-side-menu ul li a i {\n    display: inline-block;\n    font-size: 18px;\n    line-height: 17px;\n    margin-left: 3px;\n    margin-right: 15px;\n    vertical-align: middle;\n    width: 20px; }\n\n.logo {\n  display: block;\n  line-height: 70px; }\n  .logo span.logo-lg {\n    display: block; }\n  .logo span.logo-sm {\n    display: none; }\n  .logo .logo-lg-text-dark {\n    color: #343a40;\n    font-weight: 700;\n    font-size: 22px;\n    text-transform: uppercase; }\n  .logo .logo-lg-text-light {\n    color: #fff;\n    font-weight: 700;\n    font-size: 22px;\n    text-transform: uppercase; }\n\n.logo-box {\n  background-color: #ffffff;\n  height: 70px;\n  width: 240px;\n  float: left; }\n\n.logo-light {\n  display: none; }\n\n.logo-dark {\n  display: block; }\n\n.navbar-custom {\n  background-color: #2b3d51;\n  padding: 0 10px 0 0;\n  position: fixed;\n  left: 0;\n  right: 0;\n  height: 70px;\n  z-index: 100;\n  /* Search */ }\n  .navbar-custom .topnav-menu > li {\n    float: left; }\n  .navbar-custom .topnav-menu .nav-link {\n    padding: 0 15px;\n    color: #adb5bd;\n    min-width: 32px;\n    display: block;\n    line-height: 70px;\n    text-align: center;\n    max-height: 70px; }\n  .navbar-custom .dropdown.show .nav-link {\n    background-color: rgba(255, 255, 255, 0.05); }\n  .navbar-custom .app-search {\n    overflow: hidden;\n    height: 70px;\n    display: table;\n    max-width: 180px;\n    margin-right: 20px; }\n    .navbar-custom .app-search .app-search-box {\n      display: table-cell;\n      vertical-align: middle; }\n      .navbar-custom .app-search .app-search-box input::-webkit-input-placeholder {\n        font-size: 0.8125rem;\n        color: #adb5bd; }\n    .navbar-custom .app-search .form-control {\n      border: none;\n      height: 38px;\n      padding-left: 20px;\n      padding-right: 0;\n      color: #6c757d;\n      background-color: #31465d;\n      box-shadow: none;\n      border-radius: 30px 0 0 30px; }\n    .navbar-custom .app-search .input-group-append {\n      margin-left: 0;\n      z-index: 4; }\n    .navbar-custom .app-search .btn {\n      background-color: #31465d;\n      color: #adb5bd;\n      border-color: transparent;\n      border-radius: 0 30px 30px 0;\n      box-shadow: none !important; }\n  .navbar-custom .button-menu-mobile {\n    border: none;\n    color: #adb5bd;\n    display: inline-block;\n    height: 70px;\n    line-height: 70px;\n    width: 60px;\n    background-color: transparent;\n    font-size: 24px;\n    cursor: pointer; }\n  .navbar-custom .button-menu-mobile.disable-btn {\n    display: none; }\n\n/* Notification */\n.noti-scroll {\n  max-height: 230px; }\n\n.notification-list {\n  margin-left: 0; }\n  .notification-list .noti-title {\n    background-color: #64b0f2;\n    padding: 15px 20px;\n    border-radius: 0.25rem 0.25rem 0 0;\n    margin-top: -7px; }\n  .notification-list .noti-icon {\n    font-size: 22px; }\n  .notification-list .noti-icon-badge {\n    display: inline-block;\n    height: 10px;\n    width: 10px;\n    background-color: #ff5d48;\n    border-radius: 50%;\n    border: 2px solid #2b3d51;\n    position: absolute;\n    top: 22px;\n    right: 14px; }\n  .notification-list .notify-item {\n    padding: 12px 20px; }\n    .notification-list .notify-item.notify-all {\n      background-color: #f5f6f8;\n      margin-bottom: -7px; }\n    .notification-list .notify-item .notify-icon {\n      float: left;\n      height: 36px;\n      width: 36px;\n      font-size: 18px;\n      line-height: 36px;\n      text-align: center;\n      margin-top: 2px;\n      margin-right: 10px;\n      border-radius: 50%;\n      color: #fff; }\n    .notification-list .notify-item .notify-details {\n      margin-bottom: 5px;\n      overflow: hidden;\n      margin-left: 45px;\n      text-overflow: ellipsis;\n      white-space: nowrap;\n      color: #343a40;\n      font-weight: 500; }\n      .notification-list .notify-item .notify-details b {\n        font-weight: 500; }\n      .notification-list .notify-item .notify-details small {\n        display: block; }\n      .notification-list .notify-item .notify-details span {\n        display: block;\n        overflow: hidden;\n        text-overflow: ellipsis;\n        white-space: nowrap;\n        font-size: 13px; }\n    .notification-list .notify-item .user-msg {\n      margin-left: 45px;\n      white-space: normal;\n      line-height: 16px; }\n  .notification-list .inbox-widget .inbox-item {\n    padding: 12px 20px; }\n    .notification-list .inbox-widget .inbox-item:hover {\n      background-color: #f8f9fa; }\n  .notification-list .profile-dropdown .notify-item {\n    padding: 7px 20px; }\n\n.profile-dropdown {\n  width: 170px; }\n  .profile-dropdown i {\n    margin-right: 5px;\n    font-size: 16px; }\n\n.nav-user {\n  padding: 0 12px !important; }\n  .nav-user img {\n    height: 32px;\n    width: 32px; }\n\n@media (min-width: 1025px) {\n  .navbar-custom .button-menu-mobile {\n    margin-left: 8px; } }\n\n.topbar-light .navbar-custom {\n  background-color: #ffffff;\n  box-shadow: 240px 1px 0 0 #e9ecef;\n  /* app search */ }\n  .topbar-light .navbar-custom .topnav-menu .nav-link {\n    color: #6c757d; }\n  .topbar-light .navbar-custom .notification-list .noti-icon-badge {\n    border-color: #ffffff; }\n  .topbar-light .navbar-custom .button-menu-mobile {\n    color: #6c757d; }\n  .topbar-light .navbar-custom .app-search input::-webkit-input-placeholder {\n    color: #6c757d !important; }\n  .topbar-light .navbar-custom .app-search .form-control {\n    color: #343a40;\n    background-color: whitesmoke;\n    border-color: whitesmoke; }\n  .topbar-light .navbar-custom .app-search .btn {\n    background-color: whitesmoke;\n    color: #adb5bd; }\n\n.page-title-box {\n  position: relative;\n  background-color: #fff;\n  padding: 0 30px;\n  margin: 0 -30px 30px -30px;\n  box-shadow: 0px 0px 35px 0px rgba(73, 80, 87, 0.15); }\n  .page-title-box .page-title {\n    font-size: 18px;\n    margin: 0;\n    line-height: 60px; }\n  .page-title-box .page-title-right {\n    float: right;\n    margin-top: 7px; }\n  .page-title-box .breadcrumb {\n    background-color: transparent;\n    padding: .75rem 0; }\n\n@media (max-width: 767.98px) {\n  .page-title-box .page-title {\n    display: block;\n    white-space: nowrap;\n    text-overflow: ellipsis;\n    overflow: hidden;\n    line-height: 70px; }\n  .page-title-box .page-title-right {\n    display: none; } }\n\n.footer {\n  bottom: 0;\n  padding: 21px 15px 20px;\n  position: absolute;\n  right: 0;\n  color: #6c757d;\n  left: 240px;\n  background-color: #ecf1f3; }\n\n@media (max-width: 767.98px) {\n  .footer {\n    left: 0 !important;\n    text-align: center; } }\n\n.right-bar {\n  background-color: #fff;\n  box-shadow: 0 0 24px 0 rgba(0, 0, 0, 0.06), 0 1px 0 0 rgba(0, 0, 0, 0.02);\n  display: block;\n  position: fixed;\n  transition: all 200ms ease-out;\n  width: 260px;\n  z-index: 9999;\n  float: right !important;\n  right: -270px;\n  top: 0;\n  bottom: 0; }\n  .right-bar .rightbar-title {\n    background-color: #64b0f2;\n    padding: 24.5px;\n    color: #fff; }\n  .right-bar .right-bar-toggle {\n    background-color: #444c54;\n    height: 24px;\n    width: 24px;\n    line-height: 24px;\n    color: #e9ecef;\n    text-align: center;\n    border-radius: 50%;\n    margin-top: -4px; }\n    .right-bar .right-bar-toggle:hover {\n      background-color: #4b545c; }\n  .right-bar .user-box {\n    padding: 25px;\n    text-align: center; }\n    .right-bar .user-box .user-img {\n      position: relative;\n      height: 64px;\n      width: 64px;\n      margin: 0 auto 15px auto; }\n      .right-bar .user-box .user-img .user-edit {\n        position: absolute;\n        right: -5px;\n        bottom: 0px;\n        height: 24px;\n        width: 24px;\n        background-color: #fff;\n        line-height: 24px;\n        border-radius: 50%;\n        box-shadow: 0 1rem 3rem rgba(0, 0, 0, 0.175); }\n    .right-bar .user-box h5 {\n      margin-bottom: 2px; }\n      .right-bar .user-box h5 a {\n        color: #343a40; }\n\n.rightbar-overlay {\n  background-color: rgba(52, 58, 64, 0.4);\n  position: absolute;\n  left: 0;\n  right: 0;\n  top: 0;\n  bottom: 0;\n  display: none;\n  z-index: 9998;\n  transition: all .2s ease-out; }\n\n.right-bar-enabled .right-bar {\n  right: 0; }\n\n.right-bar-enabled .rightbar-overlay {\n  display: block; }\n\n@media (max-width: 767.98px) {\n  .right-bar {\n    overflow: auto; }\n    .right-bar .slimscroll-menu {\n      height: auto !important; } }\n\n.activity-widget .activity-list {\n  position: relative;\n  border-left: 2px dashed #ced4da;\n  padding-left: 24px;\n  padding-bottom: 20px; }\n  .activity-widget .activity-list::after {\n    content: \"\";\n    position: absolute;\n    left: -7px;\n    top: 6px;\n    width: 12px;\n    height: 12px;\n    background-color: #fff;\n    border: 2px solid #64b0f2;\n    border-radius: 50%; }\n\nbody.boxed-layout {\n  background-color: #f8f3ef; }\n  body.boxed-layout #wrapper {\n    background-color: #f7f9fa;\n    max-width: 1300px;\n    margin: 0 auto;\n    box-shadow: 0px 0px 35px 0px rgba(73, 80, 87, 0.15); }\n  body.boxed-layout .navbar-custom {\n    max-width: 1300px;\n    margin: 0 auto; }\n  body.boxed-layout .footer {\n    margin: 0 auto;\n    max-width: calc(1300px - 240px); }\n  body.boxed-layout.enlarged .footer {\n    max-width: calc(1300px - 70px); }\n\n@media (min-width: 992px) {\n  .unsticky-layout .left-side-menu, .unsticky-layout .navbar-custom {\n    position: absolute; } }\n\n.width-xs {\n  min-width: 80px; }\n\n.width-sm {\n  min-width: 95px; }\n\n.width-md {\n  min-width: 110px; }\n\n.width-lg {\n  min-width: 140px; }\n\n.width-xl {\n  min-width: 160px; }\n\n.font-family-secondary {\n  font-family: \"Roboto\", sans-serif; }\n\n.avatar-xs {\n  height: 1.5rem;\n  width: 1.5rem; }\n\n.avatar-sm {\n  height: 2.25rem;\n  width: 2.25rem; }\n\n.avatar-md {\n  height: 3.5rem;\n  width: 3.5rem; }\n\n.avatar-lg {\n  height: 4.5rem;\n  width: 4.5rem; }\n\n.avatar-xl {\n  height: 6rem;\n  width: 6rem; }\n\n.avatar-xxl {\n  height: 7.5rem;\n  width: 7.5rem; }\n\n.avatar-title {\n  align-items: center;\n  color: #fff;\n  display: flex;\n  height: 100%;\n  justify-content: center;\n  width: 100%; }\n\n.avatar-group {\n  padding-left: 12px; }\n  .avatar-group .avatar-group-item {\n    margin: 0 0 10px -12px;\n    display: inline-block;\n    border: 2px solid #fff;\n    border-radius: 50%; }\n\n.font-weight-medium {\n  font-weight: 500; }\n\n.font-weight-semibold {\n  font-weight: 600; }\n\n.sp-line-1,\n.sp-line-2,\n.sp-line-3,\n.sp-line-4 {\n  overflow: hidden;\n  text-overflow: ellipsis;\n  display: -webkit-box;\n  -webkit-box-orient: vertical; }\n\n.sp-line-1 {\n  -webkit-line-clamp: 1; }\n\n.sp-line-2 {\n  -webkit-line-clamp: 2; }\n\n.sp-line-3 {\n  -webkit-line-clamp: 3; }\n\n.sp-line-4 {\n  -webkit-line-clamp: 4; }\n\n.pull-in {\n  margin-left: -1.25rem;\n  margin-right: -1.25rem; }\n\n.social-list-item {\n  height: 2rem;\n  width: 2rem;\n  line-height: calc(2rem - 4px);\n  display: block;\n  border: 2px solid #adb5bd;\n  border-radius: 50%;\n  color: #adb5bd; }\n\n.tilebox-two i {\n  font-size: 48px;\n  opacity: 0.2;\n  margin-top: 14px; }\n\n.user-position {\n  position: absolute;\n  top: 0;\n  bottom: 0;\n  width: 44px;\n  font-size: 16px;\n  text-align: center;\n  right: 0;\n  left: auto;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  flex-direction: row; }\n  .user-position span {\n    transform: rotate(90deg); }\n\n.inbox-widget .inbox-item {\n  overflow: hidden;\n  padding: 12px 0px;\n  position: relative; }\n  .inbox-widget .inbox-item .inbox-item-img {\n    display: block;\n    float: left;\n    margin-right: 15px;\n    margin-top: 2px; }\n    .inbox-widget .inbox-item .inbox-item-img img {\n      width: 40px; }\n  .inbox-widget .inbox-item .inbox-item-author {\n    display: block;\n    margin-bottom: 0px;\n    color: #495057; }\n  .inbox-widget .inbox-item .inbox-item-text {\n    color: #adb5bd;\n    display: block;\n    margin: 0;\n    overflow: hidden; }\n  .inbox-widget .inbox-item .inbox-item-date {\n    color: #6c757d;\n    font-size: 0.6875rem;\n    position: absolute;\n    right: 5px;\n    top: 10px; }\n\n.checkbox label {\n  display: inline-block;\n  padding-left: 8px;\n  position: relative;\n  font-weight: normal; }\n  .checkbox label::before {\n    background-color: #fff;\n    border-radius: 3px;\n    border: 2px solid #efefef;\n    content: \"\";\n    display: inline-block;\n    height: 18px;\n    left: 0;\n    margin-left: -18px;\n    position: absolute;\n    transition: 0.3s ease-in-out;\n    width: 18px;\n    outline: none !important;\n    top: 2px; }\n  .checkbox label::after {\n    color: #495057;\n    display: inline-block;\n    font-size: 11px;\n    height: 18px;\n    left: 0;\n    margin-left: -18px;\n    padding-left: 3px;\n    padding-top: 2px;\n    position: absolute;\n    top: 0;\n    width: 18px; }\n\n.checkbox input[type=\"checkbox\"] {\n  cursor: pointer;\n  opacity: 0;\n  z-index: 1;\n  outline: none !important; }\n  .checkbox input[type=\"checkbox\"]:disabled + label {\n    opacity: 0.65; }\n\n.checkbox input[type=\"checkbox\"]:focus + label::before {\n  outline-offset: -2px;\n  outline: none; }\n\n.checkbox input[type=\"checkbox\"]:checked + label::after {\n  content: \"\";\n  position: absolute;\n  top: 6px;\n  left: 7px;\n  display: table;\n  width: 4px;\n  height: 8px;\n  border: 2px solid #6c757d;\n  border-top-width: 0;\n  border-left-width: 0;\n  -webkit-transform: rotate(45deg);\n  -ms-transform: rotate(45deg);\n  -o-transform: rotate(45deg);\n  transform: rotate(45deg); }\n\n.checkbox input[type=\"checkbox\"]:disabled + label::before {\n  background-color: #f8f9fa;\n  cursor: not-allowed; }\n\n.checkbox.checkbox-circle label::before {\n  border-radius: 50%; }\n\n.checkbox.checkbox-inline {\n  margin-top: 0; }\n\n.checkbox.checkbox-single input {\n  height: 18px;\n  width: 18px;\n  position: absolute; }\n\n.checkbox.checkbox-single label {\n  height: 18px;\n  width: 18px; }\n  .checkbox.checkbox-single label:before {\n    margin-left: 0; }\n  .checkbox.checkbox-single label:after {\n    margin-left: 0; }\n\n.checkbox-primary input[type=\"checkbox\"]:checked + label::before {\n  background-color: #64b0f2;\n  border-color: #64b0f2; }\n\n.checkbox-primary input[type=\"checkbox\"]:checked + label::after {\n  border-color: #fff; }\n\n.checkbox-secondary input[type=\"checkbox\"]:checked + label::before {\n  background-color: #6c757d;\n  border-color: #6c757d; }\n\n.checkbox-secondary input[type=\"checkbox\"]:checked + label::after {\n  border-color: #fff; }\n\n.checkbox-success input[type=\"checkbox\"]:checked + label::before {\n  background-color: #1bb99a;\n  border-color: #1bb99a; }\n\n.checkbox-success input[type=\"checkbox\"]:checked + label::after {\n  border-color: #fff; }\n\n.checkbox-info input[type=\"checkbox\"]:checked + label::before {\n  background-color: #3db9dc;\n  border-color: #3db9dc; }\n\n.checkbox-info input[type=\"checkbox\"]:checked + label::after {\n  border-color: #fff; }\n\n.checkbox-warning input[type=\"checkbox\"]:checked + label::before {\n  background-color: #ffff48;\n  border-color: #ffff48; }\n\n.checkbox-warning input[type=\"checkbox\"]:checked + label::after {\n  border-color: #fff; }\n\n.checkbox-danger input[type=\"checkbox\"]:checked + label::before {\n  background-color: #ff5d48;\n  border-color: #ff5d48; }\n\n.checkbox-danger input[type=\"checkbox\"]:checked + label::after {\n  border-color: #fff; }\n\n.checkbox-light input[type=\"checkbox\"]:checked + label::before {\n  background-color: #f8f9fa;\n  border-color: #f8f9fa; }\n\n.checkbox-light input[type=\"checkbox\"]:checked + label::after {\n  border-color: #fff; }\n\n.checkbox-dark input[type=\"checkbox\"]:checked + label::before {\n  background-color: #343a40;\n  border-color: #343a40; }\n\n.checkbox-dark input[type=\"checkbox\"]:checked + label::after {\n  border-color: #fff; }\n\n.checkbox-purple input[type=\"checkbox\"]:checked + label::before {\n  background-color: #9261c6;\n  border-color: #9261c6; }\n\n.checkbox-purple input[type=\"checkbox\"]:checked + label::after {\n  border-color: #fff; }\n\n.checkbox-pink input[type=\"checkbox\"]:checked + label::before {\n  background-color: #ff7aa3;\n  border-color: #ff7aa3; }\n\n.checkbox-pink input[type=\"checkbox\"]:checked + label::after {\n  border-color: #fff; }\n\n.radio label {\n  display: inline-block;\n  padding-left: 8px;\n  position: relative;\n  font-weight: normal; }\n  .radio label::before {\n    -o-transition: border 0.5s ease-in-out;\n    -webkit-transition: border 0.5s ease-in-out;\n    background-color: #fff;\n    border-radius: 50%;\n    border: 2px solid #efefef;\n    content: \"\";\n    display: inline-block;\n    height: 18px;\n    left: 0;\n    margin-left: -18px;\n    position: absolute;\n    transition: border 0.5s ease-in-out;\n    width: 18px;\n    outline: none !important; }\n  .radio label::after {\n    -moz-transition: -moz-transform 0.1s cubic-bezier(0.8, -0.33, 0.2, 1.33);\n    -ms-transform: scale(0, 0);\n    -o-transform: scale(0, 0);\n    -o-transition: -o-transform 0.1s cubic-bezier(0.8, -0.33, 0.2, 1.33);\n    -webkit-transform: scale(0, 0);\n    -webkit-transition: -webkit-transform 0.1s cubic-bezier(0.8, -0.33, 0.2, 1.33);\n    background-color: #6c757d;\n    border-radius: 50%;\n    content: \" \";\n    display: inline-block;\n    height: 10px;\n    left: 6px;\n    margin-left: -20px;\n    position: absolute;\n    top: 4px;\n    transform: scale(0, 0);\n    transition: transform 0.1s cubic-bezier(0.8, -0.33, 0.2, 1.33);\n    width: 10px; }\n\n.radio input[type=\"radio\"] {\n  cursor: pointer;\n  opacity: 0;\n  z-index: 1;\n  outline: none !important; }\n  .radio input[type=\"radio\"]:disabled + label {\n    opacity: 0.65; }\n\n.radio input[type=\"radio\"]:focus + label::before {\n  outline-offset: -2px;\n  outline: 5px auto -webkit-focus-ring-color;\n  outline: thin dotted; }\n\n.radio input[type=\"radio\"]:checked + label::after {\n  -ms-transform: scale(1, 1);\n  -o-transform: scale(1, 1);\n  -webkit-transform: scale(1, 1);\n  transform: scale(1, 1); }\n\n.radio input[type=\"radio\"]:disabled + label::before {\n  cursor: not-allowed; }\n\n.radio.radio-inline {\n  margin-top: 0; }\n\n.radio.radio-single label {\n  height: 18px;\n  width: 18px; }\n\n.radio-primary input[type=\"radio\"] + label::after {\n  background-color: #64b0f2; }\n\n.radio-primary input[type=\"radio\"]:checked + label::before {\n  border-color: #64b0f2; }\n\n.radio-primary input[type=\"radio\"]:checked + label::after {\n  background-color: #64b0f2; }\n\n.radio-secondary input[type=\"radio\"] + label::after {\n  background-color: #6c757d; }\n\n.radio-secondary input[type=\"radio\"]:checked + label::before {\n  border-color: #6c757d; }\n\n.radio-secondary input[type=\"radio\"]:checked + label::after {\n  background-color: #6c757d; }\n\n.radio-success input[type=\"radio\"] + label::after {\n  background-color: #1bb99a; }\n\n.radio-success input[type=\"radio\"]:checked + label::before {\n  border-color: #1bb99a; }\n\n.radio-success input[type=\"radio\"]:checked + label::after {\n  background-color: #1bb99a; }\n\n.radio-info input[type=\"radio\"] + label::after {\n  background-color: #3db9dc; }\n\n.radio-info input[type=\"radio\"]:checked + label::before {\n  border-color: #3db9dc; }\n\n.radio-info input[type=\"radio\"]:checked + label::after {\n  background-color: #3db9dc; }\n\n.radio-warning input[type=\"radio\"] + label::after {\n  background-color: #ffff48; }\n\n.radio-warning input[type=\"radio\"]:checked + label::before {\n  border-color: #ffff48; }\n\n.radio-warning input[type=\"radio\"]:checked + label::after {\n  background-color: #ffff48; }\n\n.radio-danger input[type=\"radio\"] + label::after {\n  background-color: #ff5d48; }\n\n.radio-danger input[type=\"radio\"]:checked + label::before {\n  border-color: #ff5d48; }\n\n.radio-danger input[type=\"radio\"]:checked + label::after {\n  background-color: #ff5d48; }\n\n.radio-light input[type=\"radio\"] + label::after {\n  background-color: #f8f9fa; }\n\n.radio-light input[type=\"radio\"]:checked + label::before {\n  border-color: #f8f9fa; }\n\n.radio-light input[type=\"radio\"]:checked + label::after {\n  background-color: #f8f9fa; }\n\n.radio-dark input[type=\"radio\"] + label::after {\n  background-color: #343a40; }\n\n.radio-dark input[type=\"radio\"]:checked + label::before {\n  border-color: #343a40; }\n\n.radio-dark input[type=\"radio\"]:checked + label::after {\n  background-color: #343a40; }\n\n.radio-purple input[type=\"radio\"] + label::after {\n  background-color: #9261c6; }\n\n.radio-purple input[type=\"radio\"]:checked + label::before {\n  border-color: #9261c6; }\n\n.radio-purple input[type=\"radio\"]:checked + label::after {\n  background-color: #9261c6; }\n\n.radio-pink input[type=\"radio\"] + label::after {\n  background-color: #ff7aa3; }\n\n.radio-pink input[type=\"radio\"]:checked + label::before {\n  border-color: #ff7aa3; }\n\n.radio-pink input[type=\"radio\"]:checked + label::after {\n  background-color: #ff7aa3; }\n\n@media print {\n  .left-side-menu,\n  .right-bar,\n  .page-title-box,\n  .navbar-custom,\n  .footer {\n    display: none; }\n  .card-body,\n  .content-page,\n  .right-bar,\n  .content,\n  body {\n    padding: 0;\n    margin: 0; } }\n\n/*!\r\n * Waves v0.7.6\r\n * http://fian.my.id/Waves \r\n * \r\n * Copyright 2014-2018 Alfiana E. Sibuea and other contributors \r\n * Released under the MIT license \r\n * https://github.com/fians/Waves/blob/master/LICENSE */\n.waves-effect {\n  position: relative;\n  cursor: pointer;\n  display: inline-block;\n  overflow: hidden;\n  -webkit-user-select: none;\n  -moz-user-select: none;\n  -ms-user-select: none;\n  user-select: none;\n  -webkit-tap-highlight-color: transparent; }\n\n.waves-effect .waves-ripple {\n  position: absolute;\n  border-radius: 50%;\n  width: 100px;\n  height: 100px;\n  margin-top: -50px;\n  margin-left: -50px;\n  opacity: 0;\n  background: rgba(0, 0, 0, 0.2);\n  background: -webkit-radial-gradient(rgba(0, 0, 0, 0.2) 0, rgba(0, 0, 0, 0.3) 40%, rgba(0, 0, 0, 0.4) 50%, rgba(0, 0, 0, 0.5) 60%, rgba(255, 255, 255, 0) 70%);\n  background: -o-radial-gradient(rgba(0, 0, 0, 0.2) 0, rgba(0, 0, 0, 0.3) 40%, rgba(0, 0, 0, 0.4) 50%, rgba(0, 0, 0, 0.5) 60%, rgba(255, 255, 255, 0) 70%);\n  background: -moz-radial-gradient(rgba(0, 0, 0, 0.2) 0, rgba(0, 0, 0, 0.3) 40%, rgba(0, 0, 0, 0.4) 50%, rgba(0, 0, 0, 0.5) 60%, rgba(255, 255, 255, 0) 70%);\n  background: radial-gradient(rgba(0, 0, 0, 0.2) 0, rgba(0, 0, 0, 0.3) 40%, rgba(0, 0, 0, 0.4) 50%, rgba(0, 0, 0, 0.5) 60%, rgba(255, 255, 255, 0) 70%);\n  -webkit-transition: all 0.5s ease-out;\n  -moz-transition: all 0.5s ease-out;\n  -o-transition: all 0.5s ease-out;\n  transition: all 0.5s ease-out;\n  -webkit-transition-property: -webkit-transform, opacity;\n  -moz-transition-property: -moz-transform, opacity;\n  -o-transition-property: -o-transform, opacity;\n  transition-property: transform, opacity;\n  -webkit-transform: scale(0) translate(0, 0);\n  -moz-transform: scale(0) translate(0, 0);\n  -ms-transform: scale(0) translate(0, 0);\n  -o-transform: scale(0) translate(0, 0);\n  transform: scale(0) translate(0, 0);\n  pointer-events: none; }\n\n.waves-effect.waves-light .waves-ripple {\n  background: rgba(255, 255, 255, 0.4);\n  background: -webkit-radial-gradient(rgba(255, 255, 255, 0.2) 0, rgba(255, 255, 255, 0.3) 40%, rgba(255, 255, 255, 0.4) 50%, rgba(255, 255, 255, 0.5) 60%, rgba(255, 255, 255, 0) 70%);\n  background: -o-radial-gradient(rgba(255, 255, 255, 0.2) 0, rgba(255, 255, 255, 0.3) 40%, rgba(255, 255, 255, 0.4) 50%, rgba(255, 255, 255, 0.5) 60%, rgba(255, 255, 255, 0) 70%);\n  background: -moz-radial-gradient(rgba(255, 255, 255, 0.2) 0, rgba(255, 255, 255, 0.3) 40%, rgba(255, 255, 255, 0.4) 50%, rgba(255, 255, 255, 0.5) 60%, rgba(255, 255, 255, 0) 70%);\n  background: radial-gradient(rgba(255, 255, 255, 0.2) 0, rgba(255, 255, 255, 0.3) 40%, rgba(255, 255, 255, 0.4) 50%, rgba(255, 255, 255, 0.5) 60%, rgba(255, 255, 255, 0) 70%); }\n\n.waves-effect.waves-classic .waves-ripple {\n  background: rgba(0, 0, 0, 0.2); }\n\n.waves-effect.waves-classic.waves-light .waves-ripple {\n  background: rgba(255, 255, 255, 0.4); }\n\n.waves-notransition {\n  -webkit-transition: none !important;\n  -moz-transition: none !important;\n  -o-transition: none !important;\n  transition: none !important; }\n\n.waves-button,\n.waves-circle {\n  -webkit-transform: translateZ(0);\n  -moz-transform: translateZ(0);\n  -ms-transform: translateZ(0);\n  -o-transform: translateZ(0);\n  transform: translateZ(0);\n  -webkit-mask-image: -webkit-radial-gradient(circle, white 100%, black 100%); }\n\n.waves-button,\n.waves-button:hover,\n.waves-button:visited,\n.waves-button-input {\n  white-space: nowrap;\n  vertical-align: middle;\n  cursor: pointer;\n  border: none;\n  outline: none;\n  color: inherit;\n  background-color: rgba(0, 0, 0, 0);\n  font-size: 1em;\n  line-height: 1em;\n  text-align: center;\n  text-decoration: none;\n  z-index: 1; }\n\n.waves-button {\n  padding: 0.85em 1.1em;\n  border-radius: 0.2em; }\n\n.waves-button-input {\n  margin: 0;\n  padding: 0.85em 1.1em; }\n\n.waves-input-wrapper {\n  border-radius: 0.2em;\n  vertical-align: bottom; }\n\n.waves-input-wrapper.waves-button {\n  padding: 0; }\n\n.waves-input-wrapper .waves-button-input {\n  position: relative;\n  top: 0;\n  left: 0;\n  z-index: 1; }\n\n.waves-circle {\n  text-align: center;\n  width: 2.5em;\n  height: 2.5em;\n  line-height: 2.5em;\n  border-radius: 50%; }\n\n.waves-float {\n  -webkit-mask-image: none;\n  -webkit-box-shadow: 0px 1px 1.5px 1px rgba(0, 0, 0, 0.12);\n  box-shadow: 0px 1px 1.5px 1px rgba(0, 0, 0, 0.12);\n  -webkit-transition: all 300ms;\n  -moz-transition: all 300ms;\n  -o-transition: all 300ms;\n  transition: all 300ms; }\n\n.waves-float:active {\n  -webkit-box-shadow: 0px 8px 20px 1px rgba(0, 0, 0, 0.3);\n  box-shadow: 0px 8px 20px 1px rgba(0, 0, 0, 0.3); }\n\n.waves-block {\n  display: block; }\n\n.slimScrollDiv {\n  height: auto !important; }\n\n/* =============\r\n   Notification\r\n============= */\n#toast-container > div {\n  box-shadow: 0px 0px 35px 0px rgba(73, 80, 87, 0.15);\n  opacity: 1; }\n  #toast-container > div:hover {\n    box-shadow: 0px 0px 35px 0px rgba(73, 80, 87, 0.15);\n    opacity: 0.9; }\n\n.toast-primary {\n  border: 2px solid #64b0f2 !important;\n  background-color: rgba(100, 176, 242, 0.8) !important; }\n\n.toast-secondary {\n  border: 2px solid #6c757d !important;\n  background-color: rgba(108, 117, 125, 0.8) !important; }\n\n.toast-success {\n  border: 2px solid #1bb99a !important;\n  background-color: rgba(27, 185, 154, 0.8) !important; }\n\n.toast-info {\n  border: 2px solid #3db9dc !important;\n  background-color: rgba(61, 185, 220, 0.8) !important; }\n\n.toast-warning {\n  border: 2px solid #ffff48 !important;\n  background-color: rgba(241, 181, 61, 0.8) !important; }\n\n.toast-danger {\n  border: 2px solid #ff5d48 !important;\n  background-color: rgba(255, 93, 72, 0.8) !important; }\n\n.toast-light {\n  border: 2px solid #f8f9fa !important;\n  background-color: rgba(248, 249, 250, 0.8) !important; }\n\n.toast-dark {\n  border: 2px solid #343a40 !important;\n  background-color: rgba(52, 58, 64, 0.8) !important; }\n\n.toast-purple {\n  border: 2px solid #9261c6 !important;\n  background-color: rgba(146, 97, 198, 0.8) !important; }\n\n.toast-pink {\n  border: 2px solid #ff7aa3 !important;\n  background-color: rgba(255, 122, 163, 0.8) !important; }\n\n.toast-error {\n  background-color: rgba(255, 93, 72, 0.8);\n  border: 2px solid #ff5d48; }\n\n.swal2-modal {\n  font-family: -apple-system, BlinkMacSystemFont, \"Segoe UI\", Roboto, \"Helvetica Neue\", Arial, \"Noto Sans\", sans-serif, \"Apple Color Emoji\", \"Segoe UI Emoji\", \"Segoe UI Symbol\", \"Noto Color Emoji\";\n  box-shadow: 0 10px 33px rgba(0, 0, 0, 0.1); }\n  .swal2-modal .swal2-title {\n    font-size: 24px; }\n  .swal2-modal .swal2-content {\n    font-size: 16px; }\n  .swal2-modal .swal2-spacer {\n    margin: 10px 0; }\n  .swal2-modal .swal2-file, .swal2-modal .swal2-input, .swal2-modal .swal2-textarea {\n    border: 2px solid #efefef;\n    font-size: 16px;\n    box-shadow: none; }\n  .swal2-modal .swal2-confirm.btn-confirm {\n    background-color: #64b0f2 !important;\n    font-size: 0.9rem; }\n  .swal2-modal .swal2-cancel.btn-cancel {\n    background-color: #ff5d48 !important;\n    font-size: 0.9rem; }\n  .swal2-modal .swal2-styled:focus {\n    box-shadow: none !important; }\n\n.swal2-icon.swal2-question {\n  color: #64b0f2;\n  border-color: #64b0f2; }\n\n.swal2-icon.swal2-success {\n  border-color: #1bb99a; }\n  .swal2-icon.swal2-success .line, .swal2-icon.swal2-success [class^=swal2-success-line][class$=long],\n  .swal2-icon.swal2-success [class^=swal2-success-line] {\n    background-color: #1bb99a; }\n  .swal2-icon.swal2-success .placeholder, .swal2-icon.swal2-success .swal2-success-ring {\n    border-color: #1bb99a; }\n\n.swal2-icon.swal2-warning {\n  color: #ffff48;\n  border-color: #ffff48; }\n\n.swal2-icon.swal2-error {\n  border-color: #ff5d48; }\n  .swal2-icon.swal2-error .line {\n    background-color: #ff5d48; }\n\n.swal2-modal .swal2-file:focus, .swal2-modal .swal2-input:focus, .swal2-modal .swal2-textarea:focus {\n  outline: 0;\n  border: 2px solid #64b0f2; }\n\n.swal2-container.swal2-shown {\n  background-color: rgba(52, 58, 64, 0.9); }\n\n.irs--modern .irs-bar, .irs--modern .irs-to, .irs--modern .irs-from, .irs--modern .irs-single {\n  background: #64b0f2 !important; }\n\n.irs--modern .irs-to:before, .irs--modern .irs-from:before, .irs--modern .irs-single:before {\n  border-top-color: #64b0f2; }\n\n.irs--modern .irs-line {\n  background: #efefef;\n  border-color: #efefef; }\n\n.irs--modern .irs-min, .irs--modern .irs-max {\n  color: #adb5bd;\n  background: #efefef; }\n\n.irs--modern .irs-grid-text {\n  font-size: 12px;\n  color: #ced4da; }\n\n.irs--modern .irs-handle > i:nth-child(1) {\n  width: 8px;\n  height: 8px; }\n\n/* =============\r\n   Rating\r\n============= */\n.rating-star i {\n  color: #6c757d; }\n\n.rating-md i {\n  font-size: 16px; }\n\n.rating-lg i {\n  font-size: 22px; }\n\n.jstree-default .jstree-node,\n.jstree-default .jstree-icon {\n  background-image: url(\"../images/plugins/jstree.png\"); }\n\n.jstree-default .jstree-node {\n  background-position: -292px -4px;\n  background-repeat: repeat-y; }\n\n.jstree-default .jstree-themeicon-custom {\n  background-color: transparent;\n  background-image: none;\n  background-position: 0 0; }\n\n.jstree-default .jstree-anchor {\n  line-height: 28px;\n  height: 28px; }\n\n.jstree-default > .jstree-container-ul .jstree-loading > .jstree-ocl {\n  background: url(\"../images/plugins/loading.gif\") center center no-repeat; }\n\n.jstree-default .jstree-icon:empty {\n  width: 24px;\n  height: 28px;\n  line-height: 28px;\n  font-size: 15px;\n  color: #6c757d; }\n\n.jstree-default .jstree-clicked,\n.jstree-default .jstree-wholerow-clicked {\n  background: rgba(100, 176, 242, 0.2);\n  box-shadow: none; }\n\n.jstree-default .jstree-hovered,\n.jstree-default .jstree-wholerow-hovered {\n  background: rgba(100, 176, 242, 0.25);\n  box-shadow: none; }\n\n.jstree-default .jstree-last {\n  background: transparent; }\n\n.jstree-default .jstree-wholerow {\n  height: 28px; }\n\ndiv.hopscotch-bubble {\n  border: 3px solid #64b0f2;\n  border-radius: 5px; }\n  div.hopscotch-bubble .hopscotch-next,\n  div.hopscotch-bubble .hopscotch-prev {\n    background-color: #64b0f2 !important;\n    background-image: none !important;\n    border-color: #64b0f2 !important;\n    text-shadow: none !important;\n    margin: 0 0 0 5px !important;\n    font-family: -apple-system, BlinkMacSystemFont, \"Segoe UI\", Roboto, \"Helvetica Neue\", Arial, \"Noto Sans\", sans-serif, \"Apple Color Emoji\", \"Segoe UI Emoji\", \"Segoe UI Symbol\", \"Noto Color Emoji\";\n    color: #fff !important; }\n  div.hopscotch-bubble .hopscotch-bubble-number {\n    background: #1bb99a;\n    padding: 0;\n    border-radius: 50%; }\n  div.hopscotch-bubble .hopscotch-bubble-arrow-container.left .hopscotch-bubble-arrow-border {\n    border-right: 19px solid #64b0f2; }\n  div.hopscotch-bubble .hopscotch-bubble-arrow-container.left .hopscotch-bubble-arrow {\n    border: none; }\n  div.hopscotch-bubble .hopscotch-bubble-arrow-container.right .hopscotch-bubble-arrow {\n    border-left: 19px solid #64b0f2;\n    left: -2px; }\n  div.hopscotch-bubble .hopscotch-bubble-arrow-container.right .hopscotch-bubble-arrow-border {\n    border-left: 0 solid #64b0f2; }\n  div.hopscotch-bubble .hopscotch-bubble-arrow-container.up .hopscotch-bubble-arrow {\n    border-bottom: 19px solid #64b0f2;\n    top: 0; }\n  div.hopscotch-bubble .hopscotch-bubble-arrow-container.up .hopscotch-bubble-arrow-border {\n    border-bottom: 0 solid rgba(0, 0, 0, 0.5); }\n  div.hopscotch-bubble .hopscotch-bubble-arrow-container.down .hopscotch-bubble-arrow {\n    border-top: 19px solid #64b0f2;\n    top: -2px; }\n  div.hopscotch-bubble .hopscotch-bubble-arrow-container.down .hopscotch-bubble-arrow-border {\n    border-top: 0 solid rgba(0, 0, 0, 0.5); }\n  div.hopscotch-bubble h3 {\n    font-family: \"Roboto\", sans-serif;\n    margin-bottom: 10px;\n    font-weight: 600; }\n  div.hopscotch-bubble .hopscotch-content {\n    font-family: -apple-system, BlinkMacSystemFont, \"Segoe UI\", Roboto, \"Helvetica Neue\", Arial, \"Noto Sans\", sans-serif, \"Apple Color Emoji\", \"Segoe UI Emoji\", \"Segoe UI Symbol\", \"Noto Color Emoji\"; }\n\n.calendar {\n  float: left;\n  margin-bottom: 0; }\n\n.fc-view {\n  margin-top: 30px; }\n\n.none-border .modal-footer {\n  border-top: none; }\n\n.fc-toolbar h2 {\n  font-size: 1.25rem;\n  line-height: 1.875rem;\n  text-transform: uppercase; }\n\n.fc-day-grid-event .fc-time {\n  font-weight: 500; }\n\n.fc-day {\n  background: transparent; }\n\n.fc-toolbar .fc-state-active,\n.fc-toolbar .ui-state-active,\n.fc-toolbar button:focus,\n.fc-toolbar button:hover,\n.fc-toolbar .ui-state-hover {\n  z-index: 0; }\n\n.fc th.fc-widget-header {\n  background: #efefef;\n  font-size: 13px;\n  line-height: 20px;\n  padding: 10px 0;\n  text-transform: uppercase;\n  font-weight: 600; }\n\n.fc-unthemed th,\n.fc-unthemed td,\n.fc-unthemed thead,\n.fc-unthemed tbody,\n.fc-unthemed .fc-divider,\n.fc-unthemed .fc-row,\n.fc-unthemed .fc-popover {\n  border-color: #efefef; }\n\n.fc-unthemed td.fc-today, .fc-unthemed .fc-divider {\n  background: #efefef !important; }\n\n.fc-button {\n  background: #efefef;\n  border: none;\n  color: #495057;\n  text-transform: capitalize;\n  box-shadow: none;\n  border-radius: 3px !important;\n  margin: 0 3px !important;\n  padding: 6px 12px !important;\n  height: auto !important; }\n\n.fc-text-arrow {\n  font-family: inherit;\n  font-size: 1rem; }\n\n.fc-state-hover {\n  background: #efefef; }\n\n.fc-state-highlight {\n  background: #efefef; }\n\n.fc-state-down,\n.fc-state-active,\n.fc-state-disabled {\n  background-color: #64b0f2;\n  color: #fff;\n  text-shadow: none; }\n\n.fc-cell-overlay {\n  background: #efefef; }\n\n.fc-unthemed td.fc-today {\n  background: #fff; }\n\n.fc-event {\n  border-radius: 2px;\n  border: none;\n  cursor: move;\n  font-size: 0.8125rem;\n  margin: 5px 7px;\n  padding: 5px 5px;\n  text-align: center; }\n\n.external-event {\n  cursor: move;\n  margin: 10px 0;\n  padding: 8px 10px;\n  color: #fff;\n  border-radius: 4px; }\n\n.fc-basic-view td.fc-week-number span {\n  padding-right: 8px; }\n\n.fc-basic-view td.fc-day-number {\n  padding-right: 8px; }\n\n.fc-basic-view .fc-content {\n  color: #fff; }\n\n.fc-time-grid-event .fc-content {\n  color: #fff; }\n\n@media (max-width: 767.98px) {\n  .fc-toolbar .fc-left, .fc-toolbar .fc-right, .fc-toolbar .fc-center {\n    float: none;\n    display: block;\n    clear: both;\n    margin: 10px 0; }\n  .fc .fc-toolbar > * > * {\n    float: none; }\n  .fc-today-button {\n    display: none; } }\n\n/* Bootstrap tagsinput */\n.bootstrap-tagsinput {\n  box-shadow: none;\n  padding: 4px 7px 4px;\n  width: 100%;\n  background-color: #fff;\n  border-color: #ced4da; }\n  .bootstrap-tagsinput input {\n    color: #495057; }\n  .bootstrap-tagsinput .label-info {\n    background-color: #64b0f2;\n    display: inline-block;\n    font-size: 11px;\n    margin: 3px 1px;\n    padding: 0 5px;\n    border-radius: 3px;\n    font-weight: 500; }\n\n.ms-container {\n  background: transparent url(\"../images/plugins/multiple-arrow.png\") no-repeat 50% 50%;\n  width: auto;\n  max-width: 370px; }\n  .ms-container .ms-list {\n    box-shadow: none;\n    border: 1px solid #ced4da; }\n    .ms-container .ms-list.ms-focus {\n      box-shadow: none;\n      border: 1px solid #b1bbc4; }\n  .ms-container .ms-selectable {\n    background-color: #fff; }\n    .ms-container .ms-selectable li.ms-elem-selectable {\n      border: none;\n      padding: 5px 10px;\n      color: #6c757d; }\n    .ms-container .ms-selectable li.ms-hover {\n      background-color: #64b0f2;\n      color: #fff; }\n  .ms-container .ms-selection {\n    background-color: #fff; }\n    .ms-container .ms-selection li.ms-elem-selection {\n      border: none;\n      padding: 5px 10px;\n      color: #6c757d; }\n    .ms-container .ms-selection li.ms-hover {\n      background-color: #64b0f2;\n      color: #fff; }\n\n.search-input {\n  margin-bottom: 10px; }\n\n.ms-selectable {\n  box-shadow: none;\n  outline: none !important; }\n\n.ms-optgroup-label {\n  font-weight: 500;\n  font-family: \"Roboto\", sans-serif;\n  color: #343a40 !important;\n  font-size: 13px; }\n\n.select2-container .select2-selection--single {\n  background-color: #fff;\n  border: 1px solid #ced4da;\n  height: 38px;\n  outline: none; }\n  .select2-container .select2-selection--single .select2-selection__rendered {\n    line-height: 36px;\n    padding-left: 12px;\n    color: #6c757d; }\n  .select2-container .select2-selection--single .select2-selection__arrow {\n    height: 34px;\n    width: 34px;\n    right: 3px; }\n    .select2-container .select2-selection--single .select2-selection__arrow b {\n      border-color: #6c757d transparent transparent transparent;\n      border-width: 6px 6px 0 6px; }\n\n.select2-container--open .select2-selection--single .select2-selection__arrow b {\n  border-color: transparent transparent #6c757d transparent !important;\n  border-width: 0 6px 6px 6px !important; }\n\n.select2-results__option {\n  padding: 6px 12px; }\n\n.select2-dropdown {\n  border: rgba(0, 0, 0, 0.15);\n  background-color: #fff;\n  box-shadow: 0px 0px 35px 0px rgba(73, 80, 87, 0.15); }\n\n.select2-container--default .select2-search--dropdown {\n  padding: 10px;\n  background-color: #fff; }\n  .select2-container--default .select2-search--dropdown .select2-search__field {\n    border: 1px solid #ced4da;\n    background-color: #fff;\n    color: #6c757d;\n    outline: none; }\n\n.select2-container--default .select2-results__group {\n  font-weight: 500; }\n\n.select2-container--default .select2-results__option--highlighted[aria-selected] {\n  background-color: #64b0f2; }\n\n.select2-container--default .select2-results__option[aria-selected=true] {\n  background-color: #f8f9fa;\n  color: #16181b; }\n  .select2-container--default .select2-results__option[aria-selected=true]:hover {\n    background-color: #64b0f2;\n    color: #fff; }\n\n.select2-container .select2-selection--multiple {\n  min-height: 38px;\n  background-color: #fff;\n  border: 1px solid #ced4da !important; }\n  .select2-container .select2-selection--multiple .select2-selection__rendered {\n    padding: 1px 10px; }\n  .select2-container .select2-selection--multiple .select2-search__field {\n    border: 0;\n    color: #6c757d; }\n    .select2-container .select2-selection--multiple .select2-search__field::placeholder {\n      color: #6c757d; }\n  .select2-container .select2-selection--multiple .select2-selection__choice {\n    background-color: #64b0f2;\n    border: none;\n    color: #fff;\n    border-radius: 3px;\n    padding: 0 7px;\n    margin-top: 7px; }\n  .select2-container .select2-selection--multiple .select2-selection__choice__remove {\n    color: #fff;\n    margin-right: 5px; }\n    .select2-container .select2-selection--multiple .select2-selection__choice__remove:hover {\n      color: #fff; }\n\n.autocomplete-suggestions {\n  border: 1px solid #e9ecef;\n  background: #eff1f3;\n  cursor: default;\n  overflow: auto;\n  max-height: 200px !important;\n  box-shadow: 0px 0px 35px 0px rgba(73, 80, 87, 0.15); }\n  .autocomplete-suggestions strong {\n    font-weight: 600;\n    color: #343a40; }\n\n.autocomplete-suggestion {\n  padding: 5px 10px;\n  white-space: nowrap;\n  overflow: hidden; }\n\n.autocomplete-no-suggestion {\n  padding: 5px; }\n\n.autocomplete-selected {\n  background: #e9ecef;\n  cursor: pointer; }\n\n.autocomplete-group {\n  padding: 5px;\n  font-weight: 500;\n  font-family: \"Roboto\", sans-serif; }\n  .autocomplete-group strong {\n    font-weight: 600;\n    font-size: 16px;\n    color: #343a40;\n    display: block; }\n\n/* =============\r\n   Form validation\r\n============= */\n.parsley-error {\n  border-color: #ff5d48 !important; }\n\n.parsley-errors-list {\n  margin: 0;\n  padding: 0; }\n  .parsley-errors-list > li {\n    font-size: 12px;\n    list-style: none;\n    color: #ff5d48;\n    margin-top: 5px; }\n\n.bootstrap-timepicker-widget table td input {\n  border: 1px solid rgba(52, 58, 64, 0.3);\n  width: 35px;\n  background-color: #fff;\n  color: #6c757d; }\n\n.bootstrap-timepicker-widget table td a {\n  color: #adb5bd; }\n  .bootstrap-timepicker-widget table td a:hover {\n    background-color: #eff1f3;\n    border-color: #eff1f3; }\n\n.bootstrap-timepicker-widget.dropdown-menu:after {\n  border-bottom-color: #e9ecef; }\n\n.bootstrap-timepicker-widget.timepicker-orient-bottom:after {\n  border-top-color: #e9ecef; }\n\n.datepicker {\n  padding: 10px !important; }\n  .datepicker td,\n  .datepicker th {\n    width: 30px;\n    height: 30px;\n    padding: 5px; }\n  .datepicker th {\n    font-weight: 600; }\n  .datepicker table tr td.active.active, .datepicker table tr td.active.disabled, .datepicker table tr td.active.disabled.active, .datepicker table tr td.active.disabled.disabled, .datepicker table tr td.active.disabled:active, .datepicker table tr td.active.disabled:hover, .datepicker table tr td.active.disabled:hover.active, .datepicker table tr td.active.disabled:hover.disabled, .datepicker table tr td.active.disabled:hover:active, .datepicker table tr td.active.disabled:hover:hover,\n  .datepicker table tr td .active.disabled:hover[disabled],\n  .datepicker table tr td .active.disabled[disabled],\n  .datepicker table tr td .active:active,\n  .datepicker table tr td .active:hover,\n  .datepicker table tr td .active:hover.active,\n  .datepicker table tr td .active:hover.disabled,\n  .datepicker table tr td .active:hover:active,\n  .datepicker table tr td .active:hover:hover,\n  .datepicker table tr td .active:hover[disabled],\n  .datepicker table tr td .active[disabled],\n  .datepicker table tr td span.active.active,\n  .datepicker table tr td span.active.disabled,\n  .datepicker table tr td span.active.disabled.active,\n  .datepicker table tr td span.active.disabled.disabled,\n  .datepicker table tr td span.active.disabled:active,\n  .datepicker table tr td span.active.disabled:hover,\n  .datepicker table tr td span.active.disabled:hover.active,\n  .datepicker table tr td span.active.disabled:hover.disabled,\n  .datepicker table tr td span.active.disabled:hover:active,\n  .datepicker table tr td span.active.disabled:hover:hover,\n  .datepicker table tr td span.active.disabled:hover[disabled],\n  .datepicker table tr td span.active.disabled[disabled],\n  .datepicker table tr td span.active:active,\n  .datepicker table tr td span.active:hover,\n  .datepicker table tr td span.active:hover.active,\n  .datepicker table tr td span.active:hover.disabled,\n  .datepicker table tr td span.active:hover:active,\n  .datepicker table tr td span.active:hover:hover,\n  .datepicker table tr td span.active:hover[disabled],\n  .datepicker table tr td span.active[disabled], .datepicker table tr td.today, .datepicker table tr td.today.disabled, .datepicker table tr td.today.disabled:hover, .datepicker table tr td.today:hover, .datepicker table tr td.selected {\n    background-color: #64b0f2 !important;\n    background-image: none !important;\n    color: #fff; }\n  .datepicker table tr td.day.focused, .datepicker table tr td.day:hover,\n  .datepicker table tr td span.focused,\n  .datepicker table tr td span:hover {\n    background: #e9ecef; }\n  .datepicker table tr td.new, .datepicker table tr td.old,\n  .datepicker table tr td span.new,\n  .datepicker table tr td span.old {\n    color: #adb5bd;\n    opacity: 0.6; }\n  .datepicker table tr td.range, .datepicker table tr td.range.disabled, .datepicker table tr td.range.disabled:hover, .datepicker table tr td.range:hover {\n    background-color: #f5f6f8; }\n  .datepicker .datepicker-switch:hover,\n  .datepicker .next:hover,\n  .datepicker .prev:hover,\n  .datepicker tfoot tr th:hover {\n    background: #e9ecef; }\n  .datepicker .datepicker-switch:hover {\n    background: none; }\n\n.datepicker-inline {\n  border: 2px solid rgba(52, 58, 64, 0.1); }\n\n.datepicker-dropdown:after {\n  border-bottom: 6px solid #fff; }\n\n.datepicker-dropdown:before {\n  border-bottom-color: rgba(0, 0, 0, 0.15); }\n\n.datepicker-dropdown.datepicker-orient-top:before {\n  border-top: 7px solid rgba(0, 0, 0, 0.15); }\n\n.datepicker-dropdown.datepicker-orient-top:after {\n  border-top: 6px solid #fff; }\n\n.daterangepicker {\n  font-family: -apple-system, BlinkMacSystemFont, \"Segoe UI\", Roboto, \"Helvetica Neue\", Arial, \"Noto Sans\", sans-serif, \"Apple Color Emoji\", \"Segoe UI Emoji\", \"Segoe UI Symbol\", \"Noto Color Emoji\";\n  background-color: #fff;\n  border-color: #efefef; }\n  .daterangepicker .calendar-table {\n    background-color: #fff;\n    border-color: #efefef; }\n    .daterangepicker .calendar-table .next span, .daterangepicker .calendar-table .prev span {\n      border-color: #adb5bd; }\n  .daterangepicker th, .daterangepicker td {\n    padding: 5px; }\n    .daterangepicker th.week, .daterangepicker td.week {\n      color: #ced4da; }\n    .daterangepicker th.available:hover, .daterangepicker td.available:hover {\n      background-color: #f5f6f8; }\n  .daterangepicker td.active, .daterangepicker td.active:hover, .daterangepicker .ranges li.active {\n    background-color: #64b0f2; }\n  .daterangepicker .ranges li:hover {\n    background-color: #efefef; }\n  .daterangepicker .month select, .daterangepicker .calendar-time select {\n    background-color: #fff;\n    border-color: #ced4da;\n    color: #495057; }\n  .daterangepicker td.off, .daterangepicker td.off.in-range, .daterangepicker td.off.start-date, .daterangepicker td.off.end-date {\n    background-color: transparent;\n    color: rgba(206, 212, 218, 0.7); }\n  .daterangepicker td.in-range {\n    background-color: #f5f6f8;\n    color: #6c757d; }\n  .daterangepicker.show-ranges.ltr .drp-calendar.left {\n    border-left-color: #efefef; }\n  .daterangepicker .drp-buttons {\n    border-top-color: #efefef; }\n    .daterangepicker .drp-buttons .btn {\n      font-weight: 500; }\n\n.clockpicker-popover .popover-title {\n  font-size: 16px;\n  font-weight: 600;\n  background-color: white; }\n\n.clockpicker-popover .popover-content {\n  background-color: #f5f6f8; }\n\n.clockpicker-popover .clockpicker-plate {\n  background-color: white;\n  border-color: #e9ecef; }\n\n.clockpicker-popover .clockpicker-tick {\n  color: #adb5bd; }\n\n.clockpicker-popover .btn-default {\n  background-color: #64b0f2;\n  color: #fff; }\n\n.wizard > .steps {\n  position: relative;\n  display: block;\n  width: 100%; }\n  .wizard > .steps > ul > li {\n    width: 25%; }\n  .wizard > .steps a {\n    font-size: 16px;\n    margin: 0 0.5em 0.5em; }\n  .wizard > .steps a, .wizard > .steps a:hover, .wizard > .steps a:active {\n    display: block;\n    width: auto;\n    padding: 1em 1em;\n    text-decoration: none;\n    border-radius: 2px; }\n  .wizard > .steps .disabled a {\n    background: #f5f6f8;\n    border: 1px solid #e9ecef;\n    color: #343a40;\n    cursor: default; }\n    .wizard > .steps .disabled a:hover, .wizard > .steps .disabled a:active {\n      background: #eff1f3; }\n  .wizard > .steps .current a, .wizard > .steps .current a:hover, .wizard > .steps .current a:active {\n    background: #64b0f2;\n    color: #fff;\n    cursor: default; }\n  .wizard > .steps .done a, .wizard > .steps .done a:hover, .wizard > .steps .done a:active {\n    background: #e9ecef;\n    color: #343a40; }\n\n.wizard > .steps > ul > li, .wizard > .actions > ul > li {\n  float: left;\n  position: relative; }\n\n.wizard > .content {\n  display: block;\n  margin: 0.5em;\n  min-height: 240px;\n  overflow: hidden;\n  position: relative;\n  width: auto;\n  padding: 20px;\n  border: 1px solid #e9ecef; }\n  .wizard > .content > .body {\n    padding: 0;\n    position: relative; }\n    .wizard > .content > .body ul {\n      list-style: disc !important; }\n      .wizard > .content > .body ul > li {\n        display: block;\n        line-height: 30px; }\n    .wizard > .content > .body > iframe {\n      border: 0 none;\n      width: 100%;\n      height: 100%; }\n    .wizard > .content > .body input {\n      display: block;\n      border-color: #efefef; }\n      .wizard > .content > .body input:focus {\n        border-color: #efefef; }\n      .wizard > .content > .body input[type=\"checkbox\"] {\n        display: inline-block; }\n      .wizard > .content > .body input.error {\n        background: rgba(255, 93, 72, 0.1);\n        border: 1px solid #ffedeb;\n        color: #ff5d48; }\n    .wizard > .content > .body label {\n      display: inline-block;\n      margin-bottom: 0.5em;\n      margin-top: 10px; }\n      .wizard > .content > .body label.error {\n        color: #ff5d48;\n        font-size: 12px; }\n\n.wizard > .actions {\n  position: relative;\n  display: block;\n  text-align: right;\n  width: 100%;\n  margin-top: 15px; }\n  .wizard > .actions > ul {\n    display: inline-block;\n    text-align: right; }\n    .wizard > .actions > ul > li {\n      margin: 0 0.5em; }\n  .wizard > .actions a, .wizard > .actions a:hover, .wizard > .actions a:active {\n    background: #64b0f2;\n    color: #fff;\n    display: block;\n    padding: 0.5em 1em;\n    text-decoration: none;\n    border-radius: 2px; }\n  .wizard > .actions .disabled a, .wizard > .actions .disabled a:hover, .wizard > .actions .disabled a:active {\n    background: #f5f6f8;\n    color: #343a40; }\n\n.wizard.vertical > .steps {\n  display: inline;\n  float: left;\n  width: 30%; }\n  .wizard.vertical > .steps > ul > li {\n    float: none;\n    width: 100%; }\n\n.wizard.vertical > .content {\n  width: 65%;\n  margin: 0 2.5% 0.5em;\n  display: inline;\n  float: left; }\n\n.wizard.vertical > .actions {\n  display: inline;\n  float: right;\n  width: 95%;\n  margin: 0 2.5%;\n  margin-top: 15px !important; }\n  .wizard.vertical > .actions > ul > li {\n    margin: 0 0 0 1em; }\n\n/*\r\n  Common \r\n*/\n.wizard, .tabcontrol {\n  display: block;\n  width: 100%;\n  overflow: hidden;\n  /* Accessibility */ }\n  .wizard a, .tabcontrol a {\n    outline: 0; }\n  .wizard ul, .tabcontrol ul {\n    list-style: none !important;\n    padding: 0;\n    margin: 0; }\n    .wizard ul > li, .tabcontrol ul > li {\n      display: block;\n      padding: 0; }\n  .wizard > .steps .current-info, .tabcontrol > .steps .current-info {\n    position: absolute;\n    left: -999em; }\n  .wizard > .content > .title, .tabcontrol > .content > .title {\n    position: absolute;\n    left: -999em; }\n\n@media (max-width: 767.98px) {\n  .wizard > .steps > ul > li, .wizard.vertical > .steps, .wizard.vertical > .content {\n    width: 100%; } }\n\n@font-face {\n  font-family: \"summernote\";\n  font-style: normal;\n  font-weight: normal;\n  src: url(\"../fonts/summernote.eot\");\n  src: url(\"../fonts/summernote.eot?#iefix\") format(\"embedded-opentype\"), url(\"../fonts/summernote.woff?\") format(\"woff\"), url(\"../fonts/summernote.ttf?\") format(\"truetype\"); }\n\n.note-editor.note-frame {\n  border: 1px solid #dde2e6;\n  box-shadow: none;\n  margin: 0; }\n  .note-editor.note-frame .note-statusbar {\n    background-color: #eff1f3;\n    border-top: 1px solid #e9ecef; }\n  .note-editor.note-frame .note-editable {\n    border: none; }\n\n.note-status-output {\n  display: none; }\n\n.note-editable p:last-of-type {\n  margin-bottom: 0; }\n\n.note-popover .popover-content .note-color .dropdown-menu,\n.card-header.note-toolbar .note-color .dropdown-menu {\n  min-width: 344px; }\n\n.note-toolbar {\n  z-index: 1; }\n\n@font-face {\n  font-family: 'dropify';\n  src: url(\"../fonts/dropify.eot\");\n  src: url(\"../fonts/dropify.eot#iefix\") format(\"embedded-opentype\"), url(\"../fonts/dropify.woff\") format(\"woff\"), url(\"../fonts/dropify.ttf\") format(\"truetype\"), url(\"../fonts/dropify.svg#dropify\") format(\"svg\");\n  font-weight: normal;\n  font-style: normal; }\n\n.dropify-wrapper {\n  border: 2px dashed #efefef;\n  background-color: transparent;\n  border-radius: 6px;\n  color: #ced4da; }\n  .dropify-wrapper:hover {\n    background-image: linear-gradient(-45deg, #f5f6f8 25%, transparent 25%, transparent 50%, #f5f6f8 50%, #f5f6f8 75%, transparent 75%, transparent); }\n  .dropify-wrapper .dropify-preview {\n    background-color: #f5f6f8; }\n\n.editable-clear-x {\n  background: url(\"../images/plugins/clear.png\") center center no-repeat; }\n\n.editableform-loading {\n  background: url(\"../images/plugins/loading.gif\") center center no-repeat; }\n\n.editable-checklist label {\n  display: block; }\n\n.editable-clear-x {\n  background: url(\"../images/plugins/clear.png\") center center no-repeat; }\n\n.editableform-loading {\n  background: url(\"../images/plugins/loading.gif\") center center no-repeat; }\n\n.editable-checklist label {\n  display: block; }\n\n.dataTables_wrapper.container-fluid {\n  padding: 0; }\n\ntable.dataTable {\n  border-collapse: collapse !important;\n  margin-bottom: 15px !important; }\n  table.dataTable tbody > tr.selected, table.dataTable tbody > tr > .selected {\n    background-color: #64b0f2; }\n    table.dataTable tbody > tr.selected td, table.dataTable tbody > tr > .selected td {\n      border-color: #64b0f2; }\n  table.dataTable tbody td:focus {\n    outline: none !important; }\n  table.dataTable tbody th.focus, table.dataTable tbody td.focus {\n    outline: 2px solid #64b0f2 !important;\n    outline-offset: -1px;\n    color: #64b0f2;\n    background-color: rgba(100, 176, 242, 0.15); }\n\n.dataTables_info {\n  font-weight: 500; }\n\ntable.dataTable.dtr-inline.collapsed > tbody > tr[role=row] > td:first-child:before, table.dataTable.dtr-inline.collapsed > tbody > tr[role=row] > th:first-child:before {\n  box-shadow: 0 1rem 3rem rgba(0, 0, 0, 0.175);\n  background-color: #1bb99a;\n  top: 0.75rem; }\n\ntable.dataTable.dtr-inline.collapsed > tbody > tr.parent > td:first-child:before, table.dataTable.dtr-inline.collapsed > tbody > tr.parent > th:first-child:before {\n  background-color: #ff5d48;\n  top: 0.75rem; }\n\n.dt-buttons, .dataTables_length {\n  float: left; }\n\ndiv.dt-button-info {\n  background-color: #64b0f2;\n  border: none;\n  color: #fff;\n  box-shadow: none;\n  border-radius: 3px;\n  text-align: center;\n  z-index: 21; }\n  div.dt-button-info h2 {\n    border-bottom: none;\n    background-color: rgba(255, 255, 255, 0.2);\n    color: #fff; }\n\n@media (max-width: 767.98px) {\n  li.paginate_button.previous, li.paginate_button.next {\n    display: inline-block;\n    font-size: 1.5rem; }\n  li.paginate_button {\n    display: none; }\n  .dataTables_paginate ul {\n    text-align: center;\n    display: block;\n    margin: 1rem 0 0 !important; }\n  div.dt-buttons {\n    display: inline-table;\n    margin-bottom: 1rem; } }\n\n.activate-select .sorting_1 {\n  background-color: #f8f9fa; }\n\n/* ColVid Tables */\ndiv.ColVis {\n  float: none;\n  margin-right: 30px; }\n\nbutton.ColVis_Button, button.ColVis_Button:hover {\n  float: none !important;\n  border-radius: 3px;\n  outline: none !important;\n  box-shadow: none !important;\n  color: #fff !important;\n  background: #64b0f2 !important;\n  border: 1px solid #64b0f2 !important; }\n\ndiv.ColVis_collectionBackground {\n  background-color: transparent; }\n\nul.ColVis_collection {\n  padding: 10px 0 0 0;\n  background-color: #fff;\n  box-shadow: 0px 0px 35px 0px rgba(73, 80, 87, 0.15);\n  border: none; }\n  ul.ColVis_collection li {\n    background: transparent !important;\n    padding: 3px 10px !important;\n    border: none !important;\n    box-shadow: none !important; }\n\n#datatable-colvid_info {\n  float: left; }\n\n.responsive-table-plugin .dropdown-menu li.checkbox-row {\n  padding: 7px 15px;\n  color: #6c757d; }\n  .responsive-table-plugin .dropdown-menu li.checkbox-row:hover, .responsive-table-plugin .dropdown-menu li.checkbox-row:focus {\n    background-color: #f5f6f8;\n    color: #6c757d; }\n\n.responsive-table-plugin .table-responsive {\n  border: none;\n  margin-bottom: 0; }\n\n.responsive-table-plugin .btn-toolbar {\n  display: block; }\n\n.responsive-table-plugin tbody th {\n  font-size: 14px;\n  font-weight: normal; }\n\n.responsive-table-plugin .checkbox-row {\n  padding-left: 40px; }\n  .responsive-table-plugin .checkbox-row label {\n    display: inline-block;\n    padding-left: 5px;\n    position: relative;\n    margin-bottom: 0; }\n    .responsive-table-plugin .checkbox-row label::before {\n      background-color: #f5f6f8;\n      border-radius: 3px;\n      border: 1px solid #ced4da;\n      content: \"\";\n      display: inline-block;\n      height: 17px;\n      left: 0;\n      margin-left: -20px;\n      position: absolute;\n      transition: 0.3s ease-in-out;\n      width: 17px;\n      outline: none; }\n    .responsive-table-plugin .checkbox-row label::after {\n      color: #ced4da;\n      display: inline-block;\n      font-size: 11px;\n      height: 16px;\n      left: 0;\n      margin-left: -20px;\n      padding-left: 3px;\n      padding-top: 1px;\n      position: absolute;\n      top: -1px;\n      width: 16px; }\n  .responsive-table-plugin .checkbox-row input[type=\"checkbox\"] {\n    cursor: pointer;\n    opacity: 0;\n    z-index: 1;\n    outline: none; }\n    .responsive-table-plugin .checkbox-row input[type=\"checkbox\"]:disabled + label {\n      opacity: 0.65; }\n  .responsive-table-plugin .checkbox-row input[type=\"checkbox\"]:focus + label::before {\n    outline-offset: -2px;\n    outline: none; }\n  .responsive-table-plugin .checkbox-row input[type=\"checkbox\"]:checked + label::after {\n    content: \"\\f00c\";\n    font-family: 'Font Awesome 5 Free';\n    font-weight: 900; }\n  .responsive-table-plugin .checkbox-row input[type=\"checkbox\"]:disabled + label::before {\n    background-color: #efefef;\n    cursor: not-allowed; }\n  .responsive-table-plugin .checkbox-row input[type=\"checkbox\"]:checked + label::before {\n    background-color: #f5f6f8;\n    border-color: #64b0f2; }\n  .responsive-table-plugin .checkbox-row input[type=\"checkbox\"]:checked + label::after {\n    color: #64b0f2; }\n\n.responsive-table-plugin table.focus-on tbody tr.focused th,\n.responsive-table-plugin table.focus-on tbody tr.focused td,\n.responsive-table-plugin .sticky-table-header {\n  background: #64b0f2;\n  border-color: #64b0f2;\n  color: #fff; }\n  .responsive-table-plugin table.focus-on tbody tr.focused th table,\n  .responsive-table-plugin table.focus-on tbody tr.focused td table,\n  .responsive-table-plugin .sticky-table-header table {\n    color: #fff; }\n\n.responsive-table-plugin .fixed-solution .sticky-table-header {\n  top: 70px !important; }\n\n.responsive-table-plugin .btn-default {\n  background-color: #f5f6f8;\n  color: #343a40;\n  border: 1px solid #e9ecef; }\n  .responsive-table-plugin .btn-default.btn-primary {\n    background-color: #64b0f2;\n    border-color: #64b0f2;\n    color: #fff;\n    box-shadow: 0 0 0 2px rgba(100, 176, 242, 0.5); }\n\n.responsive-table-plugin .btn-group.pull-right {\n  float: right; }\n  .responsive-table-plugin .btn-group.pull-right .dropdown-menu {\n    left: auto;\n    right: 0; }\n\n.tablesaw thead {\n  background: #f5f6f8;\n  background-image: none;\n  border: none; }\n  .tablesaw thead th {\n    text-shadow: none; }\n  .tablesaw thead tr:first-child th {\n    border: none;\n    font-weight: 500;\n    font-family: \"Roboto\", sans-serif; }\n\n.tablesaw td {\n  border-top: 1px solid #f5f6f8 !important; }\n\n.tablesaw td, .tablesaw tbody th {\n  font-size: inherit;\n  line-height: inherit;\n  padding: 10px !important; }\n\n.tablesaw-stack tbody tr, .tablesaw tbody tr {\n  border-bottom: none; }\n\n.tablesaw-bar .btn-select .btn-small:after, .tablesaw-bar .btn-select .btn-micro:after {\n  font-size: 8px;\n  padding-right: 10px; }\n\n.tablesaw-swipe .tablesaw-cell-persist {\n  box-shadow: none;\n  border-color: #f8f9fa; }\n\n.tablesaw-swipe .tablesaw-swipe-cellpersist {\n  border-right: 2px solid #f5f6f8; }\n\n.tablesaw-bar-section label {\n  color: #6c757d; }\n\n.tablesaw-enhanced .tablesaw-bar .btn {\n  text-shadow: none;\n  background-image: none;\n  text-transform: none;\n  border: 1px solid #efefef;\n  padding: 3px 10px;\n  color: #343a40; }\n  .tablesaw-enhanced .tablesaw-bar .btn:after {\n    display: none; }\n  .tablesaw-enhanced .tablesaw-bar .btn.btn-select:hover {\n    background: #fff; }\n  .tablesaw-enhanced .tablesaw-bar .btn:hover, .tablesaw-enhanced .tablesaw-bar .btn:focus, .tablesaw-enhanced .tablesaw-bar .btn:active {\n    color: #64b0f2 !important;\n    background-color: #f8f9fa;\n    outline: none !important;\n    box-shadow: none !important;\n    background-image: none; }\n\n.tablesaw-columntoggle-popup .btn-group {\n  display: block; }\n\n.tablesaw-sortable-btn {\n  cursor: pointer; }\n\n.tablesaw-swipe-cellpersist {\n  width: auto !important; }\n\n.flotTip {\n  padding: 8px 12px;\n  background-color: rgba(52, 58, 64, 0.9);\n  z-index: 100;\n  color: #f8f9fa;\n  opacity: 1;\n  border-radius: 3px; }\n\n.legend tr {\n  height: 30px;\n  font-family: \"Roboto\", sans-serif; }\n\n.legendLabel {\n  padding-left: 5px;\n  line-height: 10px;\n  padding-right: 20px;\n  font-size: 13px;\n  font-weight: 500;\n  color: #6c757d; }\n\n.legendColorBox div {\n  border-radius: 3px; }\n  .legendColorBox div div {\n    border-radius: 3px; }\n\n@media (max-width: 767.98px) {\n  .legendLabel {\n    display: none; } }\n\n.morris-chart text {\n  font-family: \"Roboto\", sans-serif !important;\n  font-weight: 600 !important;\n  fill: #adb5bd; }\n\n.morris-hover {\n  position: absolute;\n  z-index: 10; }\n  .morris-hover.morris-default-style {\n    font-size: 12px;\n    text-align: center;\n    border-radius: 5px;\n    padding: 10px 12px;\n    background: rgba(248, 249, 250, 0.8);\n    color: #343a40;\n    border: 2px solid #e9ecef;\n    font-family: -apple-system, BlinkMacSystemFont, \"Segoe UI\", Roboto, \"Helvetica Neue\", Arial, \"Noto Sans\", sans-serif, \"Apple Color Emoji\", \"Segoe UI Emoji\", \"Segoe UI Symbol\", \"Noto Color Emoji\"; }\n    .morris-hover.morris-default-style .morris-hover-row-label {\n      font-weight: bold;\n      margin: 0.25em 0;\n      font-family: \"Roboto\", sans-serif; }\n    .morris-hover.morris-default-style .morris-hover-point {\n      white-space: nowrap;\n      margin: 0.1em 0;\n      color: #fff; }\n\n.ct-golden-section:before {\n  float: none; }\n\n.ct-chart {\n  max-height: 300px; }\n  .ct-chart .ct-label {\n    fill: #adb5bd;\n    color: #adb5bd;\n    font-size: 12px;\n    line-height: 1; }\n\n.ct-chart.simple-pie-chart-chartist .ct-label {\n  color: #fff;\n  fill: #fff;\n  font-size: 16px; }\n\n.ct-grid {\n  stroke: rgba(52, 58, 64, 0.1); }\n\n.ct-chart .ct-series.ct-series-a .ct-bar,\n.ct-chart .ct-series.ct-series-a .ct-line,\n.ct-chart .ct-series.ct-series-a .ct-point,\n.ct-chart .ct-series.ct-series-a .ct-slice-donut {\n  stroke: #64b0f2; }\n\n.ct-chart .ct-series.ct-series-b .ct-bar,\n.ct-chart .ct-series.ct-series-b .ct-line,\n.ct-chart .ct-series.ct-series-b .ct-point,\n.ct-chart .ct-series.ct-series-b .ct-slice-donut {\n  stroke: #1bb99a; }\n\n.ct-chart .ct-series.ct-series-c .ct-bar,\n.ct-chart .ct-series.ct-series-c .ct-line,\n.ct-chart .ct-series.ct-series-c .ct-point,\n.ct-chart .ct-series.ct-series-c .ct-slice-donut {\n  stroke: #ffff48; }\n\n.ct-chart .ct-series.ct-series-d .ct-bar,\n.ct-chart .ct-series.ct-series-d .ct-line,\n.ct-chart .ct-series.ct-series-d .ct-point,\n.ct-chart .ct-series.ct-series-d .ct-slice-donut {\n  stroke: #ff7aa3; }\n\n.ct-chart .ct-series.ct-series-e .ct-bar,\n.ct-chart .ct-series.ct-series-e .ct-line,\n.ct-chart .ct-series.ct-series-e .ct-point,\n.ct-chart .ct-series.ct-series-e .ct-slice-donut {\n  stroke: #343a40; }\n\n.ct-chart .ct-series.ct-series-f .ct-bar,\n.ct-chart .ct-series.ct-series-f .ct-line,\n.ct-chart .ct-series.ct-series-f .ct-point,\n.ct-chart .ct-series.ct-series-f .ct-slice-donut {\n  stroke: #3db9dc; }\n\n.ct-chart .ct-series.ct-series-g .ct-bar,\n.ct-chart .ct-series.ct-series-g .ct-line,\n.ct-chart .ct-series.ct-series-g .ct-point,\n.ct-chart .ct-series.ct-series-g .ct-slice-donut {\n  stroke: #ff5d48; }\n\n.ct-series-a .ct-area,\n.ct-series-a .ct-slice-pie {\n  fill: #64b0f2; }\n\n.ct-series-b .ct-area,\n.ct-series-b .ct-slice-pie {\n  fill: #1bb99a; }\n\n.ct-series-c .ct-area,\n.ct-series-c .ct-slice-pie {\n  fill: #ffff48; }\n\n.ct-series-d .ct-area,\n.ct-series-d .ct-slice-pie {\n  fill: #1bb99a; }\n\n.ct-area {\n  fill-opacity: .33; }\n\n.chartist-tooltip {\n  position: absolute;\n  display: inline-block;\n  opacity: 0;\n  min-width: 10px;\n  padding: 2px 10px;\n  border-radius: 3px;\n  background: #343a40;\n  color: #efefef;\n  text-align: center;\n  pointer-events: none;\n  z-index: 1;\n  transition: opacity .2s linear; }\n  .chartist-tooltip.tooltip-show {\n    opacity: 1; }\n\n.chartjs-chart {\n  margin: auto;\n  position: relative;\n  width: 100%; }\n\n.chartjs-chart-example {\n  height: 300px; }\n\n.c3-tooltip {\n  box-shadow: 0 1rem 3rem rgba(0, 0, 0, 0.175);\n  opacity: 1; }\n  .c3-tooltip td {\n    border-left: none;\n    font-family: \"Roboto\", sans-serif; }\n    .c3-tooltip td > span {\n      background: #343a40; }\n  .c3-tooltip tr {\n    border: none !important; }\n  .c3-tooltip th {\n    background-color: #343a40;\n    color: #f8f9fa; }\n\n.c3-chart-arcs-title {\n  font-size: 18px;\n  font-weight: 600; }\n\n.c3 text {\n  font-family: -apple-system, BlinkMacSystemFont, \"Segoe UI\", Roboto, \"Helvetica Neue\", Arial, \"Noto Sans\", sans-serif, \"Apple Color Emoji\", \"Segoe UI Emoji\", \"Segoe UI Symbol\", \"Noto Color Emoji\";\n  fill: #6c757d; }\n\n.c3 line, .c3 path {\n  stroke: #ced4da; }\n\n.c3-chart-arc.c3-target g path {\n  stroke: #fff; }\n\n.jqstooltip {\n  box-sizing: content-box;\n  width: auto !important;\n  height: auto !important;\n  background-color: #343a40 !important;\n  box-shadow: 0 1rem 3rem rgba(0, 0, 0, 0.175);\n  padding: 5px 10px !important;\n  border-radius: 3px;\n  border-color: #212529 !important; }\n\n.jqsfield {\n  color: #e9ecef !important;\n  font-size: 12px !important;\n  line-height: 18px !important;\n  font-family: -apple-system, BlinkMacSystemFont, \"Segoe UI\", Roboto, \"Helvetica Neue\", Arial, \"Noto Sans\", sans-serif, \"Apple Color Emoji\", \"Segoe UI Emoji\", \"Segoe UI Symbol\", \"Noto Color Emoji\" !important;\n  font-weight: 500 !important; }\n\n.button-list {\n  margin-left: -8px;\n  margin-bottom: -12px; }\n  .button-list .btn {\n    margin-bottom: 12px;\n    margin-left: 8px; }\n\n.icons-list-demo div {\n  cursor: pointer;\n  line-height: 45px;\n  white-space: nowrap;\n  text-overflow: ellipsis;\n  display: block;\n  overflow: hidden; }\n  .icons-list-demo div p {\n    margin-bottom: 0;\n    line-height: inherit; }\n\n.icons-list-demo i {\n  text-align: center;\n  vertical-align: middle;\n  font-size: 22px;\n  width: 50px;\n  height: 50px;\n  line-height: 50px;\n  margin-right: 12px;\n  color: #6c757d;\n  border: 1px solid #efefef;\n  border-radius: 3px;\n  display: inline-block;\n  transition: all 0.2s; }\n\n.icons-list-demo .col-lg-4 {\n  background-clip: padding-box;\n  margin-top: 10px; }\n  .icons-list-demo .col-lg-4:hover i {\n    color: #64b0f2; }\n\n.grid-structure .grid-container {\n  background-color: #f8f9fa;\n  margin-top: 10px;\n  font-size: .8rem;\n  font-weight: 500;\n  padding: 10px 20px; }\n\n.demos-show-btn {\n  position: fixed;\n  top: 50%;\n  right: 0;\n  writing-mode: vertical-rl;\n  font-weight: 600;\n  background-color: #ff5d48;\n  color: #fff !important;\n  line-height: 36px;\n  padding: 15px 3px;\n  border-radius: 6px 0 0 6px;\n  transform: translateY(-50%);\n  text-transform: uppercase; }\n\n@media (max-width: 600px) {\n  .demos-show-btn {\n    display: none; } }\n\n.authentication-bg {\n  background-color: #64b0f2; }\n\n.account-card-box {\n  background-color: #fff;\n  padding: 7px;\n  border-radius: 8px; }\n  .account-card-box .card {\n    border: 4px solid #64b0f2; }\n\n.card-pricing {\n  box-shadow: 0 0.75rem 6rem rgba(73, 80, 87, 0.03); }\n  .card-pricing .card-pricing-features li {\n    padding: 15px; }\n  .card-pricing .card-price {\n    font-size: 48px;\n    font-weight: 300; }\n  .card-pricing.active {\n    margin: 0 -24px;\n    box-shadow: 0px 0px 35px 0px rgba(73, 80, 87, 0.15); }\n\n.maintenance-icon {\n  height: 220px;\n  width: 220px;\n  margin: 0 auto; }\n\n.line1 {\n  opacity: 0;\n  animation: fadeInLeft both 1s 0.4s, coding1 ease 6s 4s infinite; }\n\n.line2 {\n  opacity: 0;\n  animation: fadeInLeft both 1s 0.6s, coding2 ease 6s 4s infinite; }\n\n.line3 {\n  opacity: 0;\n  animation: fadeInLeft both 1s 0.8s, coding3 ease 6s 4s infinite; }\n\n.line4 {\n  opacity: 0;\n  animation: fadeInLeft both 1s 1.0s, coding4 ease 6s 4s infinite; }\n\n.line5 {\n  opacity: 0;\n  animation: fadeInLeft both 1s 1.2s, coding5 ease 6s 4s infinite; }\n\n.line6 {\n  opacity: 0;\n  animation: fadeInLeft both 1s 1.4s, coding6 ease 6s 4s infinite; }\n\n.line7 {\n  opacity: 0;\n  animation: fadeInLeft both 1s 1.6s, coding6 ease 6s 4s infinite; }\n\n@keyframes coding1 {\n  0% {\n    transform: translate(0, 0);\n    opacity: 1; }\n  14% {\n    transform: translate(0, -10px);\n    opacity: 0; }\n  15% {\n    transform: translate(0, 45px); }\n  30% {\n    transform: translate(0, 40px);\n    opacity: 1; }\n  45% {\n    transform: translate(0, 30px); }\n  60% {\n    transform: translate(0, 20px); }\n  75% {\n    transform: translate(0, 10px); }\n  90% {\n    transform: translate(0, 5px); }\n  100% {\n    transform: translate(0, 0);\n    opacity: 1; } }\n\n@keyframes coding2 {\n  0% {\n    transform: translate(0, 0);\n    opacity: 1; }\n  15% {\n    transform: translate(0, -5px);\n    opacity: 1; }\n  29% {\n    transform: translate(0, -10px);\n    opacity: 0; }\n  30% {\n    transform: translate(0, 40px); }\n  45% {\n    transform: translate(0, 30px);\n    opacity: 1; }\n  60% {\n    transform: translate(0, 20px); }\n  75% {\n    transform: translate(0, 10px); }\n  90% {\n    transform: translate(0, 5px); }\n  100% {\n    transform: translate(0, 0);\n    opacity: 1; } }\n\n@keyframes coding3 {\n  0% {\n    transform: translate(0, 0);\n    opacity: 1; }\n  15% {\n    transform: translate(0, -5px); }\n  30% {\n    transform: translate(0, -10px);\n    opacity: 1; }\n  44% {\n    transform: translate(0, -20px);\n    opacity: 0; }\n  45% {\n    transform: translate(0, 30px); }\n  60% {\n    transform: translate(0, 20px);\n    opacity: 1; }\n  75% {\n    transform: translate(0, 10px); }\n  90% {\n    transform: translate(0, 5px); }\n  100% {\n    transform: translate(0, 0);\n    opacity: 1; } }\n\n@keyframes coding4 {\n  0% {\n    transform: translate(0, 0);\n    opacity: 1; }\n  15% {\n    transform: translate(0, -5px); }\n  30% {\n    transform: translate(0, -10px); }\n  45% {\n    transform: translate(0, -20px);\n    opacity: 1; }\n  59% {\n    transform: translate(0, -30px);\n    opacity: 0; }\n  60% {\n    transform: translate(0, 20px); }\n  75% {\n    transform: translate(0, 10px);\n    opacity: 1; }\n  90% {\n    transform: translate(0, 5px); }\n  100% {\n    transform: translate(0, 0);\n    opacity: 1; } }\n\n@keyframes coding5 {\n  0% {\n    transform: translate(0, 0);\n    opacity: 1; }\n  15% {\n    transform: translate(0, -5px); }\n  30% {\n    transform: translate(0, -10px); }\n  45% {\n    transform: translate(0, -20px); }\n  60% {\n    transform: translate(0, -30px);\n    opacity: 1; }\n  74% {\n    transform: translate(0, -40px);\n    opacity: 0; }\n  75% {\n    transform: translate(0, 10px); }\n  90% {\n    transform: translate(0, 5px);\n    opacity: 1; }\n  100% {\n    transform: translate(0, 0);\n    opacity: 1; } }\n\n@keyframes coding6 {\n  0% {\n    transform: translate(0, 0);\n    opacity: 1; }\n  15% {\n    transform: translate(0, -5px); }\n  30% {\n    transform: translate(0, -10px); }\n  45% {\n    transform: translate(0, -20px); }\n  60% {\n    transform: translate(0, -30px); }\n  75% {\n    transform: translate(0, -40px);\n    opacity: 1; }\n  89% {\n    transform: translate(0, -50px);\n    opacity: 0; }\n  90% {\n    transform: translate(0, 10px); }\n  100% {\n    transform: translate(0, 0);\n    opacity: 1; } }\n\n.diamond {\n  margin: 50px auto;\n  height: 90px;\n  width: 120px; }\n  .diamond:after {\n    content: \"\";\n    position: absolute;\n    height: 14px;\n    width: 44px;\n    background: rgba(52, 58, 64, 0.1);\n    border-radius: 50%;\n    margin-top: 0;\n    margin-left: 38px;\n    z-index: 11; }\n  .diamond .top {\n    height: 30px;\n    border-left: 27px solid transparent;\n    border-right: 27px solid transparent;\n    border-bottom: 24px solid #1ecba9; }\n    .diamond .top:after {\n      content: \"\";\n      position: absolute;\n      height: 24px;\n      width: 32px;\n      margin-top: 6px;\n      margin-left: 47px;\n      background: #1bb99a;\n      -ms-transform: skew(30deg, 20deg);\n      -webkit-transform: skew(30deg, 20deg);\n      transform: skew(48deg); }\n    .diamond .top:before {\n      content: \"\";\n      position: absolute;\n      height: 24px;\n      width: 32px;\n      margin-top: 7px;\n      margin-left: -13px;\n      background: #1bb99a;\n      transform: skew(-48deg); }\n  .diamond .bot {\n    height: 60px;\n    border-left: 60px solid transparent;\n    border-right: 60px solid transparent;\n    border-top: 60px solid #1ecba9; }\n    .diamond .bot:before {\n      content: \"\";\n      position: absolute;\n      height: 60px;\n      margin-top: -60px;\n      margin-left: -27px;\n      border-left: 27px solid transparent;\n      border-right: 26px solid transparent;\n      border-top: 60px solid #1bb99a; }\n\n.timeline {\n  border-collapse: collapse;\n  border-spacing: 0;\n  display: table;\n  margin-bottom: 50px;\n  position: relative;\n  table-layout: fixed;\n  width: 100%; }\n  .timeline .time-show {\n    margin-bottom: 30px;\n    margin-right: -75px;\n    margin-top: 30px;\n    position: relative;\n    text-align: right; }\n    .timeline .time-show a {\n      color: #fff; }\n  .timeline:before {\n    background-color: rgba(173, 181, 189, 0.3);\n    bottom: 0px;\n    content: \"\";\n    left: 50%;\n    position: absolute;\n    top: 30px;\n    width: 2px;\n    z-index: 0; }\n  .timeline .timeline-icon {\n    background: #6c757d;\n    border-radius: 50%;\n    color: #fff;\n    display: block;\n    height: 21px;\n    left: -54px;\n    margin-top: -11px;\n    position: absolute;\n    text-align: center;\n    top: 50%;\n    width: 21px; }\n    .timeline .timeline-icon i {\n      color: #e9ecef; }\n  .timeline .time-icon:before {\n    font-size: 16px;\n    margin-top: 5px; }\n\nh3.timeline-title {\n  color: #6c757d;\n  font-size: 20px;\n  font-weight: 400;\n  margin: 0 0 5px;\n  text-transform: uppercase; }\n\n.timeline-item {\n  display: table-row; }\n  .timeline-item:before {\n    content: \"\";\n    display: block;\n    width: 50%; }\n  .timeline-item .timeline-desk .arrow {\n    border-bottom: 8px solid transparent;\n    border-right: 8px solid #fff !important;\n    border-top: 8px solid transparent;\n    display: block;\n    height: 0;\n    left: -7px;\n    margin-top: -10px;\n    position: absolute;\n    top: 50%;\n    width: 0; }\n\n.timeline-item.alt:after {\n  content: \"\";\n  display: block;\n  width: 50%; }\n\n.timeline-item.alt .timeline-desk .arrow-alt {\n  border-bottom: 8px solid transparent;\n  border-left: 8px solid #fff !important;\n  border-top: 8px solid transparent;\n  display: block;\n  height: 0;\n  left: auto;\n  margin-top: -10px;\n  position: absolute;\n  right: -7px;\n  top: 50%;\n  width: 0; }\n\n.timeline-item.alt .timeline-desk .album {\n  float: right;\n  margin-top: 20px; }\n  .timeline-item.alt .timeline-desk .album a {\n    float: right;\n    margin-left: 5px; }\n\n.timeline-item.alt .timeline-icon {\n  left: auto;\n  right: -58px; }\n\n.timeline-item.alt:before {\n  display: none; }\n\n.timeline-item.alt .panel {\n  margin-left: 0;\n  margin-right: 45px; }\n  .timeline-item.alt .panel .panel-body p + p {\n    margin-top: 10px !important; }\n\n.timeline-item.alt h4 {\n  text-align: right; }\n\n.timeline-item.alt p {\n  text-align: right; }\n\n.timeline-item.alt .timeline-date {\n  text-align: right; }\n\n.timeline-desk {\n  display: table-cell;\n  vertical-align: top;\n  width: 50%; }\n  .timeline-desk h4 {\n    font-size: 16px;\n    font-weight: 300;\n    margin: 0; }\n  .timeline-desk .panel {\n    background: #fff;\n    display: block;\n    margin-bottom: 5px;\n    margin-left: 45px;\n    position: relative;\n    text-align: left;\n    padding: 20px;\n    border-radius: 7px;\n    box-shadow: 0 0.75rem 6rem rgba(73, 80, 87, 0.03); }\n  .timeline-desk h5 span {\n    color: #e9ecef;\n    display: block;\n    font-size: 12px;\n    margin-bottom: 4px; }\n  .timeline-desk p {\n    color: #6c757d;\n    font-size: 14px;\n    margin-bottom: 0; }\n  .timeline-desk .album {\n    margin-top: 12px; }\n    .timeline-desk .album a {\n      float: left;\n      margin-right: 5px; }\n    .timeline-desk .album img {\n      height: 36px;\n      width: auto;\n      border-radius: 3px; }\n  .timeline-desk .notification {\n    background: none repeat scroll 0 0 #fff;\n    margin-top: 20px;\n    padding: 8px; }\n\n.text-error {\n  color: #fff;\n  font-size: 98px;\n  line-height: 150px; }\n  .text-error.shadow-text {\n    text-shadow: rgba(255, 255, 255, 0.3) 5px 1px, rgba(255, 255, 255, 0.2) 12px 3px, rgba(255, 255, 255, 0.1) 6px 4px; }\n\n.portfolioFilter a {\n  transition: all 0.3s ease-out;\n  color: #343a40;\n  border-radius: 3px;\n  padding: 5px 10px;\n  display: inline-block;\n  font-size: 13px;\n  font-weight: 500;\n  text-transform: uppercase; }\n  .portfolioFilter a:hover {\n    color: #1bb99a; }\n\n.portfolioFilter a.current {\n  background-color: #1bb99a;\n  color: #fff; }\n\n.gallery-box {\n  background-color: #fff;\n  margin-top: 24px;\n  border-radius: 4px;\n  overflow: hidden; }\n  .gallery-box a {\n    display: block;\n    background-color: #060708;\n    overflow: hidden; }\n  .gallery-box:hover .thumb-img {\n    position: relative;\n    transform: scale(1.05);\n    opacity: 0.7; }\n\n.thumb-img {\n  overflow: hidden;\n  width: 100%;\n  transition: all 0.2s ease-out; }\n\n.counter-number {\n  font-size: 60px;\n  font-weight: 600;\n  text-align: center;\n  color: #343a40; }\n  .counter-number span {\n    font-size: 16px;\n    font-weight: 400;\n    display: block;\n    text-transform: uppercase;\n    padding-top: 5px;\n    color: #1bb99a; }\n\n.coming-box {\n  float: left;\n  width: 25%; }\n\nhtml {\n  direction: rtl; }\n\nbody {\n  text-align: right; }\n\n.dropdown-menu {\n  text-align: right; }\n  .dropdown-menu.show {\n    text-align: right;\n    left: auto !important;\n    right: 0;\n    bottom: auto; }\n  .dropdown-menu.dropdown-megamenu {\n    left: 20px !important;\n    right: 20px !important; }\n    .dropdown-menu.dropdown-megamenu .megamenu-img {\n      transform: scaleX(-1); }\n\n.dropdown-menu-right {\n  right: auto !important;\n  left: 0 !important; }\n  .dropdown-menu-right.show {\n    left: 0 !important; }\n\n.dropdown-example {\n  clear: right;\n  float: right; }\n\n.dropright .dropdown-menu.show, .dropleft .dropdown-menu.show {\n  right: auto; }\n\nul {\n  padding-right: 0; }\n\n.btn-label {\n  margin: -.55rem -.9rem -.55rem .9rem; }\n\n.btn-label-right {\n  margin: -0.45rem 0.9rem -0.45rem -0.9rem; }\n\n.btn-group,\n.btn-group-vertical {\n  direction: ltr; }\n\n.pagination .page-item:first-child .page-link {\n  margin-right: 0;\n  border-top-left-radius: 0px;\n  border-bottom-left-radius: 0px;\n  border-top-right-radius: 0.25rem;\n  border-bottom-right-radius: 0.25rem; }\n\n.pagination .page-item:last-child .page-link {\n  border-top-right-radius: 0px;\n  border-bottom-right-radius: 0px;\n  border-top-left-radius: 0.25rem;\n  border-bottom-left-radius: 0.25rem; }\n\n.blockquote-reverse {\n  text-align: left !important; }\n\ndd {\n  margin-right: 0; }\n\n.modal-header .close {\n  margin: -1rem auto -1rem -1rem; }\n\n.modal-footer > :not(:first-child) {\n  margin-right: .25rem;\n  margin-left: 0; }\n\n.modal-footer > :not(:last-child) {\n  margin-left: .25rem;\n  margin-right: 0; }\n\n.alert-dismissible {\n  padding-left: 3.85rem;\n  padding-right: 1.25rem; }\n  .alert-dismissible .close {\n    left: 0;\n    right: auto; }\n\n.breadcrumb-item + .breadcrumb-item {\n  padding-right: 0.5rem;\n  padding-left: 0px; }\n  .breadcrumb-item + .breadcrumb-item::before {\n    padding-left: 0.5rem;\n    padding-right: 0px; }\n\n.form-check-inline {\n  margin-left: .75rem;\n  margin-right: 0; }\n\n.custom-control-inline {\n  margin-left: 1rem;\n  margin-right: 0; }\n\n.custom-control {\n  padding-right: 1.5rem;\n  padding-left: 0; }\n\n.custom-control-label::before {\n  left: auto;\n  right: -1.5rem; }\n\n.custom-control-label::after {\n  left: auto;\n  right: -1.5rem; }\n\n.custom-switch {\n  padding-right: 2.25rem;\n  padding-left: 0; }\n  .custom-switch .custom-control-label::before {\n    right: -2.25rem;\n    left: auto; }\n  .custom-switch .custom-control-label::after {\n    right: calc(-2.25rem + 2px);\n    left: auto; }\n  .custom-switch .custom-control-input:checked ~ .custom-control-label::after {\n    transform: translateX(-0.75rem); }\n\n.custom-file-label::after {\n  right: auto;\n  left: 0;\n  border-right: inherit; }\n\n.input-group-prepend {\n  margin-left: -1px;\n  margin-right: 0; }\n\n.input-group-append {\n  margin-right: -1px;\n  margin-left: 0; }\n\n.input-group > .input-group-prepend > .btn,\n.input-group > .input-group-prepend > .input-group-text,\n.input-group > .input-group-append:not(:last-child) > .btn,\n.input-group > .input-group-append:not(:last-child) > .input-group-text,\n.input-group > .input-group-append:last-child > .btn:not(:last-child):not(.dropdown-toggle),\n.input-group > .input-group-append:last-child > .input-group-text:not(:last-child),\n.input-group > .custom-select:not(:last-child),\n.input-group > .form-control:not(:last-child) {\n  border-top-right-radius: 0.25rem;\n  border-bottom-right-radius: 0.25rem;\n  border-top-left-radius: 0;\n  border-bottom-left-radius: 0; }\n\n.input-group > .input-group-append > .btn,\n.input-group > .input-group-append > .input-group-text,\n.input-group > .input-group-prepend:not(:first-child) > .btn,\n.input-group > .input-group-prepend:not(:first-child) > .input-group-text,\n.input-group > .input-group-prepend:first-child > .btn:not(:first-child),\n.input-group > .input-group-prepend:first-child > .input-group-text:not(:first-child),\n.input-group > .custom-select:not(:first-child),\n.input-group > .form-control:not(:first-child) {\n  border-top-left-radius: 0.25rem;\n  border-bottom-left-radius: 0.25rem;\n  border-top-right-radius: 0;\n  border-bottom-right-radius: 0; }\n\n.list-inline-item:not(:last-child) {\n  margin-left: 6px;\n  margin-right: 0px; }\n\n.m-0 {\n  margin: 0 !important; }\n\n.mt-0,\n.my-0 {\n  margin-top: 0 !important; }\n\n.mr-0 {\n  margin-left: 0 !important;\n  margin-right: 0 !important; }\n\n.mb-0,\n.my-0 {\n  margin-bottom: 0 !important; }\n\n.ml-0 {\n  margin-right: 0 !important;\n  margin-left: 0 !important; }\n\n.m-1 {\n  margin: 0.25rem !important; }\n\n.mt-1,\n.my-1 {\n  margin-top: 0.25rem !important; }\n\n.mr-1 {\n  margin-left: 0.25rem !important;\n  margin-right: 0 !important; }\n\n.mb-1,\n.my-1 {\n  margin-bottom: 0.25rem !important; }\n\n.ml-1 {\n  margin-right: 0.25rem !important;\n  margin-left: 0 !important; }\n\n.m-2 {\n  margin: 0.5rem !important; }\n\n.mt-2,\n.my-2 {\n  margin-top: 0.5rem !important; }\n\n.mr-2 {\n  margin-left: 0.5rem !important;\n  margin-right: 0 !important; }\n\n.mb-2,\n.my-2 {\n  margin-bottom: 0.5rem !important; }\n\n.ml-2 {\n  margin-right: 0.5rem !important;\n  margin-left: 0 !important; }\n\n.m-3 {\n  margin: 1rem !important; }\n\n.mt-3,\n.my-3 {\n  margin-top: 1rem !important; }\n\n.mr-3 {\n  margin-left: 1rem !important;\n  margin-right: 0 !important; }\n\n.mb-3,\n.my-3 {\n  margin-bottom: 1rem !important; }\n\n.ml-3 {\n  margin-right: 1rem !important;\n  margin-left: 0 !important; }\n\n.m-4 {\n  margin: 1.5rem !important; }\n\n.mt-4,\n.my-4 {\n  margin-top: 1.5rem !important; }\n\n.mr-4 {\n  margin-left: 1.5rem !important;\n  margin-right: 0 !important; }\n\n.mb-4,\n.my-4 {\n  margin-bottom: 1.5rem !important; }\n\n.ml-4 {\n  margin-right: 1.5rem !important;\n  margin-left: 0 !important; }\n\n.m-5 {\n  margin: 3rem !important; }\n\n.mt-5,\n.my-5 {\n  margin-top: 3rem !important; }\n\n.mr-5 {\n  margin-left: 3rem !important;\n  margin-right: 0 !important; }\n\n.mb-5,\n.my-5 {\n  margin-bottom: 3rem !important; }\n\n.ml-5 {\n  margin-right: 3rem !important;\n  margin-left: 0 !important; }\n\n.p-0 {\n  padding: 0 !important; }\n\n.pt-0,\n.py-0 {\n  padding-top: 0 !important; }\n\n.pr-0 {\n  padding-left: 0 !important;\n  padding-right: 0 !important; }\n\n.pb-0,\n.py-0 {\n  padding-bottom: 0 !important; }\n\n.pl-0 {\n  padding-right: 0 !important;\n  padding-left: 0 !important; }\n\n.p-1 {\n  padding: 0.25rem !important; }\n\n.pt-1,\n.py-1 {\n  padding-top: 0.25rem !important; }\n\n.pr-1 {\n  padding-left: 0.25rem !important;\n  padding-right: 0 !important; }\n\n.pb-1,\n.py-1 {\n  padding-bottom: 0.25rem !important; }\n\n.pl-1 {\n  padding-right: 0.25rem !important;\n  padding-left: 0 !important; }\n\n.p-2 {\n  padding: 0.5rem !important; }\n\n.pt-2,\n.py-2 {\n  padding-top: 0.5rem !important; }\n\n.pr-2 {\n  padding-left: 0.5rem !important;\n  padding-right: 0 !important; }\n\n.pb-2,\n.py-2 {\n  padding-bottom: 0.5rem !important; }\n\n.pl-2 {\n  padding-right: 0.5rem !important;\n  padding-left: 0 !important; }\n\n.p-3 {\n  padding: 1rem !important; }\n\n.pt-3,\n.py-3 {\n  padding-top: 1rem !important; }\n\n.pr-3 {\n  padding-left: 1rem !important;\n  padding-right: 0 !important; }\n\n.pb-3,\n.py-3 {\n  padding-bottom: 1rem !important; }\n\n.pl-3 {\n  padding-right: 1rem !important;\n  padding-left: 0 !important; }\n\n.p-4 {\n  padding: 1.5rem !important; }\n\n.pt-4,\n.py-4 {\n  padding-top: 1.5rem !important; }\n\n.pr-4 {\n  padding-left: 1.5rem !important;\n  padding-right: 0 !important; }\n\n.pb-4,\n.py-4 {\n  padding-bottom: 1.5rem !important; }\n\n.pl-4 {\n  padding-right: 1.5rem !important;\n  padding-left: 0 !important; }\n\n.p-5 {\n  padding: 3rem !important; }\n\n.pt-5,\n.py-5 {\n  padding-top: 3rem !important; }\n\n.pr-5 {\n  padding-left: 3rem !important;\n  padding-right: 0 !important; }\n\n.pb-5,\n.py-5 {\n  padding-bottom: 3rem !important; }\n\n.pl-5 {\n  padding-right: 3rem !important;\n  padding-left: 0 !important; }\n\n.m-n1 {\n  margin: -0.25rem !important; }\n\n.mt-n1,\n.my-n1 {\n  margin-top: -0.25rem !important; }\n\n.mr-n1,\n.mx-n1 {\n  margin-right: -0.25rem !important; }\n\n.mb-n1,\n.my-n1 {\n  margin-bottom: -0.25rem !important; }\n\n.ml-n1,\n.mx-n1 {\n  margin-left: -0.25rem !important; }\n\n.m-n2 {\n  margin: -0.5rem !important; }\n\n.mt-n2,\n.my-n2 {\n  margin-top: -0.5rem !important; }\n\n.mr-n2,\n.mx-n2 {\n  margin-right: -0.5rem !important; }\n\n.mb-n2,\n.my-n2 {\n  margin-bottom: -0.5rem !important; }\n\n.ml-n2,\n.mx-n2 {\n  margin-left: -0.5rem !important; }\n\n.m-n3 {\n  margin: -1rem !important; }\n\n.mt-n3,\n.my-n3 {\n  margin-top: -1rem !important; }\n\n.mr-n3,\n.mx-n3 {\n  margin-right: -1rem !important; }\n\n.mb-n3,\n.my-n3 {\n  margin-bottom: -1rem !important; }\n\n.ml-n3,\n.mx-n3 {\n  margin-left: -1rem !important; }\n\n.m-n4 {\n  margin: -1.5rem !important; }\n\n.mt-n4,\n.my-n4 {\n  margin-top: -1.5rem !important; }\n\n.mr-n4,\n.mx-n4 {\n  margin-right: -1.5rem !important; }\n\n.mb-n4,\n.my-n4 {\n  margin-bottom: -1.5rem !important; }\n\n.ml-n4,\n.mx-n4 {\n  margin-left: -1.5rem !important; }\n\n.m-n5 {\n  margin: -3rem !important; }\n\n.mt-n5,\n.my-n5 {\n  margin-top: -3rem !important; }\n\n.mr-n5,\n.mx-n5 {\n  margin-right: -3rem !important; }\n\n.mb-n5,\n.my-n5 {\n  margin-bottom: -3rem !important; }\n\n.ml-n5,\n.mx-n5 {\n  margin-left: -3rem !important; }\n\n.m-auto {\n  margin: auto !important; }\n\n.mt-auto,\n.my-auto {\n  margin-top: auto !important; }\n\n.mr-auto,\n.mx-auto {\n  margin-left: auto !important;\n  margin-right: inherit !important; }\n\n.mb-auto,\n.my-auto {\n  margin-bottom: auto !important; }\n\n.ml-auto,\n.mx-auto {\n  margin-right: auto !important;\n  margin-left: auto !important; }\n\n@media (min-width: 576px) {\n  .m-sm-0 {\n    margin: 0 !important; }\n  .mt-sm-0,\n  .my-sm-0 {\n    margin-top: 0 !important; }\n  .mr-sm-0 {\n    margin-left: 0 !important;\n    margin-right: 0 !important; }\n  .mb-sm-0,\n  .my-sm-0 {\n    margin-bottom: 0 !important; }\n  .ml-sm-0 {\n    margin-right: 0 !important;\n    margin-left: 0 !important; }\n  .m-sm-1 {\n    margin: 0.25rem !important; }\n  .mt-sm-1,\n  .my-sm-1 {\n    margin-top: 0.25rem !important; }\n  .mr-sm-1 {\n    margin-left: 0.25rem !important;\n    margin-right: 0 !important; }\n  .mb-sm-1,\n  .my-sm-1 {\n    margin-bottom: 0.25rem !important; }\n  .ml-sm-1 {\n    margin-right: 0.25rem !important;\n    margin-left: 0 !important; }\n  .m-sm-2 {\n    margin: 0.5rem !important; }\n  .mt-sm-2,\n  .my-sm-2 {\n    margin-top: 0.5rem !important; }\n  .mr-sm-2 {\n    margin-left: 0.5rem !important;\n    margin-right: 0 !important; }\n  .mb-sm-2,\n  .my-sm-2 {\n    margin-bottom: 0.5rem !important; }\n  .ml-sm-2 {\n    margin-right: 0.5rem !important;\n    margin-left: 0 !important; }\n  .m-sm-3 {\n    margin: 1rem !important; }\n  .mt-sm-3,\n  .my-sm-3 {\n    margin-top: 1rem !important; }\n  .mr-sm-3 {\n    margin-left: 1rem !important;\n    margin-right: 0 !important; }\n  .mb-sm-3,\n  .my-sm-3 {\n    margin-bottom: 1rem !important; }\n  .ml-sm-3 {\n    margin-right: 1rem !important;\n    margin-left: 0 !important; }\n  .m-sm-4 {\n    margin: 1.5rem !important; }\n  .mt-sm-4,\n  .my-sm-4 {\n    margin-top: 1.5rem !important; }\n  .mr-sm-4 {\n    margin-left: 1.5rem !important;\n    margin-right: 0 !important; }\n  .mb-sm-4,\n  .my-sm-4 {\n    margin-bottom: 1.5rem !important; }\n  .ml-sm-4 {\n    margin-right: 1.5rem !important;\n    margin-left: 0 !important; }\n  .m-sm-5 {\n    margin: 3rem !important; }\n  .mt-sm-5,\n  .my-sm-5 {\n    margin-top: 3rem !important; }\n  .mr-sm-5 {\n    margin-left: 3rem !important;\n    margin-right: 0 !important; }\n  .mb-sm-5,\n  .my-sm-5 {\n    margin-bottom: 3rem !important; }\n  .ml-sm-5 {\n    margin-right: 3rem !important;\n    margin-left: 0 !important; }\n  .p-sm-0 {\n    padding: 0 !important; }\n  .pt-sm-0,\n  .py-sm-0 {\n    padding-top: 0 !important; }\n  .pr-sm-0 {\n    padding-left: 0 !important;\n    padding-right: 0 !important; }\n  .pb-sm-0,\n  .py-sm-0 {\n    padding-bottom: 0 !important; }\n  .pl-sm-0 {\n    padding-right: 0 !important;\n    padding-left: 0 !important; }\n  .p-sm-1 {\n    padding: 0.25rem !important; }\n  .pt-sm-1,\n  .py-sm-1 {\n    padding-top: 0.25rem !important; }\n  .pr-sm-1 {\n    padding-left: 0.25rem !important;\n    padding-right: 0 !important; }\n  .pb-sm-1,\n  .py-sm-1 {\n    padding-bottom: 0.25rem !important; }\n  .pl-sm-1 {\n    padding-right: 0.25rem !important;\n    padding-left: 0 !important; }\n  .p-sm-2 {\n    padding: 0.5rem !important; }\n  .pt-sm-2,\n  .py-sm-2 {\n    padding-top: 0.5rem !important; }\n  .pr-sm-2 {\n    padding-left: 0.5rem !important;\n    padding-right: 0 !important; }\n  .pb-sm-2,\n  .py-sm-2 {\n    padding-bottom: 0.5rem !important; }\n  .pl-sm-2 {\n    padding-right: 0.5rem !important;\n    padding-left: 0 !important; }\n  .p-sm-3 {\n    padding: 1rem !important; }\n  .pt-sm-3,\n  .py-sm-3 {\n    padding-top: 1rem !important; }\n  .pr-sm-3 {\n    padding-left: 1rem !important;\n    padding-right: 0 !important; }\n  .pb-sm-3,\n  .py-sm-3 {\n    padding-bottom: 1rem !important; }\n  .pl-sm-3 {\n    padding-right: 1rem !important;\n    padding-left: 0 !important; }\n  .p-sm-4 {\n    padding: 1.5rem !important; }\n  .pt-sm-4,\n  .py-sm-4 {\n    padding-top: 1.5rem !important; }\n  .pr-sm-4 {\n    padding-left: 1.5rem !important;\n    padding-right: 0 !important; }\n  .pb-sm-4,\n  .py-sm-4 {\n    padding-bottom: 1.5rem !important; }\n  .pl-sm-4 {\n    padding-right: 1.5rem !important;\n    padding-left: 0 !important; }\n  .p-sm-5 {\n    padding: 3rem !important; }\n  .pt-sm-5,\n  .py-sm-5 {\n    padding-top: 3rem !important; }\n  .pr-sm-5 {\n    padding-left: 3rem !important;\n    padding-right: 0 !important; }\n  .pb-sm-5,\n  .py-sm-5 {\n    padding-bottom: 3rem !important; }\n  .pl-sm-5 {\n    padding-right: 3rem !important;\n    padding-left: 0 !important; }\n  .m-sm-n1 {\n    margin: -0.25rem !important; }\n  .mt-sm-n1,\n  .my-sm-n1 {\n    margin-top: -0.25rem !important; }\n  .mr-sm-n1,\n  .mx-sm-n1 {\n    margin-right: -0.25rem !important; }\n  .mb-sm-n1,\n  .my-sm-n1 {\n    margin-bottom: -0.25rem !important; }\n  .ml-sm-n1,\n  .mx-sm-n1 {\n    margin-left: -0.25rem !important; }\n  .m-sm-n2 {\n    margin: -0.5rem !important; }\n  .mt-sm-n2,\n  .my-sm-n2 {\n    margin-top: -0.5rem !important; }\n  .mr-sm-n2,\n  .mx-sm-n2 {\n    margin-right: -0.5rem !important; }\n  .mb-sm-n2,\n  .my-sm-n2 {\n    margin-bottom: -0.5rem !important; }\n  .ml-sm-n2,\n  .mx-sm-n2 {\n    margin-left: -0.5rem !important; }\n  .m-sm-n3 {\n    margin: -1rem !important; }\n  .mt-sm-n3,\n  .my-sm-n3 {\n    margin-top: -1rem !important; }\n  .mr-sm-n3,\n  .mx-sm-n3 {\n    margin-right: -1rem !important; }\n  .mb-sm-n3,\n  .my-sm-n3 {\n    margin-bottom: -1rem !important; }\n  .ml-sm-n3,\n  .mx-sm-n3 {\n    margin-left: -1rem !important; }\n  .m-sm-n4 {\n    margin: -1.5rem !important; }\n  .mt-sm-n4,\n  .my-sm-n4 {\n    margin-top: -1.5rem !important; }\n  .mr-sm-n4,\n  .mx-sm-n4 {\n    margin-right: -1.5rem !important; }\n  .mb-sm-n4,\n  .my-sm-n4 {\n    margin-bottom: -1.5rem !important; }\n  .ml-sm-n4,\n  .mx-sm-n4 {\n    margin-left: -1.5rem !important; }\n  .m-sm-n5 {\n    margin: -3rem !important; }\n  .mt-sm-n5,\n  .my-sm-n5 {\n    margin-top: -3rem !important; }\n  .mr-sm-n5,\n  .mx-sm-n5 {\n    margin-right: -3rem !important; }\n  .mb-sm-n5,\n  .my-sm-n5 {\n    margin-bottom: -3rem !important; }\n  .ml-sm-n5,\n  .mx-sm-n5 {\n    margin-left: -3rem !important; }\n  .m-sm-auto {\n    margin: auto !important; }\n  .mt-sm-auto,\n  .my-sm-auto {\n    margin-top: auto !important; }\n  .mr-sm-auto,\n  .mx-sm-auto {\n    margin-left: auto !important;\n    margin-right: inherit !important; }\n  .mb-sm-auto,\n  .my-sm-auto {\n    margin-bottom: auto !important; }\n  .ml-sm-auto,\n  .mx-sm-auto {\n    margin-right: auto !important;\n    margin-left: auto !important; } }\n\n@media (min-width: 768px) {\n  .m-md-0 {\n    margin: 0 !important; }\n  .mt-md-0,\n  .my-md-0 {\n    margin-top: 0 !important; }\n  .mr-md-0 {\n    margin-left: 0 !important;\n    margin-right: 0 !important; }\n  .mb-md-0,\n  .my-md-0 {\n    margin-bottom: 0 !important; }\n  .ml-md-0 {\n    margin-right: 0 !important;\n    margin-left: 0 !important; }\n  .m-md-1 {\n    margin: 0.25rem !important; }\n  .mt-md-1,\n  .my-md-1 {\n    margin-top: 0.25rem !important; }\n  .mr-md-1 {\n    margin-left: 0.25rem !important;\n    margin-right: 0 !important; }\n  .mb-md-1,\n  .my-md-1 {\n    margin-bottom: 0.25rem !important; }\n  .ml-md-1 {\n    margin-right: 0.25rem !important;\n    margin-left: 0 !important; }\n  .m-md-2 {\n    margin: 0.5rem !important; }\n  .mt-md-2,\n  .my-md-2 {\n    margin-top: 0.5rem !important; }\n  .mr-md-2 {\n    margin-left: 0.5rem !important;\n    margin-right: 0 !important; }\n  .mb-md-2,\n  .my-md-2 {\n    margin-bottom: 0.5rem !important; }\n  .ml-md-2 {\n    margin-right: 0.5rem !important;\n    margin-left: 0 !important; }\n  .m-md-3 {\n    margin: 1rem !important; }\n  .mt-md-3,\n  .my-md-3 {\n    margin-top: 1rem !important; }\n  .mr-md-3 {\n    margin-left: 1rem !important;\n    margin-right: 0 !important; }\n  .mb-md-3,\n  .my-md-3 {\n    margin-bottom: 1rem !important; }\n  .ml-md-3 {\n    margin-right: 1rem !important;\n    margin-left: 0 !important; }\n  .m-md-4 {\n    margin: 1.5rem !important; }\n  .mt-md-4,\n  .my-md-4 {\n    margin-top: 1.5rem !important; }\n  .mr-md-4 {\n    margin-left: 1.5rem !important;\n    margin-right: 0 !important; }\n  .mb-md-4,\n  .my-md-4 {\n    margin-bottom: 1.5rem !important; }\n  .ml-md-4 {\n    margin-right: 1.5rem !important;\n    margin-left: 0 !important; }\n  .m-md-5 {\n    margin: 3rem !important; }\n  .mt-md-5,\n  .my-md-5 {\n    margin-top: 3rem !important; }\n  .mr-md-5 {\n    margin-left: 3rem !important;\n    margin-right: 0 !important; }\n  .mb-md-5,\n  .my-md-5 {\n    margin-bottom: 3rem !important; }\n  .ml-md-5 {\n    margin-right: 3rem !important;\n    margin-left: 0 !important; }\n  .p-md-0 {\n    padding: 0 !important; }\n  .pt-md-0,\n  .py-md-0 {\n    padding-top: 0 !important; }\n  .pr-md-0 {\n    padding-left: 0 !important;\n    padding-right: 0 !important; }\n  .pb-md-0,\n  .py-md-0 {\n    padding-bottom: 0 !important; }\n  .pl-md-0 {\n    padding-right: 0 !important;\n    padding-left: 0 !important; }\n  .p-md-1 {\n    padding: 0.25rem !important; }\n  .pt-md-1,\n  .py-md-1 {\n    padding-top: 0.25rem !important; }\n  .pr-md-1 {\n    padding-left: 0.25rem !important;\n    padding-right: 0 !important; }\n  .pb-md-1,\n  .py-md-1 {\n    padding-bottom: 0.25rem !important; }\n  .pl-md-1 {\n    padding-right: 0.25rem !important;\n    padding-left: 0 !important; }\n  .p-md-2 {\n    padding: 0.5rem !important; }\n  .pt-md-2,\n  .py-md-2 {\n    padding-top: 0.5rem !important; }\n  .pr-md-2 {\n    padding-left: 0.5rem !important;\n    padding-right: 0 !important; }\n  .pb-md-2,\n  .py-md-2 {\n    padding-bottom: 0.5rem !important; }\n  .pl-md-2 {\n    padding-right: 0.5rem !important;\n    padding-left: 0 !important; }\n  .p-md-3 {\n    padding: 1rem !important; }\n  .pt-md-3,\n  .py-md-3 {\n    padding-top: 1rem !important; }\n  .pr-md-3 {\n    padding-left: 1rem !important;\n    padding-right: 0 !important; }\n  .pb-md-3,\n  .py-md-3 {\n    padding-bottom: 1rem !important; }\n  .pl-md-3 {\n    padding-right: 1rem !important;\n    padding-left: 0 !important; }\n  .p-md-4 {\n    padding: 1.5rem !important; }\n  .pt-md-4,\n  .py-md-4 {\n    padding-top: 1.5rem !important; }\n  .pr-md-4 {\n    padding-left: 1.5rem !important;\n    padding-right: 0 !important; }\n  .pb-md-4,\n  .py-md-4 {\n    padding-bottom: 1.5rem !important; }\n  .pl-md-4 {\n    padding-right: 1.5rem !important;\n    padding-left: 0 !important; }\n  .p-md-5 {\n    padding: 3rem !important; }\n  .pt-md-5,\n  .py-md-5 {\n    padding-top: 3rem !important; }\n  .pr-md-5 {\n    padding-left: 3rem !important;\n    padding-right: 0 !important; }\n  .pb-md-5,\n  .py-md-5 {\n    padding-bottom: 3rem !important; }\n  .pl-md-5 {\n    padding-right: 3rem !important;\n    padding-left: 0 !important; }\n  .m-md-n1 {\n    margin: -0.25rem !important; }\n  .mt-md-n1,\n  .my-md-n1 {\n    margin-top: -0.25rem !important; }\n  .mr-md-n1,\n  .mx-md-n1 {\n    margin-right: -0.25rem !important; }\n  .mb-md-n1,\n  .my-md-n1 {\n    margin-bottom: -0.25rem !important; }\n  .ml-md-n1,\n  .mx-md-n1 {\n    margin-left: -0.25rem !important; }\n  .m-md-n2 {\n    margin: -0.5rem !important; }\n  .mt-md-n2,\n  .my-md-n2 {\n    margin-top: -0.5rem !important; }\n  .mr-md-n2,\n  .mx-md-n2 {\n    margin-right: -0.5rem !important; }\n  .mb-md-n2,\n  .my-md-n2 {\n    margin-bottom: -0.5rem !important; }\n  .ml-md-n2,\n  .mx-md-n2 {\n    margin-left: -0.5rem !important; }\n  .m-md-n3 {\n    margin: -1rem !important; }\n  .mt-md-n3,\n  .my-md-n3 {\n    margin-top: -1rem !important; }\n  .mr-md-n3,\n  .mx-md-n3 {\n    margin-right: -1rem !important; }\n  .mb-md-n3,\n  .my-md-n3 {\n    margin-bottom: -1rem !important; }\n  .ml-md-n3,\n  .mx-md-n3 {\n    margin-left: -1rem !important; }\n  .m-md-n4 {\n    margin: -1.5rem !important; }\n  .mt-md-n4,\n  .my-md-n4 {\n    margin-top: -1.5rem !important; }\n  .mr-md-n4,\n  .mx-md-n4 {\n    margin-right: -1.5rem !important; }\n  .mb-md-n4,\n  .my-md-n4 {\n    margin-bottom: -1.5rem !important; }\n  .ml-md-n4,\n  .mx-md-n4 {\n    margin-left: -1.5rem !important; }\n  .m-md-n5 {\n    margin: -3rem !important; }\n  .mt-md-n5,\n  .my-md-n5 {\n    margin-top: -3rem !important; }\n  .mr-md-n5,\n  .mx-md-n5 {\n    margin-right: -3rem !important; }\n  .mb-md-n5,\n  .my-md-n5 {\n    margin-bottom: -3rem !important; }\n  .ml-md-n5,\n  .mx-md-n5 {\n    margin-left: -3rem !important; }\n  .m-md-auto {\n    margin: auto !important; }\n  .mt-md-auto,\n  .my-md-auto {\n    margin-top: auto !important; }\n  .mr-md-auto,\n  .mx-md-auto {\n    margin-left: auto !important;\n    margin-right: inherit !important; }\n  .mb-md-auto,\n  .my-md-auto {\n    margin-bottom: auto !important; }\n  .ml-md-auto,\n  .mx-md-auto {\n    margin-right: auto !important;\n    margin-left: auto !important; } }\n\n@media (min-width: 992px) {\n  .m-lg-0 {\n    margin: 0 !important; }\n  .mt-lg-0,\n  .my-lg-0 {\n    margin-top: 0 !important; }\n  .mr-lg-0 {\n    margin-left: 0 !important;\n    margin-right: 0 !important; }\n  .mb-lg-0,\n  .my-lg-0 {\n    margin-bottom: 0 !important; }\n  .ml-lg-0 {\n    margin-right: 0 !important;\n    margin-left: 0 !important; }\n  .m-lg-1 {\n    margin: 0.25rem !important; }\n  .mt-lg-1,\n  .my-lg-1 {\n    margin-top: 0.25rem !important; }\n  .mr-lg-1 {\n    margin-left: 0.25rem !important;\n    margin-right: 0 !important; }\n  .mb-lg-1,\n  .my-lg-1 {\n    margin-bottom: 0.25rem !important; }\n  .ml-lg-1 {\n    margin-right: 0.25rem !important;\n    margin-left: 0 !important; }\n  .m-lg-2 {\n    margin: 0.5rem !important; }\n  .mt-lg-2,\n  .my-lg-2 {\n    margin-top: 0.5rem !important; }\n  .mr-lg-2 {\n    margin-left: 0.5rem !important;\n    margin-right: 0 !important; }\n  .mb-lg-2,\n  .my-lg-2 {\n    margin-bottom: 0.5rem !important; }\n  .ml-lg-2 {\n    margin-right: 0.5rem !important;\n    margin-left: 0 !important; }\n  .m-lg-3 {\n    margin: 1rem !important; }\n  .mt-lg-3,\n  .my-lg-3 {\n    margin-top: 1rem !important; }\n  .mr-lg-3 {\n    margin-left: 1rem !important;\n    margin-right: 0 !important; }\n  .mb-lg-3,\n  .my-lg-3 {\n    margin-bottom: 1rem !important; }\n  .ml-lg-3 {\n    margin-right: 1rem !important;\n    margin-left: 0 !important; }\n  .m-lg-4 {\n    margin: 1.5rem !important; }\n  .mt-lg-4,\n  .my-lg-4 {\n    margin-top: 1.5rem !important; }\n  .mr-lg-4 {\n    margin-left: 1.5rem !important;\n    margin-right: 0 !important; }\n  .mb-lg-4,\n  .my-lg-4 {\n    margin-bottom: 1.5rem !important; }\n  .ml-lg-4 {\n    margin-right: 1.5rem !important;\n    margin-left: 0 !important; }\n  .m-lg-5 {\n    margin: 3rem !important; }\n  .mt-lg-5,\n  .my-lg-5 {\n    margin-top: 3rem !important; }\n  .mr-lg-5 {\n    margin-left: 3rem !important;\n    margin-right: 0 !important; }\n  .mb-lg-5,\n  .my-lg-5 {\n    margin-bottom: 3rem !important; }\n  .ml-lg-5 {\n    margin-right: 3rem !important;\n    margin-left: 0 !important; }\n  .p-lg-0 {\n    padding: 0 !important; }\n  .pt-lg-0,\n  .py-lg-0 {\n    padding-top: 0 !important; }\n  .pr-lg-0 {\n    padding-left: 0 !important;\n    padding-right: 0 !important; }\n  .pb-lg-0,\n  .py-lg-0 {\n    padding-bottom: 0 !important; }\n  .pl-lg-0 {\n    padding-right: 0 !important;\n    padding-left: 0 !important; }\n  .p-lg-1 {\n    padding: 0.25rem !important; }\n  .pt-lg-1,\n  .py-lg-1 {\n    padding-top: 0.25rem !important; }\n  .pr-lg-1 {\n    padding-left: 0.25rem !important;\n    padding-right: 0 !important; }\n  .pb-lg-1,\n  .py-lg-1 {\n    padding-bottom: 0.25rem !important; }\n  .pl-lg-1 {\n    padding-right: 0.25rem !important;\n    padding-left: 0 !important; }\n  .p-lg-2 {\n    padding: 0.5rem !important; }\n  .pt-lg-2,\n  .py-lg-2 {\n    padding-top: 0.5rem !important; }\n  .pr-lg-2 {\n    padding-left: 0.5rem !important;\n    padding-right: 0 !important; }\n  .pb-lg-2,\n  .py-lg-2 {\n    padding-bottom: 0.5rem !important; }\n  .pl-lg-2 {\n    padding-right: 0.5rem !important;\n    padding-left: 0 !important; }\n  .p-lg-3 {\n    padding: 1rem !important; }\n  .pt-lg-3,\n  .py-lg-3 {\n    padding-top: 1rem !important; }\n  .pr-lg-3 {\n    padding-left: 1rem !important;\n    padding-right: 0 !important; }\n  .pb-lg-3,\n  .py-lg-3 {\n    padding-bottom: 1rem !important; }\n  .pl-lg-3 {\n    padding-right: 1rem !important;\n    padding-left: 0 !important; }\n  .p-lg-4 {\n    padding: 1.5rem !important; }\n  .pt-lg-4,\n  .py-lg-4 {\n    padding-top: 1.5rem !important; }\n  .pr-lg-4 {\n    padding-left: 1.5rem !important;\n    padding-right: 0 !important; }\n  .pb-lg-4,\n  .py-lg-4 {\n    padding-bottom: 1.5rem !important; }\n  .pl-lg-4 {\n    padding-right: 1.5rem !important;\n    padding-left: 0 !important; }\n  .p-lg-5 {\n    padding: 3rem !important; }\n  .pt-lg-5,\n  .py-lg-5 {\n    padding-top: 3rem !important; }\n  .pr-lg-5 {\n    padding-left: 3rem !important;\n    padding-right: 0 !important; }\n  .pb-lg-5,\n  .py-lg-5 {\n    padding-bottom: 3rem !important; }\n  .pl-lg-5 {\n    padding-right: 3rem !important;\n    padding-left: 0 !important; }\n  .m-lg-n1 {\n    margin: -0.25rem !important; }\n  .mt-lg-n1,\n  .my-lg-n1 {\n    margin-top: -0.25rem !important; }\n  .mr-lg-n1,\n  .mx-lg-n1 {\n    margin-right: -0.25rem !important; }\n  .mb-lg-n1,\n  .my-lg-n1 {\n    margin-bottom: -0.25rem !important; }\n  .ml-lg-n1,\n  .mx-lg-n1 {\n    margin-left: -0.25rem !important; }\n  .m-lg-n2 {\n    margin: -0.5rem !important; }\n  .mt-lg-n2,\n  .my-lg-n2 {\n    margin-top: -0.5rem !important; }\n  .mr-lg-n2,\n  .mx-lg-n2 {\n    margin-right: -0.5rem !important; }\n  .mb-lg-n2,\n  .my-lg-n2 {\n    margin-bottom: -0.5rem !important; }\n  .ml-lg-n2,\n  .mx-lg-n2 {\n    margin-left: -0.5rem !important; }\n  .m-lg-n3 {\n    margin: -1rem !important; }\n  .mt-lg-n3,\n  .my-lg-n3 {\n    margin-top: -1rem !important; }\n  .mr-lg-n3,\n  .mx-lg-n3 {\n    margin-right: -1rem !important; }\n  .mb-lg-n3,\n  .my-lg-n3 {\n    margin-bottom: -1rem !important; }\n  .ml-lg-n3,\n  .mx-lg-n3 {\n    margin-left: -1rem !important; }\n  .m-lg-n4 {\n    margin: -1.5rem !important; }\n  .mt-lg-n4,\n  .my-lg-n4 {\n    margin-top: -1.5rem !important; }\n  .mr-lg-n4,\n  .mx-lg-n4 {\n    margin-right: -1.5rem !important; }\n  .mb-lg-n4,\n  .my-lg-n4 {\n    margin-bottom: -1.5rem !important; }\n  .ml-lg-n4,\n  .mx-lg-n4 {\n    margin-left: -1.5rem !important; }\n  .m-lg-n5 {\n    margin: -3rem !important; }\n  .mt-lg-n5,\n  .my-lg-n5 {\n    margin-top: -3rem !important; }\n  .mr-lg-n5,\n  .mx-lg-n5 {\n    margin-right: -3rem !important; }\n  .mb-lg-n5,\n  .my-lg-n5 {\n    margin-bottom: -3rem !important; }\n  .ml-lg-n5,\n  .mx-lg-n5 {\n    margin-left: -3rem !important; }\n  .m-lg-auto {\n    margin: auto !important; }\n  .mt-lg-auto,\n  .my-lg-auto {\n    margin-top: auto !important; }\n  .mr-lg-auto,\n  .mx-lg-auto {\n    margin-left: auto !important;\n    margin-right: inherit !important; }\n  .mb-lg-auto,\n  .my-lg-auto {\n    margin-bottom: auto !important; }\n  .ml-lg-auto,\n  .mx-lg-auto {\n    margin-right: auto !important;\n    margin-left: auto !important; } }\n\n@media (min-width: 1200px) {\n  .m-xl-0 {\n    margin: 0 !important; }\n  .mt-xl-0,\n  .my-xl-0 {\n    margin-top: 0 !important; }\n  .mr-xl-0 {\n    margin-left: 0 !important;\n    margin-right: 0 !important; }\n  .mb-xl-0,\n  .my-xl-0 {\n    margin-bottom: 0 !important; }\n  .ml-xl-0 {\n    margin-right: 0 !important;\n    margin-left: 0 !important; }\n  .m-xl-1 {\n    margin: 0.25rem !important; }\n  .mt-xl-1,\n  .my-xl-1 {\n    margin-top: 0.25rem !important; }\n  .mr-xl-1 {\n    margin-left: 0.25rem !important;\n    margin-right: 0 !important; }\n  .mb-xl-1,\n  .my-xl-1 {\n    margin-bottom: 0.25rem !important; }\n  .ml-xl-1 {\n    margin-right: 0.25rem !important;\n    margin-left: 0 !important; }\n  .m-xl-2 {\n    margin: 0.5rem !important; }\n  .mt-xl-2,\n  .my-xl-2 {\n    margin-top: 0.5rem !important; }\n  .mr-xl-2 {\n    margin-left: 0.5rem !important;\n    margin-right: 0 !important; }\n  .mb-xl-2,\n  .my-xl-2 {\n    margin-bottom: 0.5rem !important; }\n  .ml-xl-2 {\n    margin-right: 0.5rem !important;\n    margin-left: 0 !important; }\n  .m-xl-3 {\n    margin: 1rem !important; }\n  .mt-xl-3,\n  .my-xl-3 {\n    margin-top: 1rem !important; }\n  .mr-xl-3 {\n    margin-left: 1rem !important;\n    margin-right: 0 !important; }\n  .mb-xl-3,\n  .my-xl-3 {\n    margin-bottom: 1rem !important; }\n  .ml-xl-3 {\n    margin-right: 1rem !important;\n    margin-left: 0 !important; }\n  .m-xl-4 {\n    margin: 1.5rem !important; }\n  .mt-xl-4,\n  .my-xl-4 {\n    margin-top: 1.5rem !important; }\n  .mr-xl-4 {\n    margin-left: 1.5rem !important;\n    margin-right: 0 !important; }\n  .mb-xl-4,\n  .my-xl-4 {\n    margin-bottom: 1.5rem !important; }\n  .ml-xl-4 {\n    margin-right: 1.5rem !important;\n    margin-left: 0 !important; }\n  .m-xl-5 {\n    margin: 3rem !important; }\n  .mt-xl-5,\n  .my-xl-5 {\n    margin-top: 3rem !important; }\n  .mr-xl-5 {\n    margin-left: 3rem !important;\n    margin-right: 0 !important; }\n  .mb-xl-5,\n  .my-xl-5 {\n    margin-bottom: 3rem !important; }\n  .ml-xl-5 {\n    margin-right: 3rem !important;\n    margin-left: 0 !important; }\n  .p-xl-0 {\n    padding: 0 !important; }\n  .pt-xl-0,\n  .py-xl-0 {\n    padding-top: 0 !important; }\n  .pr-xl-0 {\n    padding-left: 0 !important;\n    padding-right: 0 !important; }\n  .pb-xl-0,\n  .py-xl-0 {\n    padding-bottom: 0 !important; }\n  .pl-xl-0 {\n    padding-right: 0 !important;\n    padding-left: 0 !important; }\n  .p-xl-1 {\n    padding: 0.25rem !important; }\n  .pt-xl-1,\n  .py-xl-1 {\n    padding-top: 0.25rem !important; }\n  .pr-xl-1 {\n    padding-left: 0.25rem !important;\n    padding-right: 0 !important; }\n  .pb-xl-1,\n  .py-xl-1 {\n    padding-bottom: 0.25rem !important; }\n  .pl-xl-1 {\n    padding-right: 0.25rem !important;\n    padding-left: 0 !important; }\n  .p-xl-2 {\n    padding: 0.5rem !important; }\n  .pt-xl-2,\n  .py-xl-2 {\n    padding-top: 0.5rem !important; }\n  .pr-xl-2 {\n    padding-left: 0.5rem !important;\n    padding-right: 0 !important; }\n  .pb-xl-2,\n  .py-xl-2 {\n    padding-bottom: 0.5rem !important; }\n  .pl-xl-2 {\n    padding-right: 0.5rem !important;\n    padding-left: 0 !important; }\n  .p-xl-3 {\n    padding: 1rem !important; }\n  .pt-xl-3,\n  .py-xl-3 {\n    padding-top: 1rem !important; }\n  .pr-xl-3 {\n    padding-left: 1rem !important;\n    padding-right: 0 !important; }\n  .pb-xl-3,\n  .py-xl-3 {\n    padding-bottom: 1rem !important; }\n  .pl-xl-3 {\n    padding-right: 1rem !important;\n    padding-left: 0 !important; }\n  .p-xl-4 {\n    padding: 1.5rem !important; }\n  .pt-xl-4,\n  .py-xl-4 {\n    padding-top: 1.5rem !important; }\n  .pr-xl-4 {\n    padding-left: 1.5rem !important;\n    padding-right: 0 !important; }\n  .pb-xl-4,\n  .py-xl-4 {\n    padding-bottom: 1.5rem !important; }\n  .pl-xl-4 {\n    padding-right: 1.5rem !important;\n    padding-left: 0 !important; }\n  .p-xl-5 {\n    padding: 3rem !important; }\n  .pt-xl-5,\n  .py-xl-5 {\n    padding-top: 3rem !important; }\n  .pr-xl-5 {\n    padding-left: 3rem !important;\n    padding-right: 0 !important; }\n  .pb-xl-5,\n  .py-xl-5 {\n    padding-bottom: 3rem !important; }\n  .pl-xl-5 {\n    padding-right: 3rem !important;\n    padding-left: 0 !important; }\n  .m-xl-n1 {\n    margin: -0.25rem !important; }\n  .mt-xl-n1,\n  .my-xl-n1 {\n    margin-top: -0.25rem !important; }\n  .mr-xl-n1,\n  .mx-xl-n1 {\n    margin-right: -0.25rem !important; }\n  .mb-xl-n1,\n  .my-xl-n1 {\n    margin-bottom: -0.25rem !important; }\n  .ml-xl-n1,\n  .mx-xl-n1 {\n    margin-left: -0.25rem !important; }\n  .m-xl-n2 {\n    margin: -0.5rem !important; }\n  .mt-xl-n2,\n  .my-xl-n2 {\n    margin-top: -0.5rem !important; }\n  .mr-xl-n2,\n  .mx-xl-n2 {\n    margin-right: -0.5rem !important; }\n  .mb-xl-n2,\n  .my-xl-n2 {\n    margin-bottom: -0.5rem !important; }\n  .ml-xl-n2,\n  .mx-xl-n2 {\n    margin-left: -0.5rem !important; }\n  .m-xl-n3 {\n    margin: -1rem !important; }\n  .mt-xl-n3,\n  .my-xl-n3 {\n    margin-top: -1rem !important; }\n  .mr-xl-n3,\n  .mx-xl-n3 {\n    margin-right: -1rem !important; }\n  .mb-xl-n3,\n  .my-xl-n3 {\n    margin-bottom: -1rem !important; }\n  .ml-xl-n3,\n  .mx-xl-n3 {\n    margin-left: -1rem !important; }\n  .m-xl-n4 {\n    margin: -1.5rem !important; }\n  .mt-xl-n4,\n  .my-xl-n4 {\n    margin-top: -1.5rem !important; }\n  .mr-xl-n4,\n  .mx-xl-n4 {\n    margin-right: -1.5rem !important; }\n  .mb-xl-n4,\n  .my-xl-n4 {\n    margin-bottom: -1.5rem !important; }\n  .ml-xl-n4,\n  .mx-xl-n4 {\n    margin-left: -1.5rem !important; }\n  .m-xl-n5 {\n    margin: -3rem !important; }\n  .mt-xl-n5,\n  .my-xl-n5 {\n    margin-top: -3rem !important; }\n  .mr-xl-n5,\n  .mx-xl-n5 {\n    margin-right: -3rem !important; }\n  .mb-xl-n5,\n  .my-xl-n5 {\n    margin-bottom: -3rem !important; }\n  .ml-xl-n5,\n  .mx-xl-n5 {\n    margin-left: -3rem !important; }\n  .m-xl-auto {\n    margin: auto !important; }\n  .mt-xl-auto,\n  .my-xl-auto {\n    margin-top: auto !important; }\n  .mr-xl-auto,\n  .mx-xl-auto {\n    margin-left: auto !important;\n    margin-right: inherit !important; }\n  .mb-xl-auto,\n  .my-xl-auto {\n    margin-bottom: auto !important; }\n  .ml-xl-auto,\n  .mx-xl-auto {\n    margin-right: auto !important;\n    margin-left: auto !important; } }\n\n.float-left {\n  float: right !important; }\n\n.float-right {\n  float: left !important; }\n\n.float-none {\n  float: none !important; }\n\n@media (min-width: 576px) {\n  .float-sm-left {\n    float: right !important; }\n  .float-sm-right {\n    float: left !important; }\n  .float-sm-none {\n    float: none !important; } }\n\n@media (min-width: 768px) {\n  .float-md-left {\n    float: right !important; }\n  .float-md-right {\n    float: left !important; }\n  .float-md-none {\n    float: none !important; } }\n\n@media (min-width: 992px) {\n  .float-lg-left {\n    float: right !important; }\n  .float-lg-right {\n    float: left !important; }\n  .float-lg-none {\n    float: none !important; } }\n\n@media (min-width: 1200px) {\n  .float-xl-left {\n    float: right !important; }\n  .float-xl-right {\n    float: left !important; }\n  .float-xl-none {\n    float: none !important; } }\n\n.text-monospace {\n  font-family: SFMono-Regular, Menlo, Monaco, Consolas, \"Liberation Mono\", \"Courier New\", monospace !important; }\n\n.text-justify {\n  text-align: justify !important; }\n\n.text-wrap {\n  white-space: normal !important; }\n\n.text-nowrap {\n  white-space: nowrap !important; }\n\n.text-truncate {\n  overflow: hidden;\n  text-overflow: ellipsis;\n  white-space: nowrap; }\n\n.text-left {\n  text-align: right !important; }\n\n.text-right {\n  text-align: left !important; }\n\n.text-center {\n  text-align: center !important; }\n\n@media (min-width: 576px) {\n  .text-sm-left {\n    text-align: right !important; }\n  .text-sm-right {\n    text-align: left !important; }\n  .text-sm-center {\n    text-align: center !important; } }\n\n@media (min-width: 768px) {\n  .text-md-left {\n    text-align: right !important; }\n  .text-md-right {\n    text-align: left !important; }\n  .text-md-center {\n    text-align: center !important; } }\n\n@media (min-width: 992px) {\n  .text-lg-left {\n    text-align: right !important; }\n  .text-lg-right {\n    text-align: left !important; }\n  .text-lg-center {\n    text-align: center !important; } }\n\n@media (min-width: 1200px) {\n  .text-xl-left {\n    text-align: right !important; }\n  .text-xl-right {\n    text-align: left !important; }\n  .text-xl-center {\n    text-align: center !important; } }\n\n.text-lowercase {\n  text-transform: lowercase !important; }\n\n.text-uppercase {\n  text-transform: uppercase !important; }\n\n.text-capitalize {\n  text-transform: capitalize !important; }\n\n.font-weight-light {\n  font-weight: 300 !important; }\n\n.font-weight-lighter {\n  font-weight: lighter !important; }\n\n.font-weight-normal {\n  font-weight: 400 !important; }\n\n.font-weight-bold {\n  font-weight: 700 !important; }\n\n.font-weight-bolder {\n  font-weight: bolder !important; }\n\n.font-italic {\n  font-style: italic !important; }\n\n.text-white {\n  color: #fff !important; }\n\n.text-primary {\n  color: #64b0f2 !important; }\n\na.text-primary:hover, a.text-primary:focus {\n  color: #1d8cec !important; }\n\n.text-secondary {\n  color: #6c757d !important; }\n\na.text-secondary:hover, a.text-secondary:focus {\n  color: #494f54 !important; }\n\n.text-success {\n  color: #1bb99a !important; }\n\na.text-success:hover, a.text-success:focus {\n  color: #117662 !important; }\n\n.text-info {\n  color: #3db9dc !important; }\n\na.text-info:hover, a.text-info:focus {\n  color: #1f8ead !important; }\n\n.text-warning {\n  color: #ffff48 !important; }\n\na.text-warning:hover, a.text-warning:focus {\n  color: #d2910f !important; }\n\n.text-danger {\n  color: #ff5d48 !important; }\n\na.text-danger:hover, a.text-danger:focus {\n  color: #fb1d00 !important; }\n\n.text-light {\n  color: #f8f9fa !important; }\n\na.text-light:hover, a.text-light:focus {\n  color: #cbd3da !important; }\n\n.text-dark {\n  color: #343a40 !important; }\n\na.text-dark:hover, a.text-dark:focus {\n  color: #121416 !important; }\n\n.text-purple {\n  color: #9261c6 !important; }\n\na.text-purple:hover, a.text-purple:focus {\n  color: #6c3aa1 !important; }\n\n.text-pink {\n  color: #ff7aa3 !important; }\n\na.text-pink:hover, a.text-pink:focus {\n  color: #ff2e6e !important; }\n\n.text-body {\n  color: #495057 !important; }\n\n.text-muted {\n  color: #6c757d !important; }\n\n.text-black-50 {\n  color: rgba(0, 0, 0, 0.5) !important; }\n\n.text-white-50 {\n  color: rgba(255, 255, 255, 0.5) !important; }\n\n.text-hide {\n  font: 0/0 a;\n  color: transparent;\n  text-shadow: none;\n  background-color: transparent;\n  border: 0; }\n\n.text-decoration-none {\n  text-decoration: none !important; }\n\n.text-break {\n  word-break: break-word !important;\n  overflow-wrap: break-word !important; }\n\n.text-reset {\n  color: inherit !important; }\n\n.logo-box {\n  float: right; }\n\n.navbar-custom {\n  padding: 0px 0px 0px 10px; }\n  .navbar-custom .topnav-menu > li {\n    float: right; }\n  .navbar-custom .topnav-menu .nav-link {\n    direction: ltr; }\n  .navbar-custom .app-search {\n    margin-left: 20px; }\n    .navbar-custom .app-search .form-control {\n      padding-right: 20px;\n      padding-left: 0;\n      border-radius: 0 30px 30px 0; }\n    .navbar-custom .app-search .input-group-append {\n      margin-right: 0; }\n    .navbar-custom .app-search .btn {\n      border-radius: 30px 0 0 30px; }\n\n/* Notification */\n.notification-list .noti-icon-badge {\n  left: 14px;\n  right: auto; }\n\n.notification-list .notify-item .notify-icon {\n  float: right;\n  margin-left: 10px;\n  margin-right: 0; }\n\n.notification-list .notify-item .notify-details {\n  margin-left: 0;\n  margin-right: 45px; }\n\n.notification-list .profile-dropdown i {\n  margin-left: 5px;\n  margin-right: 0px;\n  float: right; }\n\n.page-title-box .page-title-right {\n  float: left; }\n\n.content-page {\n  margin-right: 240px;\n  margin-left: 0; }\n\n#sidebar-menu > ul > li > a i {\n  margin: 0 3px 0 10px; }\n\n#sidebar-menu > ul > li > a .drop-arrow {\n  float: left; }\n  #sidebar-menu > ul > li > a .drop-arrow i {\n    margin-left: 0; }\n\n#sidebar-menu > ul > li > ul {\n  padding-right: 40px;\n  padding-left: 0; }\n  #sidebar-menu > ul > li > ul ul {\n    padding-right: 20px;\n    padding-left: 0; }\n\n#sidebar-menu .menu-arrow {\n  left: 20px;\n  right: auto; }\n  #sidebar-menu .menu-arrow:before {\n    content: \"\\F141\"; }\n\n#sidebar-menu li.mm-active > a > span.menu-arrow {\n  transform: rotate(-90deg); }\n\n.enlarged .left-side-menu #sidebar-menu > ul > li > a i {\n  margin-left: 20px;\n  margin-right: 5px; }\n\n.enlarged .left-side-menu #sidebar-menu > ul > li > a span {\n  padding-right: 25px;\n  padding-left: 0; }\n\n.enlarged .left-side-menu #sidebar-menu > ul > li:hover > ul {\n  right: 70px;\n  left: auto; }\n\n.enlarged .left-side-menu #sidebar-menu > ul ul li:hover > ul {\n  right: 190px;\n  margin-top: -36px; }\n\n.enlarged .left-side-menu #sidebar-menu > ul ul li > a span.pull-right {\n  left: 20px;\n  right: 0; }\n\n.enlarged .navbar-custom {\n  right: 0px; }\n\n.enlarged .content-page {\n  margin-right: 70px !important;\n  margin-left: 0 !important; }\n\n.enlarged .footer {\n  left: 0 !important;\n  right: 70px !important; }\n\n@media (max-width: 767.98px) {\n  .content-page,\n  .enlarged .content-page {\n    margin-right: 0 !important; } }\n\n@media (min-width: 1025px) {\n  .navbar-custom .button-menu-mobile {\n    margin-left: 0;\n    margin-right: 8px; } }\n\n/* =============\r\n  Small Menu\r\n============= */\n.left-side-menu-sm .left-side-menu #sidebar-menu > ul ul {\n  padding-right: 0; }\n\n.left-side-menu-sm .left-side-menu + .content-page {\n  margin-right: 160px;\n  margin-left: 0; }\n\n.left-side-menu-sm .left-side-menu + .content-page .footer {\n  left: auto;\n  right: 160px; }\n\n.enlarged.left-side-menu-sm #wrapper .left-side-menu {\n  text-align: right; }\n  .enlarged.left-side-menu-sm #wrapper .left-side-menu ul li a i {\n    margin-right: 3px;\n    margin-left: 15px; }\n\n.footer {\n  left: 0;\n  right: 240px; }\n\n@media (max-width: 767.98px) {\n  .footer {\n    right: 0 !important; } }\n\n.right-bar {\n  float: left !important;\n  left: -270px;\n  right: auto; }\n\n.right-bar-enabled .right-bar {\n  left: 0;\n  right: auto; }\n\n.activity-widget .activity-list {\n  border-left: 0;\n  border-right: 2px dashed #ced4da;\n  padding-right: 24px;\n  padding-left: 0px; }\n  .activity-widget .activity-list::after {\n    right: -7px; }\n\n.topbar-light .navbar-custom {\n  box-shadow: -240px 1px 0 0 #f8f9fa; }\n\n.enlarged.left-side-menu-dark #wrapper .navbar-custom {\n  box-shadow: -70px 1px 0 0 #f8f9fa; }\n\n.inbox-widget .inbox-item .inbox-item-img {\n  float: right;\n  margin-left: 15px;\n  margin-right: 0; }\n\n.inbox-widget .inbox-item .inbox-item-date {\n  right: auto;\n  left: 5px; }\n\n.user-position {\n  left: 0px;\n  right: auto; }\n\n.icons-list-demo i {\n  margin-left: 12px;\n  margin-right: 0; }\n\n.checkbox label {\n  padding-right: 8px;\n  padding-left: 0; }\n  .checkbox label::before {\n    left: auto;\n    right: 0;\n    margin-left: 0;\n    margin-right: -18px; }\n  .checkbox label::after {\n    left: auto;\n    right: 0;\n    margin-right: -18px;\n    margin-left: 0;\n    padding-left: 0;\n    padding-right: 3px; }\n\n.checkbox input[type=\"checkbox\"]:checked + label::after {\n  left: auto;\n  right: 7px;\n  transform: rotate(45deg); }\n\n.checkbox.checkbox-single label:before {\n  margin-right: 0; }\n\n.checkbox.checkbox-single label:after {\n  margin-right: 0; }\n\n.radio label {\n  padding-left: 0;\n  padding-right: 8px; }\n  .radio label::before {\n    left: auto;\n    right: 0;\n    margin-left: 0;\n    margin-right: -18px; }\n  .radio label::after {\n    left: 0;\n    right: 6px;\n    margin-left: 0;\n    margin-right: -20px; }\n\n@media print {\n  .content-page,\n  .content,\n  body {\n    margin-right: 0; } }\n\n.demos-show-btn {\n  left: 0;\n  right: auto;\n  border-radius: 0 6px 6px 0; }\n\n.custom-modal-title {\n  text-align: right; }\n\n.modal-demo .close {\n  left: 25px;\n  right: auto; }\n\n.legendLabel {\n  padding-left: 20px;\n  padding-right: 5px; }\n\n.select2-container .select2-selection--single .select2-selection__rendered {\n  padding-right: 12px; }\n\n.select2-container .select2-selection--single .select2-selection__arrow {\n  left: 3px;\n  right: auto; }\n\n.select2-container .select2-selection--multiple .select2-selection__choice {\n  float: right;\n  margin-left: 5px;\n  margin-right: 0; }\n\n.select2-container .select2-search--inline {\n  float: right; }\n\n.ms-container .ms-optgroup-label {\n  padding: 5px 5px 0px 0; }\n\n.editable-buttons {\n  margin-left: 0;\n  margin-right: 7px; }\n  .editable-buttons .editable-cancel {\n    margin-left: 0;\n    margin-right: 7px; }\n\n.dataTables_wrapper .dataTables_filter {\n  text-align: left !important; }\n  .dataTables_wrapper .dataTables_filter input {\n    margin-left: 0px !important;\n    margin-right: 0.5em; }\n\n.dataTables_length, .dt-buttons, #datatable-colvid_info {\n  float: right; }\n\ndiv.ColVis {\n  margin-right: 0;\n  margin-left: 30px; }\n\nbutton.ColVis_Button {\n  margin-right: 0;\n  margin-right: 3px; }\n\nul.ColVis_collection li span {\n  padding-left: 0;\n  padding-right: 0.5em; }\n\n.responsive-table-plugin .btn-group.pull-right {\n  float: left; }\n\n.responsive-table-plugin .checkbox-row label:after {\n  margin-left: -22px;\n  top: -2px; }\n\n.tablesaw-columntoggle-popup .tablesaw-btn-group > label input {\n  margin-right: 0;\n  margin-left: .8em; }\n\n.tablesaw-bar .tablesaw-bar-section .tablesaw-btn {\n  margin-left: 0;\n  margin-right: .4em; }\n\n.diamond .top:before {\n  margin-left: 0px;\n  margin-right: -13px;\n  transform: skew(48deg); }\n\n.diamond .top:after {\n  margin-left: 0px;\n  margin-right: 47px;\n  transform: skew(-48deg); }\n\n.diamond .bot:before {\n  margin-left: 0px;\n  margin-right: -27px; }\n\n.diamond:after {\n  margin-left: 0px;\n  margin-right: 38px; }\n", "// Breakpoint viewport sizes and media queries.\n//\n// Breakpoints are defined as a map of (name: minimum width), order from small to large:\n//\n//    (xs: 0, sm: 576px, md: 768px, lg: 992px, xl: 1200px)\n//\n// The map defined in the `$grid-breakpoints` global variable is used as the `$breakpoints` argument by default.\n\n// Name of the next breakpoint, or null for the last breakpoint.\n//\n//    >> breakpoint-next(sm)\n//    md\n//    >> breakpoint-next(sm, (xs: 0, sm: 576px, md: 768px, lg: 992px, xl: 1200px))\n//    md\n//    >> breakpoint-next(sm, $breakpoint-names: (xs sm md lg xl))\n//    md\n@function breakpoint-next($name, $breakpoints: $grid-breakpoints, $breakpoint-names: map-keys($breakpoints)) {\n  $n: index($breakpoint-names, $name);\n  @return if($n != null and $n < length($breakpoint-names), nth($breakpoint-names, $n + 1), null);\n}\n\n// Minimum breakpoint width. Null for the smallest (first) breakpoint.\n//\n//    >> breakpoint-min(sm, (xs: 0, sm: 576px, md: 768px, lg: 992px, xl: 1200px))\n//    576px\n@function breakpoint-min($name, $breakpoints: $grid-breakpoints) {\n  $min: map-get($breakpoints, $name);\n  @return if($min != 0, $min, null);\n}\n\n// Maximum breakpoint width. Null for the largest (last) breakpoint.\n// The maximum value is calculated as the minimum of the next one less 0.02px\n// to work around the limitations of `min-` and `max-` prefixes and viewports with fractional widths.\n// See https://www.w3.org/TR/mediaqueries-4/#mq-min-max\n// Uses 0.02px rather than 0.01px to work around a current rounding bug in Safari.\n// See https://bugs.webkit.org/show_bug.cgi?id=178261\n//\n//    >> breakpoint-max(sm, (xs: 0, sm: 576px, md: 768px, lg: 992px, xl: 1200px))\n//    767.98px\n@function breakpoint-max($name, $breakpoints: $grid-breakpoints) {\n  $next: breakpoint-next($name, $breakpoints);\n  @return if($next, breakpoint-min($next, $breakpoints) - .02, null);\n}\n\n// Returns a blank string if smallest breakpoint, otherwise returns the name with a dash in front.\n// Useful for making responsive utilities.\n//\n//    >> breakpoint-infix(xs, (xs: 0, sm: 576px, md: 768px, lg: 992px, xl: 1200px))\n//    \"\"  (Returns a blank string)\n//    >> breakpoint-infix(sm, (xs: 0, sm: 576px, md: 768px, lg: 992px, xl: 1200px))\n//    \"-sm\"\n@function breakpoint-infix($name, $breakpoints: $grid-breakpoints) {\n  @return if(breakpoint-min($name, $breakpoints) == null, \"\", \"-#{$name}\");\n}\n\n// Media of at least the minimum breakpoint width. No query for the smallest breakpoint.\n// Makes the @content apply to the given breakpoint and wider.\n@mixin media-breakpoint-up($name, $breakpoints: $grid-breakpoints) {\n  $min: breakpoint-min($name, $breakpoints);\n  @if $min {\n    @media (min-width: $min) {\n      @content;\n    }\n  } @else {\n    @content;\n  }\n}\n\n// Media of at most the maximum breakpoint width. No query for the largest breakpoint.\n// Makes the @content apply to the given breakpoint and narrower.\n@mixin media-breakpoint-down($name, $breakpoints: $grid-breakpoints) {\n  $max: breakpoint-max($name, $breakpoints);\n  @if $max {\n    @media (max-width: $max) {\n      @content;\n    }\n  } @else {\n    @content;\n  }\n}\n\n// Media that spans multiple breakpoint widths.\n// Makes the @content apply between the min and max breakpoints\n@mixin media-breakpoint-between($lower, $upper, $breakpoints: $grid-breakpoints) {\n  $min: breakpoint-min($lower, $breakpoints);\n  $max: breakpoint-max($upper, $breakpoints);\n\n  @if $min != null and $max != null {\n    @media (min-width: $min) and (max-width: $max) {\n      @content;\n    }\n  } @else if $max == null {\n    @include media-breakpoint-up($lower, $breakpoints) {\n      @content;\n    }\n  } @else if $min == null {\n    @include media-breakpoint-down($upper, $breakpoints) {\n      @content;\n    }\n  }\n}\n\n// Media between the breakpoint's minimum and maximum widths.\n// No minimum for the smallest breakpoint, and no maximum for the largest one.\n// Makes the @content apply only to the given breakpoint, not viewports any wider or narrower.\n@mixin media-breakpoint-only($name, $breakpoints: $grid-breakpoints) {\n  $min: breakpoint-min($name, $breakpoints);\n  $max: breakpoint-max($name, $breakpoints);\n\n  @if $min != null and $max != null {\n    @media (min-width: $min) and (max-width: $max) {\n      @content;\n    }\n  } @else if $max == null {\n    @include media-breakpoint-up($name, $breakpoints) {\n      @content;\n    }\n  } @else if $min == null {\n    @include media-breakpoint-down($name, $breakpoints) {\n      @content;\n    }\n  }\n}\n", "// \r\n// topbar.scss\r\n//\r\n\r\n// Logo\r\n.logo {\r\n    display: block;\r\n    line-height: $topbar-height;\r\n    span.logo-lg {\r\n        display: block;\r\n    }\r\n    span.logo-sm {\r\n        display: none;\r\n    }\r\n    .logo-lg-text-dark {\r\n        color: $dark;\r\n        font-weight: $font-weight-bold;\r\n        font-size: 22px;\r\n        text-transform: uppercase;\r\n    }\r\n    .logo-lg-text-light {\r\n        color: $white;\r\n        font-weight: $font-weight-bold;\r\n        font-size: 22px;\r\n        text-transform: uppercase;\r\n    }\r\n}\r\n\r\n.logo-box {\r\n    background-color: $bg-leftbar-light;\r\n    height: $topbar-height;\r\n    width: $leftbar-width;\r\n    float: left;\r\n}\r\n\r\n.logo-light {\r\n    display: $logo-light-display;\r\n}\r\n\r\n.logo-dark {\r\n    display: $logo-dark-display;\r\n}\r\n\r\n.navbar-custom {\r\n    background-color: $bg-topbar-dark;\r\n    padding: 0 10px 0 0;\r\n    position: fixed;\r\n    left: 0;\r\n    right: 0;\r\n    height: $topbar-height;\r\n    z-index: 100;\r\n\r\n    .topnav-menu {\r\n        > li {\r\n            float: left;\r\n        }\r\n        .nav-link {\r\n            padding: 0 15px;\r\n            color: $gray-500;\r\n            min-width: 32px;\r\n            display: block;\r\n            line-height: $topbar-height;\r\n            text-align: center;\r\n            max-height: $topbar-height;\r\n        }\r\n    }\r\n\r\n    .dropdown.show {\r\n        .nav-link {\r\n            background-color: rgba($white,0.05);\r\n        }   \r\n    }\r\n\r\n\r\n    /* Search */\r\n    .app-search {\r\n        overflow: hidden;\r\n        height: $topbar-height;\r\n        display: table;\r\n        max-width: 180px;\r\n        margin-right: 20px;\r\n        \r\n        .app-search-box {\r\n            display: table-cell;\r\n            vertical-align: middle;\r\n\r\n            input::-webkit-input-placeholder {\r\n                font-size: 0.8125rem;\r\n                color: $gray-500;\r\n            }\r\n        }\r\n        .form-control {\r\n            border: none;\r\n            height: 38px;\r\n            padding-left: 20px;\r\n            padding-right: 0;\r\n            color: $gray-600;\r\n            background-color: lighten($bg-topbar-dark, 3.5%);\r\n            box-shadow: none;\r\n            border-radius: 30px 0 0 30px;\r\n        }\r\n        .input-group-append {\r\n            margin-left: 0;\r\n            z-index: 4;\r\n        }\r\n\r\n        .btn {\r\n            background-color: lighten($bg-topbar-dark, 3.5%);\r\n            color: $gray-500;\r\n            border-color: transparent;;\r\n            border-radius: 0 30px 30px 0;\r\n            box-shadow: none !important;\r\n        }\r\n    }\r\n\r\n    .button-menu-mobile {\r\n        border: none;\r\n        color: $gray-500;\r\n        display: inline-block;\r\n        height: $topbar-height;\r\n        line-height: $topbar-height;\r\n        width: 60px;\r\n        background-color: transparent;\r\n        font-size: 24px;\r\n        cursor: pointer;\r\n    }\r\n    \r\n    .button-menu-mobile.disable-btn {\r\n        display: none;\r\n    }\r\n}\r\n\r\n\r\n/* Notification */\r\n.noti-scroll {\r\n    max-height: 230px;\r\n}\r\n\r\n.notification-list {\r\n    margin-left: 0;\r\n\r\n    .noti-title {\r\n        background-color: $primary;\r\n        padding: 15px 20px;\r\n        border-radius: 0.25rem 0.25rem 0 0;\r\n        margin-top: -7px;\r\n    }\r\n\r\n    .noti-icon {\r\n        font-size: 22px;\r\n    }\r\n\r\n    .noti-icon-badge {\r\n        display: inline-block;\r\n        height: 10px;\r\n        width: 10px;\r\n        background-color: $danger;\r\n        border-radius: 50%;\r\n        border: 2px solid $bg-topbar-dark;\r\n        position: absolute;\r\n        top: 22px;\r\n        right: 14px;\r\n    }\r\n\r\n    .notify-item {\r\n        padding: 12px 20px;\r\n\r\n        \r\n        &.notify-all{\r\n            background-color: lighten($gray-200, 4%);\r\n            margin-bottom: -7px;\r\n        }\r\n\r\n        .notify-icon {\r\n            float: left;\r\n            height: 36px;\r\n            width: 36px;\r\n            font-size: 18px;\r\n            line-height: 36px;\r\n            text-align: center;\r\n            margin-top: 2px;\r\n            margin-right: 10px;\r\n            border-radius: 50%;\r\n            color: $white;\r\n        }\r\n\r\n        .notify-details {\r\n            margin-bottom: 5px;\r\n            overflow: hidden;\r\n            margin-left: 45px;\r\n            text-overflow: ellipsis;\r\n            white-space: nowrap;\r\n            color: $gray-800;\r\n            font-weight: $font-weight-medium;\r\n\r\n            b {\r\n                font-weight: 500;\r\n            }\r\n            small {\r\n                display: block;\r\n            }\r\n            span {\r\n                display: block;\r\n                overflow: hidden;\r\n                text-overflow: ellipsis;\r\n                white-space: nowrap;\r\n                font-size: 13px;\r\n            }\r\n\r\n        }\r\n        \r\n        .user-msg {\r\n            margin-left: 45px;\r\n            white-space: normal;\r\n            line-height: 16px;\r\n        }\r\n    }\r\n\r\n    .inbox-widget {\r\n        .inbox-item{\r\n            padding: 12px 20px;\r\n\r\n            &:hover {\r\n                background-color: $dropdown-link-hover-bg;\r\n            }\r\n        }   \r\n    }\r\n\r\n    .profile-dropdown {\r\n        .notify-item {\r\n            padding: 7px 20px;\r\n        }\r\n    }\r\n}\r\n\r\n.profile-dropdown {\r\n    width: 170px;\r\n    i {\r\n        margin-right: 5px;\r\n        font-size: 16px;\r\n    }\r\n}\r\n\r\n.nav-user {\r\n    padding: 0 12px !important;\r\n    img {\r\n        height: 32px;\r\n        width: 32px;\r\n    }\r\n}\r\n\r\n// Responsive\r\n\r\n@media (min-width: 1025px){\r\n    .navbar-custom {\r\n        .button-menu-mobile{\r\n            margin-left: 8px;\r\n        }\r\n    }\r\n}\r\n\r\n// Topbar Navbar light\r\n\r\n.topbar-light{\r\n    .navbar-custom {\r\n        background-color: $bg-topbar-light;\r\n        box-shadow: $leftbar-width 1px 0 0 $gray-200;\r\n    \r\n        .topnav-menu{\r\n            .nav-link {\r\n                color: $gray-600;\r\n            }\r\n        }\r\n\r\n        .notification-list {\r\n            .noti-icon-badge{\r\n                border-color: $bg-topbar-light;\r\n            }\r\n        }\r\n    \r\n        .button-menu-mobile{\r\n            color: $gray-600;\r\n        }\r\n    \r\n        /* app search */\r\n        .app-search {\r\n            input::-webkit-input-placeholder {\r\n                color: $gray-600 !important;\r\n            }\r\n            .form-control {\r\n                color: $dark;\r\n                background-color: darken($bg-topbar-light, 4%);\r\n                border-color: darken($bg-topbar-light, 4%);\r\n            }\r\n            .btn {\r\n                background-color: darken($bg-topbar-light, 4%);\r\n                color: $gray-500;\r\n            }\r\n        }\r\n    }\r\n}", "// \r\n// page-title.scss\r\n//\r\n\r\n.page-title-box {\r\n    position: relative;\r\n    background-color: $card-bg;\r\n    padding: 0 30px;\r\n    margin: 0 -30px 30px -30px;\r\n    box-shadow: $box-shadow;\r\n    \r\n    .page-title {\r\n        font-size: 18px;\r\n        margin: 0;\r\n        line-height: 60px;\r\n    }\r\n\r\n    .page-title-right {\r\n        float: right;\r\n        margin-top: 7px;\r\n    }\r\n    .breadcrumb {\r\n        background-color: transparent;\r\n        padding: .75rem 0;\r\n    }\r\n}\r\n\r\n\r\n@include media-breakpoint-down(sm) {\r\n    .page-title-box {\r\n        .page-title {\r\n            display: block;\r\n            white-space: nowrap;\r\n            text-overflow: ellipsis;\r\n            overflow: hidden;\r\n            line-height: 70px;\r\n        }\r\n        .page-title-right {\r\n            display: none;\r\n        }\r\n    }\r\n}\r\n", "// \r\n// footer.scss\r\n//\r\n\r\n.footer {\r\n    bottom: 0;\r\n    padding: 21px 15px 20px;\r\n    position: absolute;\r\n    right: 0;\r\n    color: $gray-600;\r\n    left: $leftbar-width;\r\n    background-color: darken($body-bg,3.5%);\r\n}\r\n\r\n@include media-breakpoint-down(sm) {\r\n    .footer {\r\n        left: 0 !important;\r\n        text-align: center;\r\n    }\r\n}", "//\r\n// right-sidebar.scss\r\n//\r\n\r\n.right-bar {\r\n    background-color: $bg-rightbar;\r\n    box-shadow: 0 0 24px 0 rgba(0, 0, 0, 0.06), 0 1px 0 0 rgba(0, 0, 0, 0.02);\r\n    display: block;\r\n    position: fixed;\r\n    transition: all 200ms ease-out;\r\n    width: $rightbar-width;\r\n    z-index: 9999;\r\n    float: right !important;\r\n    right: -($rightbar-width + 10px);\r\n    top: 0;\r\n    bottom: 0;\r\n\r\n    .rightbar-title {\r\n        background-color: $primary;\r\n        padding: 24.5px;\r\n        color: $white;\r\n    }\r\n    .right-bar-toggle {\r\n        background-color: lighten($dark, 7%);\r\n        height: 24px;\r\n        width: 24px;\r\n        line-height: 24px;\r\n        color: $gray-200;\r\n        text-align: center;\r\n        border-radius: 50%;\r\n        margin-top: -4px;\r\n\r\n        &:hover {\r\n            background-color: lighten($dark, 10%);\r\n        }\r\n    }\r\n    .user-box {\r\n        padding: 25px;\r\n        text-align: center;\r\n        .user-img {\r\n            position: relative;\r\n            height: 64px;\r\n            width: 64px;\r\n            margin: 0 auto 15px auto;\r\n            .user-edit {\r\n                position: absolute;\r\n                right: -5px;\r\n                bottom: 0px;\r\n                height: 24px;\r\n                width: 24px;\r\n                background-color: $white;\r\n                line-height: 24px;\r\n                border-radius: 50%;\r\n                box-shadow: $box-shadow-lg;\r\n            }\r\n        }\r\n        h5 {\r\n            margin-bottom: 2px;\r\n            a {\r\n                color: $dark;\r\n            }\r\n        }\r\n    }\r\n}\r\n\r\n// Rightbar overlay\r\n.rightbar-overlay {\r\n    background-color: rgba($dark, 0.4);\r\n    position: absolute;\r\n    left: 0;\r\n    right: 0;\r\n    top: 0;\r\n    bottom: 0;\r\n    display: none;\r\n    z-index: 9998;\r\n    transition: all .2s ease-out;\r\n}\r\n\r\n.right-bar-enabled {\r\n    .right-bar {\r\n        right: 0;\r\n    }\r\n    .rightbar-overlay {\r\n        display: block;\r\n    }\r\n}\r\n\r\n@include media-breakpoint-down(sm) {\r\n    .right-bar {\r\n        overflow: auto;\r\n        .slimscroll-menu {\r\n            height: auto !important;\r\n        }\r\n    }\r\n}\r\n\r\n.activity-widget{\r\n    .activity-list{\r\n        position: relative;\r\n        border-left: 2px dashed $gray-400;\r\n        padding-left: 24px;\r\n        padding-bottom: 20px;\r\n        &::after{\r\n            content: \"\";\r\n            position: absolute;\r\n            left: -7px;\r\n            top: 6px;\r\n            width: 12px;\r\n            height: 12px;\r\n            background-color: $card-bg;\r\n            border: 2px solid $primary;\r\n            border-radius: 50%;\r\n        }\r\n    }\r\n}\r\n", "//\r\n// layouts.scss\r\n//\r\n\r\nbody {\r\n    &.boxed-layout {\r\n        background-color: $boxed-body-bg;\r\n        #wrapper {\r\n            background-color: $body-bg;\r\n            max-width: $boxed-layout-width;\r\n            margin: 0 auto;\r\n            box-shadow: $box-shadow;\r\n        }\r\n\r\n        .navbar-custom {\r\n            max-width: $boxed-layout-width;\r\n            margin: 0 auto;\r\n        }\r\n\r\n        .footer {\r\n            margin: 0 auto;\r\n            max-width: calc(#{$boxed-layout-width} - #{$leftbar-width});\r\n        }\r\n\r\n        &.enlarged {\r\n            .footer {\r\n                max-width: calc(#{$boxed-layout-width} - #{$leftbar-width-collapsed});\r\n            }\r\n        }\r\n    }\r\n}\r\n\r\n@media (min-width: 992px) {\r\n    // Unsticky Layout\r\n    .unsticky-layout {\r\n        .left-side-menu, .navbar-custom {\r\n            position: absolute;\r\n        }\r\n    }\r\n}", "//\n// helper.scss\n//\n\n// Minimum width\n\n.width-xs {\n  min-width: 80px;\n}\n\n.width-sm {\n  min-width: 95px;\n}\n\n.width-md {\n  min-width: 110px;\n}\n\n.width-lg {\n  min-width: 140px;\n}\n\n.width-xl {\n  min-width: 160px;\n}\n\n\n// Font Family\n.font-family-secondary {\n  font-family: $font-family-secondary;\n}\n\n// avatar height\n.avatar-xs {\n  height: 1.5rem;\n  width: 1.5rem;\n}\n\n.avatar-sm {\n  height: 2.25rem;\n  width: 2.25rem;\n}\n\n.avatar-md {\n  height: 3.5rem;\n  width: 3.5rem;\n}\n\n.avatar-lg {\n  height: 4.5rem;\n  width: 4.5rem;\n}\n\n.avatar-xl {\n  height: 6rem;\n  width: 6rem;\n}\n\n.avatar-xxl {\n  height: 7.5rem;\n  width: 7.5rem;\n}\n\n.avatar-title {\n  align-items: center;\n  color: $white;\n  display: flex;\n  height: 100%;\n  justify-content: center;\n  width: 100%;\n}\n\n.avatar-group {\n  padding-left: 12px;\n  .avatar-group-item {\n    margin: 0 0 10px -12px;\n    display: inline-block;\n    border: 2px solid $white;\n    border-radius: 50%;\n  }\n}\n\n\n// Font weight help class\n\n.font-weight-medium {\n  font-weight: 500;\n}\n\n.font-weight-semibold {\n  font-weight: 600;\n}\n\n\n// Text specify lines (Only chrome browser support)\n\n.sp-line-1,\n.sp-line-2,\n.sp-line-3,\n.sp-line-4 {\n  overflow: hidden;\n  text-overflow: ellipsis;\n  display: -webkit-box;\n  -webkit-box-orient: vertical;\n}\n\n.sp-line-1 {\n  -webkit-line-clamp: 1;\n}\n\n.sp-line-2 {\n  -webkit-line-clamp: 2;\n}\n\n\n.sp-line-3 {\n  -webkit-line-clamp: 3;\n}\n\n.sp-line-4 {\n  -webkit-line-clamp: 4;\n}\n\n// pull in\n\n.pull-in {\n  margin-left: -$card-spacer-x;\n  margin-right: -$card-spacer-x;\n}", "\r\n// \r\n// social.scss\r\n//\r\n\r\n.social-list-item {\r\n    height: 2rem;\r\n    width: 2rem;\r\n    line-height: calc(2rem - 4px);\r\n    display: block;\r\n    border: 2px solid $gray-500;\r\n    border-radius: 50%;\r\n    color: $gray-500;\r\n}  ", "// \r\n// widgets.scss\r\n//\r\n\r\n\r\n// Simple tile box\r\n\r\n.tilebox-two{\r\n    i{\r\n        font-size: 48px;\r\n        opacity: 0.2;\r\n        margin-top: 14px;\r\n    }\r\n}\r\n\r\n.user-position {\r\n    position: absolute;\r\n    top: 0;\r\n    bottom: 0;\r\n    width: 44px;\r\n    font-size: 16px;\r\n    text-align: center;\r\n    right: 0;\r\n    left: auto;\r\n    display: flex;\r\n    align-items: center;\r\n    justify-content: center;\r\n    flex-direction: row;\r\n  \r\n    span {\r\n      transform: rotate(90deg);\r\n    }\r\n}\r\n\r\n// Inbox-widget(Used Profile)\r\n.inbox-widget {\r\n    .inbox-item {\r\n        overflow: hidden;\r\n        padding: 12px 0px;\r\n        position: relative;\r\n\r\n        .inbox-item-img {\r\n            display: block;\r\n            float: left;\r\n            margin-right: 15px;\r\n            margin-top: 2px;\r\n\r\n            img {\r\n                width: 40px;\r\n            }\r\n        }\r\n\r\n        .inbox-item-author {\r\n            display: block;\r\n            margin-bottom: 0px;\r\n            color: $gray-700;\r\n        }\r\n\r\n        .inbox-item-text {\r\n            color: $gray-500;\r\n            display: block;\r\n            margin: 0;\r\n            overflow: hidden;\r\n        }\r\n\r\n        .inbox-item-date {\r\n            color: $gray-600;\r\n            font-size: 0.6875rem;\r\n            position: absolute;\r\n            right: 5px;\r\n            top: 10px;\r\n        }\r\n    }\r\n}", "// \r\n// Custom-checkbox.scss\r\n//\r\n\r\n\r\n.checkbox {\r\n    label {\r\n        display: inline-block;\r\n        padding-left: 8px;\r\n        position: relative;\r\n        font-weight: normal;\r\n        &::before {\r\n            background-color: $card-bg;\r\n            border-radius: 3px;\r\n            border: 2px solid $gray-300;\r\n            content: \"\";\r\n            display: inline-block;\r\n            height: 18px;\r\n            left: 0;\r\n            margin-left: -18px;\r\n            position: absolute;\r\n            transition: 0.3s ease-in-out;\r\n            width: 18px;\r\n            outline: none !important;\r\n            top: 2px;\r\n        }\r\n        &::after {\r\n            color: $gray-700;\r\n            display: inline-block;\r\n            font-size: 11px;\r\n            height: 18px;\r\n            left: 0;\r\n            margin-left: -18px;\r\n            padding-left: 3px;\r\n            padding-top: 2px;\r\n            position: absolute;\r\n            top: 0;\r\n            width: 18px;\r\n        }\r\n    }\r\n    input[type=\"checkbox\"] {\r\n        cursor: pointer;\r\n        opacity: 0;\r\n        z-index: 1;\r\n        outline: none !important;\r\n        &:disabled+label {\r\n            opacity: 0.65;\r\n        }\r\n    }\r\n    input[type=\"checkbox\"]:focus+label {\r\n        &::before {\r\n            outline-offset: -2px;\r\n            outline: none;\r\n        }\r\n    }\r\n    input[type=\"checkbox\"]:checked+label {\r\n        &::after {\r\n            content: \"\";\r\n            position: absolute;\r\n            top: 6px;\r\n            left: 7px;\r\n            display: table;\r\n            width: 4px;\r\n            height: 8px;\r\n            border: 2px solid $gray-600;\r\n            border-top-width: 0;\r\n            border-left-width: 0;\r\n            -webkit-transform: rotate(45deg);\r\n            -ms-transform: rotate(45deg);\r\n            -o-transform: rotate(45deg);\r\n            transform: rotate(45deg);\r\n        }\r\n    }\r\n    input[type=\"checkbox\"]:disabled+label {\r\n        &::before {\r\n            background-color: $light;\r\n            cursor: not-allowed;\r\n        }\r\n    }\r\n}\r\n\r\n.checkbox.checkbox-circle {\r\n    label {\r\n        &::before {\r\n            border-radius: 50%;\r\n        }\r\n    }\r\n}\r\n\r\n.checkbox.checkbox-inline {\r\n    margin-top: 0;\r\n}\r\n\r\n.checkbox.checkbox-single {\r\n    input {\r\n        height: 18px;\r\n        width: 18px;\r\n        position: absolute;\r\n    }\r\n    label {\r\n        height: 18px;\r\n        width: 18px;\r\n        &:before {\r\n            margin-left: 0;\r\n        }\r\n        &:after {\r\n            margin-left: 0;\r\n        }\r\n    }\r\n}\r\n\r\n@each $color,\r\n$value in $theme-colors {\r\n    .checkbox-#{$color} {\r\n        input[type=\"checkbox\"]:checked+label {\r\n            &::before {\r\n                background-color: $value;\r\n                border-color: $value;\r\n            }\r\n            &::after {\r\n                border-color: $white;\r\n            }\r\n        }\r\n    }\r\n}", "// \r\n// custom-radio.scss\r\n//\r\n\r\n.radio {\r\n    label {\r\n        display: inline-block;\r\n        padding-left: 8px;\r\n        position: relative;\r\n        font-weight: normal;\r\n        &::before {\r\n            -o-transition: border 0.5s ease-in-out;\r\n            -webkit-transition: border 0.5s ease-in-out;\r\n            background-color: $card-bg;\r\n            border-radius: 50%;\r\n            border: 2px solid $gray-300;\r\n            content: \"\";\r\n            display: inline-block;\r\n            height: 18px;\r\n            left: 0;\r\n            margin-left: -18px;\r\n            position: absolute;\r\n            transition: border 0.5s ease-in-out;\r\n            width: 18px;\r\n            outline: none !important;\r\n        }\r\n        &::after {\r\n            -moz-transition: -moz-transform 0.1s cubic-bezier(0.8, -0.33, 0.2, 1.33);\r\n            -ms-transform: scale(0, 0);\r\n            -o-transform: scale(0, 0);\r\n            -o-transition: -o-transform 0.1s cubic-bezier(0.8, -0.33, 0.2, 1.33);\r\n            -webkit-transform: scale(0, 0);\r\n            -webkit-transition: -webkit-transform 0.1s cubic-bezier(0.8, -0.33, 0.2, 1.33);\r\n            background-color: $gray-600;\r\n            border-radius: 50%;\r\n            content: \" \";\r\n            display: inline-block;\r\n            height: 10px;\r\n            left: 6px;\r\n            margin-left: -20px;\r\n            position: absolute;\r\n            top: 4px;\r\n            transform: scale(0, 0);\r\n            transition: transform 0.1s cubic-bezier(0.8, -0.33, 0.2, 1.33);\r\n            width: 10px;\r\n        }\r\n    }\r\n    input[type=\"radio\"] {\r\n        cursor: pointer;\r\n        opacity: 0;\r\n        z-index: 1;\r\n        outline: none !important;\r\n        &:disabled+label {\r\n            opacity: 0.65;\r\n        }\r\n    }\r\n    input[type=\"radio\"]:focus+label {\r\n        &::before {\r\n            outline-offset: -2px;\r\n            outline: 5px auto -webkit-focus-ring-color;\r\n            outline: thin dotted;\r\n        }\r\n    }\r\n    input[type=\"radio\"]:checked+label {\r\n        &::after {\r\n            -ms-transform: scale(1, 1);\r\n            -o-transform: scale(1, 1);\r\n            -webkit-transform: scale(1, 1);\r\n            transform: scale(1, 1);\r\n        }\r\n    }\r\n    input[type=\"radio\"]:disabled+label {\r\n        &::before {\r\n            cursor: not-allowed;\r\n        }\r\n    }\r\n\r\n    &.radio-inline {\r\n        margin-top: 0;\r\n    }\r\n\r\n    &.radio-single {\r\n        label {\r\n            height: 18px;\r\n            width: 18px;\r\n        }\r\n    }\r\n}\r\n\r\n\r\n@each $color,\r\n$value in $theme-colors {\r\n    .radio-#{$color} {\r\n        input[type=\"radio\"]+label {\r\n            &::after {\r\n                background-color: $value;\r\n            }\r\n        }\r\n        input[type=\"radio\"]:checked+label {\r\n            &::before {\r\n                border-color: $value;\r\n            }\r\n            &::after {\r\n                background-color: $value;\r\n            }\r\n        }\r\n    }\r\n}", "// \r\n// print.scss\r\n//\r\n\r\n// Used invoice page\r\n@media print {\r\n    .left-side-menu,\r\n    .right-bar,\r\n    .page-title-box,\r\n    .navbar-custom,\r\n    .footer {\r\n        display: none;\r\n    }\r\n    .card-body,\r\n    .content-page,\r\n    .right-bar,\r\n    .content,\r\n    body {\r\n        padding: 0;\r\n        margin: 0;\r\n    }\r\n}", "/*!\r\n * Waves v0.7.6\r\n * http://fian.my.id/Waves \r\n * \r\n * Copyright 2014-2018 <PERSON><PERSON><PERSON> Si<PERSON> and other contributors \r\n * Released under the MIT license \r\n * https://github.com/fians/Waves/blob/master/LICENSE */\r\n .waves-effect {\r\n  position: relative;\r\n  cursor: pointer;\r\n  display: inline-block;\r\n  overflow: hidden;\r\n  -webkit-user-select: none;\r\n  -moz-user-select: none;\r\n  -ms-user-select: none;\r\n  user-select: none;\r\n  -webkit-tap-highlight-color: transparent;\r\n}\r\n.waves-effect .waves-ripple {\r\n  position: absolute;\r\n  border-radius: 50%;\r\n  width: 100px;\r\n  height: 100px;\r\n  margin-top: -50px;\r\n  margin-left: -50px;\r\n  opacity: 0;\r\n  background: rgba(0, 0, 0, 0.2);\r\n  background: -webkit-radial-gradient(rgba(0, 0, 0, 0.2) 0, rgba(0, 0, 0, 0.3) 40%, rgba(0, 0, 0, 0.4) 50%, rgba(0, 0, 0, 0.5) 60%, rgba(255, 255, 255, 0) 70%);\r\n  background: -o-radial-gradient(rgba(0, 0, 0, 0.2) 0, rgba(0, 0, 0, 0.3) 40%, rgba(0, 0, 0, 0.4) 50%, rgba(0, 0, 0, 0.5) 60%, rgba(255, 255, 255, 0) 70%);\r\n  background: -moz-radial-gradient(rgba(0, 0, 0, 0.2) 0, rgba(0, 0, 0, 0.3) 40%, rgba(0, 0, 0, 0.4) 50%, rgba(0, 0, 0, 0.5) 60%, rgba(255, 255, 255, 0) 70%);\r\n  background: radial-gradient(rgba(0, 0, 0, 0.2) 0, rgba(0, 0, 0, 0.3) 40%, rgba(0, 0, 0, 0.4) 50%, rgba(0, 0, 0, 0.5) 60%, rgba(255, 255, 255, 0) 70%);\r\n  -webkit-transition: all 0.5s ease-out;\r\n  -moz-transition: all 0.5s ease-out;\r\n  -o-transition: all 0.5s ease-out;\r\n  transition: all 0.5s ease-out;\r\n  -webkit-transition-property: -webkit-transform, opacity;\r\n  -moz-transition-property: -moz-transform, opacity;\r\n  -o-transition-property: -o-transform, opacity;\r\n  transition-property: transform, opacity;\r\n  -webkit-transform: scale(0) translate(0, 0);\r\n  -moz-transform: scale(0) translate(0, 0);\r\n  -ms-transform: scale(0) translate(0, 0);\r\n  -o-transform: scale(0) translate(0, 0);\r\n  transform: scale(0) translate(0, 0);\r\n  pointer-events: none;\r\n}\r\n.waves-effect.waves-light .waves-ripple {\r\n  background: rgba(255, 255, 255, 0.4);\r\n  background: -webkit-radial-gradient(rgba(255, 255, 255, 0.2) 0, rgba(255, 255, 255, 0.3) 40%, rgba(255, 255, 255, 0.4) 50%, rgba(255, 255, 255, 0.5) 60%, rgba(255, 255, 255, 0) 70%);\r\n  background: -o-radial-gradient(rgba(255, 255, 255, 0.2) 0, rgba(255, 255, 255, 0.3) 40%, rgba(255, 255, 255, 0.4) 50%, rgba(255, 255, 255, 0.5) 60%, rgba(255, 255, 255, 0) 70%);\r\n  background: -moz-radial-gradient(rgba(255, 255, 255, 0.2) 0, rgba(255, 255, 255, 0.3) 40%, rgba(255, 255, 255, 0.4) 50%, rgba(255, 255, 255, 0.5) 60%, rgba(255, 255, 255, 0) 70%);\r\n  background: radial-gradient(rgba(255, 255, 255, 0.2) 0, rgba(255, 255, 255, 0.3) 40%, rgba(255, 255, 255, 0.4) 50%, rgba(255, 255, 255, 0.5) 60%, rgba(255, 255, 255, 0) 70%);\r\n}\r\n.waves-effect.waves-classic .waves-ripple {\r\n  background: rgba(0, 0, 0, 0.2);\r\n}\r\n.waves-effect.waves-classic.waves-light .waves-ripple {\r\n  background: rgba(255, 255, 255, 0.4);\r\n}\r\n.waves-notransition {\r\n  -webkit-transition: none !important;\r\n  -moz-transition: none !important;\r\n  -o-transition: none !important;\r\n  transition: none !important;\r\n}\r\n.waves-button,\r\n.waves-circle {\r\n  -webkit-transform: translateZ(0);\r\n  -moz-transform: translateZ(0);\r\n  -ms-transform: translateZ(0);\r\n  -o-transform: translateZ(0);\r\n  transform: translateZ(0);\r\n  -webkit-mask-image: -webkit-radial-gradient(circle, white 100%, black 100%);\r\n}\r\n.waves-button,\r\n.waves-button:hover,\r\n.waves-button:visited,\r\n.waves-button-input {\r\n  white-space: nowrap;\r\n  vertical-align: middle;\r\n  cursor: pointer;\r\n  border: none;\r\n  outline: none;\r\n  color: inherit;\r\n  background-color: rgba(0, 0, 0, 0);\r\n  font-size: 1em;\r\n  line-height: 1em;\r\n  text-align: center;\r\n  text-decoration: none;\r\n  z-index: 1;\r\n}\r\n.waves-button {\r\n  padding: 0.85em 1.1em;\r\n  border-radius: 0.2em;\r\n}\r\n.waves-button-input {\r\n  margin: 0;\r\n  padding: 0.85em 1.1em;\r\n}\r\n.waves-input-wrapper {\r\n  border-radius: 0.2em;\r\n  vertical-align: bottom;\r\n}\r\n.waves-input-wrapper.waves-button {\r\n  padding: 0;\r\n}\r\n.waves-input-wrapper .waves-button-input {\r\n  position: relative;\r\n  top: 0;\r\n  left: 0;\r\n  z-index: 1;\r\n}\r\n.waves-circle {\r\n  text-align: center;\r\n  width: 2.5em;\r\n  height: 2.5em;\r\n  line-height: 2.5em;\r\n  border-radius: 50%;\r\n}\r\n.waves-float {\r\n  -webkit-mask-image: none;\r\n  -webkit-box-shadow: 0px 1px 1.5px 1px rgba(0, 0, 0, 0.12);\r\n  box-shadow: 0px 1px 1.5px 1px rgba(0, 0, 0, 0.12);\r\n  -webkit-transition: all 300ms;\r\n  -moz-transition: all 300ms;\r\n  -o-transition: all 300ms;\r\n  transition: all 300ms;\r\n}\r\n.waves-float:active {\r\n  -webkit-box-shadow: 0px 8px 20px 1px rgba(0, 0, 0, 0.3);\r\n  box-shadow: 0px 8px 20px 1px rgba(0, 0, 0, 0.3);\r\n}\r\n.waves-block {\r\n  display: block;\r\n}\r\n", "//\r\n// slimscroll.scss\r\n//\r\n\r\n.slimScrollDiv {\r\n    height: auto !important;\r\n}", "//\r\n// toastr.scss\r\n//\r\n\r\n\r\n/* =============\r\n   Notification\r\n============= */\r\n#toast-container {\r\n    > div {\r\n        box-shadow: $box-shadow;\r\n        opacity: 1;\r\n        &:hover {\r\n            box-shadow: $box-shadow;\r\n            opacity: 0.9;\r\n        }\r\n    }\r\n}\r\n\r\n\r\n@each $color, $value in $theme-colors {\r\n    .toast-#{$color} {\r\n        border: 2px solid $value !important;\r\n        background-color: rgba(($value), 0.8) !important;\r\n    }\r\n}\r\n\r\n\r\n// for error\r\n\r\n.toast-error {\r\n    background-color: rgba($danger,0.8);\r\n    border: 2px solid $danger;\r\n}", "\r\n// \r\n// sweetalert.scss\r\n//\r\n\r\n.swal2-modal {\r\n  font-family: $font-family-base;\r\n  box-shadow: 0 10px 33px rgba(0,0,0,.1);\r\n\r\n  .swal2-title {\r\n    font-size: 24px;\r\n  }\r\n  .swal2-content {\r\n    font-size: 16px;\r\n  }\r\n  .swal2-spacer {\r\n    margin: 10px 0;\r\n  }\r\n  .swal2-file, .swal2-input, .swal2-textarea {\r\n    border: 2px solid $gray-300;\r\n    font-size: 16px;\r\n    box-shadow: none;\r\n  }\r\n  .swal2-confirm.btn-confirm {\r\n    background-color: $primary !important;\r\n    font-size: $font-size-base;\r\n  }\r\n\r\n  .swal2-cancel.btn-cancel {\r\n    background-color: $danger !important;\r\n    font-size: $font-size-base;\r\n  }\r\n\r\n  .swal2-styled:focus {\r\n    box-shadow: none !important;\r\n  }\r\n}\r\n\r\n.swal2-icon.swal2-question {\r\n  color: $primary;\r\n  border-color: $primary;\r\n}\r\n\r\n.swal2-icon.swal2-success {\r\n  border-color: $success;\r\n\r\n  .line,[class^=swal2-success-line][class$=long],\r\n  [class^=swal2-success-line]{\r\n    background-color: $success;\r\n  }\r\n\r\n  .placeholder,.swal2-success-ring  {\r\n    border-color: $success;\r\n  }\r\n}\r\n\r\n\r\n.swal2-icon.swal2-warning {\r\n  color: $warning;\r\n  border-color: $warning;\r\n}\r\n\r\n.swal2-icon.swal2-error {\r\n  border-color: $danger;\r\n  .line {\r\n    background-color: $danger;\r\n  }\r\n}\r\n.swal2-modal .swal2-file:focus, .swal2-modal .swal2-input:focus, .swal2-modal .swal2-textarea:focus {\r\n  outline: 0;\r\n  border: 2px solid $primary;\r\n}\r\n\r\n.swal2-container.swal2-shown {\r\n  background-color: rgba($dark, 0.9);\r\n}\r\n", "//\r\n// ion-rangeslider.scss\r\n//\r\n.irs--modern{\r\n  .irs-bar, .irs-to, .irs-from, .irs-single {\r\n    background: $primary !important;\r\n  }\r\n  .irs-to, .irs-from, .irs-single{\r\n    &:before{\r\n      border-top-color: $primary;\r\n    }\r\n  }\r\n  .irs-line{\r\n    background: $gray-300;\r\n    border-color: $gray-300;\r\n  }\r\n\r\n  .irs-min, .irs-max{\r\n    color: $gray-500;\r\n    background: $gray-300;\r\n  }\r\n  .irs-grid-text{\r\n    font-size: 12px;\r\n    color: $gray-400;\r\n  }\r\n  .irs-handle {\r\n    > i{\r\n      &:nth-child(1){\r\n        width: 8px;\r\n        height: 8px;\r\n      }\r\n    }\r\n  }\r\n}", "\r\n\r\n/* =============\r\n   Rating\r\n============= */\r\n\r\n.rating-star{\r\n  i{\r\n    color: $gray-600;\r\n  }\r\n}\r\n\r\n.rating-md {\r\n  i {\r\n    font-size: 16px;\r\n  }\r\n}\r\n\r\n.rating-lg {\r\n  i {\r\n    font-size: 22px;\r\n  }\r\n}", "//\r\n// jstree.scss\r\n//\r\n\r\n.jstree-default {\r\n    .jstree-node,\r\n    .jstree-icon {\r\n        background-image: url(\"../images/plugins/jstree.png\");\r\n    }\r\n    .jstree-node {\r\n        background-position: -292px -4px;\r\n        background-repeat: repeat-y;\r\n    }\r\n    \r\n    .jstree-themeicon-custom {\r\n        background-color: transparent;\r\n        background-image: none;\r\n        background-position: 0 0;\r\n    }\r\n    \r\n    .jstree-anchor {\r\n        line-height: 28px;\r\n        height: 28px;\r\n    }\r\n    \r\n    &>.jstree-container-ul .jstree-loading>.jstree-ocl {\r\n        background: url(\"../images/plugins/loading.gif\") center center no-repeat;\r\n    }\r\n    \r\n    .jstree-icon:empty {\r\n        width: 24px;\r\n        height: 28px;\r\n        line-height: 28px;\r\n        font-size: 15px;\r\n        color: $gray-600;\r\n    }\r\n    \r\n    .jstree-clicked,\r\n    .jstree-wholerow-clicked {\r\n        background: rgba($primary, 0.2);\r\n        box-shadow: none;\r\n    }\r\n    \r\n    .jstree-hovered,\r\n    .jstree-wholerow-hovered {\r\n        background: rgba($primary, 0.25);\r\n        box-shadow: none;\r\n    }\r\n    \r\n    .jstree-last {\r\n        background: transparent;\r\n    }\r\n    \r\n    .jstree-wholerow {\r\n        height: 28px;\r\n    }\r\n}", "//\r\n// hopscotch.scss\r\n//\r\n\r\ndiv.hopscotch-bubble {\r\n    border: 3px solid $primary;\r\n    border-radius: 5px;\r\n    .hopscotch-next,\r\n    .hopscotch-prev {\r\n        background-color: $primary !important;\r\n        background-image: none !important;\r\n        border-color: $primary !important;\r\n        text-shadow: none !important;\r\n        margin: 0 0 0 5px !important;\r\n        font-family: $font-family-base;\r\n        color: $white !important;\r\n    }\r\n\r\n    .hopscotch-bubble-number {\r\n        background: $success;\r\n        padding: 0;\r\n        border-radius: 50%;\r\n    }\r\n    \r\n    .hopscotch-bubble-arrow-container {\r\n        &.left {\r\n            .hopscotch-bubble-arrow-border {\r\n                border-right: 19px solid $primary;\r\n            }\r\n\r\n            .hopscotch-bubble-arrow {\r\n                border: none;\r\n            }\r\n        }\r\n\r\n        &.right {\r\n            .hopscotch-bubble-arrow {\r\n                border-left: 19px solid $primary;\r\n                left: -2px;\r\n            }\r\n\r\n            .hopscotch-bubble-arrow-border {\r\n                border-left: 0 solid $primary;\r\n            }\r\n        }\r\n        &.up {\r\n            .hopscotch-bubble-arrow {\r\n                border-bottom: 19px solid $primary;\r\n                top: 0;\r\n            }\r\n\r\n            .hopscotch-bubble-arrow-border {\r\n                border-bottom: 0 solid rgba(0, 0, 0, .5);\r\n            }\r\n        }\r\n        &.down {\r\n            .hopscotch-bubble-arrow {\r\n                border-top: 19px solid $primary;\r\n                top: -2px;\r\n            }\r\n            .hopscotch-bubble-arrow-border {\r\n                border-top: 0 solid rgba(0, 0, 0, .5);\r\n            }\r\n        }\r\n    }\r\n    \r\n    h3 {\r\n        font-family: $font-family-secondary;\r\n        margin-bottom: 10px;\r\n        font-weight: $font-weight-semibold;\r\n    }\r\n    .hopscotch-content {\r\n        font-family: $font-family-base;\r\n    }\r\n}", "// \r\n// calendar.scss\r\n//\r\n\r\n.calendar {\r\n    float: left;\r\n    margin-bottom: 0;\r\n}\r\n\r\n.fc-view {\r\n    margin-top: 30px;\r\n}\r\n\r\n.none-border {\r\n    .modal-footer {\r\n        border-top: none;\r\n    }\r\n}\r\n\r\n.fc-toolbar {\r\n    h2 {\r\n        font-size: 1.25rem;\r\n        line-height: 1.875rem;\r\n        text-transform: uppercase;\r\n    }\r\n}\r\n\r\n.fc-day-grid-event {\r\n    .fc-time {\r\n        font-weight: $font-weight-medium;\r\n    }\r\n}\r\n\r\n.fc-day {\r\n    background: transparent;\r\n}\r\n\r\n.fc-toolbar {\r\n    .fc-state-active,\r\n    .ui-state-active,\r\n    button:focus,\r\n    button:hover,\r\n    .ui-state-hover {\r\n        z-index: 0;\r\n    }\r\n}\r\n\r\n.fc {\r\n    th.fc-widget-header {\r\n        background: $gray-300;\r\n        font-size: 13px;\r\n        line-height: 20px;\r\n        padding: 10px 0;\r\n        text-transform: uppercase;\r\n        font-weight: $font-weight-semibold;\r\n    }\r\n}\r\n\r\n.fc-unthemed {\r\n    th,\r\n    td,\r\n    thead,\r\n    tbody,\r\n    .fc-divider,\r\n    .fc-row,\r\n    .fc-popover {\r\n        border-color: $gray-300;\r\n    }\r\n\r\n    td.fc-today, .fc-divider{\r\n        background: $gray-300 !important;\r\n    }\r\n}\r\n\r\n\r\n.fc-button {\r\n    background: $gray-300;\r\n    border: none;\r\n    color: $gray-700;\r\n    text-transform: capitalize;\r\n    box-shadow: none;\r\n    border-radius: 3px !important;\r\n    margin: 0 3px !important;\r\n    padding: 6px 12px !important;\r\n    height: auto !important;\r\n}\r\n\r\n.fc-text-arrow {\r\n    font-family: inherit;\r\n    font-size: 1rem;\r\n}\r\n\r\n.fc-state-hover {\r\n    background: $gray-300;\r\n}\r\n\r\n.fc-state-highlight {\r\n    background: $gray-300;\r\n}\r\n\r\n.fc-state-down,\r\n.fc-state-active,\r\n.fc-state-disabled {\r\n    background-color: $primary;\r\n    color: $white;\r\n    text-shadow: none;\r\n}\r\n\r\n.fc-cell-overlay {\r\n    background: $gray-300;\r\n}\r\n\r\n.fc-unthemed {\r\n    td.fc-today {\r\n        background: $white;\r\n    }\r\n}\r\n\r\n.fc-event {\r\n    border-radius: 2px;\r\n    border: none;\r\n    cursor: move;\r\n    font-size: 0.8125rem;\r\n    margin: 5px 7px;\r\n    padding: 5px 5px;\r\n    text-align: center;\r\n}\r\n\r\n.external-event {\r\n    cursor: move;\r\n    margin: 10px 0;\r\n    padding: 8px 10px;\r\n    color: $white;\r\n    border-radius: 4px;\r\n}\r\n\r\n.fc-basic-view {\r\n    td.fc-week-number {\r\n        span {\r\n            padding-right: 8px;\r\n        }\r\n    }\r\n    td.fc-day-number {\r\n        padding-right: 8px;\r\n    }\r\n    .fc-content {\r\n        color: $white;\r\n    }\r\n}\r\n\r\n.fc-time-grid-event {\r\n    .fc-content {\r\n        color: $white;\r\n    }\r\n}\r\n\r\n@include media-breakpoint-down(sm) {\r\n    .fc-toolbar {\r\n        .fc-left,.fc-right,.fc-center {\r\n            float: none;\r\n            display: block;\r\n            clear: both;\r\n            margin: 10px 0;\r\n        }\r\n    }\r\n    .fc {\r\n        .fc-toolbar{\r\n            >* {\r\n                >* {\r\n                    float: none;\r\n                }\r\n            }\r\n        }\r\n    }\r\n    .fc-today-button {\r\n        display: none;\r\n    }\r\n}", "\r\n//\r\n// bootstrap-taginput.scss\r\n//\r\n\r\n/* Bootstrap tagsinput */\r\n.bootstrap-tagsinput {\r\n  box-shadow: none;\r\n  padding: 4px 7px 4px;\r\n  width: 100%;\r\n  background-color: $input-bg;\r\n  border-color: $input-border-color;\r\n  \r\n  input{\r\n    color: $input-color;\r\n  }\r\n\r\n  .label-info {\r\n    background-color: $primary;\r\n    display: inline-block;\r\n    font-size: 11px;\r\n    margin: 3px 1px;\r\n    padding: 0 5px;\r\n    border-radius: 3px;\r\n    font-weight: $font-weight-medium;\r\n  }\r\n}", "//\r\n// multi-select.scss\r\n//\r\n\r\n.ms-container {\r\n    background: transparent url('../images/plugins/multiple-arrow.png') no-repeat 50% 50%;\r\n    width: auto;\r\n    max-width: 370px;\r\n    \r\n    .ms-list {\r\n        box-shadow: none;\r\n        border: $input-border-width solid $input-border-color;\r\n\r\n        &.ms-focus {\r\n            box-shadow: none;\r\n            border: $input-border-width solid $input-focus-border-color;\r\n        }\r\n    }\r\n    .ms-selectable {\r\n        background-color: $input-bg;\r\n        li {\r\n            &.ms-elem-selectable {\r\n                border: none;\r\n                padding: 5px 10px;\r\n                color: $gray-600;\r\n            }\r\n            &.ms-hover {\r\n                background-color: $primary;\r\n                color: $white;\r\n            }\r\n        }\r\n    }\r\n    .ms-selection {\r\n        background-color: $input-bg;\r\n        li{\r\n            &.ms-elem-selection {\r\n                border: none;\r\n                padding: 5px 10px;\r\n                color: $gray-600;\r\n            }\r\n            &.ms-hover {\r\n                background-color: $primary;\r\n                color: $white;\r\n            }\r\n        }\r\n        \r\n    }\r\n}\r\n\r\n.search-input {\r\n    margin-bottom: 10px;\r\n}\r\n\r\n.ms-selectable {\r\n    box-shadow: none;\r\n    outline: none !important;\r\n}\r\n\r\n.ms-optgroup-label {\r\n    font-weight: $font-weight-medium;\r\n    font-family: $font-family-secondary;\r\n    color: $dark !important;\r\n    font-size: 13px;\r\n}", "//\r\n// Select2.scss\r\n//\r\n\r\n.select2-container {\r\n    .select2-selection--single {\r\n        background-color: $input-bg;\r\n        border: 1px solid $input-border-color;\r\n        height: 38px;\r\n        outline: none;\r\n        .select2-selection__rendered {\r\n            line-height: 36px;\r\n            padding-left: 12px;\r\n            color: $gray-600;\r\n        }\r\n        .select2-selection__arrow {\r\n            height: 34px;\r\n            width: 34px;\r\n            right: 3px;\r\n            b {\r\n                border-color: $gray-600 transparent transparent transparent;\r\n                border-width: 6px 6px 0 6px;\r\n            }\r\n        }\r\n    }\r\n}\r\n\r\n.select2-container--open {\r\n    .select2-selection--single {\r\n        .select2-selection__arrow {\r\n            b {\r\n                border-color: transparent transparent $gray-600 transparent !important;\r\n                border-width: 0 6px 6px 6px !important;\r\n            }\r\n        }\r\n    }\r\n}\r\n\r\n.select2-results__option {\r\n    padding: 6px 12px;\r\n}\r\n\r\n.select2-dropdown {\r\n    border: $dropdown-border-color;\r\n    background-color: $dropdown-bg;\r\n    box-shadow: $box-shadow;\r\n}\r\n\r\n.select2-container--default {\r\n    .select2-search--dropdown {\r\n        padding: 10px;\r\n        background-color: $dropdown-bg;\r\n        .select2-search__field {\r\n            border: 1px solid  $input-border-color;\r\n            background-color: $input-bg;\r\n            color: $gray-600;\r\n            outline: none;\r\n        }\r\n    }\r\n    .select2-results__group{\r\n        font-weight: $font-weight-medium;\r\n    }\r\n    .select2-results__option--highlighted[aria-selected] {\r\n        background-color: $primary;\r\n    }\r\n    .select2-results__option[aria-selected=true] {\r\n        background-color: $dropdown-link-active-bg;\r\n        color: $dropdown-link-active-color;\r\n        &:hover {\r\n            background-color: $primary;\r\n            color: $white;\r\n        }\r\n    }\r\n}\r\n\r\n.select2-container {\r\n    .select2-selection--multiple {\r\n        min-height: 38px;\r\n        background-color: $input-bg;\r\n        border: 1px solid $input-border-color !important;\r\n        .select2-selection__rendered {\r\n            padding: 1px 10px;\r\n        }\r\n        .select2-search__field {\r\n            border: 0;\r\n            color: $gray-600;\r\n            &::placeholder{\r\n                color: $gray-600;\r\n            }\r\n        }\r\n        .select2-selection__choice {\r\n            background-color: $primary;\r\n            border: none;\r\n            color: $white;\r\n            border-radius: 3px;\r\n            padding: 0 7px;\r\n            margin-top: 7px;\r\n        }\r\n        .select2-selection__choice__remove {\r\n            color: $white;\r\n            margin-right: 5px;\r\n            &:hover {\r\n                color: $white;\r\n            }\r\n        }\r\n    }\r\n}", "//\r\n// autocomplete.scss\r\n//\r\n \r\n.autocomplete-suggestions {\r\n    border: 1px solid $gray-200;\r\n    background: lighten($gray-200, 2%);\r\n    cursor: default;\r\n    overflow: auto;\r\n    max-height: 200px !important;\r\n    box-shadow: $box-shadow;\r\n    strong {\r\n      font-weight: $font-weight-semibold;\r\n      color: $dark;\r\n  }\r\n}\r\n  \r\n.autocomplete-suggestion {\r\n  padding: 5px 10px;\r\n  white-space: nowrap;\r\n  overflow: hidden;\r\n}\r\n  \r\n.autocomplete-no-suggestion {\r\n  padding: 5px;\r\n}\r\n  \r\n.autocomplete-selected {\r\n  background: $gray-200;\r\n  cursor: pointer;\r\n}\r\n  \r\n  \r\n.autocomplete-group {\r\n  padding: 5px;\r\n  font-weight: $font-weight-medium;\r\n  font-family: $font-family-secondary;\r\n  strong {\r\n    font-weight: $font-weight-semibold;\r\n    font-size: 16px;\r\n    color: $dark;\r\n    display: block;\r\n  }\r\n}", "\r\n/* =============\r\n   Form validation\r\n============= */\r\n\r\n.parsley-error {\r\n  border-color: $danger !important;\r\n}\r\n.parsley-errors-list {\r\n  margin: 0;\r\n  padding: 0;\r\n  > li {\r\n    font-size: 12px;\r\n    list-style: none;\r\n    color: $danger;\r\n    margin-top: 5px;\r\n  }\r\n}", "\r\n// \r\n// timepicker.scss\r\n//\r\n\r\n\r\n.bootstrap-timepicker-widget {\r\n    table {\r\n        td {\r\n            input {\r\n                border: 1px solid rgba($dark, 0.3);\r\n                width: 35px;\r\n                background-color: $input-bg;\r\n                color: $gray-600;\r\n            }\r\n\r\n            a{\r\n                color: $gray-500;\r\n                &:hover{\r\n                    background-color: lighten($gray-200, 2%);\r\n                    border-color: lighten($gray-200, 2%);\r\n                }\r\n            }\r\n        }\r\n    }\r\n    &.dropdown-menu:after{\r\n        border-bottom-color: $gray-200;\r\n    }\r\n    &.timepicker-orient-bottom:after{\r\n        border-top-color: $gray-200;\r\n    }\r\n}", "//\r\n// Datepicker\r\n//\r\n.datepicker {\r\n    padding: 10px !important;\r\n    td,\r\n    th {\r\n        width: 30px;\r\n        height: 30px;\r\n        padding: 5px;\r\n    }\r\n\r\n    th{\r\n        font-weight: $font-weight-semibold;\r\n    }\r\n\r\n    table {\r\n        tr {\r\n            td {\r\n                &.active.active,\r\n                &.active.disabled,\r\n                &.active.disabled.active,\r\n                &.active.disabled.disabled,\r\n                &.active.disabled:active,\r\n                &.active.disabled:hover,\r\n                &.active.disabled:hover.active,\r\n                &.active.disabled:hover.disabled,\r\n                &.active.disabled:hover:active,\r\n                &.active.disabled:hover:hover,\r\n                .active.disabled:hover[disabled],\r\n                .active.disabled[disabled],\r\n                .active:active,\r\n                .active:hover,\r\n                .active:hover.active,\r\n                .active:hover.disabled,\r\n                .active:hover:active,\r\n                .active:hover:hover,\r\n                .active:hover[disabled],\r\n                .active[disabled],\r\n                span.active.active,\r\n                span.active.disabled,\r\n                span.active.disabled.active,\r\n                span.active.disabled.disabled,\r\n                span.active.disabled:active,\r\n                span.active.disabled:hover,\r\n                span.active.disabled:hover.active,\r\n                span.active.disabled:hover.disabled,\r\n                span.active.disabled:hover:active,\r\n                span.active.disabled:hover:hover,\r\n                span.active.disabled:hover[disabled],\r\n                span.active.disabled[disabled],\r\n                span.active:active,\r\n                span.active:hover,\r\n                span.active:hover.active,\r\n                span.active:hover.disabled,\r\n                span.active:hover:active,\r\n                span.active:hover:hover,\r\n                span.active:hover[disabled],\r\n                span.active[disabled],\r\n                &.today,\r\n                &.today.disabled,\r\n                &.today.disabled:hover,\r\n                &.today:hover,\r\n                &.selected {\r\n                    background-color: $primary !important;\r\n                    background-image: none !important;\r\n                    color: $white;\r\n                }\r\n\r\n                &.day.focused,\r\n                &.day:hover,\r\n                span.focused,\r\n                span:hover {\r\n                    background: $gray-200;\r\n                }\r\n\r\n                &.new,\r\n                &.old,\r\n                span.new,\r\n                span.old {\r\n                    color: $gray-500;\r\n                    opacity: 0.6;\r\n                }\r\n\r\n                &.range, &.range.disabled, &.range.disabled:hover, &.range:hover{\r\n                    background-color: lighten($gray-200, 4%);\r\n                }\r\n            }\r\n        }\r\n    }\r\n    \r\n    .datepicker-switch:hover,\r\n    .next:hover,\r\n    .prev:hover,\r\n    tfoot tr th:hover {\r\n        background: $gray-200;\r\n    }\r\n    .datepicker-switch {\r\n        &:hover {\r\n            background: none;\r\n        }\r\n    }\r\n}\r\n\r\n.datepicker-inline {\r\n    border: 2px solid rgba($dark, 0.10);\r\n}\r\n\r\n.datepicker-dropdown {\r\n\r\n    &:after {\r\n        border-bottom: 6px solid $dropdown-bg;\r\n    }\r\n    &:before {\r\n        border-bottom-color: $dropdown-border-color;\r\n    }\r\n    &.datepicker-orient-top {\r\n        &:before {\r\n            border-top: 7px solid $dropdown-border-color;\r\n        }\r\n        &:after {\r\n            border-top: 6px solid $dropdown-bg;\r\n        }\r\n    }\r\n}", "//\r\n// Daterange\r\n//\r\n\r\n.daterangepicker {\r\n    font-family: $font-family-base;\r\n    background-color: $card-bg;\r\n    border-color: $gray-300;\r\n\r\n    .calendar-table{\r\n        background-color: $card-bg;\r\n        border-color: $gray-300;\r\n        .next, .prev{\r\n            span{\r\n                border-color: $gray-500;\r\n            }\r\n        }\r\n    }\r\n    \r\n    th, td{\r\n        padding: 5px;\r\n        &.week{\r\n            color: $gray-400;\r\n        }\r\n        &.available{\r\n            &:hover{\r\n                background-color: lighten($gray-200, 4%);\r\n            }\r\n        }\r\n    }\r\n\r\n    td.active, td.active:hover, .ranges li.active {\r\n        background-color: $primary;\r\n    }\r\n\r\n    .ranges li:hover{\r\n        background-color: $gray-300;\r\n    }\r\n\r\n    .month, .calendar-time{\r\n        select{\r\n            background-color: $input-bg;\r\n            border-color: $input-border-color;\r\n            color: $input-color;\r\n\r\n        }\r\n    }\r\n\r\n    td{\r\n        &.off, &.off.in-range, &.off.start-date,  &.off.end-date{\r\n            background-color: transparent;\r\n            color: rgba($gray-400, 0.7);\r\n        }\r\n\r\n        &.in-range{\r\n            background-color: lighten($gray-200, 4%);\r\n            color: $gray-600;\r\n        }\r\n    }\r\n\r\n    &.show-ranges.ltr {\r\n        .drp-calendar.left{\r\n            border-left-color: $gray-300;\r\n        }\r\n    }\r\n\r\n\r\n    .drp-buttons {\r\n        border-top-color: $gray-300;\r\n        .btn{\r\n            font-weight: $font-weight-medium;\r\n        }\r\n    }\r\n}", "//\r\n// clockpicker.scss\r\n//\r\n\r\n.clockpicker-popover {\r\n    .popover-title{\r\n        font-size: 16px;\r\n        font-weight: $font-weight-semibold;\r\n        background-color: lighten($gray-100, 4%);\r\n    }\r\n\r\n    .popover-content{\r\n        background-color: lighten($gray-200, 4%);\r\n    }\r\n\r\n    .clockpicker-plate{\r\n        background-color: lighten($gray-100, 4%);\r\n        border-color: $gray-200;\r\n    }\r\n\r\n    .clockpicker-tick{\r\n        color: $gray-500;\r\n    }\r\n    \r\n    .btn-default {\r\n        background-color: $primary;\r\n        color: $white;\r\n    }\r\n}", "//\r\n// form-wizard.scss\r\n//\r\n\r\n\r\n.wizard{\r\n  // steps\r\n\r\n  >.steps {\r\n      \r\n      position: relative;\r\n      display: block;\r\n      width: 100%;\r\n      >ul{\r\n          >li {\r\n              width: 25%;\r\n          }\r\n      }\r\n\r\n      a{\r\n        font-size: 16px;\r\n        margin: 0 0.5em 0.5em;\r\n      }\r\n\r\n      a, a:hover, a:active{\r\n          display: block;\r\n          width: auto;\r\n          padding: 1em 1em;\r\n          text-decoration: none;\r\n          border-radius: 2px;\r\n      }\r\n\r\n      .disabled{\r\n          a{\r\n            background: lighten($gray-200, 4%);\r\n            border: 1px solid $gray-200;\r\n            color: $dark;\r\n              cursor: default;\r\n            &:hover, &:active{\r\n              background: lighten($gray-200, 2%);\r\n              \r\n          }\r\n        }\r\n      }\r\n\r\n      .current{\r\n          a, a:hover, a:active{\r\n              background: $primary;\r\n              color: $white;\r\n              cursor: default;\r\n          }\r\n      }\r\n\r\n      .done{\r\n          a, a:hover, a:active{\r\n              background: $gray-200;\r\n              color: $dark;\r\n          }\r\n      }\r\n\r\n\r\n  }\r\n\r\n  >.steps, >.actions{\r\n      >ul{\r\n          >li {\r\n              float: left;\r\n              position: relative;\r\n          }\r\n      }\r\n  }\r\n\r\n  // content\r\n\r\n  >.content {\r\n      display: block;\r\n      margin: 0.5em;\r\n      min-height: 240px;\r\n      overflow: hidden;\r\n      position: relative;\r\n      width: auto;\r\n      padding: 20px;\r\n      border: 1px solid $gray-200;\r\n      >.body {\r\n          padding: 0;\r\n          position: relative;\r\n\r\n          ul {\r\n              list-style: disc !important;\r\n              >li {\r\n                  display: block;\r\n                  line-height: 30px;\r\n              }\r\n          }\r\n\r\n          >iframe {\r\n              border: 0 none;\r\n              width: 100%;\r\n              height: 100%;\r\n          }\r\n\r\n          input {\r\n              display: block;\r\n              border-color: $gray-300;\r\n\r\n              &:focus {\r\n                  border-color: $gray-300;\r\n              }\r\n              &[type=\"checkbox\"] {\r\n                  display: inline-block;\r\n              }\r\n\r\n              &.error {\r\n                  background: rgba($danger, 0.1);\r\n                  border: 1px solid lighten($danger, 32%);\r\n                  color: $danger;\r\n              }\r\n          }\r\n\r\n          label {\r\n              display: inline-block;\r\n              margin-bottom: 0.5em;\r\n              margin-top: 10px;\r\n              &.error {\r\n                  color: $danger;\r\n                  font-size: 12px;\r\n              }\r\n          }\r\n      }\r\n  }\r\n\r\n  // actions\r\n\r\n  >.actions {\r\n      position: relative;\r\n      display: block;\r\n      text-align: right;\r\n      width: 100%;\r\n      margin-top: 15px;\r\n      >ul {\r\n          display: inline-block;\r\n          text-align: right;\r\n          >li {\r\n              margin: 0 0.5em;\r\n          }\r\n      }\r\n\r\n      a, a:hover, a:active{\r\n          background: $primary;\r\n          color: $white;\r\n          display: block;\r\n          padding: 0.5em 1em;\r\n          text-decoration: none;\r\n          border-radius: 2px;\r\n      }\r\n\r\n      .disabled{\r\n          a, a:hover, a:active{\r\n              background: lighten($gray-200, 4%);\r\n              color: $dark;\r\n          }\r\n      }\r\n  }\r\n\r\n  // vertical wizard\r\n\r\n  &.vertical{\r\n      >.steps {\r\n          display: inline;\r\n          float: left;\r\n          width: 30%;\r\n          >ul{\r\n              >li {\r\n                  float: none;\r\n                  width: 100%;\r\n              }\r\n          }\r\n      }\r\n\r\n      > .content{\r\n        width: 65%;\r\n        margin: 0 2.5% 0.5em;\r\n        display: inline;\r\n        float: left;\r\n      }\r\n\r\n      >.actions {\r\n          display: inline;\r\n          float: right;\r\n          width: 95%;\r\n          margin: 0 2.5%;\r\n          margin-top: 15px !important;\r\n          >ul{\r\n              >li {\r\n                  margin: 0 0 0 1em;\r\n              }\r\n          }\r\n      }\r\n  }  \r\n}\r\n\r\n\r\n\r\n/*\r\n  Common \r\n*/\r\n\r\n.wizard, .tabcontrol {\r\n  display: block;\r\n  width: 100%;\r\n  overflow: hidden;\r\n  a{\r\n      outline: 0;\r\n  }\r\n\r\n  ul {\r\n      list-style: none !important;\r\n      padding: 0;\r\n      margin: 0;\r\n\r\n      >li {\r\n          display: block;\r\n          padding: 0;\r\n      }\r\n  }\r\n\r\n  /* Accessibility */\r\n\r\n  >.steps {\r\n      .current-info {\r\n          position: absolute;\r\n          left: -999em;\r\n      }\r\n  }\r\n\r\n  >.content{\r\n      >.title {\r\n          position: absolute;\r\n          left: -999em;\r\n      }\r\n  }\r\n}\r\n\r\n@include media-breakpoint-down(sm) { \r\n  .wizard > .steps > ul > li,.wizard.vertical > .steps,.wizard.vertical > .content {\r\n      width: 100%;\r\n  }\r\n}", "//\r\n// summernote.scss\r\n//\r\n\r\n@font-face {\r\n  font-family: \"summernote\";\r\n  font-style: normal;\r\n  font-weight: normal;\r\n  src: url(\"../fonts/summernote.eot\");\r\n  src: url(\"../fonts/summernote.eot?#iefix\") format(\"embedded-opentype\"), url(\"../fonts/summernote.woff?\") format(\"woff\"), url(\"../fonts/summernote.ttf?\") format(\"truetype\")\r\n}\r\n\r\n.note-editor{\r\n  &.note-frame {\r\n    border: 1px solid darken($gray-200,4%);\r\n    box-shadow: none;\r\n    margin: 0;\r\n\r\n    .note-statusbar {\r\n      background-color: lighten($gray-200,2%);\r\n      border-top: 1px solid $gray-200;\r\n    }\r\n\r\n    .note-editable {\r\n        border: none;\r\n    }\r\n  }\r\n}\r\n\r\n.note-status-output {\r\n  display: none;\r\n}\r\n\r\n.note-editable {\r\n\r\n\r\n  p {\r\n    &:last-of-type {\r\n      margin-bottom: 0;\r\n    }\r\n  }\r\n}\r\n\r\n.note-popover .popover-content .note-color .dropdown-menu,\r\n.card-header.note-toolbar .note-color .dropdown-menu {\r\n    min-width: 344px;\r\n}\r\n\r\n.note-toolbar {\r\n   z-index: 1;\r\n}", "//\r\n// dropify.scss\r\n//\r\n\r\n@font-face {\r\n    font-family: 'dropify';\r\n    src: url(\"../fonts/dropify.eot\");\r\n    src: url(\"../fonts/dropify.eot#iefix\") format(\"embedded-opentype\"), url(\"../fonts/dropify.woff\") format(\"woff\"), url(\"../fonts/dropify.ttf\") format(\"truetype\"), url(\"../fonts/dropify.svg#dropify\") format(\"svg\");\r\n    font-weight: normal;\r\n    font-style: normal;\r\n}\r\n\r\n.dropify-wrapper {\r\n    border: 2px dashed $gray-300;\r\n    background-color: transparent;\r\n    border-radius: 6px;\r\n    color: $gray-400;\r\n    &:hover{\r\n        background-image: linear-gradient(-45deg, lighten($gray-200, 4%) 25%,transparent 25%,transparent 50%,lighten($gray-200, 4%) 50%, lighten($gray-200, 4%) 75%,transparent 75%,transparent);\r\n    }\r\n\r\n    .dropify-preview{\r\n        background-color: lighten($gray-200, 4%);\r\n    }\r\n}", "//\r\n// x-editable.scss\r\n//\r\n\r\n.editable-clear-x {\r\n    background: url(\"../images/plugins/clear.png\") center center no-repeat;\r\n}\r\n\r\n.editableform-loading {\r\n    background: url('../images/plugins/loading.gif') center center no-repeat;\r\n}\r\n\r\n.editable-checklist label {\r\n    display: block;\r\n}", "//\r\n// datatable.scss\r\n//\r\n\r\n.dataTables_wrapper.container-fluid {\r\n    padding: 0;\r\n}\r\n\r\ntable.dataTable {\r\n    border-collapse: collapse !important;\r\n    margin-bottom: 15px !important;\r\n\r\n    tbody {\r\n        // Multi select table\r\n\r\n        > tr.selected, >tr>.selected {\r\n            background-color: $primary;\r\n            \r\n            td {\r\n                border-color: $primary;\r\n            }\r\n        }\r\n        td {\r\n            &:focus {\r\n                outline: none !important;\r\n            }\r\n        }\r\n        // Key Tables\r\n        th, td{\r\n            &.focus{\r\n                outline: 2px solid $primary !important;\r\n                outline-offset: -1px;\r\n                color: $primary;\r\n                background-color: rgba($primary, 0.15);\r\n            }\r\n        }\r\n    }\r\n}\r\n\r\n.dataTables_info {\r\n    font-weight: $font-weight-medium;\r\n}\r\n\r\n// Responsive data table\r\ntable.dataTable.dtr-inline.collapsed {\r\n    > tbody {\r\n        >tr[role=row] {\r\n            > td, > th {\r\n                &:first-child{\r\n                    &:before{\r\n                        box-shadow: $box-shadow-lg;\r\n                        background-color: $success;\r\n                        top: $table-cell-padding;\r\n                    }\r\n                }\r\n            }\r\n        }\r\n        >tr.parent {\r\n            > td, > th {\r\n                &:first-child{\r\n                    &:before{\r\n                        background-color: $danger;\r\n                        top: $table-cell-padding;\r\n                    }\r\n                }\r\n            }\r\n        }\r\n    }\r\n}\r\n\r\n// Data Table copy button\r\n.dt-buttons, .dataTables_length{\r\n    float: left;\r\n}\r\n\r\ndiv.dt-button-info {\r\n    background-color: $primary;\r\n    border: none;\r\n    color: $white;\r\n    box-shadow: none;\r\n    border-radius: 3px;\r\n    text-align: center;\r\n    z-index: 21;\r\n\r\n    h2 {\r\n        border-bottom: none;\r\n        background-color: rgba($white, 0.2);\r\n        color: $white;\r\n    }\r\n}\r\n\r\n@include media-breakpoint-down(sm) {\r\n    li.paginate_button.previous,li.paginate_button.next {\r\n        display: inline-block;\r\n        font-size: 1.5rem;\r\n    }\r\n \r\n    li.paginate_button {\r\n        display: none;\r\n    }\r\n    .dataTables_paginate {\r\n        ul {\r\n            text-align: center;\r\n            display: block;\r\n            margin: $spacer 0 0 !important;\r\n        }\r\n    }\r\n    div.dt-buttons {\r\n        display: inline-table;\r\n        margin-bottom: $spacer;\r\n    }\r\n}\r\n\r\n\r\n.activate-select {\r\n    .sorting_1 {\r\n        background-color: $gray-100;\r\n    }\r\n}\r\n\r\n/* ColVid Tables */\r\ndiv.ColVis {\r\n    float: none;\r\n    margin-right: 30px;\r\n}\r\n\r\nbutton{\r\n    &.ColVis_Button,\r\n    &.ColVis_Button:hover {\r\n        float: none !important;\r\n        border-radius: 3px;\r\n        outline: none !important;\r\n        box-shadow: none !important;\r\n        color: $white !important;\r\n        background: $primary !important;\r\n        border: 1px solid $primary !important;\r\n    }\r\n}\r\n\r\ndiv.ColVis_collectionBackground {\r\n    background-color: transparent;\r\n}\r\n\r\nul.ColVis_collection {\r\n    padding: 10px 0 0 0;\r\n    background-color: $white;\r\n    box-shadow: $box-shadow;\r\n    border: none;\r\n    li {\r\n        background: transparent !important;\r\n        padding: 3px 10px !important;\r\n        border: none !important;\r\n        box-shadow: none !important;\r\n    }\r\n}\r\n\r\n#datatable-colvid_info {\r\n    float: left;\r\n}", "// \r\n// responsive-table.scss\r\n//\r\n\r\n.responsive-table-plugin {\r\n    .dropdown-menu li.checkbox-row {\r\n        padding: 7px 15px;\r\n        color: $gray-600;\r\n        &:hover, &:focus{\r\n            background-color: lighten($gray-200, 4%);\r\n            color: $gray-600;\r\n        }\r\n    }\r\n    .table-responsive {\r\n        border: none;\r\n        margin-bottom: 0;\r\n    }\r\n    .btn-toolbar {\r\n        display: block;\r\n    }\r\n    tbody {\r\n        th {\r\n            font-size: 14px;\r\n            font-weight: normal;\r\n        }\r\n    }\r\n    .checkbox-row {\r\n        padding-left: 40px;\r\n        label {\r\n            display: inline-block;\r\n            padding-left: 5px;\r\n            position: relative;\r\n            margin-bottom: 0;\r\n            &::before {\r\n                background-color: lighten($gray-200, 4%);\r\n                border-radius: 3px;\r\n                border: 1px solid $gray-400;\r\n                content: \"\";\r\n                display: inline-block;\r\n                height: 17px;\r\n                left: 0;\r\n                margin-left: -20px;\r\n                position: absolute;\r\n                transition: 0.3s ease-in-out;\r\n                width: 17px;\r\n                outline: none;\r\n            }\r\n            &::after {\r\n                color: $gray-400;\r\n                display: inline-block;\r\n                font-size: 11px;\r\n                height: 16px;\r\n                left: 0;\r\n                margin-left: -20px;\r\n                padding-left: 3px;\r\n                padding-top: 1px;\r\n                position: absolute;\r\n                top: -1px;\r\n                width: 16px;\r\n            }\r\n        }\r\n        input[type=\"checkbox\"] {\r\n            cursor: pointer;\r\n            opacity: 0;\r\n            z-index: 1;\r\n            outline: none;\r\n            &:disabled+label {\r\n                opacity: 0.65;\r\n            }\r\n        }\r\n        input[type=\"checkbox\"]:focus+label {\r\n            &::before {\r\n                outline-offset: -2px;\r\n                outline: none;\r\n            }\r\n        }\r\n        input[type=\"checkbox\"]:checked+label {\r\n            &::after {\r\n                content: \"\\f00c\";\r\n                font-family: 'Font Awesome 5 Free';\r\n                font-weight: 900;\r\n            }\r\n        }\r\n        input[type=\"checkbox\"]:disabled+label {\r\n            &::before {\r\n                background-color: $gray-300;\r\n                cursor: not-allowed;\r\n            }\r\n        }\r\n        input[type=\"checkbox\"]:checked+label {\r\n            &::before {\r\n                background-color: lighten($gray-200, 4%);\r\n                border-color: $primary;\r\n            }\r\n            &::after {\r\n                color: $primary;\r\n            }\r\n        }\r\n    }\r\n    table.focus-on tbody tr.focused th,\r\n    table.focus-on tbody tr.focused td,\r\n    .sticky-table-header {\r\n        background: $primary;\r\n        border-color: $primary;\r\n        color: $white;\r\n\r\n        table {\r\n            color: $white;\r\n        }\r\n    }\r\n    .fixed-solution {\r\n        .sticky-table-header {\r\n            top: $topbar-height !important;\r\n        }\r\n    }\r\n    .btn-default {\r\n        background-color: lighten($gray-200, 4%);\r\n        color: $dark;\r\n        border: 1px solid $gray-200;\r\n        &.btn-primary {\r\n            background-color: $primary;\r\n            border-color: $primary;\r\n            color: $white;\r\n            box-shadow: 0 0 0 2px rgba($primary, .5);\r\n        }\r\n    }\r\n    .btn-group{\r\n        &.pull-right {\r\n            float: right;\r\n            .dropdown-menu {\r\n                left: auto;\r\n                right: 0;\r\n            }\r\n        }\r\n    }\r\n}", "// \r\n// tablesaw.scss\r\n//\r\n\r\n.tablesaw {\r\n    thead {\r\n        background: lighten($gray-200, 4%);\r\n        background-image: none;\r\n        border: none;\r\n        th {\r\n            text-shadow: none;\r\n        }\r\n        tr:first-child th {\r\n            border: none;\r\n            font-weight: 500;\r\n            font-family: $font-family-secondary;\r\n        }\r\n    }\r\n    td {\r\n        border-top: 1px solid lighten($gray-200, 4%) !important;\r\n    }\r\n\r\n    td, tbody th {\r\n        font-size: inherit;\r\n        line-height: inherit;\r\n        padding: 10px !important;\r\n    }\r\n}\r\n\r\n\r\n.tablesaw-stack, .tablesaw  {\r\n    tbody {\r\n        tr {\r\n            border-bottom: none;\r\n        }\r\n    }\r\n}\r\n\r\n.tablesaw-bar{\r\n    .btn-select{\r\n        .btn-small, .btn-micro{\r\n            &:after{\r\n                font-size: 8px;\r\n                padding-right: 10px;\r\n            }\r\n        }\r\n    }\r\n}\r\n\r\n.tablesaw-swipe {\r\n    .tablesaw-cell-persist {\r\n        box-shadow: none;\r\n        border-color: $gray-100;\r\n    }\r\n    .tablesaw-swipe-cellpersist {\r\n        border-right: 2px solid lighten($gray-200, 4%);\r\n    }\r\n}\r\n\r\n.tablesaw-bar-section {\r\n    label{\r\n        color: $gray-600;\r\n    }\r\n}\r\n\r\n\r\n.tablesaw-enhanced {\r\n    .tablesaw-bar {\r\n        .btn {\r\n            text-shadow: none;\r\n            background-image: none;\r\n            text-transform: none;\r\n            border: 1px solid $gray-300;\r\n            padding: 3px 10px;\r\n            color: $dark;\r\n        \r\n            &:after {\r\n                display: none;\r\n            }\r\n\r\n            &.btn-select {\r\n                &:hover {\r\n                    background: $white;\r\n                }\r\n            }\r\n\r\n            &:hover, &:focus, &:active{\r\n                color: $primary !important;\r\n                background-color: $gray-100;\r\n                outline: none !important;\r\n                box-shadow: none !important;\r\n                background-image: none;\r\n            }\r\n        }\r\n    }\r\n}\r\n\r\n.tablesaw-columntoggle-popup {\r\n    .btn-group {\r\n        display: block;\r\n    }\r\n}\r\n\r\n.tablesaw-sortable-btn {\r\n    cursor: pointer;\r\n}\r\n\r\n.tablesaw-swipe-cellpersist {\r\n    width: auto !important;\r\n}", "//\r\n// flot.scss\r\n//\r\n\r\n.flotTip {\r\n    padding: 8px 12px;\r\n    background-color: rgba($dark, 0.9);\r\n    z-index: 100;\r\n    color: $gray-100;\r\n    opacity: 1;\r\n    border-radius: 3px;\r\n}\r\n\r\n.legend {\r\n    tr {\r\n        height: 30px;\r\n        font-family: $font-family-secondary;\r\n    }\r\n}\r\n\r\n.legendLabel {\r\n    padding-left: 5px;\r\n    line-height: 10px;\r\n    padding-right: 20px;\r\n    font-size: 13px;\r\n    font-weight: $font-weight-medium;\r\n    color: $gray-600;\r\n}\r\n\r\n.legendColorBox {\r\n    div {\r\n        border-radius: 3px;\r\n        div {\r\n            border-radius: 3px;\r\n        }\r\n    }\r\n}\r\n\r\n\r\n@include media-breakpoint-down(sm) {\r\n    .legendLabel {\r\n        display: none;\r\n    }\r\n}", "//\r\n// morris.scss\r\n//\r\n\r\n.morris-chart {\r\n    text {\r\n        font-family: $font-family-secondary !important;\r\n        font-weight: $font-weight-semibold !important;\r\n        fill: $gray-500;\r\n    }\r\n}\r\n.morris-hover {\r\n    position: absolute;\r\n    z-index: 10;\r\n\r\n    &.morris-default-style {\r\n        font-size: 12px;\r\n        text-align: center;\r\n        border-radius: 5px;\r\n        padding: 10px 12px;\r\n        background: rgba($gray-100, 0.8);\r\n        color: $dark;\r\n        border: 2px solid $gray-200;\r\n        font-family: $font-family-base;\r\n\r\n        .morris-hover-row-label {\r\n            font-weight: bold;\r\n            margin: 0.25em 0;\r\n            font-family: $font-family-secondary;\r\n        }\r\n\r\n        .morris-hover-point {\r\n            white-space: nowrap;\r\n            margin: 0.1em 0;\r\n            color: $white;\r\n        }\r\n    }\r\n}", "//\r\n// chartist.scss\r\n//\r\n\r\n.ct-golden-section:before {\r\n    float: none;\r\n}\r\n\r\n.ct-chart {\r\n    max-height: 300px;\r\n    .ct-label {\r\n        fill: $gray-500;\r\n        color: $gray-500;\r\n        font-size: 12px;\r\n        line-height: 1;\r\n    }\r\n}\r\n\r\n.ct-chart.simple-pie-chart-chartist {\r\n    .ct-label {\r\n        color: $white;\r\n        fill: $white;\r\n        font-size: 16px;\r\n    }\r\n}\r\n\r\n.ct-grid {\r\n    stroke: rgba($dark, 0.1);\r\n}\r\n\r\n.ct-chart {\r\n    .ct-series {\r\n        &.ct-series-a {\r\n            .ct-bar,\r\n            .ct-line,\r\n            .ct-point,\r\n            .ct-slice-donut {\r\n                stroke: $primary;\r\n            }\r\n        }\r\n        &.ct-series-b {\r\n            .ct-bar,\r\n            .ct-line,\r\n            .ct-point,\r\n            .ct-slice-donut {\r\n                stroke: $success;\r\n            }\r\n        }\r\n        &.ct-series-c {\r\n            .ct-bar,\r\n            .ct-line,\r\n            .ct-point,\r\n            .ct-slice-donut {\r\n                stroke: $warning;\r\n            }\r\n        }\r\n        &.ct-series-d {\r\n            .ct-bar,\r\n            .ct-line,\r\n            .ct-point,\r\n            .ct-slice-donut {\r\n                stroke: $pink;\r\n            }\r\n        }\r\n        &.ct-series-e {\r\n            .ct-bar,\r\n            .ct-line,\r\n            .ct-point,\r\n            .ct-slice-donut {\r\n                stroke: $dark;\r\n            }\r\n        }\r\n        &.ct-series-f {\r\n            .ct-bar,\r\n            .ct-line,\r\n            .ct-point,\r\n            .ct-slice-donut {\r\n                stroke: $info;\r\n            }\r\n        }\r\n        &.ct-series-g {\r\n            .ct-bar,\r\n            .ct-line,\r\n            .ct-point,\r\n            .ct-slice-donut {\r\n                stroke: $danger;\r\n            }\r\n        }\r\n    }\r\n}\r\n\r\n\r\n.ct-series-a {\r\n    .ct-area,\r\n    .ct-slice-pie {\r\n        fill: $primary;\r\n    }\r\n}\r\n\r\n.ct-series-b {\r\n    .ct-area,\r\n    .ct-slice-pie {\r\n        fill: $success;\r\n    }\r\n}\r\n\r\n.ct-series-c {\r\n    .ct-area,\r\n    .ct-slice-pie {\r\n        fill: $warning;\r\n    }\r\n}\r\n\r\n.ct-series-d {\r\n    .ct-area,\r\n    .ct-slice-pie {\r\n        fill: $success;\r\n    }\r\n}\r\n\r\n.ct-area {\r\n    fill-opacity: .33;\r\n}\r\n\r\n.chartist-tooltip {\r\n    position: absolute;\r\n    display: inline-block;\r\n    opacity: 0;\r\n    min-width: 10px;\r\n    padding: 2px 10px;\r\n    border-radius: 3px;\r\n    background: $dark;\r\n    color: $gray-300;\r\n    text-align: center;\r\n    pointer-events: none;\r\n    z-index: 1;\r\n    transition: opacity .2s linear;\r\n    &.tooltip-show {\r\n        opacity: 1;\r\n    }\r\n}", "//\r\n// chartjs.scss\r\n//\r\n\r\n.chartjs-chart {\r\n    margin: auto; \r\n    position: relative; \r\n    width: 100%;\r\n}\r\n\r\n.chartjs-chart-example {\r\n    height: 300px;\r\n}", "//\r\n// c3.scss\r\n//\r\n \r\n.c3-tooltip {\r\n    box-shadow: $box-shadow-lg;\r\n    opacity: 1;\r\n    td {\r\n        border-left: none;\r\n        font-family: $font-family-secondary;\r\n        >span {\r\n            background: $dark;\r\n            \r\n        }\r\n    }\r\n    tr {\r\n        border: none !important;\r\n    }    \r\n    th {\r\n        background-color: $dark;\r\n        color: $gray-100;\r\n    }\r\n}\r\n\r\n.c3-chart-arcs-title {\r\n    font-size: 18px;\r\n    font-weight: 600;\r\n}\r\n\r\n.c3 {\r\n    text {\r\n        font-family: $font-family-base;\r\n        fill: $gray-600;\r\n    }\r\n    line, path{\r\n        stroke: $gray-400;\r\n    }\r\n}\r\n\r\n\r\n.c3-chart-arc.c3-target {\r\n    g {\r\n        path {\r\n            stroke: $white;\r\n        }\r\n    }\r\n}", "//\r\n// sparkline.scss\r\n//\r\n\r\n.jqstooltip {\r\n  box-sizing: content-box;\r\n  width: auto !important;\r\n  height: auto !important;\r\n  background-color: $gray-800 !important;\r\n  box-shadow: $box-shadow-lg;\r\n  padding: 5px 10px !important;\r\n  border-radius: 3px;\r\n  border-color: $gray-900 !important;\r\n}\r\n\r\n.jqsfield {\r\n  color: $gray-200 !important;\r\n  font-size: 12px !important;\r\n  line-height: 18px !important;\r\n  font-family: $font-family-base !important;\r\n  font-weight: $font-weight-medium !important;\r\n}\r\n", "// \r\n// components-demo.scss\r\n//\r\n\r\n// Demo Only\r\n.button-list {\r\n    margin-left: -8px;\r\n    margin-bottom: -12px;\r\n    \r\n    .btn {\r\n        margin-bottom: 12px;\r\n        margin-left: 8px;\r\n    }\r\n}\r\n\r\n\r\n// Icon demo ( Demo only )\r\n.icons-list-demo {\r\n    div {\r\n        cursor: pointer;\r\n        line-height: 45px;\r\n        white-space: nowrap;\r\n        text-overflow: ellipsis;\r\n        display: block;\r\n        overflow: hidden;\r\n        p {\r\n            margin-bottom: 0;\r\n            line-height: inherit;\r\n        }\r\n    }\r\n    i {\r\n        text-align: center;\r\n        vertical-align: middle;\r\n        font-size: 22px;\r\n        width: 50px;\r\n        height: 50px;\r\n        line-height: 50px;\r\n        margin-right: 12px;\r\n        color: $gray-600;\r\n        border: 1px solid $gray-300;\r\n        border-radius: 3px;\r\n        display: inline-block;\r\n        transition: all 0.2s;\r\n    }\r\n    .col-lg-4 {\r\n        background-clip: padding-box;\r\n        margin-top: 10px;\r\n        &:hover i {\r\n            color: $primary;\r\n        }\r\n    }\r\n}\r\n\r\n\r\n// Grid\r\n\r\n.grid-structure {\r\n    .grid-container {\r\n        background-color: $gray-100;\r\n        margin-top: 10px;\r\n        font-size: .8rem;\r\n        font-weight: $font-weight-medium;\r\n        padding: 10px 20px;\r\n    }\r\n}\r\n\r\n// Demos button\r\n.demos-show-btn {\r\n    position: fixed;\r\n    top: 50%;\r\n    right: 0;\r\n    writing-mode: vertical-rl;\r\n    font-weight: 600;\r\n    background-color: $danger;\r\n    color: $white !important;\r\n    line-height: 36px;\r\n    padding: 15px 3px;\r\n    border-radius: 6px 0 0 6px;\r\n    transform: translateY(-50%);\r\n    text-transform: uppercase;\r\n}\r\n\r\n@media (max-width: 600px) {\r\n    .demos-show-btn {\r\n        display: none;\r\n    }\r\n}", "// \r\n// authentication.scss\r\n//\r\n\r\n.authentication-bg {\r\n  background-color: $primary;\r\n}\r\n\r\n.account-card-box{\r\n  background-color: $card-bg;\r\n  padding: 7px;\r\n  border-radius: 8px;\r\n  \r\n  .card{\r\n    border: 4px solid $primary;\r\n  }\r\n}", "// \r\n// Extras pages.scss\r\n//\r\n\r\n// pricing\r\n\r\n\r\n.card-pricing{\r\n    box-shadow: $box-shadow-sm;\r\n    .card-pricing-features{\r\n        li{\r\n            padding: 15px;\r\n        }\r\n    }\r\n    .card-price{\r\n        font-size: 48px;\r\n        font-weight: 300;\r\n    }\r\n    &.active{\r\n        margin: 0 -24px;\r\n        box-shadow: $box-shadow;\r\n    }\r\n}\r\n\r\n\r\n// Maintenance\r\n\r\n.maintenance-icon {\r\n    height: 220px;\r\n    width: 220px;\r\n    margin: 0 auto;\r\n}\r\n  \r\n.line1 {\r\n    opacity: 0;\r\n    animation: fadeInLeft both 1s 0.4s, coding1 ease 6s 4s infinite;\r\n}\r\n  \r\n.line2 {\r\n    opacity: 0;\r\n    animation: fadeInLeft both 1s 0.6s, coding2 ease 6s 4s infinite;\r\n}\r\n  \r\n.line3 {\r\n    opacity: 0;\r\n    animation: fadeInLeft both 1s 0.8s, coding3 ease 6s 4s infinite;\r\n}\r\n  \r\n.line4 {\r\n    opacity: 0;\r\n    animation: fadeInLeft both 1s 1.0s, coding4 ease 6s 4s infinite;\r\n}\r\n  \r\n.line5 {\r\n    opacity: 0;\r\n    animation: fadeInLeft both 1s 1.2s, coding5 ease 6s 4s infinite;\r\n}\r\n  \r\n.line6 {\r\n    opacity: 0;\r\n    animation: fadeInLeft both 1s 1.4s, coding6 ease 6s 4s infinite;\r\n}\r\n  \r\n.line7 {\r\n    opacity: 0;\r\n    animation: fadeInLeft both 1s 1.6s, coding6 ease 6s 4s infinite;\r\n}\r\n  \r\n@keyframes coding1 {\r\n    0% {\r\n      transform: translate(0, 0);\r\n      opacity: 1;\r\n    }\r\n    14% {\r\n      transform: translate(0, -10px);\r\n      opacity: 0;\r\n    }\r\n    15% {\r\n      transform: translate(0, 45px);\r\n    }\r\n    30% {\r\n      transform: translate(0, 40px);\r\n      opacity: 1;\r\n    }\r\n    45% {\r\n      transform: translate(0, 30px);\r\n    }\r\n    60% {\r\n      transform: translate(0, 20px);\r\n    }\r\n    75% {\r\n      transform: translate(0, 10px);\r\n    }\r\n    90% {\r\n      transform: translate(0, 5px);\r\n    }\r\n    100% {\r\n      transform: translate(0, 0);\r\n      opacity: 1;\r\n    }\r\n}\r\n  \r\n@keyframes coding2 {\r\n    0% {\r\n      transform: translate(0, 0);\r\n      opacity: 1;\r\n    }\r\n    15% {\r\n      transform: translate(0, -5px);\r\n      opacity: 1;\r\n    }\r\n    29% {\r\n      transform: translate(0, -10px);\r\n      opacity: 0;\r\n    }\r\n    30% {\r\n      transform: translate(0, 40px);\r\n    }\r\n    45% {\r\n      transform: translate(0, 30px);\r\n      opacity: 1;\r\n    }\r\n    60% {\r\n      transform: translate(0, 20px);\r\n    }\r\n    75% {\r\n      transform: translate(0, 10px);\r\n    }\r\n    90% {\r\n      transform: translate(0, 5px);\r\n    }\r\n    100% {\r\n      transform: translate(0, 0);\r\n      opacity: 1;\r\n    }\r\n}\r\n\r\n@keyframes coding3 {\r\n    0% {\r\n      transform: translate(0, 0);\r\n      opacity: 1;\r\n    }\r\n    15% {\r\n      transform: translate(0, -5px);\r\n    }\r\n    30% {\r\n      transform: translate(0, -10px);\r\n      opacity: 1;\r\n    }\r\n    44% {\r\n      transform: translate(0, -20px);\r\n      opacity: 0;\r\n    }\r\n    45% {\r\n      transform: translate(0, 30px);\r\n    }\r\n    60% {\r\n      transform: translate(0, 20px);\r\n      opacity: 1;\r\n    }\r\n    75% {\r\n      transform: translate(0, 10px);\r\n    }\r\n    90% {\r\n      transform: translate(0, 5px);\r\n    }\r\n    100% {\r\n      transform: translate(0, 0);\r\n      opacity: 1;\r\n    }\r\n}\r\n\r\n@keyframes coding4 {\r\n    0% {\r\n      transform: translate(0, 0);\r\n      opacity: 1;\r\n    }\r\n    15% {\r\n      transform: translate(0, -5px);\r\n    }\r\n    30% {\r\n      transform: translate(0, -10px);\r\n    }\r\n    45% {\r\n      transform: translate(0, -20px);\r\n      opacity: 1;\r\n    }\r\n    59% {\r\n      transform: translate(0, -30px);\r\n      opacity: 0;\r\n    }\r\n    60% {\r\n      transform: translate(0, 20px);\r\n    }\r\n    75% {\r\n      transform: translate(0, 10px);\r\n      opacity: 1;\r\n    }\r\n    90% {\r\n      transform: translate(0, 5px);\r\n    }\r\n    100% {\r\n      transform: translate(0, 0);\r\n      opacity: 1;\r\n    }\r\n}\r\n\r\n@keyframes coding5 {\r\n    0% {\r\n      transform: translate(0, 0);\r\n      opacity: 1;\r\n    }\r\n    15% {\r\n      transform: translate(0, -5px);\r\n    }\r\n    30% {\r\n      transform: translate(0, -10px);\r\n    }\r\n    45% {\r\n      transform: translate(0, -20px);\r\n    }\r\n    60% {\r\n      transform: translate(0, -30px);\r\n      opacity: 1;\r\n    }\r\n    74% {\r\n      transform: translate(0, -40px);\r\n      opacity: 0;\r\n    }\r\n    75% {\r\n      transform: translate(0, 10px);\r\n    }\r\n    90% {\r\n      transform: translate(0, 5px);\r\n      opacity: 1;\r\n    }\r\n    100% {\r\n      transform: translate(0, 0);\r\n      opacity: 1;\r\n    }\r\n}\r\n\r\n@keyframes coding6 {\r\n    0% {\r\n      transform: translate(0, 0);\r\n      opacity: 1;\r\n    }\r\n    15% {\r\n      transform: translate(0, -5px);\r\n    }\r\n    30% {\r\n      transform: translate(0, -10px);\r\n    }\r\n    45% {\r\n      transform: translate(0, -20px);\r\n    }\r\n    60% {\r\n      transform: translate(0, -30px);\r\n    }\r\n    75% {\r\n      transform: translate(0, -40px);\r\n      opacity: 1;\r\n    }\r\n    89% {\r\n      transform: translate(0, -50px);\r\n      opacity: 0;\r\n    }\r\n    90% {\r\n      transform: translate(0, 10px);\r\n    }\r\n    100% {\r\n      transform: translate(0, 0);\r\n      opacity: 1;\r\n    }\r\n}\r\n\r\n// coming soon\r\n\r\n.diamond {\r\n  margin: 50px auto;\r\n  height: 90px;\r\n  width: 120px;\r\n\r\n  &:after {\r\n    content: \"\";\r\n    position: absolute;\r\n    height: 14px;\r\n    width: 44px;\r\n    background: rgba($dark,.1);\r\n    border-radius: 50%;\r\n    margin-top: 0;\r\n    margin-left: 38px;\r\n    z-index: 11;\r\n  }\r\n  .top {\r\n    height: 30px;\r\n    border-left: 27px solid transparent;\r\n    border-right: 27px solid transparent;\r\n    border-bottom: 24px solid lighten($success, 4%);\r\n    &:after {\r\n      content: \"\";\r\n      position: absolute;\r\n      height: 24px;\r\n      width: 32px;\r\n      margin-top: 6px;\r\n      margin-left: 47px;\r\n      background: $success;\r\n      -ms-transform: skew(30deg, 20deg);\r\n      -webkit-transform: skew(30deg, 20deg);\r\n      transform: skew(48deg);\r\n    }\r\n    &:before {\r\n      content: \"\";\r\n      position: absolute;\r\n      height: 24px;\r\n      width: 32px;\r\n      margin-top: 7px;\r\n      margin-left: -13px;\r\n      background: $success;\r\n      transform: skew(-48deg);\r\n    }\r\n  }\r\n  .bot {\r\n    height: 60px;\r\n    border-left: 60px solid transparent;\r\n    border-right: 60px solid transparent;\r\n    border-top: 60px solid lighten($success, 4%);\r\n    &:before {\r\n      content: \"\";\r\n      position: absolute;\r\n      height: 60px;\r\n      margin-top: -60px;\r\n      margin-left: -27px;\r\n      border-left: 27px solid transparent;\r\n      border-right: 26px solid transparent;\r\n      border-top: 60px solid $success;\r\n    }\r\n  }\r\n}", "// \r\n// timeline.scss\r\n//\r\n\r\n.timeline {\r\n    border-collapse: collapse;\r\n    border-spacing: 0;\r\n    display: table;\r\n    margin-bottom: 50px;\r\n    position: relative;\r\n    table-layout: fixed;\r\n    width: 100%;\r\n  \r\n    .time-show {\r\n      margin-bottom: 30px;\r\n      margin-right: -75px;\r\n      margin-top: 30px;\r\n      position: relative;\r\n      text-align: right;\r\n      a {\r\n        color: $white;\r\n      }\r\n    }\r\n    &:before {\r\n      background-color: rgba($gray-500, 0.3);\r\n      bottom: 0px;\r\n      content: \"\";\r\n      left: 50%;\r\n      position: absolute;\r\n      top: 30px;\r\n      width: 2px;\r\n      z-index: 0;\r\n    }\r\n    .timeline-icon {\r\n      background: $gray-600;\r\n      border-radius: 50%;\r\n      color: $white;\r\n      display: block;\r\n      height: 21px;\r\n      left: -54px;\r\n      margin-top: -11px;\r\n      position: absolute;\r\n      text-align: center;\r\n      top: 50%;\r\n      width: 21px;\r\n      i {\r\n        color: $gray-200;\r\n      }\r\n    }\r\n    .time-icon {\r\n      &:before {\r\n        font-size: 16px;\r\n        margin-top: 5px;\r\n      }\r\n    }\r\n  \r\n  }\r\n  \r\n  h3{\r\n    &.timeline-title {\r\n      color: $gray-600;\r\n      font-size: 20px;\r\n      font-weight: 400;\r\n      margin: 0 0 5px;\r\n      text-transform: uppercase;\r\n    }\r\n  }\r\n  \r\n  .timeline-item {\r\n    display: table-row;\r\n    &:before {\r\n      content: \"\";\r\n      display: block;\r\n      width: 50%;\r\n    }\r\n    .timeline-desk {\r\n      .arrow {\r\n        border-bottom: 8px solid transparent;\r\n        border-right: 8px solid $card-bg !important;\r\n        border-top: 8px solid transparent;\r\n        display: block;\r\n        height: 0;\r\n        left: -7px;\r\n        margin-top: -10px;\r\n        position: absolute;\r\n        top: 50%;\r\n        width: 0;\r\n      }\r\n    }\r\n  }\r\n  \r\n  .timeline-item.alt {\r\n    &:after {\r\n      content: \"\";\r\n      display: block;\r\n      width: 50%;\r\n    }\r\n    .timeline-desk {\r\n      .arrow-alt {\r\n        border-bottom: 8px solid transparent;\r\n        border-left: 8px solid $card-bg !important;\r\n        border-top: 8px solid transparent;\r\n        display: block;\r\n        height: 0;\r\n        left: auto;\r\n        margin-top: -10px;\r\n        position: absolute;\r\n        right: -7px;\r\n        top: 50%;\r\n        width: 0;\r\n      }\r\n      .album {\r\n        float: right;\r\n        margin-top: 20px;\r\n        a {\r\n          float: right;\r\n          margin-left: 5px;\r\n        }\r\n      }\r\n    }\r\n    .timeline-icon {\r\n      left: auto;\r\n      right: -58px;\r\n    }\r\n    &:before {\r\n      display: none;\r\n    }\r\n    .panel {\r\n      margin-left: 0;\r\n      margin-right: 45px;\r\n      .panel-body p + p {\r\n        margin-top: 10px !important;\r\n      }\r\n    }\r\n    h4 {\r\n      text-align: right;\r\n    }\r\n    p {\r\n      text-align: right;\r\n    }\r\n    .timeline-date {\r\n      text-align: right;\r\n    }\r\n  }\r\n  \r\n  .timeline-desk {\r\n    display: table-cell;\r\n    vertical-align: top;\r\n    width: 50%;\r\n    h4 {\r\n      font-size: 16px;\r\n      font-weight: 300;\r\n      margin: 0;\r\n    }\r\n    .panel {\r\n      background: $card-bg;\r\n      display: block;\r\n      margin-bottom: 5px;\r\n      margin-left: 45px;\r\n      position: relative;\r\n      text-align: left;\r\n      padding: 20px;\r\n      border-radius: 7px;\r\n      box-shadow: $box-shadow-sm;\r\n    }\r\n    h5 {\r\n      span {\r\n        color: $gray-200;\r\n        display: block;\r\n        font-size: 12px;\r\n        margin-bottom: 4px;\r\n      }\r\n    }\r\n    p {\r\n      color: $gray-600;\r\n      font-size: 14px;\r\n      margin-bottom: 0;\r\n    }\r\n    .album {\r\n      margin-top: 12px;\r\n      a {\r\n        float: left;\r\n        margin-right: 5px;\r\n      }\r\n  \r\n      img {\r\n        height: 36px;\r\n        width: auto;\r\n        border-radius: 3px;\r\n      }\r\n    }\r\n    .notification {\r\n      background: none repeat scroll 0 0 $white;\r\n      margin-top: 20px;\r\n      padding: 8px;\r\n    }\r\n  }", "// \r\n// error.scss\r\n//\r\n\r\n// Error text with shadow\r\n.text-error {\r\n    color: $white;\r\n    font-size: 98px;\r\n    line-height: 150px;\r\n    &.shadow-text{\r\n        text-shadow: rgba($white, 0.3) 5px 1px, rgba($white, 0.2) 12px 3px, rgba($white, 0.1) 6px 4px;\r\n    }\r\n}\r\n", "// \r\n// gallery.scss\r\n//\r\n\r\n.portfolioFilter {\r\n    a {\r\n        transition: all 0.3s ease-out;\r\n        color: $dark;\r\n        border-radius: 3px;\r\n        padding: 5px 10px;\r\n        display: inline-block;\r\n        font-size: 13px;\r\n        font-weight: $font-weight-medium;\r\n        text-transform: uppercase;\r\n        &:hover {\r\n            color: $success;\r\n        }\r\n    }\r\n    a.current {\r\n        background-color: $success;\r\n        color: $white;\r\n    }\r\n}\r\n\r\n\r\n.gallery-box{\r\n    background-color: $card-bg;\r\n    margin-top: 24px;\r\n    border-radius: 4px;\r\n    overflow: hidden;\r\n    a {\r\n        display: block;\r\n        background-color: darken($dark, 20%);\r\n        overflow: hidden;\r\n      }\r\n\r\n      &:hover {\r\n        .thumb-img {\r\n          position: relative;\r\n          transform: scale(1.05);\r\n          opacity: 0.7;\r\n        }\r\n      }\r\n}\r\n  \r\n.thumb-img {\r\n    overflow: hidden;\r\n    width: 100%;\r\n    transition: all 0.2s ease-out;\r\n}\r\n\r\n\r\n", "// \r\n// coming-soon.scss\r\n//\r\n\r\n.counter-number {\r\n    font-size: 60px;\r\n    font-weight: $font-weight-semibold;\r\n    text-align: center;\r\n    color: $dark;\r\n    span {\r\n        font-size: 16px;\r\n        font-weight: $font-weight-normal;\r\n        display: block;\r\n        text-transform: uppercase;\r\n        padding-top: 5px;\r\n        color: $success;\r\n    }\r\n}\r\n\r\n.coming-box {\r\n    float: left;\r\n    width: 25%;\r\n}\r\n", "// \r\n// general-rtl.scss\r\n//\r\n\r\nhtml {\r\n    direction: rtl;\r\n}\r\n\r\nbody {\r\n    text-align: right;\r\n}", "// \r\n// bootstrap-rtl.scss\r\n//\r\n\r\n// Dropdowns\r\n\r\n.dropdown-menu {\r\n    text-align: right;\r\n    &.show {\r\n        text-align: right;\r\n        left: auto !important;\r\n        right: 0;\r\n        bottom: auto;\r\n    }\r\n\r\n    &.dropdown-megamenu {\r\n        left: 20px!important;\r\n        right: 20px!important;\r\n        .megamenu-img{\r\n            transform: scaleX(-1);\r\n        }\r\n    }\r\n}\r\n\r\n.dropdown-menu-right {\r\n    right: auto !important;\r\n    left: 0 !important;\r\n    &.show {\r\n        left: 0 !important;\r\n    }\r\n}\r\n\r\n.dropdown-example{\r\n    clear: right;\r\n    float: right;\r\n}\r\n\r\n.dropright, .dropleft{\r\n    .dropdown-menu{\r\n        &.show{\r\n            right: auto;\r\n        }\r\n    }\r\n}\r\n\r\n// List\r\n\r\nul {\r\n    padding-right: 0;\r\n}\r\n\r\n\r\n// Buttons\r\n\r\n.btn-label {\r\n    margin: -.55rem -.9rem -.55rem .9rem;\r\n}\r\n\r\n.btn-label-right {\r\n    margin: (-$btn-padding-y) ($btn-padding-x) (-$btn-padding-y) (-$btn-padding-x);\r\n}\r\n\r\n.btn-group,\r\n.btn-group-vertical {\r\n    direction: ltr;\r\n}\r\n\r\n// pagination\r\n\r\n.pagination{\r\n    .page-item {\r\n        &:first-child {\r\n            .page-link {\r\n                margin-right: 0;//rtl\r\n                border-top-left-radius: 0px;\r\n                border-bottom-left-radius: 0px;\r\n                @include border-right-radius($border-radius);//rtl\r\n            }\r\n        }\r\n        &:last-child {\r\n            .page-link {\r\n                border-top-right-radius: 0px;\r\n                border-bottom-right-radius: 0px;\r\n                @include border-left-radius($border-radius);//rtl\r\n            }\r\n        }\r\n    }\r\n}\r\n\r\n\r\n// blockquote\r\n\r\n.blockquote-reverse{\r\n    text-align: left !important;\r\n}\r\n\r\n// dl\r\n\r\ndd {\r\n    margin-right: 0;\r\n}\r\n\r\n// Modal\r\n\r\n.modal-header {\r\n    .close {\r\n        margin: (-$modal-header-padding-y) auto (-$modal-header-padding-x) (-$modal-header-padding-y);\r\n    }\r\n}\r\n\r\n.modal-footer {\r\n    > :not(:first-child) {\r\n        margin-right: .25rem;\r\n        margin-left: 0;\r\n    }\r\n\r\n    > :not(:last-child) {\r\n        margin-left: .25rem;\r\n        margin-right: 0;\r\n    }\r\n}\r\n\r\n\r\n// Alerts\r\n\r\n.alert-dismissible {\r\n    padding-left: $close-font-size + $alert-padding-x * 2;\r\n    padding-right: $alert-padding-x;\r\n\r\n    .close {\r\n        left: 0;\r\n        right: auto;\r\n    }\r\n}\r\n\r\n\r\n// Breadcrumb item arrow\r\n\r\n.breadcrumb-item {\r\n    +.breadcrumb-item {\r\n        padding-right: $breadcrumb-item-padding;\r\n        padding-left: 0px;\r\n        &::before {\r\n            padding-left: $breadcrumb-item-padding;\r\n            padding-right: 0px;\r\n        }\r\n    }\r\n}\r\n\r\n// Custom Checkbox-Radio \r\n\r\n.form-check-inline{\r\n    margin-left: .75rem;\r\n    margin-right: 0;\r\n}\r\n\r\n.custom-control-inline{\r\n    margin-left: 1rem;\r\n    margin-right: 0;\r\n}\r\n\r\n.custom-control {\r\n    padding-right: $custom-control-gutter + $custom-control-indicator-size;\r\n    padding-left: 0;\r\n}\r\n\r\n.custom-control-label {\r\n    &::before {\r\n        left: auto;\r\n        right: -($custom-control-gutter + $custom-control-indicator-size);\r\n    }\r\n\r\n    // Foreground (icon)\r\n    &::after {\r\n        left: auto;\r\n        right: -($custom-control-gutter + $custom-control-indicator-size);\r\n    }\r\n}\r\n\r\n.custom-switch {\r\n    padding-right: $custom-switch-width + $custom-control-gutter;\r\n    padding-left: 0;\r\n\r\n    .custom-control-label {\r\n        &::before {\r\n            right: -($custom-switch-width + $custom-control-gutter);\r\n            left: auto;\r\n        }\r\n\r\n        &::after {\r\n            right: calc(#{-($custom-switch-width + $custom-control-gutter)} + #{$custom-control-indicator-border-width * 2});\r\n            left: auto;\r\n        }\r\n    }\r\n\r\n    .custom-control-input:checked~.custom-control-label {\r\n        &::after {\r\n            transform: translateX(#{-($custom-switch-width - $custom-control-indicator-size)});\r\n        }\r\n    }\r\n}\r\n\r\n.custom-file-label {\r\n    &::after {\r\n        right: auto;\r\n        left: 0;\r\n        border-right: inherit;\r\n    }\r\n}\r\n\r\n\r\n\r\n// Input Group\r\n\r\n.input-group-prepend {\r\n    margin-left: -1px;\r\n    margin-right: 0;\r\n}\r\n\r\n.input-group-append {\r\n    margin-right: -1px;\r\n    margin-left: 0;\r\n}\r\n\r\n.input-group>.input-group-prepend>.btn,\r\n.input-group>.input-group-prepend>.input-group-text,\r\n.input-group>.input-group-append:not(:last-child)>.btn,\r\n.input-group>.input-group-append:not(:last-child)>.input-group-text,\r\n.input-group>.input-group-append:last-child>.btn:not(:last-child):not(.dropdown-toggle),\r\n.input-group>.input-group-append:last-child>.input-group-text:not(:last-child),\r\n.input-group>.custom-select:not(:last-child),\r\n.input-group>.form-control:not(:last-child) {\r\n    border-top-right-radius: $input-border-radius;\r\n    border-bottom-right-radius: $input-border-radius;\r\n    border-top-left-radius: 0;\r\n    border-bottom-left-radius: 0;\r\n}\r\n\r\n.input-group>.input-group-append>.btn,\r\n.input-group>.input-group-append>.input-group-text,\r\n.input-group>.input-group-prepend:not(:first-child)>.btn,\r\n.input-group>.input-group-prepend:not(:first-child)>.input-group-text,\r\n.input-group>.input-group-prepend:first-child>.btn:not(:first-child),\r\n.input-group>.input-group-prepend:first-child>.input-group-text:not(:first-child),\r\n.input-group>.custom-select:not(:first-child),\r\n.input-group>.form-control:not(:first-child) {\r\n    border-top-left-radius: $input-border-radius;\r\n    border-bottom-left-radius: $input-border-radius;\r\n    border-top-right-radius: 0;\r\n    border-bottom-right-radius: 0;\r\n}\r\n\r\n.list-inline-item{\r\n    &:not(:last-child) {\r\n        margin-left: 6px;\r\n        margin-right: 0px;\r\n    }\r\n}", "// stylelint-disable property-blacklist\n// Single side border-radius\n\n@mixin border-radius($radius: $border-radius, $fallback-border-radius: false) {\n  @if $enable-rounded {\n    border-radius: $radius;\n  }\n  @else if $fallback-border-radius != false {\n    border-radius: $fallback-border-radius;\n  }\n}\n\n@mixin border-top-radius($radius) {\n  @if $enable-rounded {\n    border-top-left-radius: $radius;\n    border-top-right-radius: $radius;\n  }\n}\n\n@mixin border-right-radius($radius) {\n  @if $enable-rounded {\n    border-top-right-radius: $radius;\n    border-bottom-right-radius: $radius;\n  }\n}\n\n@mixin border-bottom-radius($radius) {\n  @if $enable-rounded {\n    border-bottom-right-radius: $radius;\n    border-bottom-left-radius: $radius;\n  }\n}\n\n@mixin border-left-radius($radius) {\n  @if $enable-rounded {\n    border-top-left-radius: $radius;\n    border-bottom-left-radius: $radius;\n  }\n}\n\n@mixin border-top-left-radius($radius) {\n  @if $enable-rounded {\n    border-top-left-radius: $radius;\n  }\n}\n\n@mixin border-top-right-radius($radius) {\n  @if $enable-rounded {\n    border-top-right-radius: $radius;\n  }\n}\n\n@mixin border-bottom-right-radius($radius) {\n  @if $enable-rounded {\n    border-bottom-right-radius: $radius;\n  }\n}\n\n@mixin border-bottom-left-radius($radius) {\n  @if $enable-rounded {\n    border-bottom-left-radius: $radius;\n  }\n}\n", "// stylelint-disable declaration-no-important\n\n// <PERSON><PERSON> and Padding\n\n@each $breakpoint in map-keys($grid-breakpoints) {\n  @include media-breakpoint-up($breakpoint) {\n    $infix: breakpoint-infix($breakpoint, $grid-breakpoints);\n\n    @each $prop, $abbrev in (margin: m, padding: p) {\n      @each $size, $length in $spacers {\n        .#{$abbrev}#{$infix}-#{$size} { #{$prop}: $length !important; }\n        .#{$abbrev}t#{$infix}-#{$size},\n        .#{$abbrev}y#{$infix}-#{$size} {\n          #{$prop}-top: $length !important;\n        }\n        .#{$abbrev}r#{$infix}-#{$size} {\n          #{$prop}-left: $length !important;\n          #{$prop}-right: 0 !important;\n        }\n        .#{$abbrev}b#{$infix}-#{$size},\n        .#{$abbrev}y#{$infix}-#{$size} {\n          #{$prop}-bottom: $length !important;\n        }\n        .#{$abbrev}l#{$infix}-#{$size} {\n          #{$prop}-right: $length !important;\n          #{$prop}-left: 0 !important;\n        }\n      }\n    }\n\n    // Negative margins (e.g., where `.mb-n1` is negative version of `.mb-1`)\n    @each $size, $length in $spacers {\n      @if $size != 0 {\n        .m#{$infix}-n#{$size} { margin: -$length !important; }\n        .mt#{$infix}-n#{$size},\n        .my#{$infix}-n#{$size} {\n          margin-top: -$length !important;\n        }\n        .mr#{$infix}-n#{$size},\n        .mx#{$infix}-n#{$size} {\n          margin-right: -$length !important;\n        }\n        .mb#{$infix}-n#{$size},\n        .my#{$infix}-n#{$size} {\n          margin-bottom: -$length !important;\n        }\n        .ml#{$infix}-n#{$size},\n        .mx#{$infix}-n#{$size} {\n          margin-left: -$length !important;\n        }\n      }\n    }\n\n    // Some special margin utils\n    .m#{$infix}-auto { margin: auto !important; }\n    .mt#{$infix}-auto,\n    .my#{$infix}-auto {\n      margin-top: auto !important;\n    }\n    .mr#{$infix}-auto,\n    .mx#{$infix}-auto {\n      margin-left: auto !important;\n      margin-right: inherit !important;\n    }\n    .mb#{$infix}-auto,\n    .my#{$infix}-auto {\n      margin-bottom: auto !important;\n    }\n    .ml#{$infix}-auto,\n    .mx#{$infix}-auto {\n      margin-right: auto !important;\n      margin-left: auto !important;\n    }\n  }\n}\n", "// stylelint-disable declaration-no-important\n\n@each $breakpoint in map-keys($grid-breakpoints) {\n  @include media-breakpoint-up($breakpoint) {\n    $infix: breakpoint-infix($breakpoint, $grid-breakpoints);\n\n    .float#{$infix}-left  { float: right !important; }\n    .float#{$infix}-right { float: left !important; }\n    .float#{$infix}-none  { float: none !important; }\n  }\n}\n", "// stylelint-disable declaration-no-important\n\n//\n// Text\n//\n\n.text-monospace { font-family: $font-family-monospace !important; }\n\n// Alignment\n\n.text-justify  { text-align: justify !important; }\n.text-wrap     { white-space: normal !important; }\n.text-nowrap   { white-space: nowrap !important; }\n.text-truncate { @include text-truncate; }\n\n// Responsive alignment\n\n@each $breakpoint in map-keys($grid-breakpoints) {\n  @include media-breakpoint-up($breakpoint) {\n    $infix: breakpoint-infix($breakpoint, $grid-breakpoints);\n\n    .text#{$infix}-left   { text-align: right !important; }\n    .text#{$infix}-right  { text-align: left !important; }\n    .text#{$infix}-center { text-align: center !important; }\n  }\n}\n\n// Transformation\n\n.text-lowercase  { text-transform: lowercase !important; }\n.text-uppercase  { text-transform: uppercase !important; }\n.text-capitalize { text-transform: capitalize !important; }\n\n// Weight and italics\n\n.font-weight-light   { font-weight: $font-weight-light !important; }\n.font-weight-lighter { font-weight: $font-weight-lighter !important; }\n.font-weight-normal  { font-weight: $font-weight-normal !important; }\n.font-weight-bold    { font-weight: $font-weight-bold !important; }\n.font-weight-bolder  { font-weight: $font-weight-bolder !important; }\n.font-italic         { font-style: italic !important; }\n\n// Contextual colors\n\n.text-white { color: $white !important; }\n\n@each $color, $value in $theme-colors {\n  @include text-emphasis-variant(\".text-#{$color}\", $value);\n}\n\n.text-body { color: $body-color !important; }\n.text-muted { color: $text-muted !important; }\n\n.text-black-50 { color: rgba($black, .5) !important; }\n.text-white-50 { color: rgba($white, .5) !important; }\n\n// Misc\n\n.text-hide {\n  @include text-hide($ignore-warning: true);\n}\n\n.text-decoration-none { text-decoration: none !important; }\n\n.text-break {\n  word-break: break-word !important; // IE & < Edge 18\n  overflow-wrap: break-word !important;\n}\n\n// Reset\n\n.text-reset { color: inherit !important; }\n", "// Text truncate\n// Requires inline-block or block for proper styling\n\n@mixin text-truncate() {\n  overflow: hidden;\n  text-overflow: ellipsis;\n  white-space: nowrap;\n}\n", "// stylelint-disable declaration-no-important\n\n// Typography\n\n@mixin text-emphasis-variant($parent, $color) {\n  #{$parent} {\n    color: $color !important;\n  }\n  @if $emphasized-link-hover-darken-percentage != 0 {\n    a#{$parent} {\n      @include hover-focus {\n        color: darken($color, $emphasized-link-hover-darken-percentage) !important;\n      }\n    }\n  }\n}\n", "// Hover mixin and `$enable-hover-media-query` are deprecated.\n//\n// Originally added during our alphas and maintained during betas, this mixin was\n// designed to prevent `:hover` stickiness on iOS-an issue where hover styles\n// would persist after initial touch.\n//\n// For backward compatibility, we've kept these mixins and updated them to\n// always return their regular pseudo-classes instead of a shimmed media query.\n//\n// Issue: https://github.com/twbs/bootstrap/issues/25195\n\n@mixin hover {\n  &:hover { @content; }\n}\n\n@mixin hover-focus {\n  &:hover,\n  &:focus {\n    @content;\n  }\n}\n\n@mixin plain-hover-focus {\n  &,\n  &:hover,\n  &:focus {\n    @content;\n  }\n}\n\n@mixin hover-focus-active {\n  &:hover,\n  &:focus,\n  &:active {\n    @content;\n  }\n}\n", "// CSS image replacement\n@mixin text-hide($ignore-warning: false) {\n  // stylelint-disable-next-line font-family-no-missing-generic-family-keyword\n  font: 0/0 a;\n  color: transparent;\n  text-shadow: none;\n  background-color: transparent;\n  border: 0;\n\n  @include deprecate(\"`text-hide()`\", \"v4.1.0\", \"v5\", $ignore-warning);\n}\n", "// \r\n// structure-rtl.scss\r\n//\r\n\r\n\r\n\r\n// topbar.scss\r\n\r\n\r\n.logo-box{\r\n    float: right;\r\n}\r\n\r\n.navbar-custom {\r\n    padding: 0px 0px 0px 10px;\r\n\r\n    .topnav-menu {\r\n        >li {\r\n            float: right;\r\n        }\r\n\r\n        .nav-link {\r\n            direction: ltr;\r\n        }\r\n    }\r\n    \r\n    .app-search{\r\n        margin-left: 20px;\r\n        .form-control{\r\n            padding-right: 20px;\r\n            padding-left: 0;\r\n            border-radius: 0 30px 30px 0;\r\n        }\r\n\r\n        .input-group-append {\r\n            margin-right: 0;\r\n        }\r\n        .btn{\r\n            border-radius: 30px 0 0 30px;\r\n        }\r\n    }\r\n}\r\n\r\n\r\n/* Notification */\r\n.notification-list {\r\n\r\n    .noti-icon-badge {\r\n        left: 14px;\r\n        right: auto;\r\n    }\r\n\r\n    .notify-item {\r\n\r\n        .notify-icon {\r\n            float: right;\r\n            margin-left: 10px;\r\n            margin-right: 0;\r\n        }\r\n\r\n        .notify-details {\r\n            margin-left: 0;\r\n            margin-right: 45px;\r\n        }\r\n    }\r\n\r\n    .profile-dropdown {\r\n        i {\r\n            margin-left: 5px;\r\n            margin-right: 0px;\r\n            float: right;\r\n        }\r\n    }\r\n}\r\n\r\n// page-title\r\n\r\n.page-title-box {\r\n    .page-title-right {\r\n        float: left;\r\n    }\r\n}\r\n\r\n\r\n// Left-sidebar\r\n\r\n.content-page {\r\n    margin-right: $leftbar-width;\r\n    margin-left: 0;\r\n}\r\n\r\n// Sidebar\r\n#sidebar-menu {\r\n    >ul {\r\n        >li {\r\n            >a {\r\n                i {\r\n                    margin: 0 3px 0 10px;\r\n                }\r\n\r\n                .drop-arrow {\r\n                    float: left;\r\n\r\n                    i {\r\n                        margin-left: 0;\r\n                    }\r\n                }\r\n            }\r\n\r\n            >ul {\r\n                padding-right: 40px;\r\n                padding-left: 0;\r\n\r\n                ul {\r\n                    padding-right: 20px;\r\n                    padding-left: 0;\r\n                }\r\n            }\r\n        }\r\n    }\r\n\r\n    .menu-arrow {\r\n        left: 20px;\r\n        right: auto;\r\n\r\n        &:before {\r\n            content: \"\\F141\";\r\n        }\r\n    }\r\n\r\n    li.mm-active {\r\n        >a {\r\n            >span.menu-arrow {\r\n                transform: rotate(-90deg);\r\n            }\r\n        }\r\n    }\r\n}\r\n\r\n// Enlarge menu\r\n.enlarged {\r\n\r\n    // Side menu\r\n    .left-side-menu {\r\n\r\n        // Sidebar Menu\r\n        #sidebar-menu {\r\n\r\n            >ul {\r\n                >li {\r\n\r\n                    >a {\r\n                        i {\r\n                            margin-left: 20px;\r\n                            margin-right: 5px;\r\n                        }\r\n\r\n                        span {\r\n                            padding-right: 25px;\r\n                            padding-left: 0;\r\n                        }\r\n                    }\r\n\r\n                    &:hover {\r\n\r\n                        >ul {\r\n                            right: $leftbar-width-collapsed;\r\n                            left: auto;\r\n                        }\r\n                    }\r\n                }\r\n\r\n                ul {\r\n\r\n                    li {\r\n                        &:hover {\r\n                            >ul {\r\n                                right: 190px;\r\n                                margin-top: -36px;\r\n                            }\r\n                        }\r\n\r\n                        >a {\r\n                            span.pull-right {\r\n                                left: 20px;\r\n                                right: 0;\r\n                            }\r\n                        }\r\n                    }\r\n                }\r\n            }\r\n        }\r\n    }\r\n\r\n    // Navbar\r\n\r\n    .navbar-custom{\r\n        right: 0px;\r\n    }\r\n\r\n    // Content Page\r\n    .content-page {\r\n        margin-right: $leftbar-width-collapsed !important;\r\n        margin-left: 0 !important;\r\n    }\r\n\r\n    //Footer\r\n    .footer {\r\n        left: 0 !important;\r\n        right: $leftbar-width-collapsed !important;\r\n    }\r\n}\r\n\r\n@include media-breakpoint-down(sm) {\r\n\r\n    .content-page,\r\n    .enlarged .content-page {\r\n        margin-right: 0 !important;\r\n    }\r\n}\r\n\r\n@media (min-width: 1025px){\r\n    .navbar-custom {\r\n        .button-menu-mobile{\r\n            margin-left: 0;\r\n            margin-right: 8px;\r\n        }\r\n    }\r\n}\r\n\r\n/* =============\r\n  Small Menu\r\n============= */\r\n\r\n.left-side-menu-sm {\r\n\r\n    .left-side-menu {\r\n        #sidebar-menu {\r\n            >ul {\r\n                ul {\r\n                    padding-right: 0;\r\n                }\r\n            }\r\n        }\r\n\r\n        &+.content-page {\r\n            margin-right: $leftbar-width-sm;\r\n            margin-left: 0;\r\n        }\r\n\r\n        +.content-page .footer {\r\n            left: auto;\r\n            right: $leftbar-width-sm;\r\n        }\r\n    }\r\n}\r\n\r\n.enlarged.left-side-menu-sm {\r\n    #wrapper {\r\n        .left-side-menu {\r\n            text-align: right;\r\n\r\n            ul {\r\n                li {\r\n                    a {\r\n                        i {\r\n                            margin-right: 3px;\r\n                            margin-left: 15px;\r\n                        }\r\n                    }\r\n                }\r\n            }\r\n        }\r\n    }\r\n}\r\n\r\n// footer.scss\r\n\r\n.footer {\r\n    left: 0;\r\n    right: $leftbar-width;\r\n}\r\n\r\n@include media-breakpoint-down(sm) {\r\n    .footer {\r\n        right: 0 !important;\r\n    }\r\n}\r\n\r\n// right-sidebar.scss\r\n//\r\n\r\n.right-bar {\r\n    float: left !important;\r\n    left: -($rightbar-width + 10px);\r\n    right: auto;\r\n}\r\n\r\n.right-bar-enabled {\r\n    .right-bar {\r\n        left: 0;\r\n        right: auto;\r\n    }\r\n}\r\n\r\n.activity-widget{\r\n    .activity-list{\r\n        border-left: 0;\r\n        border-right: 2px dashed $gray-400;\r\n        padding-right: 24px;\r\n        padding-left: 0px;\r\n        &::after{\r\n            right: -7px;\r\n        }\r\n    }\r\n}\r\n\r\n// Topbar light\r\n\r\n.topbar-light {\r\n    .navbar-custom{\r\n        box-shadow: -$leftbar-width 1px 0 0 $gray-100;\r\n    }\r\n}   \r\n\r\n.enlarged{\r\n    &.left-side-menu-dark{\r\n        #wrapper .navbar-custom{\r\n            box-shadow: -$leftbar-width-collapsed  1px 0 0 $gray-100;\r\n        }\r\n    }\r\n}", "//\n// components-rtl.scss\n//\n\n\n// Inbox-widget\n\n.inbox-widget {\n  .inbox-item {\n    .inbox-item-img {\n      float: right;\n      margin-left: 15px;\n      margin-right: 0;\n    }\n\n    .inbox-item-date {\n      right: auto;\n      left: 5px;\n    }\n  }\n}\n\n// widget user\n\n.user-position{\n  left: 0px;\n  right: auto;\n}\n\n\n// icons\n\n.icons-list-demo{\n  i{\n    margin-left: 12px;\n    margin-right: 0;\n  }\n}\n\n\n// Custom-radio\n\n.checkbox {\n  label {\n    padding-right: 8px;\n    padding-left: 0;\n\n    &::before {\n      left: auto;\n      right: 0;\n      margin-left: 0;\n      margin-right: -18px;\n    }\n\n    &::after {\n      left: auto;\n      right: 0;\n      margin-right: -18px;\n      margin-left: 0;\n      padding-left: 0;\n      padding-right: 3px;\n    }\n  }\n\n  input[type=\"checkbox\"]:checked+label {\n    &::after {\n      left: auto;\n      right: 7px;\n      transform: rotate(45deg);\n    }\n  }\n}\n\n.checkbox.checkbox-single {\n  label {\n    &:before {\n      margin-right: 0;\n    }\n\n    &:after {\n      margin-right: 0;\n    }\n  }\n}\n\n\n// custom-radio\n\n.radio {\n  label {\n    padding-left: 0;\n    padding-right: 8px;\n\n    &::before {\n      left: auto;\n      right: 0;\n      margin-left: 0;\n      margin-right: -18px;\n    }\n\n    &::after {\n      left: 0;\n      right: 6px;\n      margin-left: 0;\n      margin-right: -20px;\n    }\n  }\n}\n\n// Invoice\n\n@media print {\n  .content-page,\n  .content,\n  body {\n      margin-right: 0;\n  }\n}\n\n// Demos button \n.demos-show-btn {\n  left: 0;\n  right: auto;\n  border-radius: 0 6px 6px 0;\n}\n\n", "// \r\n// plugins-rtl.scss\r\n//\r\n\r\n// custombox modal\r\n\r\n.custom-modal-title{\r\n    text-align: right;\r\n}\r\n\r\n.modal-demo {\r\n    .close{\r\n        left: 25px;\r\n        right: auto;\r\n    }\r\n}\r\n\r\n// flot chart\r\n\r\n.legendLabel {\r\n    padding-left: 20px;\r\n    padding-right: 5px;\r\n}\r\n\r\n// Select 2\r\n\r\n.select2-container {\r\n    .select2-selection--single {\r\n        .select2-selection__rendered {\r\n            padding-right: 12px;\r\n        }\r\n\r\n        .select2-selection__arrow {\r\n            left: 3px;\r\n            right: auto;\r\n        }\r\n    }\r\n\r\n    .select2-selection--multiple {\r\n        .select2-selection__choice {\r\n            float: right;\r\n            margin-left: 5px;\r\n            margin-right: 0;\r\n        }\r\n    }\r\n\r\n    .select2-search--inline {\r\n        float: right;\r\n    }\r\n}\r\n\r\n// Multiple select\r\n\r\n.ms-container {\r\n    .ms-optgroup-label {\r\n        padding: 5px 5px 0px 0;\r\n    }\r\n}\r\n\r\n// X-ediatable \r\n\r\n.editable-buttons {\r\n    margin-left: 0;\r\n    margin-right: 7px;\r\n\r\n    .editable-cancel {\r\n        margin-left: 0;\r\n        margin-right: 7px;\r\n    }\r\n}\r\n\r\n\r\n// datatable\r\n\r\n.dataTables_wrapper {\r\n    .dataTables_filter{\r\n        text-align: left !important;\r\n        input{\r\n            margin-left: 0px !important;\r\n            margin-right: 0.5em;\r\n        }\r\n    }\r\n}\r\n\r\n.dataTables_length, .dt-buttons, #datatable-colvid_info{\r\n    float: right;\r\n}\r\n\r\ndiv{\r\n    &.ColVis{\r\n        margin-right: 0;\r\n        margin-left: 30px;\r\n    }\r\n}\r\n\r\nbutton.ColVis_Button{\r\n    margin-right: 0;\r\n    margin-right: 3px;\r\n}\r\n\r\nul.ColVis_collection {\r\n    li {\r\n        span{\r\n            padding-left: 0;\r\n            padding-right: 0.5em;\r\n        }\r\n    }\r\n}\r\n\r\n// Responsive Table\r\n\r\n.responsive-table-plugin {\r\n    .btn-group.pull-right {\r\n        float: left;\r\n    }\r\n    .checkbox-row {\r\n        label{\r\n            &:after{\r\n                margin-left: -22px;\r\n                top: -2px;\r\n            }\r\n        }\r\n    }\r\n}\r\n\r\n// tablesaw\r\n\r\n.tablesaw-columntoggle-popup {\r\n    .tablesaw-btn-group {\r\n        > label {\r\n            input{\r\n                margin-right: 0;\r\n                margin-left: .8em;\r\n            }\r\n        }\r\n    }\r\n}\r\n\r\n.tablesaw-bar {\r\n    .tablesaw-bar-section {\r\n        .tablesaw-btn{\r\n            margin-left: 0;\r\n            margin-right: .4em;\r\n        }\r\n    }\r\n}", "//\n// pages-rtl.scss\n//\n\n// coming-soon\n\n.diamond{\n  .top{\n    &:before{\n      margin-left: 0px;\n      margin-right: -13px;\n      transform: skew(48deg);\n    }\n\n    &:after{\n      margin-left: 0px;\n      margin-right: 47px;\n      transform: skew(-48deg);\n    }\n  }\n  \n  .bot{\n    &:before{\n      margin-left: 0px;\n      margin-right: -27px;\n    }\n  }\n\n  &:after{\n    margin-left: 0px;\n    margin-right: 38px;\n  }\n}"]}