@extends('warehouse.content')
@section('resource')
@vite(['resources/js/style.js','resources/js/pengajuan/Confirmationho.js'])

@endsection
@section('contentho')
<div class="container bgwhite shadow-kit card">
    <h1>User List</h1>
    <a href="/users/create">Add User</a>
    <table>
        <tr>
            <th>NIP</th>
            <th>Username</th>
            <th>Email</th>
            <th>Role</th>
            <th>Aksi</th>
        </tr>
        @foreach ($users as $user)
        <tr>
            <td>{{ $user->nip }}</td>
            <td>{{ $user->username }}</td>
            <td>{{ $user->email }}</td>
            <td>{{ $user->role }}</td>
            <td>
                <form action="/users/{{ $user->id }}" method="POST">
                    @csrf
                    @method('DELETE')
                    <button type="submit">Delete</button>
                </form>
            </td>
        </tr>
        @endforeach
    </table>
</div>
@endsection