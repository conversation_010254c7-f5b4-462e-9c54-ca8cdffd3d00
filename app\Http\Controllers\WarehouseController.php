<?php

namespace App\Http\Controllers;

use App\Models\LogAktivitas;
use App\Models\Part;
use App\Models\PartInventory;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Validator;

class WarehouseController extends Controller
{
    public function index()
    {
        return view('warehouse.dashboard');
    }

    public function createPartList(Request $request)
    {
        $query = DB::table('part_inventories')
            ->join('sites', 'part_inventories.site_id', '=', 'sites.site_id')
            ->join('parts', 'part_inventories.part_code', '=', 'parts.part_code')
            ->select(
                'part_inventories.part_inventory_id',
                'parts.part_code',
                'parts.part_name',
                'sites.site_name',
                'part_inventories.min_stock',
                'part_inventories.max_stock',
                'part_inventories.stock_quantity',
                'part_inventories.location',
                'sites.site_id'
            );

        if ($request->has('site_filter')) {
            $query->where('sites.site_id', $request->site_filter);
        }

        if ($request->has('search')) {
            $searchTerm = $request->search;
            $query->where(function ($q) use ($searchTerm) {
                $q->where('parts.part_code', 'LIKE', "%$searchTerm%")
                    ->orWhere('parts.part_name', 'LIKE', "%$searchTerm%")
                    ->orWhere('sites.site_name', 'LIKE', "%$searchTerm%")
                    ->orWhere('part_inventories.location', 'LIKE', "%$searchTerm%");
            });
        }

        $parts = $query->paginate(10);
        $sites = DB::table('sites')->get();

        return view('warehouse.create_part_list', compact('parts', 'sites'));
    }

    public function storePart(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'part_code' => 'required|unique:parts,part_code',
            'part_name' => 'required',
            'part_type' => 'required|in:AC,TYRE,FABRIKASI,PERLENGKAPAN AC,PERSEDIAAN LAINNYA',
            'bin_location' => 'nullable',
        ]);

        if ($validator->fails()) {
            return redirect()->back()
                ->withErrors($validator)
                ->withInput();
        }

        DB::beginTransaction();
        try {
            DB::table('parts')->insert([
                'part_code' => $request->part_code,
                'part_name' => $request->part_name,
                'part_type' => $request->part_type,
                'bin_location' => $request->bin_location,
                'created_at' => now(),
                'updated_at' => now(),
            ]);

            DB::commit();
            return redirect()->back()->with([
                'alert_type' => 'success',
                'alert_title' => 'Berhasil',
                'alert_message' => 'Part berhasil ditambahkan'
            ]);
        } catch (\Exception $e) {
            DB::rollBack();
            return redirect()->back()->with([
                'alert_type' => 'error',
                'alert_title' => 'Gagal!',
                'alert_message' => 'Gagal menambahkan part: ' . $e->getMessage()
            ]);
        }
    }

    public function updatePart(Request $request, $id)
    {
        $request->validate([
            'min_stock' => 'required|integer',
            'max_stock' => 'required|integer',
            'stock_quantity' => 'required|numeric',
            'location' => 'required|string'
        ]);

        // Get part inventory data before update
        $partInventory = DB::table('part_inventories')
            ->join('parts', 'part_inventories.part_code', '=', 'parts.part_code')
            ->where('part_inventory_id', $id)
            ->select('part_inventories.*', 'parts.part_name')
            ->first();

        // Update the part inventory
        DB::table('part_inventories')
            ->where('part_inventory_id', $id)
            ->update($request->only(['min_stock', 'max_stock', 'stock_quantity', 'location']));

        // Log the activity
        LogAktivitas::create([
            'site_id' => session('site_id'),
            'name' => session('name'),
            'action' => 'Mengubah Data Part',
            'description' => "User " . session('name') . " mengubah Data Part " . $partInventory->part_name . " min = " . $request->min_stock . " max = " . $request->max_stock,
            'table' => "Part Inventory",
            'ip_address' => $request->ip(),
        ]);

        return redirect()->back()->with([
            'alert_type' => 'success',
            'alert_title' => 'Berhasil ',
            'alert_message' => 'Part berhasil Diperbaharui'
        ]);
    }
}