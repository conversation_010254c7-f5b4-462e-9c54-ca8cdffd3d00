<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class StockTransaction extends Model
{
    
    use HasFactory;
    protected $table = 'stock_transactions'; // Nama tabel di database
    protected $primaryKey = 'stock_transaction_id'; // Nama kolom primary key
    public $timestamps = true; // Aktifkan timestamps (created_at, updated_at)
    protected $keyType = 'string';
    public $incrementing = false;

    protected $fillable = [
        'part_code',
        'transaction_type',
        'destination_siteid',
        'transaction_date',
        'quantity_sent',
        'quantity_received',
        'quantity_returned',
        'status',
        'discrepancy_reason',
        'discrepancy_resolution',
        'quantity_discrepancy',
        'discrepancy_status',
        'discrepancy_type',
        'notes',
        'surat_jalan_path',
        'requisition_details_id',
    ];
    protected $casts = [
        'transaction_date' => 'datetime',
        'quantity_sent' => 'float',
        'quantity_received' => 'float',
        'quantity_returned' => 'float',
        'quantity_discrepancy' => 'float',
    ];

    public function part()
    {
        return $this->belongsTo(Part::class, 'part_code', 'part_code');
    }
    public function site()
    {
        return $this->belongsTo(Site::class, 'destination_siteid', 'site_id');
    }
}
