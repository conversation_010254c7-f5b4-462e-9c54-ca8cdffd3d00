<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class UnitPart extends Model
{
    protected $fillable = [
        'unit_id',
        'part_inventory_id',
        'quantity',
        'price',
        'eum'
    ];

    protected $casts = [
        'price' => 'float',
        'quantity' => 'float'
    ];

    public function unit()
    {
        return $this->belongsTo(Unit::class);
    }

    public function partInventory()
    {
        return $this->belongsTo(PartInventory::class, 'part_inventory_id', 'part_inventory_id');
    }
}