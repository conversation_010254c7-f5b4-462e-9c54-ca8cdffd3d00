<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('daily_report_images', function (Blueprint $table) {
            // Update the enum to include 'unit' type
            $table->enum('type', ['before', 'after', 'unit'])->change();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('daily_report_images', function (Blueprint $table) {
            // Revert back to original enum values
            $table->enum('type', ['before', 'after'])->change();
        });
    }
};
