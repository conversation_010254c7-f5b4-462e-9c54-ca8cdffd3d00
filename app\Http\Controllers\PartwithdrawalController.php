<?php
// app/Http/Controllers/WithdrawalController.php
namespace App\Http\Controllers;

use App\Models\Notification;
use App\Models\Part;
use App\Models\Site;
use App\Models\PartWithdrawal;
use App\Models\PartInventory;
use App\Models\SiteOutStock;
use App\Models\WarehouseInStock;
use App\Models\LogAktivitas;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;

class PartwithdrawalController extends Controller
{
    public function __construct()
    {
        if (session('role') != 'adminho') {
            redirect()->route('logout');
        }
    }
    public function index()
    {
        $parts = Part::all();
        $sites = Site::all();
        return view('withdrawals.index', compact('parts', 'sites'));
    }

    public function getPartSuggestions(Request $request)
    {
        $query = $request->get('query');
        $parts = Part::join('part_inventories', 'parts.part_code', '=', 'part_inventories.part_code')
            ->join('sites', 'part_inventories.site_id', '=', 'sites.site_id')
            ->where(function ($q) use ($query) {
                $q->where('parts.part_code', 'like', '%' . $query . '%')
                    ->orWhere('parts.part_name', 'like', '%' . $query . '%');
            })
            ->where('part_inventories.stock_quantity', '>', 0)
            ->select(
                'parts.part_code',
                'parts.part_name',
                'parts.part_type',
                'part_inventories.stock_quantity',
                'sites.site_name',
                'sites.site_id'
            )
            ->limit(10)
            ->get();

        return response()->json($parts);
    }

    public function getWithdrawals(Request $request)
    {
        $page = $request->input('page', 1);
        $perPage = $request->input('per_page', 15); // Default to 15 items per page

        $query = PartWithdrawal::with(['part', 'fromSite']);

        // Filter Status
        if ($request->has('status') && $request->status != '') {
            $query->where('status', $request->status);
        }

        // Filter Site
        if ($request->has('site') && $request->site != '') {
            $query->where('from_site_id', $request->site);
        }

        //Ordering
        $orderColumn = $request->input('order.0.column', 1);
        $orderDirection = $request->input('order.0.dir', 'asc');
        $columns = ['withdrawal_id', 'part_code', 'from_site_id', 'requested_quantity', 'status'];

        $query->orderBy($columns[$orderColumn], $orderDirection);

        // Get total count for pagination
        $total = $query->count();

        // Get paginated data
        $withdrawals = $query->skip(($page - 1) * $perPage)
                           ->take($perPage)
                           ->get();

        return response()->json([
            'data' => $withdrawals,
            'current_page' => (int)$page,
            'per_page' => (int)$perPage,
            'last_page' => ceil($total / $perPage),
            'total' => $total
        ]);
    }
    public function store(Request $request)
    {
        try {
            $validatedData = $request->validate([
                'part_code' => 'required|exists:parts,part_code',
                'from_site_id' => 'required|exists:sites,site_id',
                'requested_quantity' => 'required|integer|min:1',
                'withdrawal_reason' => 'nullable|string',
                'notes' => 'nullable|string',
            ]);
            if ($request->requested_quantity > $request->maxstock) {
                return response()->json(['success' => false, 'message' => 'Pengajuan Penarikan melebihi kuota, Stock Tersisa: ' . $request->maxstock . ' !']); // Tambahkan success
            }
            $withdrawal = PartWithdrawal::create($validatedData);

            // Log the withdrawal creation
            $part = Part::find($validatedData['part_code']);
            $site = Site::find($validatedData['from_site_id']);

            LogAktivitas::create([
                'site_id' => session('site_id'),
                'name' => session('name'),
                'action' => 'Membuat Pengajuan Penarikan Part',
                'description' => 'User ' . session('name') . ' membuat pengajuan penarikan part ' . $part->part_name . ' dari site ' . $site->site_name . ' sebanyak ' . $validatedData['requested_quantity'],
                'table' => 'Part Withdrawals',
                'ip_address' => $request->ip(),
            ]);

            Notification::create([
                'title' => 'Pengajuan Return Part',
                'message' => 'Pengajuan Baru dari HO',
                'type' => 'part_request',
                'routes' => 'returnpart.site',
                'site_id' => $request->from_site_id,
                'from_site' => session('site_id'),
                'is_read' => false,
            ]);

            return response()->json(['success' => true, 'message' => 'Permintaan penarikan berhasil dibuat'], 201); // Tambahkan success
        } catch (\Exception $e) {
            return response()->json(['success' => false, 'message' => 'Gagal membuat permintaan penarikan', 'error' => $e->getMessage()], 500); // Tambahkan success
        }
    }

    public function update(Request $request, $id) // Pastikan parameter $id ada
    {
        try {
            $withdrawal = PartWithdrawal::with(['part', 'fromSite'])->findOrFail($id);

            // Check if this is a status update request (from the edit status modal)
            if ($request->has('status') && !$request->has('part_code')) {
                // Validate status update request
                $validatedData = $request->validate([
                    'status' => 'required|in:Pending,Rejected',
                    'notes' => 'nullable|string',
                ]);

                // Get original values for logging
                $originalStatus = $withdrawal->status;

                // Update the withdrawal
                $withdrawal->status = $validatedData['status'];
                if ($request->has('notes')) {
                    $withdrawal->notes = $validatedData['notes'];
                }
                $withdrawal->save();

                // Log the status update
                $changes = [];
                if ($originalStatus != $validatedData['status']) {
                    $changes[] = "status: {$originalStatus} → {$validatedData['status']}";
                }

                LogAktivitas::create([
                    'site_id' => session('site_id'),
                    'name' => session('name'),
                    'action' => 'Update Status Return Part',
                    'description' => 'User ' . session('name') . ' mengubah status return part ' . $withdrawal->part->part_name . ' dari ' . $withdrawal->fromSite->site_name .
                                    (!empty($changes) ? ' dengan perubahan ' . implode(', ', $changes) : ''),
                    'table' => 'Part Withdrawals',
                    'ip_address' => $request->ip(),
                ]);

                // Create notification for the site
                Notification::create([
                    'title' => 'Update Status Return Part',
                    'message' => 'Status return part ' . $withdrawal->part->part_name . ' diubah menjadi ' . $validatedData['status'],
                    'type' => 'part_request',
                    'routes' => 'returnpart.site',
                    'site_id' => $withdrawal->from_site_id,
                    'from_site' => session('site_id'),
                    'is_read' => false,
                ]);

                return response()->json(['success' => true, 'message' => 'Status return berhasil diperbarui']);
            } else {
                // Regular update request (editing the withdrawal details)
                $validatedData = $request->validate([
                    'part_code' => 'required|exists:parts,part_code',
                    'from_site_id' => 'required|exists:sites,site_id',
                    'requested_quantity' => 'required|integer|min:1',
                    'withdrawal_reason' => 'nullable|string',
                    'notes' => 'nullable|string',
                ]);

                // Get original values for logging
                $originalQuantity = $withdrawal->requested_quantity;

                $withdrawal->update($validatedData);

                // Log the withdrawal update
                $part = Part::find($validatedData['part_code']);
                $site = Site::find($validatedData['from_site_id']);

                $changes = [];
                if ($originalQuantity != $validatedData['requested_quantity']) {
                    $changes[] = "requested_quantity: {$originalQuantity} → {$validatedData['requested_quantity']}";
                }

                LogAktivitas::create([
                    'site_id' => session('site_id'),
                    'name' => session('name'),
                    'action' => 'Mengubah Pengajuan Penarikan Part',
                    'description' => 'User ' . session('name') . ' mengubah pengajuan penarikan part ' . $part->part_name . ' dari ' . $site->site_name .
                                    (!empty($changes) ? ' dengan perubahan ' . implode(', ', $changes) : ''),
                    'table' => 'Part Withdrawals',
                    'ip_address' => $request->ip(),
                ]);

                return response()->json(['success' => true, 'message' => 'Permintaan penarikan berhasil diperbarui']);
            }
        } catch (\Exception $e) {
            return response()->json(['success' => false, 'message' => 'Gagal memperbarui permintaan penarikan', 'error' => $e->getMessage()], 500);
        }
    }

    public function getPartDetails(Request $request)
    {
        $partCode = $request->input('part_code');
        $part = Part::find($partCode);

        if ($part) {
            return response()->json($part);
        } else {
            return response()->json(['message' => 'Part tidak ditemukan'], 404);
        }
    }
    public function destroy(Request $request, $id)
    {
        try {
            $withdrawal = PartWithdrawal::with(['part', 'fromSite'])->findOrFail($id);

            // Log the withdrawal deletion before deleting
            LogAktivitas::create([
                'site_id' => session('site_id'),
                'name' => session('name'),
                'action' => 'Menghapus Pengajuan Penarikan Part',
                'description' => 'User ' . session('name') . ' menghapus pengajuan penarikan part ' . $withdrawal->part->part_name . ' dari ' . $withdrawal->fromSite->site_name,
                'table' => 'Part Withdrawals',
                'ip_address' => $request->ip(),
            ]);

            $withdrawal->delete();

            return response()->json(['success' => true, 'message' => 'Permintaan penarikan berhasil dihapus']); // Tambahkan success
        } catch (\Exception $e) {
            return response()->json(['success' => false, 'message' => 'Gagal menghapus permintaan penarikan', 'error' => $e->getMessage()], 500); // Tambahkan success
        }
    }

    public function show($id)
    {
        $withdrawal = PartWithdrawal::with(['part', 'fromSite'])->findOrFail($id);
        return response()->json($withdrawal);
    }

    public function confirmReceipt($id)
    {
        try {
            DB::beginTransaction();

            $withdrawal = PartWithdrawal::findOrFail($id);
            $partCode = $withdrawal->part_code;
            $fromSiteId = $withdrawal->from_site_id;
            $quantity = $withdrawal->approved_quantity ?? $withdrawal->requested_quantity;

            // Find part inventory in the site and decrease stock
            $siteInventory = PartInventory::where('part_code', $partCode)
                ->where('site_id', $fromSiteId)
                ->first();

            if (!$siteInventory) {
                throw new \Exception("Part inventory not found in site");
            }

            // Check if site has enough stock
            if ($siteInventory->stock_quantity < $quantity) {
                throw new \Exception("Site doesn't have enough stock. Available: {$siteInventory->stock_quantity}");
            }

            // Decrease site stock
            $siteInventory->stock_quantity -= $quantity;
            $siteInventory->save();

            // Create site out stock record
            SiteOutStock::create([
                'part_inventory_id' => $siteInventory->part_inventory_id,
                'site_id' => $fromSiteId,
                'employee_id' => session('employee_id'),
                'date_out' => now(),
                'quantity' => $quantity,
                'status' => 'out stock',
                'notes' => 'Part withdrawal to warehouse: ' . $withdrawal->withdrawal_id,
            ]);

            // Find part inventory in warehouse (site_id = 'WH01') and increase stock
            $warehouseInventory = PartInventory::where('part_code', $partCode)
                ->where('site_id', session('site_id'))
                ->first();
            // Increase warehouse stock
            $warehouseInventory->stock_quantity += $quantity;
            $warehouseInventory->save();

            // Create warehouse in stock record
            WarehouseInStock::create([
                'part_inventory_id' => $warehouseInventory->part_inventory_id,
                'employee_id' => session('employee_id'),
                'date_in' => now(),
                'quantity' => $quantity,
                'notes' => 'Part withdrawal from site: ' . $fromSiteId,
            ]);

            // Update withdrawal status
            $withdrawal->status = 'Completed';
            $withdrawal->save();

            // Log activity
            $partName = Part::where('part_code', $partCode)->first()->part_name;
            LogAktivitas::create([
                'site_id' => session('site_id'),
                'name' => session('name'),
                'action' => 'Konfirmasi Penerimaan Part',
                'description' => "Admin " . session('name') . " Mengkonfirmasi penerimaan part " . $partName . " dari site " . $fromSiteId . " sebanyak " . $quantity,
                'ip_address' => request()->ip(),
                'table' => "Part Withdrawals",
            ]);

            DB::commit();
            return response()->json(['success' => true, 'message' => 'Penerimaan part berhasil dikonfirmasi']);
        } catch (\Exception $e) {
            DB::rollBack();
            return response()->json(['success' => false, 'message' => 'Gagal mengkonfirmasi penerimaan part', 'error' => $e->getMessage()], 500);
        }
    }
}