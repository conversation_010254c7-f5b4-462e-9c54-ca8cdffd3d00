<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class LogAktivitas extends Model
{
    use HasFactory;

    protected $table = 'log_aktivitas';

    protected $fillable = [
        'name',
        'site_id',
        'action',
        'description', // Fixed typo: decription -> description
        'table',
        'ip_address',
    ];

    /**
     * Boot the model.
     */
    protected static function boot()
    {
        parent::boot();

        static::creating(function ($model) {
            // Check if we're using the old column name (decription) instead of the new one (description)
            if (isset($model->attributes['decription']) && !isset($model->attributes['description'])) {
                // Copy the value to the correct column
                $model->description = $model->attributes['decription'];
            }

            // Ensure description is never empty
            if (empty($model->description)) {
                // Create a more specific description based on the action
                $action = $model->action ?? 'aktivitas';
                $table = $model->table ?? 'sistem';
                $name = $model->name ?? session('name');
                $ipAddress = $model->ip_address ?? 'unknown';
                $timestamp = now()->format('Y-m-d H:i:s');

                // Try to extract item information from the action
                $itemInfo = "";
                if (isset($model->attributes['item_name'])) {
                    $itemInfo = ": " . $model->attributes['item_name'];
                }

                // Create a more specific description based on common action patterns
                if (strpos($action, 'Menambahkan') !== false || strpos($action, 'Membuat') !== false) {
                    $model->description = "User {$name} menambahkan {$table}{$itemInfo}. IP: {$ipAddress}, Timestamp: {$timestamp}";
                }
                elseif (strpos($action, 'Mengubah') !== false) {
                    $model->description = "User {$name} mengubah {$table}{$itemInfo}. IP: {$ipAddress}, Timestamp: {$timestamp}";
                }
                elseif (strpos($action, 'Menghapus') !== false || strpos($action, 'Hapus') !== false) {
                    $model->description = "User {$name} menghapus {$table}{$itemInfo}. IP: {$ipAddress}, Timestamp: {$timestamp}";
                }
                else {
                    $model->description = "User {$name} melakukan {$action} pada {$table}{$itemInfo}. IP: {$ipAddress}, Timestamp: {$timestamp}";
                }
            }
        });
    }

    // Relasi ke Site
    public function site()
    {
        return $this->belongsTo(Site::class, 'site_id', 'site_id');
    }
}
