<?php

namespace App\Providers;

use Illuminate\Support\ServiceProvider;

class AppServiceProvider extends ServiceProvider
{
    /**
     * Register any application services.
     */
    public function register(): void
    {
        //
    }

    /**
     * Bootstrap any application services.
     */
    public function boot(): void
    {
        // Set the default timezone for the application
        date_default_timezone_set(config('app.timezone'));

        // Set Carbon locale to Indonesian for date formatting
        \Carbon\Carbon::setLocale('id');

        // Note: <PERSON><PERSON>'s default 'auth' middleware is already registered
        // Custom middleware aliases are registered in bootstrap/app.php
    }
}
