<?php

namespace App\Http\Controllers\Sites;

use Illuminate\Http\Request;
use App\Http\Controllers\Controller;
use App\Models\LogAktivitas;
use App\Models\Notification;
use App\Models\Part;
use App\Models\PartInventory;
use App\Models\PartWithdrawal;
use App\Models\Site;

class PartwithdrawsiteContoller extends Controller
{
    public function __construct()
    {
        if (session('role') != 'adminsite') {
            redirect()->route('logout');
        }
    }
    public function index()
    {
        $parts = Part::all();
        $sites = Site::all();
        return view('sites.partwithdraw', compact('parts', 'sites'));
    }

    public function getPartSuggestions(Request $request)
    {
        $query = $request->get('query');
        $parts = Part::where('part_code', 'like', '%' . $query . '%')
            ->orWhere('part_name', 'like', '%' . $query . '%')
            ->limit(10) // Batasi jumlah sugesti
            ->get();
        return response()->json($parts);
    }

    public function getWithdrawals(Request $request)
    {
        $siteId = session()->get('site_id');
        if (!$siteId) {
            return response()->json(['error' => 'ID Site tidak ditemukan di session'], 403);
        }

        $query = PartWithdrawal::with(['part', 'fromSite'])->where('from_site_id', $siteId);

        // Filter by status
        if ($request->has('status') && $request->status != '') {
            $query->where('status', $request->status);
        }

        // Filter by site
        if ($request->has('site') && $request->site != '') {
            $query->where('from_site_id', $request->site);
        }

        // Filter by date range
        if ($request->has('start_date') && $request->has('end_date')) {
            $startDate = $request->start_date;
            $endDate = $request->end_date;
            $query->whereDate('created_at', '>=', $startDate)
                  ->whereDate('created_at', '<=', $endDate);
        }

        // Urutkan berdasarkan status (Pending > In Transit > Rejected > Complete)
        $query->orderByRaw("FIELD(status, 'Pending', 'In Transit', 'Rejected', 'Complete')");

        $withdrawals = $query->get();

        return response()->json(['data' => $withdrawals]);
    }


    public function getPartDetails(Request $request)
    {
        $partCode = $request->input('part_code');
        $part = Part::find($partCode);

        if ($part) {
            return response()->json($part);
        } else {
            return response()->json(['message' => 'Part tidak ditemukan'], 404);
        }
    }

    public function store(Request $request)
    {
        try {
            $validatedData = $request->validate([
                'part_code' => 'required|exists:parts,part_code',
                'from_site_id' => 'required|exists:sites,site_id',
                'requested_quantity' => 'required|integer|min:1',
                'withdrawal_reason' => 'required|string',
            ]);

            // Check if part exists in site inventory and has enough stock
            $partInventory = PartInventory::where('part_code', $validatedData['part_code'])
                ->where('site_id', $validatedData['from_site_id'])
                ->first();

            if (!$partInventory) {
                return response()->json([
                    'success' => false,
                    'message' => 'Part tidak ditemukan di inventaris site Anda.'
                ], 400);
            }

            if ($partInventory->stock_quantity < $validatedData['requested_quantity']) {
                return response()->json([
                    'success' => false,
                    'message' => 'Jumlah permintaan melebihi stok yang tersedia. Stok tersedia: ' . $partInventory->stock_quantity
                ], 400);
            }

            // Set default values
            $validatedData['status'] = 'In Transit'; // Changed from 'Pending' to 'In Transit'
            $validatedData['previous_stock'] = $partInventory->stock_quantity;
            $validatedData['new_stock'] = $partInventory->stock_quantity - $validatedData['requested_quantity'];
            $validatedData['updated_by'] = session('name');

            // Create withdrawal record
            $withdrawal = PartWithdrawal::create($validatedData);

            // Get part name for logging
            $part = Part::find($validatedData['part_code']);
            $site = Site::find($validatedData['from_site_id']);

            // Create log entry
            LogAktivitas::create([
                'site_id' => session('site_id'),
                'name' => session('name'),
                'action' => 'Membuat Pengajuan Return Part',
                'description' => 'User ' . session('name') . ' membuat pengajuan return part ' . $part->part_name . ' dari site ' . $site->site_name . ' sebanyak ' . $validatedData['requested_quantity'],
                'table' => 'Part Returns',
                'ip_address' => $request->ip(),
            ]);

            // Create notification for warehouse
            Notification::create([
                'title' => 'Pengajuan Return Part',
                'message' => 'Pengajuan Return Baru dari Site ' . session('site_id'),
                'type' => 'part_request',
                'routes' => 'returnpart',
                'site_id' => 'WHO', // Warehouse site ID
                'from_site' => session('site_id'),
                'is_read' => false,
            ]);

            return response()->json(['success' => true, 'message' => 'Permintaan return berhasil dibuat'], 201);
        } catch (\Exception $e) {
            return response()->json(['success' => false, 'message' => 'Gagal membuat permintaan return', 'error' => $e->getMessage()], 500);
        }
    }

    public function destroy(Request $request, $id)
    {
        try {
            $withdrawal = PartWithdrawal::with(['part', 'fromSite'])->findOrFail($id);

            // Only allow deletion if status is In Transit (before warehouse confirmation)
            if ($withdrawal->status !== 'In Transit') {
                return response()->json([
                    'success' => false,
                    'message' => 'Hanya permintaan dengan status In Transit yang dapat dihapus.'
                ], 400);
            }

            // Get part name for logging
            $partName = $withdrawal->part ? $withdrawal->part->part_name : $withdrawal->part_code;
            $siteName = $withdrawal->fromSite ? $withdrawal->fromSite->site_name : $withdrawal->from_site_id;

            // Log the withdrawal deletion
            LogAktivitas::create([
                'site_id' => session('site_id'),
                'name' => session('name'),
                'action' => 'Menghapus Pengajuan Return Part',
                'description' => 'User ' . session('name') . ' menghapus pengajuan return part ' . $partName . ' dari site ' . $siteName . ' sebanyak ' . $withdrawal->requested_quantity,
                'table' => 'Part Returns',
                'ip_address' => $request->ip(),
            ]);

            $withdrawal->delete();

            return response()->json(['success' => true, 'message' => 'Permintaan return berhasil dihapus']);
        } catch (\Exception $e) {
            return response()->json(['success' => false, 'message' => 'Gagal menghapus permintaan return', 'error' => $e->getMessage()], 500);
        }
    }

    public function updateStatus(Request $request, $id)
    {
        try {
            $withdrawal = PartWithdrawal::findOrFail($id);
            if ($withdrawal->part_code) {
                $partCode = $withdrawal->part_code;
                // $partname = $partCode;
                $partname = Part::where('part_code', $partCode)->first()->part_name;
            } else {
                return response()->json(['success' => false, 'message' => 'Withdrawal tidak memiliki relasi part yang valid.'], 400);
            }

            $validatedData = $request->validate([
                'approved_quantity' => 'nullable|integer|min:0',
                'status' => 'required|in:Pending,Approved,Rejected,In Transit,Completed',
                'notes' => 'nullable|string',
            ]);

            // Update the withdrawal with validated data
            $withdrawal->status = $validatedData['status'];
            if (isset($validatedData['approved_quantity'])) {
                $withdrawal->approved_quantity = $validatedData['approved_quantity'];
            }

            // Save notes to withdrawal_reason if provided
            if (isset($validatedData['notes']) && !empty($validatedData['notes'])) {
                $withdrawal->withdrawal_reason = $validatedData['notes'];
            }

            $withdrawal->save();

            LogAktivitas::create([
                'site_id' => session('site_id'),
                'name' => session('name'),
                'action' => 'Update Status Return Part',
                'description' => "User " . session('name') . " Mengubah Status Pengajuan Return Part: " . $partname . ". Status: " . $validatedData['status'] . ". Keterangan: " . ($validatedData['notes'] ?? '-'),
                'ip_address' => $request->ip(),
                'table' => "Penarikan Part",
            ]);

            Notification::create([
                'title' => 'Update pengajuan return',
                'message' => "Status return part: {$partname}",
                'type' => 'other',            // jangan lupa enum('stock', 'part_request', 'other')
                'routes' => 'returnpart',
                'site_id' => 'WHO', //Id dari home asal jangan diubah dari database harusnya aman
                'from_site' => session('site_id'),
                'is_read' => false,
            ]);

            return response()->json(['success' => true, 'message' => 'Status withdrawal berhasil diperbarui']); // Tambahkan success
        } catch (\Exception $e) {
            return response()->json(['success' => false, 'message' => 'Gagal memperbarui status withdrawal', 'error' => $e->getMessage()], 500); // Tambahkan success
        }
    }
}
