import Swal from "sweetalert2";

// Global variables for pagination
let currentSitePage = 1;
const sitePerPage = 5; // 5 items per page for site table

document.addEventListener("DOMContentLoaded", function () {
    const siteTableBody = document.querySelector("#siteTable tbody");
    const siteIdInput = document.getElementById("siteId");
    const siteNameInput = document.getElementById("siteName");
    const addressInput = document.getElementById("address");
    const siteForm = document.getElementById("siteForm");

    // Function to create pagination item
    function createPaginationItem(page, text, isActive = false) {
        const li = document.createElement('li');
        li.className = `page-item ${isActive ? 'active' : ''}`;

        const a = document.createElement('a');
        a.className = 'page-link';
        a.href = '#';
        a.textContent = text;
        a.dataset.page = page;

        li.appendChild(a);
        return li;
    }

    // Function to render pagination
    function renderSitePagination(data) {
        try {
            const paginationContainer = document.getElementById('site-pagination');
            if (!paginationContainer) {
                return;
            }

            paginationContainer.innerHTML = '';

            if (data.last_page > 1) {
                const pagination = document.createElement('ul');
                pagination.className = 'pagination pagination-rounded  justify-content-center';

                // Previous button
                if (data.current_page > 1) {
                    pagination.appendChild(createPaginationItem(data.current_page - 1, '\u00ab'));
                }

                // Page numbers
                for (let i = 1; i <= data.last_page; i++) {
                    pagination.appendChild(createPaginationItem(i, i, i === data.current_page));
                }

                // Next button
                if (data.current_page < data.last_page) {
                    pagination.appendChild(createPaginationItem(data.current_page + 1, '\u00bb'));
                }

                paginationContainer.appendChild(pagination);

                // Add event listeners to pagination links
                paginationContainer.querySelectorAll('.page-link').forEach(link => {
                    link.addEventListener('click', function(e) {
                        e.preventDefault();
                        const page = parseInt(this.dataset.page);
                        loadSites(page);
                    });
                });
            }

            // Update global pagination data
            window.sitePaginationData = data;
        } catch (error) {
            console.error('Error rendering pagination:', error);
        }
    }

    function loadSites(page = 1) {
        // Update current page
        currentSitePage = page;

        // Show loading indicator
        siteTableBody.innerHTML = '<tr><td colspan="4" class="text-center">Loading...</td></tr>';

        fetch(`/sites/data?page=${page}&per_page=${sitePerPage}`)
            .then((response) => {
                if (!response.ok) {
                    throw new Error("Failed to load data");
                }
                return response.json();
            })
            .then((data) => {
                siteTableBody.innerHTML = "";
                let i = ((page - 1) * sitePerPage) + 1; // Calculate starting index based on page

                if (data.data && data.data.length > 0) {
                    data.data.forEach((site) => {
                        const row = document.createElement("tr");
                        row.innerHTML = `
                            <td class="py-1">${i++}</td>
                            <td class="py-1">${site.site_name}</td>
                            <td class="py-1">${site.address || '-'}</td>
                            <td class="py-1">
                                <button class="edit-btn btn-primary text-white px-2 py-1 rounded" data-id="${site.site_id}">Edit</button>
                                <button class="delete-btn btn-danger text-white px-2 py-1 rounded" data-id="${site.site_id}">Delete</button>
                            </td>
                        `;
                        siteTableBody.appendChild(row);
                    });
                } else {
                    siteTableBody.innerHTML = '<tr><td colspan="4" class="text-center">No sites found</td></tr>';
                }

                // Render pagination
                renderSitePagination({
                    current_page: data.current_page,
                    per_page: data.per_page,
                    last_page: data.last_page,
                    total: data.total
                });
            })
            .catch((error) => {
                siteTableBody.innerHTML = '<tr><td colspan="4" class="text-center text-danger">Failed to load data. Please try again.</td></tr>';
                Swal.fire({
                    icon: "error",
                    title: "Oops...",
                    text: "Failed to load sites! " + error,
                });
            });
    }

    siteForm.addEventListener("submit", function (e) {
        e.preventDefault();

        const siteId = siteIdInput.value;
        const url = siteId ? `/sites/${siteId}` : "/sites";
        const method = siteId ? "PUT" : "POST";

        const formData = {
            site_name: siteNameInput.value,
            address: addressInput.value,
        };

        fetch(url, {
            method: method,
            headers: {
                "Content-Type": "application/json",
                "X-CSRF-TOKEN": document
                    .querySelector('meta[name="csrf-token"]')
                    .getAttribute("content"),
            },
            body: JSON.stringify(formData),
        })
            .then((response) => {
                if (!response.ok) {
                    return response.json().then((error) => {
                        throw new Error(error.message || "Failed to save site");
                    });
                }
                return response.json();
            })
            .then((data) => {
                Swal.fire({
                    icon: "success",
                    title: "Success!",
                    text: siteId
                        ? "Site updated successfully!"
                        : "Site added successfully!",
                });
                loadSites(1); // Reload data tabel and reset to page 1
                siteForm.reset(); // Reset form
                siteIdInput.value = ""; // Kosongkan ID
            })
            .catch((error) => {
                Swal.fire({
                    icon: "error",
                    title: "Oops...",
                    text: "Failed to save site! " + error,
                });
            });
    });

    window.editSite = function (id) {
        fetch(`/sites/${id}`)
            .then((response) => {
                if (!response.ok) {
                    return response.json().then((error) => {
                        throw new Error(
                            error.message || "Failed to load site data"
                        );
                    });
                }
                return response.json();
            })
            .then((data) => {
                // Isi form dengan data yang diterima
                siteIdInput.value = data.data.site_id;
                siteNameInput.value = data.data.site_name;
                addressInput.value = data.data.address;
            })
            .catch((error) => {
                Swal.fire({
                    icon: "error",
                    title: "Oops...",
                    text: "Failed to load site data! " + error,
                });
            });
    };

    window.deleteSite = function (id) {
        Swal.fire({
            title: "Are you sure?",
            text: "You won't be able to revert this!",
            icon: "warning",
            showCancelButton: true,
            confirmButtonColor: "#3085d6",
            cancelButtonColor: "#d33",
            confirmButtonText: "Yes, delete it!",
        }).then((result) => {
            if (result.isConfirmed) {
                fetch(`/sites/${id}`, {
                    method: "DELETE",
                    headers: {
                        "X-CSRF-TOKEN": document
                            .querySelector('meta[name="csrf-token"]')
                            .getAttribute("content"),
                    },
                })
                    .then((response) => {
                        if (!response.ok) {
                            return response.json().then((error) => {
                                throw new Error(
                                    error.message || "Failed to delete site"
                                );
                            });
                        }
                        loadSites(1); // Reset to page 1 after delete

                        Swal.fire({
                            icon: "success",
                            title: "Deleted!",
                            text: "Site has been deleted.",
                        });
                    })
                    .catch((error) => {
                        Swal.fire({
                            icon: "error",
                            title: "Oops...",
                            text: "Failed to delete site! " + error,
                        });
                    });
            }
        });
    };

    siteTableBody.addEventListener("click", function (e) {
        if (e.target.classList.contains("edit-btn")) {
            const siteId = e.target.getAttribute("data-id");
            editSite(siteId);
        } else if (e.target.classList.contains("delete-btn")) {
            const siteId = e.target.getAttribute("data-id");
            deleteSite(siteId);
        }
    });
    // Initialize with page 1
    loadSites(1);
});