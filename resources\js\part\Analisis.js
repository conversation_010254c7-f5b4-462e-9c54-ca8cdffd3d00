document.addEventListener('DOMContentLoaded', function() {
    const analyzeButton = document.getElementById('analyzeButton');
    const resultsDiv = document.getElementById('results');
    const startDateInput = document.getElementById('start_date');
    const endDateInput = document.getElementById('end_date');
    const limitInput = document.getElementById('limit');
    const siteIdSelect = document.getElementById('site_id');
    const partCategorySelect = document.getElementById('part_category');

    // Set default values on page load
    const today = new Date();
    const startOfMonth = new Date(today.getFullYear(), today.getMonth(), 1);

    // Format dates as YYYY-MM-DD for input type="date"
    const todayFormatted = today.toISOString().slice(0, 10);
    const startOfMonthFormatted = startOfMonth.toISOString().slice(0, 10);

    startDateInput.value = startOfMonthFormatted;
    endDateInput.value = todayFormatted;
    limitInput.value = 3;

    // Function to perform the analysis
    function performAnalysis() {
        const startDate = startDateInput.value;
        const siteId = siteIdSelect.value;
        const endDate = endDateInput.value;
        const limit = limitInput.value;
        const partCategory = partCategorySelect.value;

        fetch('/inventory-analysis/analyze', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                },
                body: JSON.stringify({
                    start_date: startDate,
                    end_date: endDate,
                    limit: limit,
                    site_id: siteId,
                    part_category: partCategory
                })
            })
            .then(response => response.json())
            .then(data => {
                displayResults(data, limit); // Lewatkan limit ke displayResults
            })
            .catch(error => {
                console.error('Error:', error);
                resultsDiv.innerHTML = '<p class="text-red-500">Error fetching data. ! Harap Input form dengan benar</p>';
            });
    }

    // Event listener for the analyze button
    analyzeButton.addEventListener('click', performAnalysis);

    // Perform analysis on page load with default values
    performAnalysis();


    function displayResults(data, limit) { // Terima limit sebagai argumen
        resultsDiv.innerHTML = '';

        for (const siteId in data) {
            if (data.hasOwnProperty(siteId)) {
                const siteData = data[siteId];
                const siteName = siteData.site_name;
                const topQuantity = siteData.top_quantity;
                const topFrequency = siteData.top_frequency;

                let tableHtml = `
                <div class="mb-8 p-4 border rounded-lg shadow-md">
                    <h2 class="text-xl font-semibold mb-4">Site: ${siteName}</h2>

                    <div class="mb-4">
                        <h3 class="text-lg font-semibold mb-2">${limit} Best Seller Berdasarkan Quantity Part</h3>
                        <table class="table-auto w-full table-hover">
                            <thead class="table-primary">
                                <tr>
                                    <th class="px-4 py-2 text-left">No</th>
                                    <th class="px-4 py-2 text-left">Part Code</th>
                                    <th class="px-4 py-2 text-left">Part Name</th>
                                    <th class="px-4 py-2 text-left">Total Quantity</th>
                                </tr>
                            </thead>
                            <tbody>`;

                            if (topQuantity.length > 0) {
                                let i = 1;
                                topQuantity.forEach(item => {
                                    tableHtml += `
                                <tr>
                                    <td class="border px-4 py-2">${i++}</td>
                                    <td class="border px-4 py-2">${item.part_code}</td>
                                    <td class="border px-4 py-2">${item.part_name}</td>
                                    <td class="border px-4 py-2">${item.total_quantity}</td>
                                </tr>`;
                                });
                            } else {
                                tableHtml += `<tr><td class="text-center border px-4 py-2 font-bold" colspan="4">No data available</td></tr>`;
                            }
                            tableHtml += `
                            </tbody>
                        </table>
                    </div>

                    <div>
                        <h3 class="text-lg font-semibold mb-2">${limit} Best Seller Berdasarkan Jumlah Pemakaian</h3>
                        <table class="table-auto w-full table-hover">
                            <thead class="table-success">
                                <tr>
                                    <th class="px-4 py-2 text-left">No</th>
                                    <th class="px-4 py-2 text-left">Part Code</th>
                                    <th class="px-4 py-2 text-left">Part Name</th>
                                    <th class="px-4 py-2 text-left">Total Frequency</th>
                                </tr>
                            </thead>
                            <tbody>`;

                            if (topFrequency.length > 0) {
                                let j=1;
                                topFrequency.forEach(item => {
                                    tableHtml += `
                                <tr>
                                    <td class="border px-4 py-2">${j++}</td>
                                    <td class="border px-4 py-2">${item.part_code}</td>
                                    <td class="border px-4 py-2">${item.part_name}</td>
                                    <td class="border px-4 py-2">${item.total_frequency}</td>
                                </tr>`;
                                });
                            } else {
                                tableHtml += `<tr><td class="font-bold text-center border px-4 py-2" colspan="4">No data available</td></tr>`;
                            }
                            tableHtml += `
                            </tbody>
                        </table>
                    </div>
                </div>`;
                resultsDiv.innerHTML += tableHtml;
            }
        }

        if (Object.keys(data).length === 0) {
            resultsDiv.innerHTML = '<p>No data found for the selected criteria.</p>';
        }
    }

});                                                                                                                                                                                                                   