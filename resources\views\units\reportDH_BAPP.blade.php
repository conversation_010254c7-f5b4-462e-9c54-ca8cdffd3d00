<!DOCTYPE html>
<html>

<head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
    <title>Berita Acara Pemakaian Part</title>
    <style>
        body {
            font-family: arial, sans-serif;
            font-size: 10px;
            line-height: 1.4;
            margin: 0px 30px 0px 30px;
            padding: 0px;
        }

        .header-container {
            width: 100%;
            padding: 0px;
            margin: 0;
        }

        .logo-left {
            margin-left: 50px;
            float: left;
        }

        .title-center {
            float: left;
            width: 60%;
            text-align: center;
        }

        .title-center p {
            font-size: 12px;
            margin: 0;
            padding: 0;
        }

        .number-right {
            float: right;
            width: 20%;
            text-align: right;
        }

        .header-title {
            padding: 0px;
            font-size: 16px;
            font-weight: bold;
            text-align: center;
            margin-bottom: 0px;
        }

        /* Customer info table */
        .customer-table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 15px;
        }

        .customer-table td {
            padding: 5px;
            vertical-align: top;
        }

        .customer-table .label {
            font-weight: bold;
            width: 120px;
        }

        /* Main table */
        .main-table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 20px;
        }

        .main-table th,
        .main-table td {
            padding: 0px 4px 0px 4px;
            text-align: center;
        }

        .main-table th {
            background-color: #f2f2f2;
            font-weight: bold;
        }

        .main-table .text-left {
            text-align: left;
        }

        .main-table .text-right {
            text-align: right;
        }

        /* Signature section */
        .signature-table {
            width: 100%;
            border-collapse: collapse;
        }

        .signature-table td {
            width: 33.33%;
            padding: 5px;
            text-align: center;
            vertical-align: bottom;
        }

        .clearfix::after {
            content: "";
            clear: both;
            display: table;
        }
    </style>
</head>

<body>
    <!-- Header with logo, title and number -->
    <div class="header-container clearfix">
        <div class="logo-left" style="text-align: center;">
            <img src="{{ public_path('assets/images/logo-small.png') }}" alt="PWB LOGO" style=" width: 60px; height: 60px;">
            <p style="margin: 0 0 5px 0; padding: 0;"><strong>PT PUTERA WIBOWO BORNEO</strong></p>
        </div>
        <div class="title-center">
            <div class="header-title" style="font-weight: bold;">BERITA ACARA PEMAKAIAN PART</div>
            <p>PT. DH SITE ASAM-ASAM</p>
            <p>Tanggal {{ $transactions[0]->tanggalstart ? \Carbon\Carbon::parse($transactions[0]->tanggalstart)->translatedFormat('j F Y') : '-' }} s/d {{ $transactions[0]->tanggalend ? \Carbon\Carbon::parse($transactions[0]->tanggalend)->translatedFormat('j F Y') : '-' }}</p>

            <p style="font-weight: bolder;">NO BA : {{ $transactions[0]->noBA ?? ' - ' }}</p>
        </div>
    </div>

    <!-- Main Content Table -->
    <table class="main-table">
        <thead>
            <tr style="border: 1px solid #000;">
                <th style="width:5%;border: 1px solid #000;">NO</th>
                <th style="width:10%;border: 1px solid #000;">DATE</th>
                <th style="width:15%;border: 1px solid #000;">IREQ</th>
                <th style="width:10%;border: 1px solid #000;">UNIT</th>
                <th style="width:15%;border: 1px solid #000;">PART NUMBER</th>
                <th style="width:25%;border: 1px solid #000;">PART NAME</th>
                <th style="width:5%;border: 1px solid #000;">QTY</th>
                <th style="width:5%;border: 1px solid #000;">UOM</th>
                <th style="width:14%;border: 1px solid #000;">UNIT PRICE</th>
                <th style="width:14%;border: 1px solid #000;">AMOUNT</th>
            </tr>
        </thead>
        <tbody>
            @if(count($transactions) > 0)
            @php
            $no = 1;
            $totalAmount = 0;
            // Sort transactions by MR date
            $sortedTransactions = $transactions->sortBy(function($transaction) {
                return $transaction->mr_date ?? '9999-12-31'; // Default to far future date if null
            });
            @endphp
            @foreach($sortedTransactions as $transaction)
            @php
            // Use MR date for display, consistent with the sorting
            $transactionDate = \Carbon\Carbon::parse($transaction->mr_date ?? now())->translatedFormat('d F Y');
            $sortedParts = $transaction->parts->sortBy('id');
            @endphp
            @foreach($sortedParts as $part)
            @php
            $subtotal = $part->price * $part->quantity;
            $totalAmount += $subtotal;
            @endphp
            <tr>
                <td style="border: 1px solid #000;">{{ $no++ }}</td>
                <td style="border: 1px solid #000;">{{ $transactionDate }}</td>
                <td style="border: 1px solid #000;">{{ $transaction->noireq ?? '-' }}</td>
                <td style="border: 1px solid #000;">{{ $transaction->unit->unit_code }}</td>
                <td style="border: 1px solid #000;">{{ $part->partInventory->part->part_code }}</td>
                <td style="border: 1px solid #000;">{{ $part->partInventory->part->part_name }}</td>
                <td style="border: 1px solid #000;">{{ $part->quantity}}</td>
                <td style="border: 1px solid #000;">{{ $part->eum }}</td>
                <td style="border: 1px solid #000;">
                    <table width="100%" style="border: none; padding: 0; margin: 0;">
                        <tr style="border: 0; padding: 0; margin: 0;">
                            <td style="text-align: left; padding: 0; margin: 0;">Rp</td>
                            <td style="text-align: right; padding: 0; margin: 0;">{{ number_format($part->price, 0, ',', '.') }}</td>
                        </tr>
                    </table>
                </td>
                <td style="border: 1px solid #000;">
                    <table width="100%" style="border: none; padding: 0; margin: 0;">
                        <tr style="border: none; padding: 0; margin: 0;">
                            <td style="text-align: left; padding: 0; margin: 0;">Rp</td>
                            <td style="text-align: right; padding: 0; margin: 0;">{{ number_format($subtotal, 0, ',', '.') }}</td>
                        </tr>
                    </table>
                </td>
            </tr>
            @endforeach
            @endforeach
            <tr>
                <td colspan="8" style="border: none;"></td>
                <td style="text-align: left; font-weight: bold; border: 1px solid #000;">TOTAL</td>
                <td style="text-align: right; border: 1px solid #000;">
                    <span style="float: left;">Rp</span>
                    <span style="float: right;">{{ number_format($totalAmount, 0, ',', '.') }}</span>
                </td>
            </tr>
            <tr>
                <td colspan="8" style="border: none;"></td>
                <td style="text-align: left; font-weight: bold; border: 1px solid #000;">PPN 11%</td>
                <td style="text-align: right; border: 1px solid #000;">
                    <span style="float: left;">Rp</span>
                    <span style="float: right;">{{ number_format($totalAmount * 0.11, 0, ',', '.') }}</span>
                </td>
            </tr>
            <tr>
                <td colspan="8" style="border: none;"></td>
                <td style="text-align: left; font-weight: bold; border: 1px solid #000;">GRAND TOTAL</td>
                <td style="text-align: right; border: 1px solid #000;">
                    <span style="float: left;">Rp</span>
                    <span style="float: right;">{{ number_format($totalAmount * 1.11, 0, ',', '.') }}</span>
                </td>
            </tr>
            @else
            <tr>
                <td colspan="10" class="text-center">Tidak ada data transaksi</td>
            </tr>
            @endif
        </tbody>
    </table>

    <!-- Signature Section -->
    <div class="clearfix">
        <table class="signature-table">
            <tr>
                <td>
                    <div>Approve,</div>
                </td>
                <td>
                    <div>Approve,</div>
                </td>
                <td>
                    <div>Approve,</div>
                </td>
            </tr>
            <tr>
                <td style="padding-top: 60px;">
                    <div>(ADMIN PT PWB)</div>
                </td>
                <td style="padding-top: 60px;">
                    <div>(HOS MHS)</div>
                </td>
                <td style="padding-top: 60px;">
                    <div>(HOD PLM)</div>
                </td>
            </tr>
        </table>
    </div>
</body>

</html>