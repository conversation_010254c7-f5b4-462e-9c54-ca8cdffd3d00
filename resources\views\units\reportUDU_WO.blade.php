<!DOCTYPE html>
<html>

<head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
    <title>Penawaran Part</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            font-size: 11px;
            line-height: 1.4;
            margin: 20px;
        }

        /* Header styles */
        .header-container {
            width: 100%;
            margin-bottom: 100px;
        }

        .logo-left {
            float: left;
            text-align: center;
            width: 25%;
        }

        .title-right {
            margin: 0px auto;
            width: 80%;
            text-align: center;
            display: block;
        }

        .right {
            width: 80%;
            float: right;
            text-align: right;
        }

        .company-name {
            font-weight: bold;
            font-size: 14px;
            margin-top: 5px;
        }

        .title-bar {
            background-color: #e6c34a;
            text-align: center;
            padding: 5px;
            margin: 0;
            font-weight: bold;
            font-size: 14px;
            border: 1px solid #000;
            width: 100%;
        }

        /* Info table styles */
        .info-container {
            width: 100%;
            margin-bottom: 20px;
        }

        .info-left {
            width: 50%;
            float: left;
        }

        .info-right {
            width: 50%;
            float: right;
        }

        .info-row {
            margin-bottom: 5px;
        }

        .info-label {
            display: inline-block;
            width: 100px;
            font-weight: bold;
        }

        .info-value {
            display: inline-block;
        }

        /* Main table styles */
        .main-table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 10px;
        }

        .main-table th,
        .main-table td {
            border: 1px solid #000;
            padding: 1px;
            font-size: 10px;
        }

        .main-table th {
            background-color: #4ca64c;
            color: white;
            font-weight: bold;
        }

        /* Totals table styles */
        .totals-table {
            width: 30%;
            border-collapse: collapse;
            margin-left: auto;
            margin-bottom: 20px;
        }

        .totals-table td {
            border: 1px solid #000;
            padding: 5px;
            font-size: 10px;
        }

        .total-label {
            text-align: left;
            float: left;
            font-weight: bold;
        }

        .total-value {
            text-align: right;
        }

        /* Signature section */
        .signature-table {
            width: 100%;
            margin-top: 30px;
        }

        .signature-left {
            width: 50%;
            float: left;
            text-align: center;
        }

        .signature-right {
            width: 50%;
            float: right;
            text-align: center;
        }

        .signature-name {
            margin-top: 50px;
            font-weight: bold;
        }

        .signature-title {
            font-style: italic;
        }

        /* Utility classes */
        .clearfix::after {
            content: "";
            clear: both;
            display: table;
        }

        .text-right {
            text-align: right;
        }

        .text-center {
            text-align: center;
        }

        .text-left {
            text-align: left;
        }
    </style>
</head>

<body>
    <!-- Header with company info and title -->
    <div class="header-container">
        <div class="logo-left" style="text-align: left;">
            <img style="max-width: 45px;" src="{{ public_path('assets/images/logo-small.png') }}" alt="PWB Logo">
            <div class="company-name">PT. PUTERA WIBOWO BORNEO</div>
        </div>
        <div class="right">
            <div class="title-right">
                <div class="title-bar">PENAWARAN PART</div>
            </div>
        </div>
    </div>
    <div class="clearfix"></div>

    <!-- Document Info -->
    <div class="info-container">
        <div class="info-left">
            <div class="info-row">
                <span class="info-label">CUSTOMER</span>
                <span class="info-value">: PT. UNGGUL DINAMIKA UTAMA</span>
            </div>
            <div class="info-row">
                <span class="info-label">SITE</span>
                <span class="info-value">: {{ $transactions[0]->sitework ?? '' }}</span>
            </div>
            <div class="info-row">
                <span class="info-label">SUPPLIER</span>
                <span class="info-value">: PT PUTERA WIBOWO BORNEO</span>
            </div>
            <div class="info-row">
                <span class="info-label">UNIT NO.</span>
                <span class="info-value">: {{ $transactions[0]->unit->unit_code ?? '' }}</span>
            </div>
        </div>

        <div class="info-right">
            <div class="info-row">
                <span class="info-label">NO.</span>
                <span class="info-value">: {{ $transactions[0]->noSPB ?? '' }}</span>
            </div>
            <div class="info-row">
                <span class="info-label">DATE</span>
                <span class="info-value">: {{ \Carbon\Carbon::parse($date ?? \Carbon\Carbon::now())->format('d-m-Y') }}</span>
            </div>
            <div class="info-row">
                <span class="info-label">REFF</span>
                <span class="info-value">: {{ $transactions[0]->do_number ?? '' }}</span>
            </div>
            <div class="info-row">
                <span class="info-label">PR NO.</span>
                <span class="info-value">: {{ $transactions[0]->wo_number ?? '' }}</span>
            </div>
        </div>
        <div class="clearfix"></div>
    </div>

    <!-- Main Content Table -->
    <table class="main-table">
        <thead>
            <tr style="text-align: center;">
                <th style="width:5%;">NO.</th>
                <th style="width:15%;">MATERIAL NUMBER</th>
                <th style="width:30%;">DESCRIPTION</th>
                <th style="width:5%;">QTY</th>
                <th style="width:5%;">UOM</th>
                <th style="width:15%;">UNIT PRICE</th>
                <th style="width:15%;">UNIT PRICE (DISC. 10%)</th>
                <th style="width:10%;">TOTAL PRICE</th>
            </tr>
        </thead>
        <tbody>
            @if(count($transactions) > 0)
            @php
            $no = 1;
            $totalAmount = 0;
            @endphp
            @foreach($transactions as $transaction)
            @foreach($transaction->parts as $part)
            @php
            $unitPrice = $part->price;
            $discountedPrice = $unitPrice * 0.9; // 10% discount
            $totalPrice = $discountedPrice * $part->quantity;
            $totalAmount += $totalPrice;
            @endphp
            <tr>
                <td style="padding: 0; text-align: center;">{{ $no++ }}</td>
                <td style="text-align: center;">{{ $part->partInventory->part->part_code }}</td>
                <td class="text-left">{{ $part->partInventory->part->part_name }}</td>
                <td style="text-align: center;">{{ $part->quantity }}</td>
                <td style="text-align: center;">{{ $part->eum ?? '' }}</td>
                <td class="text-right">Rp {{ number_format($unitPrice, 0, ',', '.') }}</td>
                <td class="text-right">Rp {{ number_format($discountedPrice, 0, ',', '.') }}</td>
                <td class="text-right">Rp {{ number_format($totalPrice, 0, ',', '.') }}</td>
            </tr>
            @endforeach
            @endforeach

            <!-- Add empty rows to make the table look complete -->
            @php $totalRows = $no - 1; @endphp
            <!-- @for($i = $totalRows; $i < 10; $i++)
                <tr>
                <td>&nbsp;</td>
                <td></td>
                <td></td>
                <td></td>
                <td></td>
                <td></td>
                <td></td>
                <td></td>
                </tr>
                @endfor -->
                @else
                <tr>
                    <td colspan="8" class="text-center">No parts found.</td>
                </tr>
                <!-- Add empty rows to make the table look complete -->
                @for($i = 0; $i < 9; $i++)
                    <tr>
                    <td>&nbsp;</td>
                    <td></td>
                    <td></td>
                    <td></td>
                    <td></td>
                    <td></td>
                    <td></td>
                    <td></td>
                    </tr>
                    @endfor
                    @endif

                    <!-- Totals Section -->
                    @if(count($transactions) > 0)
                    @php
                    $ppn = $totalAmount * 0.11; // 11% tax
                    $grandTotal = $totalAmount + $ppn;
                    @endphp
                    <tr>
                        <td colspan="6" style="border: none;"></td>
                        <td class="total-label">TOTAL</td>
                        <td class="total-value">Rp {{ number_format($totalAmount, 0, ',', '.') }}</td>
                    </tr>
                    <tr>
                        <td colspan="6" style="border: none;"></td>
                        <td class="total-label">PPN 11%</td>
                        <td class="total-value">Rp {{ number_format($ppn, 0, ',', '.') }}</td>
                    </tr>
                    <tr>
                        <td colspan="6" style="border: none;"></td>
                        <td class="total-label">GRAND TOTAL</td>
                        <td class="total-value">Rp {{ number_format($grandTotal, 0, ',', '.') }}</td>
                    </tr>
                    @endif
        </tbody>
    </table>


    <!-- Signature Section -->
    <div class="signature-table">
        <div class="signature-left">
            <div>Prepared by,</div>
            <div class="signature-name">Admin PWB</div>
            <div class="signature-title"><u>{{ session('name') }}</u></div>
        </div>
        <div class="signature-right">
            <div>Approved by,</div>
            <div class="signature-name">PT. Unggul Dinamika Utama</div>
            <div class="signature-title">Customer</div>
        </div>
        <div class="clearfix"></div>
    </div>
</body>

</html>