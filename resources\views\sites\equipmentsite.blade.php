@extends('sites.content')
@section('resourcesite')
@vite(['resources/js/site/Equipmentsite.js','resources/js/style.js'])
@endsection
@section('contentsite')
@section('title', 'Perlengkapan')
<div class="row bgwhite page-title-box shadow-kit mb-1">
    <div class="col d-flex align-items-center" style="max-width: fit-content;">
        <button class="button-menu-mobile text-blue-500 btn-sm">
            <i style="font-size: 30px;" class="mdi mdi-menu"></i>
        </button>
        <div class="text">
            <p class="font-bold m-0">{{ session('name') }}</p>
        </div>
    </div>

    <div class="col">
        <ul class="list-unstyled topnav-menu float-right mb-0">
            <li class="dropdown notification-list">
                <a class="nav-link dropdown-toggle  waves-effect waves-light" data-toggle="dropdown" href="#"
                    role="button" aria-haspopup="false" aria-expanded="false">
                    <i class="mdi mdi-bell-outline noti-icon"></i>
                    <span class="badge badge-danger badge-pill float-right badgenotif"></span>
                </a>
                <div class="dropdown-menu dropdown-menu-right dropdown-lg">
                    <div class="dropdown-item noti-title">
                        <h5 class="font-16 text-white m-0">
                            <span class="float-right">
                                <a href="javascript:void(0);" class="text-white" id="clear-all">
                                    <small></small>
                                </a>
                            </span>Notifikasi
                        </h5>
                    </div>
                    <div class="noti-scroll" id="notification-list">
                    </div>
                    </a>
                </div>
            </li>
        </ul>
    </div>
</div>

<div class="row m-2">
    <div class="col">
        <div class=" rounded-lg row mt-2 p-4 bgwhite shadow-kit">
            <div class="d-flex justify-content-between align-items-center w-100 mb-4">
                <h2 class="h4 font-bold m-0">Perlengkapan Yang Ada</h2>
                <div class="d-flex gap-3 align-items-center">
                    <div class="date-range-filter mr-2">
                        <div class="d-flex">
                            <div class="mr-2">
                                <label for="start_date" class="small mb-0">Dari Tanggal:</label>
                                <input type="date" id="start_date" class="form-control form-control-sm" onchange="window.applyFilters()">
                            </div>
                            <div>
                                <label for="end_date" class="small mb-0">Sampai Tanggal:</label>
                                <input type="date" id="end_date" class="form-control form-control-sm" onchange="window.applyFilters()">
                            </div>
                        </div>
                    </div>
                    <div class="max200">
                        <label for="status" class="small mb-0">Status:</label>
                        <select id="status" name="status" class="form-control form-control-sm" onchange="window.applyFilters()">
                            <option value="">Tampilkan Semua</option>
                            @foreach($statuses as $status)
                            <option value="{{ $status }}">{{ $status }}</option>
                            @endforeach
                        </select>
                    </div>
                </div>
            </div>
            <div id="equipment-table-container" class=" w-100">
                <table class="table">
                    <thead class="table-dark text-white tabel-bordered">
                        <tr>
                            <th class="p-2">No</th>
                            <th class="p-2">Nama Perlengkapan</th>
                            <th class="p-2">Site</th>
                            <th class="p-2">Status</th>
                            <th class="p-2">Tanggal Dibuat</th>
                            <th>Aksi</th>
                        </tr>
                    </thead>
                    <tbody id="equipmentStockTableBody">
                    </tbody>
                </table>
                <div id="pagination-container" class="mt-3">
                    <!-- Pagination will be rendered here by JavaScript -->
                </div>
            </div>
        </div>
    </div>

    <div class="col max500" style="display:none">
        <div class="ml-2 bg-white shadow-kit rounded-lg p-4 m-0">
            <h2 class="text-xl font-bold mb-4">Perlengkapan Form</h2>
            <form id="equipmentForm" class="space-y-4">
                <input type="hidden" id="equipment_id" name="equipment_id">

                <div>
                    <label for="name" class="block text-sm font-medium text-gray-700">Name:</label>
                    <input type="text" id="name" name="name" required class="form-control">
                </div>

                <div>
                    <label for="description" class="block text-sm font-medium text-gray-700">Description:</label>
                    <textarea id="description" name="description" class="form-control"></textarea>
                </div>

                <div class="flex space-x-2">
                    <button type="button" onclick="saveEquipment()" class="btn btn-primary">Save</button>
                    <button type="button" onclick="closeForm()" class="btn btn-danger">Cancel</button>
                </div>
            </form>
        </div>

    </div>
</div>
<div class="modal fade" id="editStockModal" tabindex="-1" role="dialog" aria-labelledby="editStockModalLabel" aria-hidden="true">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="editStockModalLabel">Ubah Status Peralatan</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <form id="editEquipmentStockForm">
                    <input type="hidden" id="stock_id" name="stock_id">
                    <div class="form-group">
                        <label for="status_stock">Status:</label>
                        <select id="status_stock" name="status" class="form-control">
                            <option value="Baik">Baik</option>
                            <option value="Cukup Baik">Cukup Baik</option>
                            <option value="Kurang Baik">Kurang Baik</option>
                            <option value="Rusak">Rusak</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label for="received_at_stock">Tanggal:</label>
                        <input type="date" id="received_at_stock" name="received_at" class="form-control">
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <button type="button" class="btn btn-primary" onclick="updateEquipmentStock()">Save Changes</button>
            </div>
        </div>
    </div>
</div>
<style>
    /* Pagination styling */
    .pagination {
        display: flex;
        justify-content: center;
        list-style: none;
        padding: 0;
        margin-top: 15px;
    }
    .pagination .page-item {
        margin: 0 2px;
    }
    .pagination .page-item .page-link {
        color: #333;
        background-color: #fff;
        border: 1px solid #ddd;
        padding: 6px 12px;
        text-decoration: none;
        border-radius: 4px;
        cursor: pointer;
    }
    .pagination .page-item.active .page-link {
        color: #fff;
        background-color: #28a745;
        border-color: #28a745;
    }
    .pagination .page-item .page-link:hover {
        background-color: #f8f9fa;
    }
</style>
@endsection