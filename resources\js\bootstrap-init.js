// Bootstrap initialization
import * as bootstrap from 'bootstrap';

// Make bootstrap available globally
window.bootstrap = bootstrap;

// Ensure bootstrap is available even if loaded after other scripts
document.addEventListener('DOMContentLoaded', function() {
    // Double-check that bootstrap is available globally
    if (!window.bootstrap) {
        window.bootstrap = bootstrap;
    }
});

// Export for module usage
export default bootstrap;
