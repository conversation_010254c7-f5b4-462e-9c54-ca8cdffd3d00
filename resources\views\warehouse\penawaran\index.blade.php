@extends('warehouse.content')
@section('contentho')
<div class="container-fluid mt-3">
    <div class="row">
        <div class="col-12">
            <div class="bgwhite shadow-kit rounded-lg p-3">
                <div class="d-flex justify-content-between align-items-center mb-3">
                    <h4 class="font-weight-bold">Daftar Penawaran </h4>
                </div>
                <div class="table-responsive">
                    <table class="table table-bordered table-hover">
                        <thead class="bg-primary text-white">
                            <tr>
                                <th>No</th>
                                <th>Nomor Penawaran</th>
                                <th>Perihal</th>
                                <th>Customer</th>
                                <th>Tanggal</th>
                                <th>Status</th>
                                <th>Aksi</th>
                            </tr>
                        </thead>
                        <tbody>
                            @foreach($penawarans as $index => $penawaran)
                            <tr>
                                <td>{{ $index + 1 }}</td>
                                <td>{{ $penawaran->nomor }}</td>
                                <td>{{ $penawaran->perihal }}</td>
                                <td>{{ $penawaran->customer }}</td>
                                <td>{{ $penawaran->created_at->format('d/m/Y') }}</td>
                                <td>
                                    @php
                                        $statusClass = '';
                                        switch($penawaran->status) {
                                            case 'Draft':
                                                $statusClass = 'bg-secondary';
                                                break;
                                            case 'Dikirim ke customer':
                                                $statusClass = 'bg-info';
                                                break;
                                            case 'PO customer':
                                                $statusClass = 'bg-primary';
                                                break;
                                            case 'Proses penyediaan':
                                                $statusClass = 'bg-secondary';
                                                break;
                                            case 'Selesai':
                                                $statusClass = 'bg-success';
                                                break;
                                        }
                                    @endphp
                                    <span class="badge {{ $statusClass }} text-white">{{ $penawaran->status }}</span>
                                </td>
                                <td>
                                    <a href="{{ route('warehouse.penawaran.show', $penawaran->id) }}" class="btn btn-sm btn-info">
                                        <i class="mdi mdi-eye"></i> Detail & Update Status
                                    </a>
                                </td>
                            </tr>
                            @endforeach
                        </tbody>
                    </table>
                </div>

                <div class="d-flex justify-content-between align-items-center mt-3">
                    <div>
                        <span>Showing {{ $penawarans->firstItem() ?? 0 }} to {{ $penawarans->lastItem() ?? 0 }} of {{ $penawarans->total() }} entries</span>
                    </div>
                    <div>
                        {{ $penawarans->links() }}
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection
