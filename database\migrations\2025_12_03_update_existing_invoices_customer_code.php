<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Update existing invoices that are linked to penawarans but don't have customer_code
        $this->updateExistingInvoicesCustomerCode();
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // This migration only updates data, no schema changes to reverse
    }

    /**
     * Update existing invoices with customer_code from their linked penawarans
     */
    private function updateExistingInvoicesCustomerCode(): void
    {
        // Get all invoices that have penawaran_id but no customer_code
        $invoicesNeedingUpdate = DB::table('invoices')
            ->whereNotNull('penawaran_id')
            ->where(function($query) {
                $query->whereNull('customer_code')
                      ->orWhere('customer_code', '');
            })
            ->get();

        echo "Found " . $invoicesNeedingUpdate->count() . " invoices that need customer_code updates\n";

        foreach ($invoicesNeedingUpdate as $invoice) {
            // Get the penawaran for this invoice
            $penawaran = DB::table('penawarans')
                ->where('id', $invoice->penawaran_id)
                ->first();

            if ($penawaran && $penawaran->customer_code) {
                // Update the invoice with the customer_code from penawaran
                DB::table('invoices')
                    ->where('id', $invoice->id)
                    ->update([
                        'customer_code' => $penawaran->customer_code,
                        'updated_at' => now()
                    ]);

                echo "Updated invoice ID {$invoice->id} with customer_code: {$penawaran->customer_code}\n";
            } else if ($penawaran && !$penawaran->customer_code && $penawaran->customer) {
                // Try to find customer_code from customer_sales table
                $customerSales = DB::table('customersales')
                    ->where('nama_customer', $penawaran->customer)
                    ->first();

                if ($customerSales) {
                    // Update both penawaran and invoice
                    DB::table('penawarans')
                        ->where('id', $penawaran->id)
                        ->update([
                            'customer_code' => $customerSales->code,
                            'updated_at' => now()
                        ]);

                    DB::table('invoices')
                        ->where('id', $invoice->id)
                        ->update([
                            'customer_code' => $customerSales->code,
                            'updated_at' => now()
                        ]);

                    echo "Updated invoice ID {$invoice->id} and penawaran ID {$penawaran->id} with customer_code: {$customerSales->code}\n";
                }
            }
        }

        echo "Customer code update completed\n";
    }
};
