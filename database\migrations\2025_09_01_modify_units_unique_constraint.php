<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Drop the existing unique constraint on unit_code
        Schema::table('units', function (Blueprint $table) {
            $table->dropUnique(['unit_code']);
        });

        // Add a new composite unique constraint on unit_code and site_id
        Schema::table('units', function (Blueprint $table) {
            $table->unique(['unit_code', 'site_id']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // Revert back to the original constraint
        Schema::table('units', function (Blueprint $table) {
            $table->dropUnique(['unit_code', 'site_id']);
            $table->unique(['unit_code']);
        });
    }
};
