<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\Backlog;
use App\Models\Unit;
use App\Models\DailyReport;
use Carbon\Carbon;

class BacklogSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Get units that have daily reports to create realistic backlog data
        $unitsWithReports = Unit::whereHas('dailyReports')->get();
        
        if ($unitsWithReports->isEmpty()) {
            if ($this->command) {
                $this->command->info('No units with daily reports found. Creating backlogs for all units.');
            }
            $unitsWithReports = Unit::take(10)->get();
        }

        $problemDescriptions = [
            'Engine overheating during operation',
            'Hydraulic system pressure drop',
            'Transmission slipping under load',
            'Electrical system malfunction',
            'Brake system requires maintenance',
            'Cooling system leak detected',
            'Fuel injection system clogged',
            'Steering mechanism loose',
            'Suspension system worn out',
            'Air filter replacement needed',
            'Oil leak from main engine',
            'Radiator fan not working',
            'Battery charging system failure',
            'Exhaust system damaged',
            'Tire pressure monitoring issue',
            'Alternator belt replacement needed',
            'Clutch system adjustment required',
            'Differential oil change needed',
            'Turbocharger maintenance required',
            'Air conditioning system repair'
        ];

        $backlogJobs = [
            'Replace engine coolant pump',
            'Repair hydraulic cylinder seals',
            'Overhaul transmission system',
            'Replace electrical wiring harness',
            'Service brake pads and rotors',
            'Fix cooling system radiator',
            'Clean fuel injection nozzles',
            'Tighten steering linkage',
            'Replace suspension springs',
            'Install new air filter element',
            'Repair engine oil pan gasket',
            'Replace radiator cooling fan',
            'Service battery and charging system',
            'Repair exhaust pipe and muffler',
            'Calibrate tire pressure sensors',
            'Replace alternator drive belt',
            'Adjust clutch pedal free play',
            'Change differential gear oil',
            'Service turbocharger components',
            'Repair AC compressor unit'
        ];

        $statuses = ['OPEN', 'IN_PROGRESS', 'CLOSED'];
        $statusWeights = [60, 30, 10]; // 60% OPEN, 30% IN_PROGRESS, 10% CLOSED

        foreach ($unitsWithReports as $unit) {
            // Create 1-3 backlogs per unit
            $backlogCount = rand(1, 3);
            
            for ($i = 0; $i < $backlogCount; $i++) {
                // Get a random problem and job
                $problemIndex = array_rand($problemDescriptions);
                $jobIndex = array_rand($backlogJobs);
                
                // Weighted random status selection
                $statusIndex = $this->weightedRandom($statusWeights);
                $status = $statuses[$statusIndex];
                
                // Generate realistic HM values based on unit's daily reports
                $latestReport = DailyReport::where('unit_id', $unit->id)
                    ->orderBy('date_in', 'desc')
                    ->first();
                
                $hmFound = $latestReport ? 
                    $latestReport->hm + rand(-500, 1000) : 
                    rand(1000, 15000);
                
                $planHm = $hmFound + rand(100, 2000);
                
                // Generate plan pull date (1-30 days from now for OPEN/IN_PROGRESS)
                $planPullDate = null;
                if ($status !== 'CLOSED') {
                    $planPullDate = Carbon::now()->addDays(rand(1, 30));
                }
                
                // Generate creation date (1-60 days ago)
                $createdAt = Carbon::now()->subDays(rand(1, 60));
                
                // Generate notes for some backlogs
                $notes = null;
                if (rand(1, 100) <= 40) { // 40% chance of having notes
                    $noteOptions = [
                        'Waiting for spare parts delivery',
                        'Scheduled during next maintenance window',
                        'Requires specialized technician',
                        'Customer approval pending',
                        'Parts ordered from supplier',
                        'Coordinating with operations team',
                        'Weather dependent work',
                        'Requires equipment shutdown',
                        'Waiting for technical documentation',
                        'Budget approval required'
                    ];
                    $notes = $noteOptions[array_rand($noteOptions)];
                }

                Backlog::create([
                    'unit_code' => $unit->unit_code,
                    'hm_found' => $hmFound,
                    'problem_description' => $problemDescriptions[$problemIndex],
                    'backlog_job' => $backlogJobs[$jobIndex],
                    'plan_hm' => $planHm,
                    'status' => $status,
                    'plan_pull_date' => $planPullDate,
                    'notes' => $notes,
                    'created_at' => $createdAt,
                    'updated_at' => $createdAt,
                ]);
            }
        }

        if ($this->command) {
            $this->command->info('Backlog seeder completed successfully!');
            $this->command->info('Created backlogs for ' . $unitsWithReports->count() . ' units.');
        }
    }

    /**
     * Weighted random selection
     */
    private function weightedRandom($weights)
    {
        $totalWeight = array_sum($weights);
        $random = rand(1, $totalWeight);
        
        $currentWeight = 0;
        foreach ($weights as $index => $weight) {
            $currentWeight += $weight;
            if ($random <= $currentWeight) {
                return $index;
            }
        }
        
        return 0; // fallback
    }
}
