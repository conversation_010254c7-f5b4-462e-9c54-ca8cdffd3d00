<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('unit_transactions', function (Blueprint $table) {
            $table->date('tanggalstart')->nullable()->after('do_date');
            $table->date('tanggalend')->nullable()->after('tanggalstart');
            $table->string('noireq')->nullable()->after('tanggalend');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('unit_transactions', function (Blueprint $table) {
            $table->dropColumn(['tanggalstart', 'tanggalend', 'noireq']);
        });
    }
};
