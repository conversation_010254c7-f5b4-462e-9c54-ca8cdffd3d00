<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Symfony\Component\HttpFoundation\Response;
use Illuminate\Support\Facades\Auth;

class CheckKasir
{
    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle(Request $request, Closure $next): Response
    {
        if (!Auth::check() || Auth::user()->role !== 'kasir') {
            return redirect('/login')->withErrors(['username' => '<PERSON><PERSON><PERSON>! Anda tidak memiliki izin untuk mengakses halaman ini.']);
        }
        
        return $next($request);
    }
}
