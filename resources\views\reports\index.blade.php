
<body>
    <div class="container">
        <!-- Export Buttons -->
        <div class="my-4">
            <button class="btn btn-sm btn-primary report-btn" data-type="in">Laporan In</button>
            <button class="btn btn-sm btn-danger report-btn" data-type="out">Laporan Out</button>
        </div>
        <!-- Filter Modal -->
        <div class="modal fade" id="exportModal" tabindex="-1">
            <div class="modal-dialog modal-lg">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title">Filter Laporan</h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                    </div>
                    <form id="exportForm" method="GET">
                        <input type="hidden" name="report_type" id="reportType">
                        <div class="modal-body">
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="start_date" class="form-label">Tanggal Mulai:</label>
                                        <input type="date" class="form-control" name="start_date">
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="end_date" class="form-label">Tanggal Selesai:</label>
                                        <input type="date" class="form-control" name="end_date">
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="sort_by" class="form-label">Urutkan Berdasarkan:</label>
                                        <select class="form-select" name="sort_by">
                                            <option value="date">Tanggal</option>
                                            <option value="part_name">Nama Part</option>
                                        </select>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="site_id" class="form-label">Site:</label>
                                        <select class="form-select" name="site_id">
                                            <option value="">Semua Site</option>
                                            @foreach(\App\Models\Site::all() as $site)
                                            <option value="{{ $site->site_id }}">{{ $site->site_name }}</option>
                                            @endforeach
                                        </select>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Tutup</button>
                            <button type="button" class="btn btn-primary" id="exportPdf">Export PDF</button>
                            <button type="button" class="btn btn-success" id="exportExcel">Export Excel</button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
    @vite('resources/js/laporan.js')
</body>

</html>