import Swal from "sweetalert2";
import bootstrap from "../bootstrap-init";

document.addEventListener("DOMContentLoaded", function () {
    const siteId = document.getElementById("site_id").value;
    const tableContainer = document.getElementById("instock-table-container");
    const startDateInput = document.getElementById("start_date");
    const endDateInput = document.getElementById("end_date");
    const searchInput = document.getElementById("search_input");
    const total = document.getElementById("total");

    const today = new Date();

    // Buat objek Date untuk kemarin dan atur tanggalnya
    const yesterday = new Date(today);
    yesterday.setDate(today.getDate() - 1);

    // Buat objek Date untuk besok dan atur tanggalnya
    const tomorrow = new Date(today);
    tomorrow.setDate(today.getDate() + 1);

    // Format dan atur nilai input
    startDateInput.value = yesterday.toISOString().split("T")[0];
    endDateInput.value = tomorrow.toISOString().split("T")[0];

    // Global variables for pagination
    let currentPage = 1;
    let transactionsCurrentPage = 1;
    const itemsPerPage = 10; // 5 items per page

    let adjustmentFormButtons; // Declare adjustmentFormButtons here

    window.viewSuratJalan = function (path) {
        if (!path) {
            alert("Tidak ada lampiran tersedia");
            return;
        }

        const fileUrl = `/storage/${path}`;
        const iframe = document.getElementById("suratJalanFrame");
        const downloadBtn = document.getElementById("downloadSuratJalan");

        iframe.src = fileUrl;
        downloadBtn.href = fileUrl;

        // Use Bootstrap 5 modal method
        const suratJalanModal = new bootstrap.Modal(
            document.getElementById("suratJalanModal")
        );
        suratJalanModal.show();
    };

    function loadLastActivities() {
        fetch("/last-activities")
            .then((response) => response.json())
            .then((data) => {
                const ul = document.getElementById("activityList");

                if (!ul) {
                    Swal.fire({
                        icon: "error",
                        title: "Kesalahan!",
                        text: 'Element with ID "activityList" not found!',
                    });
                    return;
                }
                ul.innerHTML = "";
                data.forEach((activity) => {
                    const li = document.createElement("li");
                    const a = document.createElement("a");
                    a.href = "#";
                    a.textContent = activity.description;
                    li.appendChild(a);
                    ul.appendChild(li);
                });
            })
            .catch(() => {
                Swal.fire({
                    icon: "error",
                    title: "Kesalahan!",
                    text: "Gagal memuat aktivitas terakhir.",
                });
                const ul = document.getElementById("activityList");
                if (ul) {
                    ul.innerHTML = "<li>Error loading activities.</li>";
                }
            });
    }

    function formatDate(dateString) {
        const date = new Date(dateString);
        const day = date.getDate();

        // Array of month names in Indonesian
        const monthNames = [
            'Januari', 'Februari', 'Maret', 'April', 'Mei', 'Juni',
            'Juli', 'Agustus', 'September', 'Oktober', 'November', 'Desember'
        ];

        const month = monthNames[date.getMonth()];
        const year = date.getFullYear();

        return `${day} ${month} ${year}`;
    }

    // Function to create pagination item
    function createPaginationItem(page, text, isActive = false) {
        const li = document.createElement("li");
        li.className = `page-item ${isActive ? "active" : ""}`;

        const a = document.createElement("a");
        a.className = "page-link";
        a.href = "#";
        a.textContent = text;
        a.dataset.page = page;

        li.appendChild(a);
        return li;
    }

    // Function to render pagination
    function renderPagination(data) {
        const container = document.getElementById("pagination-container");
        container.innerHTML = "";

        if (data.last_page > 1) {
            const pagination = document.createElement("ul");
            pagination.className = "pagination pagination-rounded  justify-content-center";

            // Previous button
            if (data.current_page > 1) {
                pagination.appendChild(
                    createPaginationItem(data.current_page - 1, "«")
                );
            }

            // Page numbers
            for (let i = 1; i <= data.last_page; i++) {
                pagination.appendChild(
                    createPaginationItem(i, i, i === data.current_page)
                );
            }

            // Next button
            if (data.current_page < data.last_page) {
                pagination.appendChild(
                    createPaginationItem(data.current_page + 1, "»")
                );
            }

            container.appendChild(pagination);

            // Add event listeners to pagination links
            container.querySelectorAll(".page-link").forEach((link) => {
                link.addEventListener("click", function (e) {
                    e.preventDefault();
                    currentPage = parseInt(this.dataset.page);
                    loadData();
                });
            });
        }
    }

    function renderTable(data) {
        let totalq = 0;
        if (tableContainer.querySelector("table")) {
            tableContainer.querySelector("table").remove();
        }

        const table = document.createElement("table");

        table.className = "table table-bordered";

        const thead = document.createElement("thead");
        thead.className = "table-dark text-white";
        thead.innerHTML = `
        <tr>
        <th class="p-2">Part Code</th>
        <th class="p-2">Part Name</th>
        <th class="p-2">Date In</th>
        <th class="p-2">Quantity</th>
        <th class="p-2">Catatan</th>
        </tr>
        `;
        table.appendChild(thead);

        const tbody = document.createElement("tbody");
        let count = 1;

        // Check if we have data in the response
        if (data.data && Array.isArray(data.data)) {
            // Using data.data for the items when it's a paginated response
            data.data.forEach((item) => {
                // We'll use total_quantity from API for the total display
                // but still calculate page total for reference
                totalq = totalq + item.quantity;
                count = count += 1;
                const row = document.createElement("tr");
                row.innerHTML = `
                    <td>${item.part_code}</td>
                    <td>${item.part_name}</td>
                    <td>${formatDate(item.date_in)}</td>
                    <td>${item.quantity}</td>
                    <td>${item.notes || ""}</td>
                `;
                tbody.appendChild(row);
            });

            // Use the total_quantity from API response for all matching records
            if (data.total_quantity !== undefined) {
                total.innerText = data.total_quantity;
            } else {
                total.innerText = totalq; // Fallback to page total
            }
        } else if (Array.isArray(data)) {
            // Fallback for non-paginated data
            data.forEach((item) => {
                totalq = totalq + item.quantity;
                count = count += 1;
                const row = document.createElement("tr");
                row.innerHTML = `
                    <td>${item.part_code}</td>
                    <td>${item.part_name}</td>
                    <td>${formatDate(item.date_in)}</td>
                    <td>${item.quantity}</td>
                    <td>${item.notes || ""}</td>
                `;
                tbody.appendChild(row);
            });

            total.innerText = totalq;
        }
        if (count == 1) {
            const row = document.createElement("tr");
            row.innerHTML = `
                <td class="text-center" colspan="5">TIDAK DITEMUKAN DATA</td>
            `;
            tbody.appendChild(row);
        }
        table.appendChild(tbody);
        tableContainer.appendChild(table);

        // Render pagination if we have paginated data
        if (data.current_page !== undefined) {
            renderPagination(data);
        }

        // Add event listeners to delete buttons
        const deleteButtons = document.querySelectorAll(".delete-btn");
        deleteButtons.forEach((button) => {
            button.addEventListener("click", function () {
                const id = this.dataset.id;
                deleteItem(id);
            });
        });
    }

    function loadData() {
        loadLastActivities();
        const startDate = startDateInput.value;
        const endDate = endDateInput.value;
        const search = searchInput.value;

        let url = `/sites/instock/loadData?site_id=${siteId}&page=${currentPage}&per_page=${itemsPerPage}`;

        if (startDate && endDate) {
            url += `&start_date=${startDate}&end_date=${endDate}`;
        }

        if (search) {
            url += `&search=${search}`;
        }

        fetch(url)
            .then((response) => response.json())
            .then((data) => {
                renderTable(data);
            })
            .catch(() => {
                Swal.fire({
                    icon: "error",
                    title: "Gagal !",
                    text: "Terdapat Error, silahkan coba lagi.",
                });
            });
    }

    // Event listeners for filtering
    startDateInput.addEventListener("change", function () {
        currentPage = 1; // Reset to first page when changing date filter
        loadData();
    });
    endDateInput.addEventListener("change", function () {
        currentPage = 1; // Reset to first page when changing date filter
        loadData();
    });
    searchInput.addEventListener("input", function () {
        currentPage = 1; // Reset to first page when searching
        loadData();
    });

    function loadTransactions() {
        fetch(
            `/transactions/site/get?page=${transactionsCurrentPage}&per_page=${itemsPerPage}`
        )
            .then((response) => response.json())
            .then((data) => {
                renderTransactionsTable(data);
            })
            .catch((error) => {
                Swal.fire("Error!", "Gagal memuat data pengiriman.", "error");
            });
    }

    // Function to render transactions pagination
    function renderTransactionsPagination(data) {
        const container = document.getElementById("transactions-pagination");
        container.innerHTML = "";

        if (data.last_page > 1) {
            const pagination = document.createElement("ul");
            pagination.className = "pagination pagination-rounded  justify-content-center";

            // Previous button
            if (data.current_page > 1) {
                pagination.appendChild(
                    createPaginationItem(data.current_page - 1, "«")
                );
            }

            // Page numbers
            for (let i = 1; i <= data.last_page; i++) {
                pagination.appendChild(
                    createPaginationItem(i, i, i === data.current_page)
                );
            }

            // Next button
            if (data.current_page < data.last_page) {
                pagination.appendChild(
                    createPaginationItem(data.current_page + 1, "»")
                );
            }

            container.appendChild(pagination);

            // Add event listeners to pagination links
            container.querySelectorAll(".page-link").forEach((link) => {
                link.addEventListener("click", function (e) {
                    e.preventDefault();
                    transactionsCurrentPage = parseInt(this.dataset.page);
                    loadTransactions();
                });
            });
        }
    }

    function renderTransactionsTable(data) {
        const tableBody = document.querySelector("#transactions-table tbody");
        tableBody.innerHTML = "";

        let i = 1;

        // Check if we have data in the response
        const transactions = data.data || data;

        if (transactions.length === 0) {
            const row = document.createElement("tr");
            row.innerHTML = `<td colspan="9" class="text-center">Tidak ada data pengiriman</td>`;
            tableBody.appendChild(row);
        } else {
            transactions.forEach((transaction) => {
                const suratJalanColumn = transaction.surat_jalan_path
                    ? `<button class="btn btn-sm btn-info" onclick="viewSuratJalan('${transaction.surat_jalan_path}')">Lihat</button>`
                    : '<em class="text-muted">Tidak ada</em>';

                const row = document.createElement("tr");
                row.innerHTML = `
                    <td>${i++}</td>
                    <td>${transaction.part.part_code}</td>
                    <td>${transaction.part.part_name}</td>
                    <td>${new Date(
                        transaction.transaction_date
                    ).toLocaleDateString()}</td>
                    <td class="text-center">${transaction.quantity_sent}</td>
                    <td class="text-center">${
                        transaction.quantity_received
                            ? transaction.quantity_received
                            : "belum konfirmasi"
                    }</td>
                    <td>${transaction.status}</td>
                    <td>${
                        transaction.status === "intransit"
                            ? `
                            <div class="action-buttons">
                                <button class="confirm-transaction-btn btn btn-primary btn-sm" data-transaction-id="${transaction.stock_transaction_id}">Konfirmasi</button>
                                <button class="show-adjustment-form-btn btn btn-secondary btn-sm" data-qu_sent="${transaction.quantity_sent}" data-transaction-id="${transaction.stock_transaction_id}">Ada perubahan</button>
                            </div>
                        `
                            : ""
                    }
                    </td>
                    <td>${suratJalanColumn}</td>
                `;
                tableBody.appendChild(row);
            });
        }

        // Render pagination if we have paginated data
        if (data.current_page !== undefined) {
            renderTransactionsPagination(data);
        }

        // Inisialisasi ulang event listeners setelah data diperbarui
        initializeTransactionEventListeners();
    }
    function initializeTransactionEventListeners() {
        // Event listeners untuk tombol konfirmasi
        const confirmButtons = document.querySelectorAll(
            ".confirm-transaction-btn"
        );
        confirmButtons.forEach((button) => {
            button.addEventListener("click", function () {
                const transactionId = this.dataset.transactionId;
                confirmTransaction(transactionId);
            });
        });

        // Event listeners untuk tombol show adjustment form
        adjustmentFormButtons = document.querySelectorAll(
            // Initialize adjustmentFormButtons here
            ".show-adjustment-form-btn"
        );
        adjustmentFormButtons.forEach((button) => {
            button.addEventListener("click", function () {
                const transactionId = this.dataset.transactionId;
                const qu_sent = this.dataset.qu_sent;

                showAdjustmentForm(transactionId, qu_sent);
            });
        });
    }

    function showAdjustmentForm(transactionId, qu_sent) {
        const adjustmentForm = document.getElementById("adjustment-form");
        document
            .querySelectorAll(".col.right")
            .forEach((col) => col.classList.remove("hidden")); //make it remove hidden class
        adjustmentForm.style.display = "block";

        document.getElementById("adjust-transaction-id").value = transactionId;
        document.getElementById("quantity_received").value = qu_sent;
        document
            .getElementById("quantity_received")
            .setAttribute("max", qu_sent);
    }

    function submitAdjustmentForm() {
        Swal.fire({
            title: "Simpan Penyesuaian?",
            text: "Apakah Anda yakin ingin menyimpan penyesuaian ini?",
            icon: "warning",
            showCancelButton: true,
            confirmButtonColor: "#3085d6",
            cancelButtonColor: "#d33",
            confirmButtonText: "Ya, Simpan!",
            cancelButtonText: "Batal",
        }).then((result) => {
            if (result.isConfirmed) {
                const form = document.getElementById("adjust-form");
                const formData = new FormData(form);
                const messageDiv = document.getElementById("adjust-message");
                const transactionId = document.getElementById(
                    "adjust-transaction-id"
                ).value;
                formData.append("transaction_id", transactionId);
                document
                    .querySelectorAll(".error")
                    .forEach((el) => (el.textContent = ""));
                messageDiv.textContent = "";

                fetch(`/stock-transactions/${transactionId}/adjust`, {
                    // Ganti dengan rute yang benar
                    method: "POST",
                    body: formData,
                    headers: {
                        "X-CSRF-TOKEN": document.querySelector(
                            'meta[name="csrf-token"]'
                        ).content,
                    },
                })
                    .then((response) => response.json())
                    .then((data) => {
                        if (data.success) {
                            Swal.fire(
                                "Berhasil!",
                                data.message,
                                "success"
                            ).then(() => {
                                loadData();
                                transactionsCurrentPage = 1; // Reset to first page
                                loadTransactions();
                                document
                                    .querySelector(".col.right")
                                    .classList.add("hidden");
                            });
                        } else {
                            Swal.fire("Gagal!", data.message, "error"); // Menampilkan pesan dari backend
                        }
                    })
                    .catch((error) => {
                        handleApiError(error);
                    });
            }
        });
    }

    function confirmTransaction(transactionId) {
        Swal.fire({
            title: "Tindakan ini tidak bisa diubah/dihapus?",
            text: "Patikan bahwa anda telah menerima part !",
            icon: "warning",
            showCancelButton: true,
            confirmButtonColor: "#3085d6",
            cancelButtonColor: "#d33",
            confirmButtonText: "Ya, Konfirmasi!",
            cancelButtonText: "Batal",
        }).then((result) => {
            if (result.isConfirmed) {
                const messageDiv = document.getElementById("message");
                messageDiv.textContent = "";

                fetch(`/stock-transactions/${transactionId}/confirm`, {
                    method: "POST",
                    headers: {
                        "X-CSRF-TOKEN": document.querySelector(
                            'meta[name="csrf-token"]'
                        ).content,
                    },
                })
                    .then((response) => response.json())
                    .then((data) => {
                        if (data.success) {
                            Swal.fire(
                                "Berhasil!",
                                data.message,
                                "success"
                            ).then(() => {
                                // reload data
                                loadData();
                                transactionsCurrentPage = 1; // Reset to first page
                                loadTransactions();
                            });
                        } else {
                            Swal.fire("Gagal!", data.message, "error"); // Menampilkan pesan dari backend
                        }
                    })
                    .catch((error) => {
                        handleApiError(error);
                    });
            }
        });
    }

    function handleApiError(error) {
        try {
            const errorData = JSON.parse(error.message);

            if (errorData.errors) {
                // Tampilkan validasi kesalahan
                for (const key in errorData.errors) {
                    const errorElement = document.getElementById(
                        `${key}_error`
                    );
                    if (errorElement) {
                        errorElement.textContent = errorData.errors[key][0];
                    }
                }
            }

            // Tampilkan pesan kesalahan dari backend (jika ada)
            if (errorData.message) {
                Swal.fire("Error!", errorData.message, "error");
            } else {
                // Jika tidak ada pesan khusus, tampilkan kesalahan umum
                Swal.fire("Error!", "Terjadi kesalahan.", "error");
            }
        } catch (e) {
            // Jika gagal mem-parse JSON, tampilkan kesalahan umum
            Swal.fire("Error!", "Terjadi kesalahan: " + error.message, "error");
        }
    }

    // Event listener untuk tombol submit adjustment form
    const submitAdjustmentFormBtn = document.getElementById(
        "submit-adjustment-form-btn"
    );
    submitAdjustmentFormBtn.addEventListener("click", submitAdjustmentForm);

    // Event listener untuk tombol batal pada form adjustment
    const closeAdjustmentFormBtn = document.getElementById(
        "close-adjustment-form-btn"
    );
    closeAdjustmentFormBtn.addEventListener("click", function () {
        // Hide the form when cancel button is clicked
        document.querySelector(".col.right").classList.add("hidden");
    });

    loadData();
    loadTransactions();
});
