document.addEventListener('DOMContentLoaded', function() {
    const revenueCards = document.querySelectorAll('.revenue-card');

    revenueCards.forEach((card, index) => {
        card.addEventListener('click', function() {
            const partType = this.getAttribute('data-part-type');
            const typeName = this.getAttribute('data-type-name');
            openPartTypeModal(partType, typeName);
        });
    });
    function openPartTypeModal(partType, typeName) {
        const modalLabel = document.getElementById('partTypeDetailModalLabel');
        const partTypeName = document.getElementById('part-type-name');
        if (modalLabel) modalLabel.textContent = `Detail Divisi ${typeName}`;
        if (partTypeName) partTypeName.textContent = typeName;

        // Set the appropriate icon and color based on part type
        const iconElement = document.getElementById('part-type-icon');
        const emptyIconElement = document.getElementById('part-type-empty-icon');

        if (partType === 'AC') {
            iconElement.className = 'text-white me-3';
            iconElement.style.width = '48px';
            iconElement.style.height = '48px';
            iconElement.style.display = 'flex';
            iconElement.style.alignItems = 'center';
            iconElement.style.justifyContent = 'center';
            iconElement.style.borderRadius = '50%';
            iconElement.style.backgroundColor = '#28a745';
            iconElement.innerHTML = '<i class="mdi mdi-air-conditioner" style="font-size: 24px;"></i>';
            emptyIconElement.className = 'mdi mdi-air-conditioner';
            emptyIconElement.style.color = '#28a745';
        } else if (partType === 'TYRE') {
            iconElement.className = 'text-white me-3';
            iconElement.style.width = '48px';
            iconElement.style.height = '48px';
            iconElement.style.display = 'flex';
            iconElement.style.alignItems = 'center';
            iconElement.style.justifyContent = 'center';
            iconElement.style.borderRadius = '50%';
            iconElement.style.backgroundColor = '#58c0f6';
            iconElement.innerHTML = '<i class="ion ion-md-aperture" style="font-size: 24px;"></i>';
            emptyIconElement.className = 'ion ion-md-aperture';
            emptyIconElement.style.color = '#58c0f6';
        } else if (partType === 'FABRIKASI') {
            iconElement.className = 'text-white me-3';
            iconElement.style.width = '48px';
            iconElement.style.height = '48px';
            iconElement.style.display = 'flex';
            iconElement.style.alignItems = 'center';
            iconElement.style.justifyContent = 'center';
            iconElement.style.borderRadius = '50%';
            iconElement.style.backgroundColor = '#eb3124';
            iconElement.innerHTML = '<i class="mdi mdi-factory" style="font-size: 24px;"></i>';
            emptyIconElement.className = 'mdi mdi-factory';
            emptyIconElement.style.color = '#eb3124';
        }

        // Get the revenue for this part type from the card
        const revenueElement = document.querySelector(`.revenue-card[data-part-type="${partType}"] h4`);
        const revenue = revenueElement ? revenueElement.textContent : 'Rp 0';

        // Set the revenue color based on part type
        const revenueDisplay = document.getElementById('part-type-revenue');
        revenueDisplay.textContent = revenue;

        if (partType === 'AC') {
            revenueDisplay.style.color = '#28a745';
        } else if (partType === 'TYRE') {
            revenueDisplay.style.color = '#58c0f6';
        } else if (partType === 'FABRIKASI') {
            revenueDisplay.style.color = '#eb3124';
        }

        // Get the table body
        const tableBody = document.getElementById('partTypeDetailTableBody');
        const tableContainer = document.querySelector('#partTypeDetailModal .table-responsive');
        const emptyStateContainer = document.getElementById('part-type-empty-state');
        if (!tableBody) {
            return;
        }

        tableBody.innerHTML = '';
        tableBody.innerHTML = '<tr><td colspan="10" class="text-center"><div class="spinner-border text-primary" role="status"></div><p class="mt-2">Memuat data...</p></td></tr>';
        if (tableContainer) tableContainer.style.display = 'block';
        if (emptyStateContainer) emptyStateContainer.style.display = 'none';
        const startDateInput = document.getElementById('start-date') || document.getElementById('startDate');
        const endDateInput = document.getElementById('end-date') || document.getElementById('endDate');
        const monthPicker = document.getElementById('month-picker') || document.getElementById('monthPicker');
        const selectedMonth = monthPicker ? monthPicker.value : '';
        let queryParams = new URLSearchParams();
        if (startDateInput && endDateInput && startDateInput.value && endDateInput.value) {
            queryParams.append('start_date', startDateInput.value);
            queryParams.append('end_date', endDateInput.value);
        } else if (selectedMonth) {
            queryParams.append('month', selectedMonth);
        } else {
            const currentDate = new Date();
            const currentMonth = currentDate.getFullYear() + '-' + String(currentDate.getMonth() + 1).padStart(2, '0');
            queryParams.append('month', currentMonth);
        }

        // Add division filter
        queryParams.append('division', partType);

        // Add site filter if available
        const siteFilter = document.getElementById('site-filter');
        if (siteFilter && siteFilter.value) {
            queryParams.append('site', siteFilter.value);
        }

        const apiUrl = `/superadmin/division-parts-detail?${queryParams.toString()}`;
        fetch(apiUrl)
            .then(response => {
                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status} - ${response.statusText}`);
                }
                return response.json();
            })
            .then(response => {
                if (response.success && response.data && response.data.length > 0) {
                    if (tableContainer) tableContainer.style.display = 'block';
                    if (emptyStateContainer) emptyStateContainer.style.display = 'none';
                    if (response.summary) {
                        const revenueDisplay = document.getElementById('part-type-revenue');
                        if (revenueDisplay) {
                            revenueDisplay.textContent = `Rp ${formatNumber(response.summary.total_value_with_ppn)}`;
                        }

                        // Update summary cards
                        const totalItemsElement = document.getElementById('total-items');
                        const totalQuantityElement = document.getElementById('total-quantity');

                        if (totalItemsElement) {
                            totalItemsElement.textContent = formatNumber(response.summary.total_items);
                        }

                        if (totalQuantityElement) {
                            totalQuantityElement.textContent = formatNumber(response.summary.total_quantity);
                        }
                    }
                    tableBody.innerHTML = '';

                    response.data.forEach((item, index) => {
                        const row = document.createElement('tr');
                        row.innerHTML = `
                            <td>${index + 1}</td>
                            <td><small>${item.part_code || '-'}</small></td>
                            <td>
                                <div class="fw-bold">${item.part_name}</div>
                                ${item.site_part_name && item.site_part_name !== item.part_name ?
                                    `<small class="text-muted">${item.site_part_name}</small>` : ''}
                            </td>
                            <td><small>${item.site_name}</small></td>
                            <td class="text-center">${item.quantity}</td>
                            <td class="text-end">Rp ${formatNumber(item.price)}</td>
                            <td class="text-end fw-bold">Rp ${formatNumber(item.total_value)}</td>
                            <td><small>${item.transaction_date}</small></td>
                            <td><small>${item.wo_number || '-'}</small></td>
                            <td>
                                <span class="badge badge-sm ${getStatusBadgeClass(item.transaction_status)}">
                                    ${item.transaction_status}
                                </span>
                            </td>
                        `;
                        tableBody.appendChild(row);
                    });
                } else {
                    if (tableContainer) tableContainer.style.display = 'none';
                    if (emptyStateContainer) emptyStateContainer.style.display = 'block';
                }
            })
            .catch(error => {
                if (tableBody) {
                    tableBody.innerHTML = `<tr><td colspan="10" class="text-center text-danger">
                        <i class="mdi mdi-alert-circle me-2"></i>
                        Gagal memuat data: ${error.message}<br>
                        <small class="text-muted">Silakan periksa koneksi internet dan coba lagi.</small>
                    </td></tr>`;
                }

                if (tableContainer) tableContainer.style.display = 'block';
                if (emptyStateContainer) emptyStateContainer.style.display = 'none';
            });
    }

    /**
     * Get Bootstrap badge class for transaction status
     *
     * @param {string} status - The transaction status
     * @returns {string} - The badge class
     */
    function getStatusBadgeClass(status) {
        switch (status) {
            case 'Selesai':
                return 'bg-success';
            case 'Ready PO':
                return 'bg-primary';
            case 'On Process':
                return 'bg-warning text-dark';
            case 'Pending':
                return 'bg-info';
            case 'perbaikan':
                return 'bg-danger';
            default:
                return 'bg-secondary';
        }
    }

    /**
     * Format a number with thousands separators
     *
     * @param {number} number - The number to format
     * @returns {string} - The formatted number
     */
    function formatNumber(number) {
        return new Intl.NumberFormat('id-ID').format(number);
    }

    // Extract the bestPartsAllSites data from the page and make it available globally
    window.addEventListener('load', function() {
        // Get the data from the existing best parts section
        const categories = ['AC', 'TYRE', 'FABRIKASI'];
        window.bestPartsAllSites = {};

        categories.forEach(category => {
            const items = [];
            const categoryPane = document.getElementById(`category-${category}`);

            if (categoryPane) {
                // Get total revenue
                const totalRevenueElement = categoryPane.querySelector('.text-success, .text-info, .text-warning');
                const totalRevenueText = totalRevenueElement ? totalRevenueElement.textContent : 'Rp 0';
                const totalRevenue = parseFloat(totalRevenueText.replace(/[^\d]/g, ''));

                // Get items from the table
                const tableRows = categoryPane.querySelectorAll('tbody tr');
                tableRows.forEach(row => {
                    const cells = row.querySelectorAll('td');
                    if (cells.length >= 4) {
                        const partName = cells[0].textContent.trim();
                        const quantity = parseInt(cells[1].textContent.trim(), 10);
                        const totalValueText = cells[2].textContent.trim();
                        const totalValue = parseFloat(totalValueText.replace(/[^\d]/g, ''));
                        const contributionPercent = parseFloat(cells[3].textContent);

                        // Extract site name if available (not in the current table)
                        // We'll use a placeholder for now
                        const siteName = 'Semua Site';

                        // Calculate average price for the part
                        const avgPrice = quantity > 0 ? totalValue / quantity : 0;

                        items.push({
                            part_name: partName,
                            part_code: '', // We don't have this in the table, but the modal expects it
                            total_quantity: quantity,
                            total_value: totalValue,
                            avg_price: avgPrice,
                            site_name: siteName,
                            contribution_percent: contributionPercent
                        });
                    }
                });

                window.bestPartsAllSites[category] = {
                    items: items,
                    count: items.length,
                    total_revenue_with_ppn: totalRevenue
                };
            }
        });

        // If we have access to the API, we could fetch more detailed data
        // This would be a better approach than scraping the DOM
        const monthPicker = document.getElementById('month-picker') || document.getElementById('monthPicker');
        const selectedMonth = monthPicker ? monthPicker.value : '';

        // Try to fetch more detailed data from the API
        fetch(`/superadmin/best-parts-data?month=${selectedMonth}`)
            .then(response => {
                if (!response.ok) {
                    throw new Error('Network response was not ok');
                }
                return response.json();
            })
            .then(data => {
                if (data) {
                    window.bestPartsAllSites = data;
                }
            })
            .catch(error => {
                console.error('Error loading best parts data:', error);
            });
    });
});
