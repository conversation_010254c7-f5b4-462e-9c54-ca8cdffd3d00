/* Kasir Dashboard Specific Styles */

/* Card animations */
.card {
    transition: transform 0.2s ease-in-out, box-shadow 0.2s ease-in-out;
}

.card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

/* Form styling */
.form-control:focus {
    border-color: #225297;
    box-shadow: 0 0 0 0.2rem rgba(34, 82, 151, 0.25);
}

.form-select:focus {
    border-color: #225297;
    box-shadow: 0 0 0 0.2rem rgba(34, 82, 151, 0.25);
}

/* Button hover effects */
.btn-primary:hover {
    background-color: #1e4785;
    border-color: #1e4785;
    transform: translateY(-1px);
}

.btn-outline-primary:hover {
    background-color: #225297;
    border-color: #225297;
    transform: translateY(-1px);
}

/* Table styling */
.table-hover tbody tr:hover {
    background-color: rgba(34, 82, 151, 0.05);
}

/* Badge styling */
.badge {
    font-weight: 500;
    padding: 0.5em 0.75em;
}

/* Currency input styling */
.currency-input {
    font-family: 'Courier New', monospace;
    font-weight: bold;
    text-align: right;
}

/* Skeleton loader animation */
@keyframes skeleton-loading {
    0% {
        background-position: -200px 0;
    }
    100% {
        background-position: calc(200px + 100%) 0;
    }
}

.skeleton {
    background-color: #eee;
    background-image: linear-gradient(90deg, #eee, #f5f5f5, #eee);
    background-size: 200px 100%;
    background-repeat: no-repeat;
    animation: skeleton-loading 1.3s ease-in-out infinite;
}

/* Empty state styling */
.empty-state {
    padding: 3rem 1rem;
    text-align: center;
    color: #6c757d;
}

.empty-state img {
    max-width: 150px;
    opacity: 0.6;
    margin-bottom: 1rem;
}

.empty-state h5 {
    color: #495057;
    margin-bottom: 0.5rem;
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .card-header h5 {
        font-size: 0.9rem;
    }
    
    .btn {
        font-size: 0.8rem;
        padding: 0.375rem 0.75rem;
    }
    
    .table {
        font-size: 0.8rem;
    }
    
    .form-control,
    .form-select {
        font-size: 0.9rem;
    }
}

@media (max-width: 576px) {
    .card-header {
        padding: 0.75rem;
    }
    
    .card-body {
        padding: 1rem;
    }
    
    .btn-sm {
        font-size: 0.75rem;
        padding: 0.25rem 0.5rem;
    }
}

/* Loading spinner */
.spinner-border-sm {
    width: 1rem;
    height: 1rem;
}

/* File input styling */
.form-control[type="file"] {
    padding: 0.375rem 0.75rem;
}

/* Modal styling */
.modal-header {
    border-bottom: 1px solid #dee2e6;
}

.modal-footer {
    border-top: 1px solid #dee2e6;
}

/* Attachment preview */
.attachment-preview img {
    max-width: 100%;
    height: auto;
    border-radius: 4px;
}

.attachment-preview embed {
    width: 100%;
    height: 500px;
    border: none;
    border-radius: 4px;
}

/* Success/Error states */
.text-success {
    color: #28a745 !important;
}

.text-danger {
    color: #dc3545 !important;
}

.bg-success {
    background-color: #28a745 !important;
}

.bg-danger {
    background-color: #dc3545 !important;
}

/* Pagination styling */
.pagination .page-link {
    color: #225297;
    border-color: #dee2e6;
}

.pagination .page-item.active .page-link {
    background-color: #225297;
    border-color: #225297;
}

.pagination .page-link:hover {
    color: #1e4785;
    background-color: #e9ecef;
    border-color: #dee2e6;
}

/* Header gradient */
.kasir-header {
    background: linear-gradient(135deg, #225297 0%, #58c0f6 100%);
}

/* Form validation states */
.is-invalid {
    border-color: #dc3545;
}

.is-valid {
    border-color: #28a745;
}

.invalid-feedback {
    color: #dc3545;
    font-size: 0.875rem;
}

.valid-feedback {
    color: #28a745;
    font-size: 0.875rem;
}

/* Utility classes */
.shadow-kit {
    box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
}

.rounded-lg {
    border-radius: 0.5rem;
}

.font-bold {
    font-weight: 700;
}

/* Print styles */
@media print {
    .kasir-header,
    .btn,
    .pagination {
        display: none !important;
    }
    
    .card {
        border: 1px solid #000 !important;
        box-shadow: none !important;
    }
    
    .table {
        border-collapse: collapse !important;
    }
    
    .table th,
    .table td {
        border: 1px solid #000 !important;
    }
}
