@extends('sales.contentsales')
@section('resourcesales')
<!-- SweetAlert2 CSS -->
<link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/sweetalert2@11.7.32/dist/sweetalert2.min.css">
<!-- SweetAlert2 JS -->
<script src="https://cdn.jsdelivr.net/npm/sweetalert2@11.7.32/dist/sweetalert2.all.min.js"></script>
<style>
    .w-fit-content {
        width: fit-content;
    }
    .shadow-kit {
        border: 1px ;
        border-radius: 0.5rem;
        background-color: #fff;
    }
    .btn {
        font-size: 11px;
    }
    .table {
        font-size: 11px;
    }
    .form-control {
        font-size: 11px;
    }
    .form-label {
        font-size: 11px;
        font-weight: bold;
    }

    /* Skeleton loader styles */
    .skeleton {
        background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
        background-size: 200% 100%;
        animation: loading 1.5s infinite;
    }

    @keyframes loading {
        0% {
            background-position: 200% 0;
        }
        100% {
            background-position: -200% 0;
        }
    }

    .skeleton-row {
        height: 40px;
        margin-bottom: 8px;
        border-radius: 4px;
    }
</style>
@endsection
@section('contentsales')

@include('sales.partials.navigation')

<div class="container-fluid">
    <div class="row">
        <!-- Left side: Customer Table -->
        <div class="col-md-7">
            <div class="bgwhite shadow-kit rounded-lg mb-4">
                <div class="card-header bg-primary text-white d-flex justify-content-between align-items-center">
                    <h5 class="mb-0 font-bold text-uppercase text-white">Daftar Customer</h5>
                    <div class="d-flex align-items-center">
                        <input type="text" id="search-input" class="form-control form-control-sm me-2" placeholder="Cari customer..." style="width: 200px;">
                    </div>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-bordered w-100" id="customer-table">
                            <thead class="bg-light">
                                <tr>
                                    <th>No</th>
                                    <th>KODE</th>
                                    <th>NAMA CUSTOMER</th>
                                    <th>ALAMAT</th>
                                    <th>TOTAL INVOICE</th>
                                    <th>TOTAL PEMBAYARAN</th>
                                    <th>SALDO PIUTANG</th>
                                    <th>Aksi</th>
                                </tr>
                            </thead>
                            <tbody id="customer-table-body">
                                <!-- Data will be loaded dynamically -->
                            </tbody>
                        </table>
                    </div>
                    <div class="d-flex justify-content-between align-items-center mt-3">
                        <div>
                            <span id="entries-info">Showing 0 to 0 of 0 entries</span>
                        </div>
                        <div id="pagination-container">
                            <!-- Pagination will be loaded dynamically -->
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Right side: Form Customer -->
        <div class="col-md-5" id="form-container">
            <div class="bgwhite shadow-kit rounded-lg mb-4 sticky-top" style="top: 10px; z-index: 100;">
                <div class="card-header bg-primary text-white d-flex justify-content-between align-items-center">
                    <h5 class="mb-0 font-bold text-uppercase text-white">Form Customer</h5>
                    <div>
                        <button type="button" id="btn-reset-form" class="btn btn-sm btn-light me-1">
                            <i class="mdi mdi-refresh"></i> Reset
                        </button>
                    </div>
                </div>
                <div class="card-body">
                    <form id="customer-form">
                        <input type="hidden" id="customer-id">
                        <div class="mb-3">
                            <label for="code" class="form-label">Kode Customer</label>
                            <input type="text" class="form-control" id="code" name="code" required>
                        </div>
                        <div class="mb-3">
                            <label for="nama_customer" class="form-label">Nama Customer</label>
                            <input type="text" class="form-control" id="nama_customer" name="nama_customer" required>
                        </div>
                        <div class="mb-3">
                            <label for="alamat" class="form-label">Alamat</label>
                            <textarea class="form-control" id="alamat" name="alamat" rows="3"></textarea>
                        </div>
                        <div class="d-grid">
                            <button type="submit" class="btn btn-primary">
                                <i class="mdi mdi-content-save"></i> Simpan
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

@endsection

@section('scripts')
<!-- Customer management JavaScript -->
@vite('resources/js/sales/customer.js')
@endsection