<?php

namespace App\Http\Controllers;

use App\Models\LogAktivitas;
use App\Models\Part;
use App\Models\PartInventory;
use App\Models\RequisitionDetail;
use App\Models\SiteInStock;
use App\Models\StockTransaction;
use Illuminate\Http\Request;
use Illuminate\Support\Carbon;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Validator;

class StocktransactionController extends Controller
{
    public function index()
    {
        $transactions = StockTransaction::all();
        return view('sites.instocksite', compact('transactions'));
    }

    public function processConfirmation(Request $request, string $id)
    {
        try {
            DB::beginTransaction();
            $transaction = StockTransaction::findOrFail($id);
            Log::error($transaction); // Berguna untuk debugging
            // Cari PartInventory di DESTINATION site untuk penerimaan
            $partInventory = PartInventory::where('site_id', $transaction->destination_siteid)
                ->where('part_code', $transaction->part_code)
                ->first();

            if (!$partInventory) {
                Log::error("PartInventory tidak ditemukan untuk code_part: " . $transaction->part_code . " (ID transaksi: " . $id . ") dan site_id: " . $transaction->destination_siteid);
                throw new \Exception("PartInventory tidak ditemukan di destination site."); // Pesan error lebih spesifik
            }

            $siteInStock = new SiteInStock();
            $siteInStock->part_inventory_id = $partInventory->part_inventory_id;
            $siteInStock->employee_id = session('employee_id');
            $siteInStock->quantity = $transaction->quantity_sent;
            $siteInStock->date_in = Carbon::now();
            $siteInStock->notes = '';
            $siteInStock->save();

            // Cari PartInventory di DESTINATION site untuk UPDATE
            $addInventory = PartInventory::where('part_code', $transaction->part_code)
                ->where('site_id', $transaction->destination_siteid) //Perbaiki: destination siteid
                ->first();

            if ($addInventory) {
                $addInventory->stock_quantity += $transaction->quantity_sent;
                $addInventory->save();
            } else {
                Log::error("PartInventory tidak ditemukan untuk part_code: " . $transaction->part_code . " (ID transaksi: " . $id . ") dan site_id: " . $transaction->destination_siteid . " saat update.");
                throw new \Exception("PartInventory tidak ditemukan di destination site saat update."); // Pesan error lebih spesifik
            }

            $partname = Part::where('part_code', $transaction->part_code)->first()->part_name;
            LogAktivitas::create([
                'site_id' => session('site_id'),
                'name' => session('name'),
                'action' => 'Konfirmasi In Stock',
                'decription' => "Admin " . session('name') . " Menambahkan " . $partname . " sebanyak " . $transaction->quantity_sent,
                'ip_address' => $request->ip(),
                'table' => "In stock",
            ]);

            // Update requisition detail status if exists
            $requisitionDetail = RequisitionDetail::where('part_code', $transaction->part_code)
                ->whereHas('requisition', function ($query) use ($transaction) {
                    $query->where('site_id', $transaction->destination_siteid);
                })
                ->where('status_details', '!=', 'selesai')
                ->orderBy('created_at', 'desc')
                ->first();

            if ($requisitionDetail) {
                $totalConfirmed = $requisitionDetail->quantity_confirm + $transaction->quantity_sent;
                $requisitionDetail->quantity_confirm = $totalConfirmed;

                // Update status based on confirmation
                if ($totalConfirmed >= $requisitionDetail->quantity) {
                    $requisitionDetail->status_details = 'selesai';
                } else {
                    $requisitionDetail->status_details = 'dikirim sebagian';
                }

                $requisitionDetail->save();

                // Update parent requisition status
                $this->updateRequisitionStatus($requisitionDetail->requisition);
            }

            $transaction->status = 'selesai';
            $transaction->save();

            DB::commit();

            return response()->json(['success' => true, 'message' => 'Konfirmasi berhasil.']);
        } catch (\Exception $e) {
            DB::rollback();
            Log::error($e);
            return response()->json(['success' => false, 'message' => 'Terjadi kesalahan: ' . $e->getMessage()]);
        }
    }

    public function processAdjustment(Request $request)
    {
        $messages = [
            'transaction_id.required' => 'ID transaksi harus diisi',
            'transaction_id.exists' => 'ID transaksi tidak valid',
            'quantity_received.required' => 'Jumlah yang diterima harus diisi',
            'quantity_received.integer' => 'Jumlah yang diterima harus berupa angka',
            'quantity_received.min' => 'Jumlah yang diterima tidak boleh negatif',
            'discrepancy_reason.required_if' => 'Alasan selisih harus diisi jika ada perbedaan jumlah',
            'discrepancy_type.required_if' => 'Tipe selisih harus dipilih jika ada perbedaan jumlah',
        ];

        $rules = [
            'transaction_id' => 'required|exists:stock_transactions,id',
            'quantity_received' => 'required|integer|min:0',
            'discrepancy_reason' => 'required_if:quantity_received,!quantity_sent|string',
            'quantity_discrepancy' => 'nullable|integer',
            'discrepancy_type' => 'required_if:quantity_received,!quantity_sent|string',
            'notes' => 'nullable|string|max:255',
        ];

        $validator = Validator::make($request->all(), $rules, $messages);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Validasi gagal',
                'errors' => $validator->errors()
            ], 422);
        }

        try {
            $transaction = StockTransaction::where('id', $request->transaction_id)
                ->where(function ($query) {
                    $query->where('status', 'intransit')
                        ->orWhere('status', 'pending');
                })
                ->first();

            if (!$transaction) {
                throw new \Exception('Transaksi tidak ditemukan atau sudah selesai diproses');
            }

            // Prevent duplicate processing
            if (in_array($transaction->status, ['complete', 'adjusted'])) {
                throw new \Exception('Transaksi ini sudah diproses sebelumnya');
            }

            $transaction->quantity_received = $request->input('quantity_received');
            $transaction->discrepancy_reason = $request->input('discrepancy_reason');
            $transaction->notes = $request->input('notes');

            // Hitung dan set selisih
            $sent = $transaction->quantity_sent;
            $received = $request->input('quantity_received');
            $discrepancy = $sent - $received;

            $transaction->quantity_discrepancy = $discrepancy;

            // Discrepancy type
            $transaction->discrepancy_type = $request->input('discrepancy_type');

            // Update status berdasarkan kondisi
            if ($discrepancy == 0) {
                $transaction->status = 'complete';
            } else {
                $transaction->status = 'adjusted';  // Atau status lain sesuai logika Anda
                $transaction->discrepancy_status = 'open';  //misalnya
            }

            $transaction->save();

            return response()->json(['success' => true, 'message' => 'Penyesuaian berhasil disimpan!']);
        } catch (\Exception $e) {
            Log::error($e);
            return response()->json(['success' => false, 'message' => 'Terjadi kesalahan: ' . $e->getMessage()]);
        }
    }

    public function warehouseIndex(Request $request)
    {
        if ($request->ajax()) {
            $siteId = $request->input('site_id');
            $page = $request->input('page', 1);
            $perPage = $request->input('per_page', 15); // Default to 15 items per page

            $query = StockTransaction::whereIn('status', ['intransit', 'return', 'hilang','pending'])
                ->with(['part', 'site'])
                ->where(function($q) {
                    $q->where('discrepancy_status', '!=', 'selesai')
                      ->orWhereNull('discrepancy_status');
                });


            // Filter by site if site_id is provided
            if ($siteId) {
                $query->where('destination_siteid', $siteId);
            }

            // Get total count for pagination
            $total = $query->count();

            // Get paginated data
            $transactions = $query->skip(($page - 1) * $perPage)
                ->take($perPage)
                ->get();

            // Get all sites for the filter dropdown
            $sites = \App\Models\Site::all();

            return response()->json([
                'transactions' => $transactions,
                'sites' => $sites,
                'current_page' => (int)$page,
                'per_page' => (int)$perPage,
                'last_page' => ceil($total / $perPage),
                'total' => $total
            ]);
        }

        return view('warehouse.transactions_part');
    }

    public function warehouseResolveDiscrepancy(Request $request, string $id)
    {
        try {
            $transaction = StockTransaction::findOrFail($id);
            $request->validate([
                'discrepancy_resolution' => 'required|string',
                'discrepancy_status' => 'required|in:investigation,return,pending,hilang', // Ensure all possible values are included in validation.
            ]);

            DB::beginTransaction();

            if ($request->input('discrepancy_status') == 'return') {
                // For return status, update inventory by adding back quantity_sent - quantity_received
                $partInventory = PartInventory::where('part_code', $transaction->part_code)
                    ->where('site_id', session('site_id'))
                    ->first();

                if (!$partInventory) {
                    throw new \Exception('Part inventory tidak ditemukan di warehouse');
                }

                // Update inventory
                $returnQuantity = $transaction->quantity_sent - $transaction->quantity_received;
                $partInventory->stock_quantity += $returnQuantity;
                $partInventory->save();

                // Log the inventory change
                Log::info("Return inventory update: Added {$returnQuantity} to {$transaction->part_code} inventory. New quantity: {$partInventory->stock_quantity}");

                // Update transaction
                $transaction->discrepancy_status = 'selesai'; // Set discrepancy_status to 'selesai'
                $transaction->notes = $request->input('discrepancy_resolution');
                $transaction->status = 'return'; 
                $transaction->save();
            } else if ($request->input('discrepancy_status') == 'hilang') {
                // For hilang status, only update the status without changing inventory
                $transaction->discrepancy_status = 'selesai'; 
                $transaction->status = 'hilang'; 
                $transaction->notes = $request->input('discrepancy_resolution');
                $transaction->save();

                // Log the status change
                Log::info("Hilang status update for transaction ID {$id}: Part {$transaction->part_code}, Quantity: {$transaction->quantity_sent}");
            } else {
                // For other statuses
                $transaction->notes = $request->input('discrepancy_resolution');
                $transaction->status = $request->input('discrepancy_status');
                $transaction->save();
            }

            DB::commit();
            return response()->json(['success' => true, 'message' => 'Perubahan selisih berhasil disimpan!']);
        } catch (\Illuminate\Validation\ValidationException $e) {
            return response()->json(['success' => false, 'message' => 'Validasi gagal', 'errors' => $e->errors()], 422);
        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('Error in warehouseResolveDiscrepancy: ' . $e->getMessage());
            return response()->json(['success' => false, 'message' => 'Terjadi kesalahan: ' . $e->getMessage()], 500);
        }
    }

    // deleted
    public function destroy(string $id)
    {
        try {
            $transaction = StockTransaction::findOrFail($id);

            $partInventory = PartInventory::where('part_code', $transaction->part_code)
                ->where('site_id', session('site_id'))
                ->first();

            if ($partInventory && $transaction->status == 'intransit') {
                $partInventory->stock_quantity += $transaction->quantity_sent;
                $partInventory->save();
                $transaction->delete();
            }
            return response()->json(['success' => true, 'message' => 'Transaksi berhasil dihapus.']);
        } catch (\Exception $e) {
            Log::error($e);
            return response()->json(['success' => false, 'message' => 'Terjadi kesalahan: ' . $e->getMessage()]);
        }
    }

    private function updateRequisitionStatus($requisition)
    {
        $allDetails = $requisition->requisitionDetails;
        $allComplete = $allDetails->every(function ($detail) {
            return $detail->status_details === 'selesai';
        });

        $requisition->status = $allComplete ? 'selesai' : 'pending';
        $requisition->save();
    }
}