document.addEventListener('DOMContentLoaded', function() {
    // Set CSRF token for all AJAX requests
    const csrfToken = document.querySelector('meta[name="csrf-token"]').getAttribute('content');

    // Helper function to format currency
    function formatCurrency(amount) {
        return 'Rp ' + new Intl.NumberFormat('id-ID').format(amount);
    }

    // Form elements
    const formContainer = document.getElementById('form-container');
    const customerForm = document.getElementById('customer-form');
    const customerId = document.getElementById('customer-id');
    const code = document.getElementById('code');
    const namaCustomer = document.getElementById('nama_customer');
    const alamat = document.getElementById('alamat');
    const searchInput = document.getElementById('search-input');

    // Buttons
    const btnResetForm = document.getElementById('btn-reset-form');

    // Table elements
    const customerTableBody = document.getElementById('customer-table-body');
    const entriesInfo = document.getElementById('entries-info');
    const paginationContainer = document.getElementById('pagination-container');

    // Pagination variables
    let currentPage = 1;
    let currentSearch = '';

    // Event listeners for buttons
    btnResetForm.addEventListener('click', resetForm);

    // Search functionality
    let searchTimeout;
    searchInput.addEventListener('input', function() {
        clearTimeout(searchTimeout);
        searchTimeout = setTimeout(() => {
            currentSearch = this.value;
            currentPage = 1;
            loadCustomers();
        }, 500);
    });

    // Form submission
    customerForm.addEventListener('submit', handleFormSubmit);

    // Load customers on page load
    loadCustomers();

    // Function to load customers with AJAX
    function loadCustomers() {
        // Show skeleton loader
        showSkeletonLoader();

        $.ajax({
            url: '/sales/customer-data',
            type: 'GET',
            data: {
                page: currentPage,
                search: currentSearch
            },
            success: function(response) {
                renderTable(response);
                renderPagination(response);
                updateEntriesInfo(response);
            },
            error: function(xhr) {
                Swal.fire({
                    icon: 'error',
                    title: 'Error',
                    text: 'Gagal memuat data customer. Silakan coba lagi.'
                });
            }
        });
    }

    // Function to show skeleton loader
    function showSkeletonLoader() {
        let skeletonHtml = '';
        for (let i = 0; i < 10; i++) {
            skeletonHtml += `
                <tr>
                    <td><div class="skeleton skeleton-row"></div></td>
                    <td><div class="skeleton skeleton-row"></div></td>
                    <td><div class="skeleton skeleton-row"></div></td>
                    <td><div class="skeleton skeleton-row"></div></td>
                    <td><div class="skeleton skeleton-row"></div></td>
                    <td><div class="skeleton skeleton-row"></div></td>
                    <td><div class="skeleton skeleton-row"></div></td>
                    <td><div class="skeleton skeleton-row"></div></td>
                </tr>
            `;
        }
        customerTableBody.innerHTML = skeletonHtml;
        entriesInfo.textContent = 'Loading...';
        paginationContainer.innerHTML = '';
    }

    // Function to render table data
    function renderTable(response) {
        const data = response.data;
        let html = '';

        if (data.length === 0) {
            html = `
                <tr>
                    <td colspan="8" class="text-center py-4">
                        <div class="d-flex flex-column align-items-center">
                            <i class="mdi mdi-account-off mdi-48px text-muted mb-2"></i>
                            <p class="text-muted mb-0">Tidak ada data customer</p>
                        </div>
                    </td>
                </tr>
            `;
        } else {
            data.forEach((customer, index) => {
                const rowNumber = (response.current_page - 1) * response.per_page + index + 1;
                html += `
                    <tr>
                        <td>${rowNumber}</td>
                        <td>${customer.code}</td>
                        <td>${customer.nama_customer}</td>
                        <td>${customer.alamat || '-'}</td>
                        <td>${formatCurrency(customer.total_invoice)}</td>
                        <td>${formatCurrency(customer.total_pembayaran)}</td>
                        <td>${formatCurrency(customer.saldo_piutang)}</td>
                        <td>
                            <button type="button" class="btn btn-sm btn-warning me-1" onclick="editCustomer(${customer.id})">
                                <i class="mdi mdi-pencil"></i>
                            </button>
                            <button type="button" class="btn btn-sm btn-danger" onclick="deleteCustomer(${customer.id})">
                                <i class="mdi mdi-delete"></i>
                            </button>
                        </td>
                    </tr>
                `;
            });
        }

        customerTableBody.innerHTML = html;
    }

    // Function to render pagination
    function renderPagination(response) {
        if (response.last_page <= 1) {
            paginationContainer.innerHTML = '';
            return;
        }

        let paginationHtml = '<nav><ul class="pagination pagination-sm mb-0">';

        // Previous button
        if (response.current_page > 1) {
            paginationHtml += `
                <li class="page-item">
                    <a class="page-link" href="#" onclick="changePage(${response.current_page - 1})">Previous</a>
                </li>
            `;
        }

        // Page numbers
        const startPage = Math.max(1, response.current_page - 2);
        const endPage = Math.min(response.last_page, response.current_page + 2);

        for (let i = startPage; i <= endPage; i++) {
            paginationHtml += `
                <li class="page-item ${i === response.current_page ? 'active' : ''}">
                    <a class="page-link" href="#" onclick="changePage(${i})">${i}</a>
                </li>
            `;
        }

        // Next button
        if (response.current_page < response.last_page) {
            paginationHtml += `
                <li class="page-item">
                    <a class="page-link" href="#" onclick="changePage(${response.current_page + 1})">Next</a>
                </li>
            `;
        }

        paginationHtml += '</ul></nav>';
        paginationContainer.innerHTML = paginationHtml;
    }

    // Function to update entries info
    function updateEntriesInfo(response) {
        const start = (response.current_page - 1) * response.per_page + 1;
        const end = Math.min(response.current_page * response.per_page, response.total);
        entriesInfo.textContent = `Showing ${start} to ${end} of ${response.total} entries`;
    }

    // Function to change page
    window.changePage = function(page) {
        currentPage = page;
        loadCustomers();
    };

    // Function to reset the form
    function resetForm() {
        customerForm.reset();
        customerId.value = '';
    }

    // Function to handle form submission
    function handleFormSubmit(e) {
        e.preventDefault();

        const id = customerId.value;

        const data = {
            code: code.value,
            nama_customer: namaCustomer.value,
            alamat: alamat.value
        };

        if (id) {
            // Update existing customer
            updateCustomer(id, data);
        } else {
            // Create new customer
            createCustomer(data);
        }
    }

    // Function to create a new customer
    function createCustomer(data) {
        fetch('/sales/customer', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRF-TOKEN': csrfToken
            },
            body: JSON.stringify(data)
        })
        .then(response => response.json())
        .then(result => {
            if (result.success) {
                Swal.fire({
                    icon: 'success',
                    title: 'Berhasil',
                    text: result.message,
                    showConfirmButton: false,
                    timer: 1500
                }).then(() => {
                    resetForm();
                    loadCustomers();
                });
            } else {
                Swal.fire({
                    icon: 'error',
                    title: 'Gagal',
                    text: result.message
                });
            }
        })
        .catch(error => {
            Swal.fire({
                icon: 'error',
                title: 'Gagal',
                text: 'Terjadi kesalahan saat menyimpan data'
            });
        });
    }

    // Function to edit a customer
    window.editCustomer = function(id) {
        fetch(`/sales/customer/${id}`)
        .then(response => response.json())
        .then(data => {
            customerId.value = data.id;
            code.value = data.code;
            namaCustomer.value = data.nama_customer;
            alamat.value = data.alamat || '';
        })
        .catch(error => {
            Swal.fire({
                icon: 'error',
                title: 'Gagal',
                text: 'Terjadi kesalahan saat mengambil data customer'
            });
        });
    };

    // Function to update a customer
    function updateCustomer(id, data) {
        fetch(`/sales/customer/${id}`, {
            method: 'PUT',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRF-TOKEN': csrfToken
            },
            body: JSON.stringify(data)
        })
        .then(response => response.json())
        .then(result => {
            if (result.success) {
                Swal.fire({
                    icon: 'success',
                    title: 'Berhasil',
                    text: result.message,
                    showConfirmButton: false,
                    timer: 1500
                }).then(() => {
                    resetForm();
                    loadCustomers();
                });
            } else {
                Swal.fire({
                    icon: 'error',
                    title: 'Gagal',
                    text: result.message
                });
            }
        })
        .catch(error => {
            Swal.fire({
                icon: 'error',
                title: 'Gagal',
                text: 'Terjadi kesalahan saat memperbarui data'
            });
        });
    }

    // Function to delete a customer
    window.deleteCustomer = function(id) {
        Swal.fire({
            title: 'Konfirmasi',
            text: 'Apakah Anda yakin ingin menghapus customer ini?',
            icon: 'warning',
            showCancelButton: true,
            confirmButtonColor: '#d33',
            cancelButtonColor: '#3085d6',
            confirmButtonText: 'Ya, Hapus!',
            cancelButtonText: 'Batal'
        }).then((result) => {
            if (result.isConfirmed) {
                fetch(`/sales/customer/${id}`, {
                    method: 'DELETE',
                    headers: {
                        'X-CSRF-TOKEN': csrfToken
                    }
                })
                .then(response => response.json())
                .then(result => {
                    if (result.success) {
                        Swal.fire({
                            icon: 'success',
                            title: 'Berhasil',
                            text: result.message,
                            showConfirmButton: false,
                            timer: 1500
                        }).then(() => {
                            loadCustomers();
                        });
                    } else {
                        Swal.fire({
                            icon: 'error',
                            title: 'Gagal',
                            text: result.message
                        });
                    }
                })
                .catch(error => {
                    Swal.fire({
                        icon: 'error',
                        title: 'Gagal',
                        text: 'Terjadi kesalahan saat menghapus data'
                    });
                });
            }
        });
    };
});
