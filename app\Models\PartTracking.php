<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class PartTracking extends Model
{
    use HasFactory;
    protected $table = 'part_trackings';
    protected $primaryKey = 'part_tracking_id';
    protected $fillable = [
        'part_inventory_id',
        'requestor_id',
        'receiver_id',
        'transaction_type',
        'date_created',
        'date_updated',
        'status',
    ];
    protected $casts = [
        'date_created' => 'datetime',
        'date_updated' => 'datetime',
    ];
    public function partInventory()
    {
        return $this->belongsTo(PartInventory::class, 'part_inventory_id', 'part_inventory_id');
    }
    public function requestor()
    {
        return $this->belongsTo(User::class, 'requestor_id', 'employee_id');
    }
    public function receiver()
    {
        return $this->belongsTo(User::class, 'receiver_id', 'employee_id');
    }
}