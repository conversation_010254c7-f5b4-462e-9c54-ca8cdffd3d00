<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class ReturnStock extends Model
{
    use HasFactory;

    protected $table = 'return_stocks';
    protected $primaryKey = 'return_stock_id';

    protected $fillable = [
        'from_site_id',
        'to_site_id',
        'employee_id',
        'part_inventory_id',
        'part_type',
        'quantity',
    ];

    public function fromSite()
    {
        return $this->belongsTo(Site::class, 'from_site_id', 'site_id');
    }

    public function toSite()
    {
        return $this->belongsTo(Site::class, 'to_site_id', 'site_id');
    }

    public function user()
    {
        return $this->belongsTo(User::class, 'employee_id', 'employee_id');
    }

    public function partInventory()
    {
        return $this->belongsTo(PartInventory::class, 'part_inventory_id', 'part_inventory_id');
    }
}