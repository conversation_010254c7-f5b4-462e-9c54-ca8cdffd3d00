/* Unit Recap Dashboard Styles */

.chart-container {
    position: relative;
    width: 100%;
    height: 400px;
}

.skeleton-loader {
    animation: pulse 1.5s ease-in-out infinite;
}

.skeleton-chart {
    height: 400px;
    background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
    background-size: 200% 100%;
    animation: loading 1.5s infinite;
    border-radius: 8px;
}

@keyframes loading {
    0% {
        background-position: 200% 0;
    }
    100% {
        background-position: -200% 0;
    }
}

@keyframes pulse {
    0%, 100% {
        opacity: 1;
    }
    50% {
        opacity: 0.5;
    }
}

/* Unit search dropdown */
.dropdown-menu {
    border: 1px solid #ddd;
    border-radius: 4px;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
    z-index: 1000;
}

.dropdown-item {
    padding: 8px 12px;
    cursor: pointer;
    border-bottom: 1px solid #f0f0f0;
}

.dropdown-item:hover {
    background-color: #f8f9fa;
}

.dropdown-item:last-child {
    border-bottom: none;
}

/* Report dates list */
.list-group-item {
    cursor: pointer;
    transition: background-color 0.2s ease;
}

.list-group-item:hover {
    background-color: #f8f9fa;
}

.list-group-item.active {
    background-color: #0d6efd;
    border-color: #0d6efd;
    color: white;
}

/* Chart clickable bars */
#problemComponentChart {
    cursor: pointer;
}

/* Modal table styling */
.table th {
    background-color: #343a40;
    color: white;
    border-color: #454d55;
    font-weight: 600;
    font-size: 0.875rem;
}

.table td {
    vertical-align: middle;
    font-size: 0.875rem;
}

.table-striped > tbody > tr:nth-of-type(odd) > td {
    background-color: rgba(0,0,0,.02);
}

.table-hover > tbody > tr:hover > td {
    background-color: rgba(0,0,0,.075);
}

/* Highlighted job text */
.highlighted-job-text {
    color: #1e2ab2;
}

/* Badge styling */
.badge {
    font-size: 0.75rem;
    padding: 0.375rem 0.5rem;
}

/* Loading states */
.spinner-border-sm {
    width: 1rem;
    height: 1rem;
}

/* Card styling */
.card {
    border: none;
    box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
    border-radius: 0.375rem;
}

.card-body {
    padding: 1.5rem;
}

.header-title {
    font-size: 1.125rem;
    font-weight: 600;
    color: #495057;
    margin-bottom: 0;
}

/* Form controls */
.form-control-sm {
    padding: 0.25rem 0.5rem;
    font-size: 0.875rem;
    border-radius: 0.25rem;
}

/* Alert styling */
.alert-info {
    background-color: #d1ecf1;
    border-color: #bee5eb;
    color: #0c5460;
    border-radius: 0.375rem;
}

/* Empty state styling */
.text-muted {
    color: #6c757d !important;
}

/* Full-screen modal styling */
.modal-dialog.modal-fullscreen {
    width: 100vw;
    max-width: none;
    height: 100vh;
    margin: 0;
    padding: 0;
}

.modal-dialog.modal-fullscreen .modal-content {
    height: 100%;
    border-radius: 0;
    border: 0;
}

.modal-dialog.modal-fullscreen .modal-body {
    overflow-y: auto;
    padding: 1.5rem;
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .chart-container {
        height: 300px;
    }

    .modal-dialog.modal-fullscreen .modal-body {
        padding: 1rem;
    }

    .table-responsive {
        font-size: 0.8rem;
    }

    .card-body {
        padding: 1rem;
    }
}

/* Custom scrollbar for dropdown and dates list */
.dropdown-menu::-webkit-scrollbar,
.list-group::-webkit-scrollbar {
    width: 6px;
}

.dropdown-menu::-webkit-scrollbar-track,
.list-group::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 3px;
}

.dropdown-menu::-webkit-scrollbar-thumb,
.list-group::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 3px;
}

.dropdown-menu::-webkit-scrollbar-thumb:hover,
.list-group::-webkit-scrollbar-thumb:hover {
    background: #a8a8a8;
}

/* Chart legend styling */
.chart-legend {
    display: flex;
    justify-content: center;
    margin-top: 1rem;
    flex-wrap: wrap;
}

.chart-legend-item {
    display: flex;
    align-items: center;
    margin: 0 1rem 0.5rem 0;
}

.chart-legend-color {
    width: 12px;
    height: 12px;
    margin-right: 0.5rem;
    border-radius: 2px;
}

/* Tooltip styling */
.tooltip {
    font-size: 0.875rem;
}

.tooltip-inner {
    max-width: 300px;
    text-align: left;
}
