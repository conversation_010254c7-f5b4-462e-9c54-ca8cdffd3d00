@extends('sales.contentsales')
@section('resourcesales')
@vite(['resources/js/sales/dashboard.js'])
<style>
    .w-fit-content {
        width: fit-content;
    }
    .shadow-kit {
        border: 1px solid rgb(42, 105, 168);
        box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
        border-radius: 0.5rem;
        background-color: #fff;
    }
</style>
@endsection
@section('contentsales')
<div class="content">
    <div class="container-fluid">
        <div class="row">
            <div class="col-12">
                <div class="page-title-box">
                    <div class="page-title-right">
                        <ol class="breadcrumb m-0">
                            <li class="breadcrumb-item"><a href="javascript: void(0);">Dashboard</a></li>
                            <li class="breadcrumb-item active">Unit Transactions</li>
                        </ol>
                    </div>
                    <h4 class="page-title">Unit Transactions</h4>
                </div>
            </div>
        </div>

        <div class="row">
            <div class="col-md-12">
                <div class="card">
                    <div class="card-header bg-primary text-white d-flex justify-content-between align-items-center">
                        <div class="d-flex align-items-center">
                            <h5 class="mb-0 mr-3 font-bold text-uppercase text-white">Unit Transactions</h5>
                        </div>
                        <div class="d-flex">
                            <input type="date" id="date-from" class="form-control form-control-sm mr-2" value="{{ now()->subDay()->format('Y-m-d') }}">
                            <input type="date" id="date-to" class="form-control form-control-sm mr-2" value="{{ now()->addDay()->format('Y-m-d') }}">
                            <select id="site-filter" class="form-control form-control-sm mr-2">
                                <option value="">Semua Site</option>
                                @foreach($sites as $site)
                                <option value="{{ $site->site_id }}">{{ $site->site_name }}</option>
                                @endforeach
                            </select>
                            <select id="unit-filter" class="form-control form-control-sm mr-2">
                                <option value="">Semua Unit</option>
                                <!-- Units will be loaded dynamically based on selected site -->
                            </select>
                            <select id="status-filter" class="form-control form-control-sm mr-2">
                                <option value="">Semua Status</option>
                                @foreach($statuses as $status)
                                <option value="{{ $status }}">{{ $status }}</option>
                                @endforeach
                            </select>
                            <input type="text" id="search-input" class="form-control form-control-sm" placeholder="Search...">
                        </div>
                    </div>
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table table-bordered w-100" id="transactions-table">
                                <thead class="bg-light">
                                    <tr>
                                        <th>No</th>
                                        <th>Site</th>
                                        <th>Unit</th>
                                        <th>Status</th>
                                        <th>Nomor WO</th>
                                        <th>Nomor PO</th>
                                        <th>Nomor DO</th>
                                        <th>Tanggal</th>
                                        <th>Aksi</th>
                                    </tr>
                                </thead>
                                <tbody id="transactions-table-body">
                                    <!-- Data will be loaded dynamically -->
                                </tbody>
                            </table>
                        </div>
                        <div class="d-flex justify-content-between align-items-center mt-3">
                            <div>
                                <span id="entries-info">Showing 0 to 0 of 0 entries</span>
                            </div>
                            <div id="pagination-container">
                                <!-- Pagination will be loaded dynamically -->
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Invoiced Units Section -->
<div class="row mt-4">
    <div class="col-md-12">
        <div class="card">
            <div class="card-header bg-success text-white d-flex justify-content-between align-items-center">
                <div class="d-flex align-items-center">
                    <h5 class="mb-0 mr-3 font-bold text-uppercase text-white">Unit Transactions dengan Invoice</h5>
                </div>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-bordered w-100" id="invoiced-transactions-table">
                        <thead class="bg-light">
                            <tr>
                                <th>No</th>
                                <th>Site</th>
                                <th>Unit</th>
                                <th>Nomor Invoice</th>
                                <th>Nomor PO</th>
                                <th>Tanggal Units Out</th>
                                <th>Tanggal Invoice</th>
                                <th>Aksi</th>
                            </tr>
                        </thead>
                        <tbody id="invoiced-transactions-table-body">
                            <!-- Data will be loaded dynamically -->
                        </tbody>
                    </table>
                </div>
                <div class="d-flex justify-content-between align-items-center mt-3">
                    <div>
                        <span id="invoiced-entries-info">Showing 0 to 0 of 0 entries</span>
                    </div>
                    <div id="invoiced-pagination-container">
                        <!-- Pagination will be loaded dynamically -->
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Transaction Review Modal -->
<div class="modal fade" id="transaction-details-modal" tabindex="-1" role="dialog" aria-labelledby="transaction-details-modal-label" aria-hidden="true">
    <div class="modal-dialog modal-xl" role="document" style="max-width: 95%;">
        <div class="modal-content">
            <div class="modal-header bg-primary text-white">
                <div class="d-flex justify-content-between align-items-center w-100">
                    <h5 class="modal-title text-white font-bold" id="transaction-details-modal-label">Review Transaksi Unit</h5>
                    <div>
                        <button type="button" id="btn-toggle-status-form" class="btn btn-light btn-sm me-2">
                            <i class="mdi mdi-pencil"></i> Ubah Status
                        </button>
                        <button type="button" id="btn-close-main-modal" class="btn btn-danger btn-sm me-2">
                            <i class="mdi mdi-close"></i> Tutup
                        </button>
                        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                    </div>
                </div>
            </div>
            <div class="modal-body">
                <div class="row" style="font-size: 11px;">
                    <!-- Left Column: Transaction Details -->
                    <div class="col-md-5">
                        <div class="card">
                            <div class="card-header bg-light">
                                <h5 class="mb-0 font-bold">Detail Transaksi</h5>
                            </div>
                            <div class="card-body">
                                <div class="row mb-3">
                                    <div class="col-md-12">
                                        <table class="table table-sm table-bordered">
                                            <tr>
                                                <th class="bg-light">Unit Name</th>
                                                <td id="detail-unit-type"></td>
                                            </tr>
                                            <tr>
                                                <th class="bg-light">Unit Code</th>
                                                <td id="detail-unit-code"></td>
                                            </tr>
                                            <tr>
                                                <th class="bg-light">Site</th>
                                                <td id="detail-site"></td>
                                            </tr>
                                            <tr>
                                                <th class="bg-light">No. Invoice</th>
                                                <td id="detail-invoice-number"></td>
                                            </tr>
                                            <tr>
                                                <th class="bg-light">No. PO</th>
                                                <td id="detail-po-number"></td>
                                            </tr>
                                            <tr>
                                                <th class="bg-light">Tanggal Dibuat</th>
                                                <td id="detail-created-at"></td>
                                            </tr>
                                        </table>
                                    </div>
                                </div>
                                <div class="row" style="font-size: 10px;">
                                    <div class="col-md-12">
                                        <h5 class="font-bold">Daftar Parts</h5>
                                        <div class="table-responsive">
                                            <table class="table table-sm table-bordered">
                                                <thead class="bg-light font-bold">
                                                    <tr>
                                                        <th>Part Code</th>
                                                        <th>Part Name</th>
                                                        <th>Quantity</th>
                                                        <th>Price</th>
                                                        <th>Total</th>
                                                    </tr>
                                                </thead>
                                                <tbody id="detail-parts-table-body">
                                                    <!-- Parts will be loaded dynamically -->
                                                </tbody>
                                                <tfoot>
                                                    <tr>
                                                        <th colspan="4" class="text-end">Total</th>
                                                        <th id="detail-total-price"></th>
                                                    </tr>
                                                </tfoot>
                                            </table>
                                        </div>
                                    </div>
                                </div>

                            </div>
                        </div>
                    </div>

                    <!-- Right Column: Attachment and Status Update -->
                    <div class="col-md-7">
                        <!-- Attachment Section -->
                        <div class="card mb-3">
                            <div class="card-header bg-light">
                                <h5 class="mb-0 font-bold">Lampiran</h5>
                            </div>
                            <div class="card-body">
                                <div id="attachment-container" class="text-center">
                                    <!-- Attachment will be loaded here -->
                                    <div id="no-attachment-message" class="alert alert-info d-none pt-4 mt-4">
                                        Tidak ada lampiran untuk transaksi ini.
                                    </div>
                                    <div id="attachment-preview" class="mb-3">
                                        <!-- Preview will be shown here -->
                                    </div>
                                    <div id="attachment-actions" class="d-none">
                                        <a id="download-attachment" href="#" class="btn btn-sm btn-primary" target="_blank">
                                            <i class="mdi mdi-download"></i> Download Lampiran
                                        </a>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Status Update Form (Hidden by default) -->
                <div id="status-update-container" class="position-absolute shadow-kit" style="display: none; top: 50%; left: 50%; transform: translate(-50%, -50%); z-index: 1050;">
                    <div class="row w-fit-content">
                        <div class="shadow-kit p-4 bg-white rounded" style="min-width: 600px;">
                            <div class="card-header bg-light d-flex justify-content-between align-items-center ">
                                <h5 class="mb-0 font-bold">Update Status</h5>
                                <button type="button" id="close-status-form" class="btn-close" aria-label="Close"></button>
                            </div>
                            <div class="card-body">
                                <form id="update-status-form">
                                    <input type="hidden" id="transaction-id">
                                    <div class="form-group mb-3">
                                        <label for="status" class="form-label">Status</label>
                                        <select id="status" name="status" class="btn btn-sm btn-primary">
                                            <option value="Ready PO">Ready PO</option>
                                            <option value="Pending">Pending</option>
                                            <option value="Perbaikan">Perbaikan</option>
                                            <option value="Selesai">Selesai</option>
                                        </select>
                                    </div>
                                    <div class="form-group mb-3">
                                        <label for="remarks" class="form-label">Catatan</label>
                                        <textarea id="remarks" name="remarks" class="form-control" rows="4" placeholder="Tambahkan catatan untuk transaksi ini..."></textarea>
                                    </div>
                                    <div class="d-grid">
                                        <button type="submit" class="btn btn-primary">
                                            <i class="mdi mdi-content-save"></i> Simpan Perubahan
                                        </button>
                                    </div>
                                </form>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Include Invoice Form Modal -->
@include('sales.invoice-form-modal')
@endsection
