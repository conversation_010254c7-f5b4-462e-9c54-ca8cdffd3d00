/**
 * Superadmin Dashboard Best Parts
 * Handles best parts display and filtering
 */

document.addEventListener('DOMContentLoaded', function() {
    // Initialize best parts data
    loadBestPartsData();

    // Listen for date range change events
    document.addEventListener('dateRangeChanged', function(event) {
        console.log('Date range changed event received in best-parts.js:', event.detail);
        // Reload best parts data when date range changes
        loadBestPartsData(event.detail);
    });

    // Listen for filter change events
    const searchBtn = document.getElementById('searchBtn');
    if (searchBtn) {
        searchBtn.addEventListener('click', function() {
            // Get the date range and filter values
            const startDateInput = document.getElementById('start-date');
            const endDateInput = document.getElementById('end-date');
            const divisionFilter = document.getElementById('division-filter');
            const siteFilter = document.getElementById('site-filter');
            
            const data = {
                startDate: startDateInput ? startDateInput.value : '',
                endDate: endDateInput ? endDateInput.value : '',
                division: divisionFilter ? divisionFilter.value : '',
                site: siteFilter ? siteFilter.value : ''
            };
            
            // Reload best parts data with the new filters
            loadBestPartsData(data);
        });
    }
});

/**
 * Load best parts data via AJAX
 *
 * @param {Object} data - Filter data
 */
function loadBestPartsData(data = {}) {
    // Show loading overlay
    showLoadingOverlay();
    
    // Get the date range from the header form or from the data parameter
    const startDateInput = document.getElementById('start-date');
    const endDateInput = document.getElementById('end-date');
    const divisionFilter = document.getElementById('division-filter');
    const siteFilter = document.getElementById('site-filter');
    
    // Get values from inputs or data parameter
    const startDate = data.startDate || (startDateInput ? startDateInput.value : '');
    const endDate = data.endDate || (endDateInput ? endDateInput.value : '');
    const division = data.division || (divisionFilter ? divisionFilter.value : '');
    const site = data.site || (siteFilter ? siteFilter.value : '');
    
    // Fallback to month picker if date range is not provided
    const monthPicker = document.getElementById('month-picker');
    const selectedMonth = monthPicker ? monthPicker.value : '';
    
    // Build query parameters
    let queryParams = new URLSearchParams();
    
    // Add date range parameters if available
    if (startDate && endDate) {
        queryParams.append('start_date', startDate);
        queryParams.append('end_date', endDate);
    } else if (selectedMonth) {
        queryParams.append('month', selectedMonth);
    }
    
    // Add filters if available
    if (division) {
        queryParams.append('division', division);
    }
    
    if (site) {
        queryParams.append('site', site);
    }
    
    console.log('Loading best parts data with params:', queryParams.toString());
    
    // Fetch the updated best parts data with all parameters
    fetch(`/superadmin/best-parts-data?${queryParams.toString()}`)
        .then(response => {
            if (!response.ok) {
                throw new Error('Network response was not ok');
            }
            return response.json();
        })
        .then(data => {
            console.log('Best parts data loaded:', data);
            // Update the best parts content
            updateBestPartsContent(data);

            // Hide loading overlay
            hideLoadingOverlay();
        })
        .catch(error => {
            console.error('Error loading best parts data:', error);
            alert('Gagal memuat data part terbaik. Silakan coba lagi.');
            hideLoadingOverlay();
        });
}

/**
 * Update best parts content with the loaded data
 *
 * @param {Object} data - Best parts data
 */
function updateBestPartsContent(data) {
    // Get the best parts container
    const bestPartsContainer = document.getElementById('best-parts-container');
    if (!bestPartsContainer) return;
    
    // Clear existing content
    bestPartsContainer.innerHTML = '';
    
    // Check if data is empty
    if (!data || data.length === 0) {
        bestPartsContainer.innerHTML = `
            <div class="text-center py-4">
                <div class="empty-state-icon mb-3">
                    <i class="mdi mdi-package-variant text-muted" style="font-size: 48px;"></i>
                </div>
                <h5 class="empty-state-title">Belum Ada Data Part</h5>
                <p class="empty-state-text">Belum ada data part untuk periode yang dipilih.</p>
            </div>
        `;
        return;
    }
    
    // Create HTML for each part
    let html = '';
    data.forEach((part, index) => {
        const partTypeClass = getPartTypeClass(part.part_type);
        const partTypeIcon = getPartTypeIcon(part.part_type);
        
        html += `
            <div class="part-type-item" data-part-code="${part.part_code}" data-part-type="${part.part_type}">
                <div class="part-type-name">
                    <div class="part-type-icon ${partTypeClass}">
                        <i class="${partTypeIcon}"></i>
                    </div>
                    <div>
                        <h6 class="mb-0">${part.part_name}</h6>
                        <small class="text-muted">${part.part_code}</small>
                    </div>
                </div>
                <div class="text-end">
                    <h6 class="mb-0">Rp ${formatNumber(part.total_amount)}</h6>
                    <small class="text-muted">${part.quantity} unit</small>
                </div>
            </div>
        `;
    });
    
    // Update the container
    bestPartsContainer.innerHTML = html;
    
    // Add click event listeners to part items
    const partItems = bestPartsContainer.querySelectorAll('.part-type-item');
    partItems.forEach(item => {
        item.addEventListener('click', function() {
            const partCode = this.dataset.partCode;
            const partType = this.dataset.partType;
            showPartDetails(partCode, partType);
        });
    });
}

/**
 * Get CSS class for part type
 *
 * @param {string} partType - Part type
 * @returns {string} CSS class
 */
function getPartTypeClass(partType) {
    partType = (partType || '').toUpperCase();
    if (partType === 'AC') return 'ac-bg';
    if (partType === 'TYRE') return 'tyre-bg';
    if (partType === 'FABRIKASI') return 'fabrikasi-bg';
    return 'bg-secondary';
}

/**
 * Get icon for part type
 *
 * @param {string} partType - Part type
 * @returns {string} Icon class
 */
function getPartTypeIcon(partType) {
    partType = (partType || '').toUpperCase();
    if (partType === 'AC') return 'mdi mdi-air-conditioner';
    if (partType === 'TYRE') return 'mdi mdi-tire';
    if (partType === 'FABRIKASI') return 'mdi mdi-factory';
    return 'mdi mdi-package-variant';
}

/**
 * Show part details in modal
 *
 * @param {string} partCode - Part code
 * @param {string} partType - Part type
 */
function showPartDetails(partCode, partType) {
    // Implementation for showing part details
    console.log('Show details for part:', partCode, partType);
}

/**
 * Show loading overlay
 */
function showLoadingOverlay() {
    const overlay = document.getElementById('loading-overlay');
    if (overlay) {
        overlay.style.display = 'flex';
    }
}

/**
 * Hide loading overlay
 */
function hideLoadingOverlay() {
    const overlay = document.getElementById('loading-overlay');
    if (overlay) {
        overlay.style.display = 'none';
    }
}

/**
 * Format number with thousand separators
 * 
 * @param {number} number 
 * @returns {string}
 */
function formatNumber(number) {
    return new Intl.NumberFormat('id-ID').format(number);
}
