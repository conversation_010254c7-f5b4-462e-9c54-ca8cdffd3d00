<?php

namespace App\Helpers;

class NumberToWords
{
    private static $digits = [
        '', 'satu', 'dua', 'tiga', 'empat', 'lima', 'enam', 'tujuh', 'delapan', 'sembilan', 'sepuluh', 'sebelas'
    ];

    public static function convert($number)
    {
        if ($number < 0) {
            return 'minus ' . self::convert(abs($number));
        }
        
        if ($number < 12) {
            return self::$digits[$number];
        }
        
        if ($number < 20) {
            return self::$digits[$number % 10] . ' belas';
        }
        
        if ($number < 100) {
            return self::$digits[floor($number / 10)] . ' puluh ' . self::$digits[$number % 10];
        }
        
        if ($number < 200) {
            return 'seratus ' . self::convert($number - 100);
        }
        
        if ($number < 1000) {
            return self::$digits[floor($number / 100)] . ' ratus ' . self::convert($number % 100);
        }
        
        if ($number < 2000) {
            return 'seribu ' . self::convert($number - 1000);
        }
        
        if ($number < 1000000) {
            return self::convert(floor($number / 1000)) . ' ribu ' . self::convert($number % 1000);
        }
        
        if ($number < 1000000000) {
            return self::convert(floor($number / 1000000)) . ' juta ' . self::convert($number % 1000000);
        }
        
        if ($number < 1000000000000) {
            return self::convert(floor($number / 1000000000)) . ' milyar ' . self::convert($number % 1000000000);
        }
        
        return self::convert(floor($number / 1000000000000)) . ' trilyun ' . self::convert($number % 1000000000000);
    }
}
