<?php

namespace App\Http\Controllers;

use App\Models\StockTransaction;
use Illuminate\Http\Request;

class TransactionhistoryController extends Controller
{
    public function warehouseIndex()
    {
        return view('warehouse.transaction_history');
    }

    public function siteIndex()
    {
        return view('sites.transaction_history');
    }

    public function getWarehouseTransactions(Request $request)
    {
        $query = StockTransaction::with(['part'])
            ->orderBy('created_at', 'desc');
        if ($request->status) {
            $query->where('status', $request->status);
        }
        if ($request->start_date && $request->end_date) {
            $query->whereBetween('transaction_date', [$request->start_date, $request->end_date]);
        }

        if ($request->search) {
            $query->whereHas('part', function($q) use ($request) {
                $q->where('part_name', 'like', '%' . $request->search . '%')
                  ->orWhere('part_code', 'like', '%' . $request->search . '%');
            });
        }

        $transactions = $query->paginate(10);
        return response()->json($transactions);
    }

    public function getSiteTransactions(Request $request)
    {
        $siteId = session('site_id');
        $useLastDate = $request->input('use_last_date', false);

        // Base query for site transactions
        $baseQuery = StockTransaction::with(['part'])
            ->where('destination_siteid', $siteId)
            ->orderBy('created_at', 'desc');

        // Clone the query for potential last date lookup
        $query = clone $baseQuery;

        // Apply filters
        if ($request->status) {
            $query->where('status', $request->status);
        }

        if ($request->start_date) {
            $query->whereDate('transaction_date', '>=', $request->start_date);
        }

        if ($request->end_date) {
            $query->whereDate('transaction_date', '<=', $request->end_date);
        }

        if ($request->search) {
            $query->whereHas('part', function($q) use ($request) {
                $q->where('part_code', 'like', '%' . $request->search . '%')
                  ->orWhere('part_name', 'like', '%' . $request->search . '%');
            });
        }

        // Check if we have data for the selected filters
        $hasData = $query->exists();
        $usedLastDate = false;
        $lastDate = null;

        // If no data found and we should use the last date with data
        if (!$hasData && $useLastDate) {
            // Find the last date that has data
            $lastDateQuery = clone $baseQuery;
            if ($request->status) {
                $lastDateQuery->where('status', $request->status);
            }

            if ($request->search) {
                $lastDateQuery->whereHas('part', function($q) use ($request) {
                    $q->where('part_code', 'like', '%' . $request->search . '%')
                      ->orWhere('part_name', 'like', '%' . $request->search . '%');
                });
            }

            $lastTransaction = $lastDateQuery->first();

            if ($lastTransaction) {
                $lastDate = date('Y-m-d', strtotime($lastTransaction->transaction_date));

                // Reset the query to use the last date
                $query = clone $baseQuery;
                if ($request->status) {
                    $query->where('status', $request->status);
                }

                if ($request->search) {
                    $query->whereHas('part', function($q) use ($request) {
                        $q->where('part_code', 'like', '%' . $request->search . '%')
                          ->orWhere('part_name', 'like', '%' . $request->search . '%');
                    });
                }

                $query->whereDate('transaction_date', $lastDate);
                $usedLastDate = true;
            }
        }

        // Paginate the results
        $transactions = $query->paginate(10);

        // Add metadata to the response
        $transactions->usedLastDate = $usedLastDate;
        $transactions->lastDate = $lastDate;

        return $transactions;
    }
}