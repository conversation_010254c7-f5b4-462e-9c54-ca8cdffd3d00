<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\Equipment;
use App\Models\Site;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Facades\DB;
use Illuminate\Database\Eloquent\ModelNotFoundException;
use Illuminate\Support\Facades\Log;

class PerlengkapanController extends Controller
{
    public function index(Request $request)
    {
        $equipment = Equipment::with('site')
            ->when($request->site_id, fn($q) => $q->where('site_id', $request->site_id))
            ->when($request->search, fn($q) => $q->where('equipment_name', 'LIKE', "%{$request->search}%"))
            ->paginate(10);

        $datasites = Site::all();

        if ($request->ajax()) {
            $view = view('warehouse.perlengkapan')
                ->with(compact('equipment', 'datasites'))
                ->renderSections();

            return response()->json([
                'tbody' => $view['table'],
                'pagination' => (string) $equipment->links()
            ]);
        }

        return view('warehouse.perlengkapan', compact('equipment', 'datasites'));
    }

    public function store(Request $request)
    {
        $rules = [
            'equipment_name' => 'required|string|max:255',
            'site_id' => 'required|exists:sites,site_id',
            'quantity' => 'required|integer|min:1',
            'date_in' => 'required|date',
        ];

        $validator = Validator::make($request->all(), $rules);

        if ($validator->fails()) {
            return response()->json([
                'alert_type' => 'error',
                'alert_title' => 'Validation Error!',
                'alert_message' => $validator->errors()->first()
            ], 422);
        }

        try {
            $equipment = new Equipment();
            $equipment->equipment_name = strip_tags($request->input('equipment_name')); // Sanitize
            $equipment->site_id = $request->input('site_id');
            $equipment->quantity = $request->input('quantity');
            $equipment->date_in = $request->input('date_in');
            $equipment->save();

            return response()->json([
                'alert_type' => 'success',
                'alert_title' => 'Success!',
                'alert_message' => 'Equipment added successfully!',
            ], 201);

        } catch (\Exception $e) {
            return response()->json([
                'alert_type' => 'error',
                'alert_title' => 'Server Error!',
                'alert_message' => 'Failed to add equipment. Please try again.'
            ], 500);
        }
    }

    public function update(Request $request, $equipment_id)
    {
        Log::info('Data diterima di update:', $request->all()); 
        $rules = [
            'quantity' => 'required|integer|min:1',
            'date_in' => 'required|date',
        ];

        $validator = Validator::make($request->all(), $rules);

        if ($validator->fails()) {
            return response()->json([
                'alert_type' => 'error',
                'alert_title' => 'Validation Error!',
                'alert_message' => $validator->errors()->first()
            ], 422);
        }

        try {
            $equipment = Equipment::findOrFail($equipment_id);

            DB::beginTransaction();
            try {
                $equipment->equipment_name = strip_tags($request->input('equipment_name')); // Sanitize
                $equipment->site_id = $request->input('site_id');
                $equipment->quantity = $request->input('quantity');
                $equipment->date_in = $request->input('date_in');
                $equipment->save();
                DB::commit();

                return response()->json([
                    'alert_type' => 'success',
                    'alert_title' => 'Success!',
                    'alert_message' => 'Equipment updated successfully!',
                ], 200);

            } catch (\Exception $e) {
                DB::rollback();
                throw $e;
            }

        } catch (ModelNotFoundException $e) {
            return response()->json([
                'alert_type' => 'error',
                'alert_title' => 'Not Found!',
                'alert_message' => 'Equipment not found.',
            ], 404);

        } catch (\Exception $e) {
            \Log::error('Error updating equipment: ' . $e->getMessage());

            return response()->json([
                'alert_type' => 'error',
                'alert_title' => 'Server Error!',
                'alert_message' => 'Failed to update equipment. Please try again.'
            ], 500);
        }
    }

    public function destroy($equipment_id)
    {
        try {
            $equipment = Equipment::findOrFail($equipment_id);
            $equipment->delete();

            return response()->json([
                'alert_type' => 'success',
                'alert_title' => 'Success!',
                'alert_message' => 'Equipment deleted successfully!',
            ], 200);

        } catch (ModelNotFoundException $e) {
            return response()->json([
                'alert_type' => 'error',
                'alert_title' => 'Not Found!',
                'alert_message' => 'Equipment not found.',
            ], 404);

        } catch (\Exception $e) {
            Log::error('Error deleting equipment: ' . $e->getMessage());

            return response()->json([
                'alert_type' => 'error',
                'alert_title' => 'Server Error!',
                'alert_message' => 'Failed to delete equipment. Please try again.'
            ], 500);
        }
    }

    public function autocomplete(Request $request)
    {
        $term = $request->input('term');
        $suggestions = Equipment::where('equipment_name', 'LIKE', "%{$term}%")
            ->distinct()
            ->orderBy('equipment_name')
            ->pluck('equipment_name');

        return response()->json($suggestions);
    }

    public function edit($equipment_id)
    {
       try {
            $equipment = Equipment::findOrFail($equipment_id);

            return response()->json($equipment, 200);

        } catch (ModelNotFoundException $e) {
             return response()->json([
                'alert_type' => 'error',
                'alert_title' => 'Not Found!',
                'alert_message' => 'Equipment not found.',
            ], 404);

        }  catch (\Exception $e) {
            Log::error('Error editing equipment: ' . $e->getMessage());

            return response()->json([
                'alert_type' => 'error',
                'alert_title' => 'Server Error!',
                'alert_message' => 'Failed to retrieve equipment for editing.'
            ], 500);
        }
    }
}