/*
Template Name: Uplon - Responsive Bootstrap 4 Admin Dashboard
Author: CoderThemes
Version: 2.0.0
Website: https://coderthemes.com/
Contact: <EMAIL>
File: Main Css File
*/
@import url("https://fonts.googleapis.com/css?family=Roboto:300,400,500,700&display=swap");
html {
  position: relative;
  min-height: 100%; }

body {
  padding-bottom: 60px;
  overflow-x: hidden; }

.metismenu {
  padding: 0; }
  .metismenu li {
    list-style: none; }
  .metismenu ul {
    padding: 0; }
    .metismenu ul li {
      width: 100%; }
  .metismenu .mm-collapse:not(.mm-show) {
    display: none; }
  .metismenu .mm-collapsing {
    position: relative;
    height: 0;
    overflow: hidden;
    -webkit-transition-timing-function: ease;
            transition-timing-function: ease;
    -webkit-transition-duration: .35s;
            transition-duration: .35s;
    -webkit-transition-property: height, visibility;
    transition-property: height, visibility; }

.nav-second-level li a,
.nav-thrid-level li a {
  padding: 8px 20px;
  color: #6e768e;
  display: block;
  position: relative;
  -webkit-transition: all 0.4s;
  transition: all 0.4s; }
  .nav-second-level li a:focus, .nav-second-level li a:hover,
  .nav-thrid-level li a:focus,
  .nav-thrid-level li a:hover {
    color: #141a21; }

.nav-second-level li.mm-active > a,
.nav-third-level li.mm-active > a {
  color: #64b0f2; }

#wrapper {
  height: 100%;
  overflow: hidden;
  width: 100%; }

.content-page {
  margin-left: 240px;
  overflow: hidden;
  padding: 0 15px 5px 15px;
  min-height: 80vh;
  margin-top: 70px; }

.left-side-menu {
  width: 240px;
  background: #ffffff;
  bottom: 0;
  padding: 20px 0;
  position: fixed;
  -webkit-transition: all .2s ease-out;
  transition: all .2s ease-out;
  top: 70px; }

#sidebar-menu > ul > li > a {
  color: #6e768e;
  display: block;
  padding: 13px 20px;
  position: relative;
  -webkit-transition: all 0.4s;
  transition: all 0.4s;
  font-size: 15.5px; }
  #sidebar-menu > ul > li > a:hover, #sidebar-menu > ul > li > a:focus, #sidebar-menu > ul > li > a:active {
    color: #141a21;
    text-decoration: none; }
  #sidebar-menu > ul > li > a i {
    display: inline-block;
    line-height: 1.0625rem;
    margin: 0 10px 0 3px;
    text-align: center;
    width: 20px;
    font-size: 18px; }
  #sidebar-menu > ul > li > a .drop-arrow {
    float: right; }
    #sidebar-menu > ul > li > a .drop-arrow i {
      margin-right: 0; }

#sidebar-menu > ul > li > a.active {
  background-color: #f5f8fb;
  color: #64b0f2; }

#sidebar-menu > ul > li > ul {
  padding-left: 38px; }
  #sidebar-menu > ul > li > ul ul {
    padding-left: 20px; }

#sidebar-menu .menu-arrow {
  -webkit-transition: -webkit-transform .15s;
  transition: -webkit-transform .15s;
  transition: transform .15s;
  transition: transform .15s, -webkit-transform .15s;
  position: absolute;
  right: 20px;
  display: inline-block;
  font-family: 'Material Design Icons';
  text-rendering: auto;
  line-height: 1.5rem;
  font-size: 1.1rem;
  -webkit-transform: translate(0, 0);
          transform: translate(0, 0); }
  #sidebar-menu .menu-arrow:before {
    content: "\F142"; }

#sidebar-menu .badge {
  margin-top: 4px; }

#sidebar-menu li.mm-active > a > span.menu-arrow {
  -webkit-transform: rotate(90deg);
          transform: rotate(90deg); }

#sidebar-menu .menu-title {
  padding: 10px 20px;
  letter-spacing: .05em;
  pointer-events: none;
  cursor: default;
  font-size: 0.6875rem;
  text-transform: uppercase;
  color: #6e768e;
  font-weight: 600; }

.enlarged .logo-box {
  width: 70px !important; }

.enlarged .logo span.logo-lg {
  display: none; }

.enlarged .logo span.logo-sm {
  display: block; }

.enlarged .left-side-menu {
  position: absolute;
  padding-top: 0;
  width: 70px !important;
  z-index: 5; }
  .enlarged .left-side-menu .slimScrollDiv,
  .enlarged .left-side-menu .slimscroll-menu {
    overflow: inherit !important;
    height: auto !important; }
  .enlarged .left-side-menu .slimScrollBar {
    visibility: hidden; }
  .enlarged .left-side-menu #sidebar-menu .menu-title,
  .enlarged .left-side-menu #sidebar-menu .menu-arrow,
  .enlarged .left-side-menu #sidebar-menu .label,
  .enlarged .left-side-menu #sidebar-menu .badge {
    display: none !important; }
  .enlarged .left-side-menu #sidebar-menu > ul > li {
    position: relative;
    white-space: nowrap; }
    .enlarged .left-side-menu #sidebar-menu > ul > li > a {
      padding: 15px 20px;
      min-height: 54px;
      -webkit-transition: none;
      transition: none; }
      .enlarged .left-side-menu #sidebar-menu > ul > li > a:hover, .enlarged .left-side-menu #sidebar-menu > ul > li > a:active, .enlarged .left-side-menu #sidebar-menu > ul > li > a:focus {
        color: #141a21; }
      .enlarged .left-side-menu #sidebar-menu > ul > li > a i {
        font-size: 1.125rem;
        margin-right: 20px; }
      .enlarged .left-side-menu #sidebar-menu > ul > li > a span {
        display: none;
        padding-left: 25px; }
    .enlarged .left-side-menu #sidebar-menu > ul > li:hover > a {
      position: relative;
      width: calc(190px + 70px);
      background-color: #f5f8fb;
      -webkit-transition: none;
      transition: none; }
      .enlarged .left-side-menu #sidebar-menu > ul > li:hover > a span {
        display: inline; }
    .enlarged .left-side-menu #sidebar-menu > ul > li:hover a.open :after, .enlarged .left-side-menu #sidebar-menu > ul > li:hover a.mm-active :after {
      display: none; }
    .enlarged .left-side-menu #sidebar-menu > ul > li:hover > ul {
      display: block;
      left: 70px;
      position: absolute;
      width: 190px;
      height: auto !important;
        padding: 8px 20px;
        position: relative;
        width: 190px;
        z-index: 6; }
        .enlarged .left-side-menu #sidebar-menu > ul > li:hover > ul a:hover {
          color: #141a21; }
  .enlarged .left-side-menu #sidebar-menu > ul ul {
    padding: 5px 0;
    z-index: 9999;
    display: none;
    background-color: #ffffff; }
    .enlarged .left-side-menu #sidebar-menu > ul ul li:hover > ul {
      display: block;
      left: 190px;
      margin-top: -36px;
      height: auto !important;
      position: absolute;
      width: 190px; }
    .enlarged .left-side-menu #sidebar-menu > ul ul li > a span.pull-right {
      position: absolute;
      right: 20px;
      top: 12px;
      -webkit-transform: rotate(270deg);
              transform: rotate(270deg); }
    .enlarged .left-side-menu #sidebar-menu > ul ul li.active a {
      color: #64b0f2; }

.enlarged .content-page {
  margin-left: 70px !important; }

.enlarged .footer {
  left: 70px !important; }

.enlarged .user-box {
  display: none; }

body.enlarged {
  min-height: 1200px; }

@media (max-width: 767.98px) {
  body {
    overflow-x: hidden;
    padding-bottom: 80px; }
  .left-side-menu {
    display: none;
    z-index: 10 !important; }
  .sidebar-enable .left-side-menu {
    display: block; }
  .content-page, .enlarged .content-page {
    margin-left: 0 !important;
    padding: 0 10px; }
  .logo-box {
    display: none; } }

.left-side-menu-dark .logo-box {
  background-color: #363c4a; }
  .left-side-menu-dark .logo-box .logo-light {
    display: block; }
  .left-side-menu-dark .logo-box .logo-dark {
    display: none; }

.left-side-menu-dark .left-side-menu {
  background-color: #363c4a; }
  .left-side-menu-dark .left-side-menu #sidebar-menu > ul > li > a {
    color: #929fbf; }
    .left-side-menu-dark .left-side-menu #sidebar-menu > ul > li > a:hover, .left-side-menu-dark .left-side-menu #sidebar-menu > ul > li > a:focus, .left-side-menu-dark .left-side-menu #sidebar-menu > ul > li > a:active {
      color: #c8cddc; }
  .left-side-menu-dark .left-side-menu #sidebar-menu > ul > li > a.active {
    color: #ffffff;
    background-color: #3c4352;
    border-right-color: #ffffff; }
  .left-side-menu-dark .left-side-menu .nav-second-level li a,
  .left-side-menu-dark .left-side-menu .nav-thrid-level li a {
    color: #929fbf; }
    .left-side-menu-dark .left-side-menu .nav-second-level li a:focus, .left-side-menu-dark .left-side-menu .nav-second-level li a:hover,
    .left-side-menu-dark .left-side-menu .nav-thrid-level li a:focus,
    .left-side-menu-dark .left-side-menu .nav-thrid-level li a:hover {
      background-color: transparent;
      color: #c8cddc; }
  .left-side-menu-dark .left-side-menu .nav-second-level li > a.active,
  .left-side-menu-dark .left-side-menu .nav-thrid-level li > a.active {
    color: #ffffff; }

.enlarged.left-side-menu-dark #wrapper .navbar-custom {
  -webkit-box-shadow: 70px 1px 0 0 #e9ecef;
          box-shadow: 70px 1px 0 0 #e9ecef; }

.enlarged.left-side-menu-dark #wrapper .left-side-menu #sidebar-menu > ul > li:hover > a {
  background-color: #3c4352; }

.enlarged.left-side-menu-dark #wrapper .left-side-menu #sidebar-menu > ul ul {
  background-color: #363c4a; }

.enlarged.left-side-menu-dark #wrapper .left-side-menu #sidebar-menu .nav-second-level li > a.active, .enlarged.left-side-menu-dark #wrapper .left-side-menu #sidebar-menu .nav-second-level li a:focus, .enlarged.left-side-menu-dark #wrapper .left-side-menu #sidebar-menu .nav-second-level li a:hover,
.enlarged.left-side-menu-dark #wrapper .left-side-menu #sidebar-menu .nav-third-level li > a.active,
.enlarged.left-side-menu-dark #wrapper .left-side-menu #sidebar-menu .nav-third-level li a:focus,
.enlarged.left-side-menu-dark #wrapper .left-side-menu #sidebar-menu .nav-third-level li a:hover {
  color: #64b0f2; }

/* =============
  Small Menu
============= */
.left-side-menu-sm .logo-box {
  width: 160px; }

.left-side-menu-sm .left-side-menu {
  width: 160px;
  text-align: center; }
  .left-side-menu-sm .left-side-menu #sidebar-menu > ul > li > a > i {
    display: block;
    font-size: 18px;
    line-height: 24px;
    width: 100%;
    margin: 0; }
  .left-side-menu-sm .left-side-menu #sidebar-menu > ul ul {
    padding-left: 0; }
    .left-side-menu-sm .left-side-menu #sidebar-menu > ul ul a {
      padding: 10px 20px; }
  .left-side-menu-sm .left-side-menu .menu-arrow,
  .left-side-menu-sm .left-side-menu .badge {
    display: none !important; }
  .left-side-menu-sm .left-side-menu + .content-page {
    margin-left: 160px; }
  .left-side-menu-sm .left-side-menu + .content-page .footer {
    left: 160px; }

.enlarged.left-side-menu-sm #wrapper .left-side-menu {
  text-align: left; }
  .enlarged.left-side-menu-sm #wrapper .left-side-menu ul li a i {
    display: inline-block;
    font-size: 18px;
    line-height: 17px;
    margin-left: 3px;
    margin-right: 15px;
    vertical-align: middle;
    width: 20px; }

.logo {
  display: block;
  line-height: 70px; }
  .logo span.logo-lg {
    display: block; }
  .logo span.logo-sm {
    display: none; }
  .logo .logo-lg-text-dark {
    color: #343a40;
    font-weight: 700;
    font-size: 22px;
    text-transform: uppercase; }
  .logo .logo-lg-text-light {
    color: #fff;
    font-weight: 700;
    font-size: 22px;
    text-transform: uppercase; }

.logo-box {
  background-color: #ffffff;
  height: 70px;
  width: 240px;
  float: left; }

.logo-light {
  display: none; }

.logo-dark {
  display: block; }

.navbar-custom {
  background-color: #2b3d51;
  padding: 0 10px 0 0;
  position: fixed;
  left: 0;
  right: 0;
  height: 70px;
  z-index: 100;
  /* Search */ }
  .navbar-custom .topnav-menu > li {
    float: left; }
  .navbar-custom .topnav-menu .nav-link {
    padding: 0 15px;
    color: #adb5bd;
    min-width: 32px;
    display: block;
    line-height: 70px;
    text-align: center;
    max-height: 70px; }
  .navbar-custom .dropdown.show .nav-link {
    background-color: rgba(255, 255, 255, 0.05); }
  .navbar-custom .app-search {
    overflow: hidden;
    height: 70px;
    display: table;
    max-width: 180px;
    margin-right: 20px; }
    .navbar-custom .app-search .app-search-box {
      display: table-cell;
      vertical-align: middle; }
      .navbar-custom .app-search .app-search-box input::-webkit-input-placeholder {
        font-size: 0.8125rem;
        color: #adb5bd; }
    .navbar-custom .app-search .form-control {
      border: none;
      height: 38px;
      padding-left: 20px;
      padding-right: 0;
      color: #6c757d;
      background-color: #31465d;
      -webkit-box-shadow: none;
              box-shadow: none;
      border-radius: 30px 0 0 30px; }
    .navbar-custom .app-search .input-group-append {
      margin-left: 0;
      z-index: 4; }
    .navbar-custom .app-search .btn {
      background-color: #31465d;
      color: #adb5bd;
      border-color: transparent;
      border-radius: 0 30px 30px 0;
      -webkit-box-shadow: none !important;
              box-shadow: none !important; }
  .navbar-custom .button-menu-mobile {
    border: none;
    color: #adb5bd;
    display: inline-block;
    height: 70px;
    line-height: 70px;
    width: 60px;
    background-color: transparent;
    font-size: 24px;
    cursor: pointer; }
  .navbar-custom .button-menu-mobile.disable-btn {
    display: none; }

/* Notification */
.noti-scroll {
  max-height: 230px; }

.notification-list {
  margin-left: 0; }
  .notification-list .noti-title {
    background-color: #64b0f2;
    padding: 15px 20px;
    border-radius: 0.25rem 0.25rem 0 0;
    margin-top: -7px; }
  .notification-list .noti-icon {
    font-size: 22px; }
  .notification-list .noti-icon-badge {
    display: inline-block;
    height: 10px;
    width: 10px;
    background-color: #ff5d48;
    border-radius: 50%;
    border: 2px solid #2b3d51;
    position: absolute;
    top: 22px;
    right: 14px; }
  .notification-list .notify-item {
    padding: 12px 20px; }
    .notification-list .notify-item.notify-all {
      background-color: #f5f6f8;
      margin-bottom: -7px; }
    .notification-list .notify-item .notify-icon {
      float: left;
      height: 36px;
      width: 36px;
      font-size: 18px;
      line-height: 36px;
      text-align: center;
      margin-top: 2px;
      margin-right: 10px;
      border-radius: 50%;
      color: #fff; }
    .notification-list .notify-item .notify-details {
      margin-bottom: 5px;
      overflow: hidden;
      margin-left: 45px;
      text-overflow: ellipsis;
      white-space: nowrap;
      color: #343a40;
      font-weight: 500; }
      .notification-list .notify-item .notify-details b {
        font-weight: 500; }
      .notification-list .notify-item .notify-details small {
        display: block; }
      .notification-list .notify-item .notify-details span {
        display: block;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
        font-size: 13px; }
    .notification-list .notify-item .user-msg {
      margin-left: 45px;
      white-space: normal;
      line-height: 16px; }
  .notification-list .inbox-widget .inbox-item {
    padding: 12px 20px; }
    .notification-list .inbox-widget .inbox-item:hover {
      background-color: #f8f9fa; }
  .notification-list .profile-dropdown .notify-item {
    padding: 7px 20px; }

.profile-dropdown {
  width: 170px; }
  .profile-dropdown i {
    margin-right: 5px;
    font-size: 16px; }

.nav-user {
  padding: 0 12px !important; }
  .nav-user img {
    height: 32px;
    width: 32px; }

@media (min-width: 1025px) {
  .navbar-custom .button-menu-mobile {
    margin-left: 8px; } }

.topbar-light .navbar-custom {
  background-color: #ffffff;
  -webkit-box-shadow: 240px 1px 0 0 #e9ecef;
          box-shadow: 240px 1px 0 0 #e9ecef;
  /* app search */ }
  .topbar-light .navbar-custom .topnav-menu .nav-link {
    color: #6c757d; }
  .topbar-light .navbar-custom .notification-list .noti-icon-badge {
    border-color: #ffffff; }
  .topbar-light .navbar-custom .button-menu-mobile {
    color: #6c757d; }
  .topbar-light .navbar-custom .app-search input::-webkit-input-placeholder {
    color: #6c757d !important; }
  .topbar-light .navbar-custom .app-search .form-control {
    color: #343a40;
    background-color: whitesmoke;
    border-color: whitesmoke; }
  .topbar-light .navbar-custom .app-search .btn {
    background-color: whitesmoke;
    color: #adb5bd; }

.page-title-box {
  position: relative;
  background-color: #fff;
  padding: 0 30px;
  margin: 0 -30px 30px -30px;
  -webkit-box-shadow: 0px 0px 35px 0px rgba(73, 80, 87, 0.15);
          box-shadow: 0px 0px 35px 0px rgba(73, 80, 87, 0.15); }
  .page-title-box .page-title {
    font-size: 18px;
    margin: 0;
    line-height: 60px; }
  .page-title-box .page-title-right {
    float: right;
    margin-top: 7px; }
  .page-title-box .breadcrumb {
    background-color: transparent;
    padding: .75rem 0; }

@media (max-width: 767.98px) {
  .page-title-box .page-title {
    display: block;
    white-space: nowrap;
    text-overflow: ellipsis;
    overflow: hidden;
    line-height: 70px; }
  .page-title-box .page-title-right {
    display: none; } }

.footer {
  bottom: 0;
  padding: 21px 15px 20px;
  position: absolute;
  right: 0;
  color: #6c757d;
  left: 240px;
  background-color: #ecf1f3; }

@media (max-width: 767.98px) {
  .footer {
    left: 0 !important;
    text-align: center; } }

.right-bar {
  background-color: #fff;
  -webkit-box-shadow: 0 0 24px 0 rgba(0, 0, 0, 0.06), 0 1px 0 0 rgba(0, 0, 0, 0.02);
          box-shadow: 0 0 24px 0 rgba(0, 0, 0, 0.06), 0 1px 0 0 rgba(0, 0, 0, 0.02);
  display: block;
  position: fixed;
  -webkit-transition: all 200ms ease-out;
  transition: all 200ms ease-out;
  width: 260px;
  z-index: 9999;
  float: right !important;
  right: -270px;
  top: 0;
  bottom: 0; }
  .right-bar .rightbar-title {
    background-color: #64b0f2;
    padding: 24.5px;
    color: #fff; }
  .right-bar .right-bar-toggle {
    background-color: #444c54;
    height: 24px;
    width: 24px;
    line-height: 24px;
    color: #e9ecef;
    text-align: center;
    border-radius: 50%;
    margin-top: -4px; }
    .right-bar .right-bar-toggle:hover {
      background-color: #4b545c; }
  .right-bar .user-box {
    padding: 25px;
    text-align: center; }
    .right-bar .user-box .user-img {
      position: relative;
      height: 64px;
      width: 64px;
      margin: 0 auto 15px auto; }
      .right-bar .user-box .user-img .user-edit {
        position: absolute;
        right: -5px;
        bottom: 0px;
        height: 24px;
        width: 24px;
        background-color: #fff;
        line-height: 24px;
        border-radius: 50%;
        -webkit-box-shadow: 0 1rem 3rem rgba(0, 0, 0, 0.175);
                box-shadow: 0 1rem 3rem rgba(0, 0, 0, 0.175); }
    .right-bar .user-box h5 {
      margin-bottom: 2px; }
      .right-bar .user-box h5 a {
        color: #343a40; }

.rightbar-overlay {
  background-color: rgba(52, 58, 64, 0.4);
  position: absolute;
  left: 0;
  right: 0;
  top: 0;
  bottom: 0;
  display: none;
  z-index: 9998;
  -webkit-transition: all .2s ease-out;
  transition: all .2s ease-out; }

.right-bar-enabled .right-bar {
  right: 0; }

.right-bar-enabled .rightbar-overlay {
  display: block; }

@media (max-width: 767.98px) {
  .right-bar {
    overflow: auto; }
    .right-bar .slimscroll-menu {
      height: auto !important; } }

.activity-widget .activity-list {
  position: relative;
  border-left: 2px dashed #ced4da;
  padding-left: 24px;
  padding-bottom: 20px; }
  .activity-widget .activity-list::after {
    content: "";
    position: absolute;
    left: -7px;
    top: 6px;
    width: 12px;
    height: 12px;
    background-color: #fff;
    border: 2px solid #64b0f2;
    border-radius: 50%; }

body.boxed-layout {
  background-color: #f8f3ef; }
  body.boxed-layout #wrapper {
    background-color: #f7f9fa;
    max-width: 1300px;
    margin: 0 auto;
    -webkit-box-shadow: 0px 0px 35px 0px rgba(73, 80, 87, 0.15);
            box-shadow: 0px 0px 35px 0px rgba(73, 80, 87, 0.15); }
  body.boxed-layout .navbar-custom {
    max-width: 1300px;
    margin: 0 auto; }
  body.boxed-layout .footer {
    margin: 0 auto;
    max-width: calc(1300px - 240px); }
  body.boxed-layout.enlarged .footer {
    max-width: calc(1300px - 70px); }

@media (min-width: 992px) {
  .unsticky-layout .left-side-menu, .unsticky-layout .navbar-custom {
    position: absolute; } }

.width-xs {
  min-width: 80px; }

.width-sm {
  min-width: 95px; }

.width-md {
  min-width: 110px; }

.width-lg {
  min-width: 140px; }

.width-xl {
  min-width: 160px; }

.font-family-secondary {
  font-family: "Roboto", sans-serif; }

.avatar-xs {
  height: 1.5rem;
  width: 1.5rem; }

.avatar-sm {
  height: 2.25rem;
  width: 2.25rem; }

.avatar-md {
  height: 3.5rem;
  width: 3.5rem; }

.avatar-lg {
  height: 4.5rem;
  width: 4.5rem; }

.avatar-xl {
  height: 6rem;
  width: 6rem; }

.avatar-xxl {
  height: 7.5rem;
  width: 7.5rem; }

.avatar-title {
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  color: #fff;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  height: 100%;
  -webkit-box-pack: center;
      -ms-flex-pack: center;
          justify-content: center;
  width: 100%; }

.avatar-group {
  padding-left: 12px; }
  .avatar-group .avatar-group-item {
    margin: 0 0 10px -12px;
    display: inline-block;
    border: 2px solid #fff;
    border-radius: 50%; }

.font-weight-medium {
  font-weight: 500; }

.font-weight-semibold {
  font-weight: 600; }

.sp-line-1,
.sp-line-2,
.sp-line-3,
.sp-line-4 {
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-box-orient: vertical; }

.sp-line-1 {
  -webkit-line-clamp: 1; }

.sp-line-2 {
  -webkit-line-clamp: 2; }

.sp-line-3 {
  -webkit-line-clamp: 3; }

.sp-line-4 {
  -webkit-line-clamp: 4; }

.pull-in {
  margin-left: -1.25rem;
  margin-right: -1.25rem; }

.social-list-item {
  height: 2rem;
  width: 2rem;
  line-height: calc(2rem - 4px);
  display: block;
  border: 2px solid #adb5bd;
  border-radius: 50%;
  color: #adb5bd; }

.tilebox-two i {
  font-size: 48px;
  opacity: 0.2;
  margin-top: 14px; }

.user-position {
  position: absolute;
  top: 0;
  bottom: 0;
  width: 44px;
  font-size: 16px;
  text-align: center;
  right: 0;
  left: auto;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  -webkit-box-pack: center;
      -ms-flex-pack: center;
          justify-content: center;
  -webkit-box-orient: horizontal;
  -webkit-box-direction: normal;
      -ms-flex-direction: row;
          flex-direction: row; }
  .user-position span {
    -webkit-transform: rotate(90deg);
            transform: rotate(90deg); }

.inbox-widget .inbox-item {
  overflow: hidden;
  padding: 12px 0px;
  position: relative; }
  .inbox-widget .inbox-item .inbox-item-img {
    display: block;
    float: left;
    margin-right: 15px;
    margin-top: 2px; }
    .inbox-widget .inbox-item .inbox-item-img img {
      width: 40px; }
  .inbox-widget .inbox-item .inbox-item-author {
    display: block;
    margin-bottom: 0px;
    color: #495057; }
  .inbox-widget .inbox-item .inbox-item-text {
    color: #adb5bd;
    display: block;
    margin: 0;
    overflow: hidden; }
  .inbox-widget .inbox-item .inbox-item-date {
    color: #6c757d;
    font-size: 0.6875rem;
    position: absolute;
    right: 5px;
    top: 10px; }

.checkbox label {
  display: inline-block;
  padding-left: 8px;
  position: relative;
  font-weight: normal; }
  .checkbox label::before {
    background-color: #fff;
    border-radius: 3px;
    border: 2px solid #efefef;
    content: "";
    display: inline-block;
    height: 18px;
    left: 0;
    margin-left: -18px;
    position: absolute;
    -webkit-transition: 0.3s ease-in-out;
    transition: 0.3s ease-in-out;
    width: 18px;
    outline: none !important;
    top: 2px; }
  .checkbox label::after {
    color: #495057;
    display: inline-block;
    font-size: 11px;
    height: 18px;
    left: 0;
    margin-left: -18px;
    padding-left: 3px;
    padding-top: 2px;
    position: absolute;
    top: 0;
    width: 18px; }

.checkbox input[type="checkbox"] {
  cursor: pointer;
  opacity: 0;
  z-index: 1;
  outline: none !important; }
  .checkbox input[type="checkbox"]:disabled + label {
    opacity: 0.65; }

.checkbox input[type="checkbox"]:focus + label::before {
  outline-offset: -2px;
  outline: none; }

.checkbox input[type="checkbox"]:checked + label::after {
  content: "";
  position: absolute;
  top: 6px;
  left: 7px;
  display: table;
  width: 4px;
  height: 8px;
  border: 2px solid #6c757d;
  border-top-width: 0;
  border-left-width: 0;
  -webkit-transform: rotate(45deg);
  transform: rotate(45deg); }

.checkbox input[type="checkbox"]:disabled + label::before {
  background-color: #f8f9fa;
  cursor: not-allowed; }

.checkbox.checkbox-circle label::before {
  border-radius: 50%; }

.checkbox.checkbox-inline {
  margin-top: 0; }

.checkbox.checkbox-single input {
  height: 18px;
  width: 18px;
  position: absolute; }

.checkbox.checkbox-single label {
  height: 18px;
  width: 18px; }
  .checkbox.checkbox-single label:before {
    margin-left: 0; }
  .checkbox.checkbox-single label:after {
    margin-left: 0; }

.checkbox-primary input[type="checkbox"]:checked + label::before {
  background-color: #64b0f2;
  border-color: #64b0f2; }

.checkbox-primary input[type="checkbox"]:checked + label::after {
  border-color: #fff; }

.checkbox-secondary input[type="checkbox"]:checked + label::before {
  background-color: #6c757d;
  border-color: #6c757d; }

.checkbox-secondary input[type="checkbox"]:checked + label::after {
  border-color: #fff; }

.checkbox-success input[type="checkbox"]:checked + label::before {
  background-color: #1bb99a;
  border-color: #1bb99a; }

.checkbox-success input[type="checkbox"]:checked + label::after {
  border-color: #fff; }

.checkbox-info input[type="checkbox"]:checked + label::before {
  background-color: #3db9dc;
  border-color: #3db9dc; }

.checkbox-info input[type="checkbox"]:checked + label::after {
  border-color: #fff; }

.checkbox-warning input[type="checkbox"]:checked + label::before {
  background-color: #ffff48;
  border-color: #ffff48; }

.checkbox-warning input[type="checkbox"]:checked + label::after {
  border-color: #fff; }

.checkbox-danger input[type="checkbox"]:checked + label::before {
  background-color: #ff5d48;
  border-color: #ff5d48; }

.checkbox-danger input[type="checkbox"]:checked + label::after {
  border-color: #fff; }

.checkbox-light input[type="checkbox"]:checked + label::before {
  background-color: #f8f9fa;
  border-color: #f8f9fa; }

.checkbox-light input[type="checkbox"]:checked + label::after {
  border-color: #fff; }

.checkbox-dark input[type="checkbox"]:checked + label::before {
  background-color: #343a40;
  border-color: #343a40; }

.checkbox-dark input[type="checkbox"]:checked + label::after {
  border-color: #fff; }

.checkbox-purple input[type="checkbox"]:checked + label::before {
  background-color: #9261c6;
  border-color: #9261c6; }

.checkbox-purple input[type="checkbox"]:checked + label::after {
  border-color: #fff; }

.checkbox-pink input[type="checkbox"]:checked + label::before {
  background-color: #ff7aa3;
  border-color: #ff7aa3; }

.checkbox-pink input[type="checkbox"]:checked + label::after {
  border-color: #fff; }

.radio label {
  display: inline-block;
  padding-left: 8px;
  position: relative;
  font-weight: normal; }
  .radio label::before {
    -o-transition: border 0.5s ease-in-out;
    -webkit-transition: border 0.5s ease-in-out;
    background-color: #fff;
    border-radius: 50%;
    border: 2px solid #efefef;
    content: "";
    display: inline-block;
    height: 18px;
    left: 0;
    margin-left: -18px;
    position: absolute;
    transition: border 0.5s ease-in-out;
    width: 18px;
    outline: none !important; }
  .radio label::after {
    -moz-transition: -moz-transform 0.1s cubic-bezier(0.8, -0.33, 0.2, 1.33);
    -ms-transform: scale(0, 0);
    -o-transform: scale(0, 0);
    -o-transition: -o-transform 0.1s cubic-bezier(0.8, -0.33, 0.2, 1.33);
    -webkit-transform: scale(0, 0);
    -webkit-transition: -webkit-transform 0.1s cubic-bezier(0.8, -0.33, 0.2, 1.33);
    background-color: #6c757d;
    border-radius: 50%;
    content: " ";
    display: inline-block;
    height: 10px;
    left: 6px;
    margin-left: -20px;
    position: absolute;
    top: 4px;
    transform: scale(0, 0);
    transition: -webkit-transform 0.1s cubic-bezier(0.8, -0.33, 0.2, 1.33);
    transition: transform 0.1s cubic-bezier(0.8, -0.33, 0.2, 1.33);
    transition: transform 0.1s cubic-bezier(0.8, -0.33, 0.2, 1.33), -webkit-transform 0.1s cubic-bezier(0.8, -0.33, 0.2, 1.33);
    width: 10px; }

.radio input[type="radio"] {
  cursor: pointer;
  opacity: 0;
  z-index: 1;
  outline: none !important; }
  .radio input[type="radio"]:disabled + label {
    opacity: 0.65; }

.radio input[type="radio"]:focus + label::before {
  outline-offset: -2px;
  outline: 5px auto -webkit-focus-ring-color;
  outline: thin dotted; }

.radio input[type="radio"]:checked + label::after {
  -webkit-transform: scale(1, 1);
  transform: scale(1, 1); }

.radio input[type="radio"]:disabled + label::before {
  cursor: not-allowed; }

.radio.radio-inline {
  margin-top: 0; }

.radio.radio-single label {
  height: 18px;
  width: 18px; }

.radio-primary input[type="radio"] + label::after {
  background-color: #64b0f2; }

.radio-primary input[type="radio"]:checked + label::before {
  border-color: #64b0f2; }

.radio-primary input[type="radio"]:checked + label::after {
  background-color: #64b0f2; }

.radio-secondary input[type="radio"] + label::after {
  background-color: #6c757d; }

.radio-secondary input[type="radio"]:checked + label::before {
  border-color: #6c757d; }

.radio-secondary input[type="radio"]:checked + label::after {
  background-color: #6c757d; }

.radio-success input[type="radio"] + label::after {
  background-color: #1bb99a; }

.radio-success input[type="radio"]:checked + label::before {
  border-color: #1bb99a; }

.radio-success input[type="radio"]:checked + label::after {
  background-color: #1bb99a; }

.radio-info input[type="radio"] + label::after {
  background-color: #3db9dc; }

.radio-info input[type="radio"]:checked + label::before {
  border-color: #3db9dc; }

.radio-info input[type="radio"]:checked + label::after {
  background-color: #3db9dc; }

.radio-warning input[type="radio"] + label::after {
  background-color: #ffff48; }

.radio-warning input[type="radio"]:checked + label::before {
  border-color: #ffff48; }

.radio-warning input[type="radio"]:checked + label::after {
  background-color: #ffff48; }

.radio-danger input[type="radio"] + label::after {
  background-color: #ff5d48; }

.radio-danger input[type="radio"]:checked + label::before {
  border-color: #ff5d48; }

.radio-danger input[type="radio"]:checked + label::after {
  background-color: #ff5d48; }

.radio-light input[type="radio"] + label::after {
  background-color: #f8f9fa; }

.radio-light input[type="radio"]:checked + label::before {
  border-color: #f8f9fa; }

.radio-light input[type="radio"]:checked + label::after {
  background-color: #f8f9fa; }

.radio-dark input[type="radio"] + label::after {
  background-color: #343a40; }

.radio-dark input[type="radio"]:checked + label::before {
  border-color: #343a40; }

.radio-dark input[type="radio"]:checked + label::after {
  background-color: #343a40; }

.radio-purple input[type="radio"] + label::after {
  background-color: #9261c6; }

.radio-purple input[type="radio"]:checked + label::before {
  border-color: #9261c6; }

.radio-purple input[type="radio"]:checked + label::after {
  background-color: #9261c6; }

.radio-pink input[type="radio"] + label::after {
  background-color: #ff7aa3; }

.radio-pink input[type="radio"]:checked + label::before {
  border-color: #ff7aa3; }

.radio-pink input[type="radio"]:checked + label::after {
  background-color: #ff7aa3; }

@media print {
  .left-side-menu,
  .right-bar,
  .page-title-box,
  .navbar-custom,
  .footer {
    display: none; }
  .card-body,
  .content-page,
  .right-bar,
  .content,
  body {
    padding: 0;
    margin: 0; } }

/*!
 * Waves v0.7.6
 * http://fian.my.id/Waves 
 * 
 * Copyright 2014-2018 Alfiana E. Sibuea and other contributors 
 * Released under the MIT license 
 * https://github.com/fians/Waves/blob/master/LICENSE */
.waves-effect {
  position: relative;
  cursor: pointer;
  display: inline-block;
  overflow: hidden;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
  -webkit-tap-highlight-color: transparent; }

.waves-effect .waves-ripple {
  position: absolute;
  border-radius: 50%;
  width: 100px;
  height: 100px;
  margin-top: -50px;
  margin-left: -50px;
  opacity: 0;
  background: rgba(0, 0, 0, 0.2);
  background: radial-gradient(rgba(0, 0, 0, 0.2) 0, rgba(0, 0, 0, 0.3) 40%, rgba(0, 0, 0, 0.4) 50%, rgba(0, 0, 0, 0.5) 60%, rgba(255, 255, 255, 0) 70%);
  -webkit-transition: all 0.5s ease-out;
  transition: all 0.5s ease-out;
  -webkit-transition-property: -webkit-transform, opacity;
  -webkit-transition-property: opacity, -webkit-transform;
  transition-property: opacity, -webkit-transform;
  transition-property: transform, opacity;
  transition-property: transform, opacity, -webkit-transform;
  -webkit-transform: scale(0) translate(0, 0);
  transform: scale(0) translate(0, 0);
  pointer-events: none; }

.waves-effect.waves-light .waves-ripple {
  background: rgba(255, 255, 255, 0.4);
  background: radial-gradient(rgba(255, 255, 255, 0.2) 0, rgba(255, 255, 255, 0.3) 40%, rgba(255, 255, 255, 0.4) 50%, rgba(255, 255, 255, 0.5) 60%, rgba(255, 255, 255, 0) 70%); }

.waves-effect.waves-classic .waves-ripple {
  background: rgba(0, 0, 0, 0.2); }

.waves-effect.waves-classic.waves-light .waves-ripple {
  background: rgba(255, 255, 255, 0.4); }

.waves-notransition {
  -webkit-transition: none !important;
  transition: none !important; }

.waves-button,
.waves-circle {
  -webkit-transform: translateZ(0);
  transform: translateZ(0);
  -webkit-mask-image: -webkit-radial-gradient(circle, white 100%, black 100%); }

.waves-button,
.waves-button:hover,
.waves-button:visited,
.waves-button-input {
  white-space: nowrap;
  vertical-align: middle;
  cursor: pointer;
  border: none;
  outline: none;
  color: inherit;
  background-color: rgba(0, 0, 0, 0);
  font-size: 1em;
  line-height: 1em;
  text-align: center;
  text-decoration: none;
  z-index: 1; }

.waves-button {
  padding: 0.85em 1.1em;
  border-radius: 0.2em; }

.waves-button-input {
  margin: 0;
  padding: 0.85em 1.1em; }

.waves-input-wrapper {
  border-radius: 0.2em;
  vertical-align: bottom; }

.waves-input-wrapper.waves-button {
  padding: 0; }

.waves-input-wrapper .waves-button-input {
  position: relative;
  top: 0;
  left: 0;
  z-index: 1; }

.waves-circle {
  text-align: center;
  width: 2.5em;
  height: 2.5em;
  line-height: 2.5em;
  border-radius: 50%; }

.waves-float {
  -webkit-mask-image: none;
  -webkit-box-shadow: 0px 1px 1.5px 1px rgba(0, 0, 0, 0.12);
  box-shadow: 0px 1px 1.5px 1px rgba(0, 0, 0, 0.12);
  -webkit-transition: all 300ms;
  transition: all 300ms; }

.waves-float:active {
  -webkit-box-shadow: 0px 8px 20px 1px rgba(0, 0, 0, 0.3);
  box-shadow: 0px 8px 20px 1px rgba(0, 0, 0, 0.3); }

.waves-block {
  display: block; }

.slimScrollDiv {
  height: auto !important; }

/* =============
   Notification
============= */
#toast-container > div {
  -webkit-box-shadow: 0px 0px 35px 0px rgba(73, 80, 87, 0.15);
          box-shadow: 0px 0px 35px 0px rgba(73, 80, 87, 0.15);
  opacity: 1; }
  #toast-container > div:hover {
    -webkit-box-shadow: 0px 0px 35px 0px rgba(73, 80, 87, 0.15);
            box-shadow: 0px 0px 35px 0px rgba(73, 80, 87, 0.15);
    opacity: 0.9; }

.toast-primary {
  border: 2px solid #64b0f2 !important;
  background-color: rgba(100, 176, 242, 0.8) !important; }

.toast-secondary {
  border: 2px solid #6c757d !important;
  background-color: rgba(108, 117, 125, 0.8) !important; }

.toast-success {
  border: 2px solid #1bb99a !important;
  background-color: rgba(27, 185, 154, 0.8) !important; }

.toast-info {
  border: 2px solid #3db9dc !important;
  background-color: rgba(61, 185, 220, 0.8) !important; }

.toast-warning {
  border: 2px solid #ffff48 !important;
  background-color: rgba(241, 181, 61, 0.8) !important; }

.toast-danger {
  border: 2px solid #ff5d48 !important;
  background-color: rgba(255, 93, 72, 0.8) !important; }

.toast-light {
  border: 2px solid #f8f9fa !important;
  background-color: rgba(248, 249, 250, 0.8) !important; }

.toast-dark {
  border: 2px solid #343a40 !important;
  background-color: rgba(52, 58, 64, 0.8) !important; }

.toast-purple {
  border: 2px solid #9261c6 !important;
  background-color: rgba(146, 97, 198, 0.8) !important; }

.toast-pink {
  border: 2px solid #ff7aa3 !important;
  background-color: rgba(255, 122, 163, 0.8) !important; }

.toast-error {
  background-color: rgba(255, 93, 72, 0.8);
  border: 2px solid #ff5d48; }

.swal2-modal {
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, "Noto Sans", sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol", "Noto Color Emoji";
  -webkit-box-shadow: 0 10px 33px rgba(0, 0, 0, 0.1);
          box-shadow: 0 10px 33px rgba(0, 0, 0, 0.1); }
  .swal2-modal .swal2-title {
    font-size: 24px; }
  .swal2-modal .swal2-content {
    font-size: 16px; }
  .swal2-modal .swal2-spacer {
    margin: 10px 0; }
  .swal2-modal .swal2-file, .swal2-modal .swal2-input, .swal2-modal .swal2-textarea {
    border: 2px solid #efefef;
    font-size: 16px;
    -webkit-box-shadow: none;
            box-shadow: none; }
  .swal2-modal .swal2-confirm.btn-confirm {
    background-color: #64b0f2 !important;
    font-size: 0.9rem; }
  .swal2-modal .swal2-cancel.btn-cancel {
    background-color: #ff5d48 !important;
    font-size: 0.9rem; }
  .swal2-modal .swal2-styled:focus {
    -webkit-box-shadow: none !important;
            box-shadow: none !important; }

.swal2-icon.swal2-question {
  color: #64b0f2;
  border-color: #64b0f2; }

.swal2-icon.swal2-success {
  border-color: #1bb99a; }
  .swal2-icon.swal2-success .line, .swal2-icon.swal2-success [class^=swal2-success-line][class$=long],
  .swal2-icon.swal2-success [class^=swal2-success-line] {
    background-color: #1bb99a; }
  .swal2-icon.swal2-success .placeholder, .swal2-icon.swal2-success .swal2-success-ring {
    border-color: #1bb99a; }

.swal2-icon.swal2-warning {
  color: #ffff48;
  border-color: #ffff48; }

.swal2-icon.swal2-error {
  border-color: #ff5d48; }
  .swal2-icon.swal2-error .line {
    background-color: #ff5d48; }

.swal2-modal .swal2-file:focus, .swal2-modal .swal2-input:focus, .swal2-modal .swal2-textarea:focus {
  outline: 0;
  border: 2px solid #64b0f2; }

.swal2-container.swal2-shown {
  background-color: rgba(52, 58, 64, 0.9); }

.irs--modern .irs-bar, .irs--modern .irs-to, .irs--modern .irs-from, .irs--modern .irs-single {
  background: #64b0f2 !important; }

.irs--modern .irs-to:before, .irs--modern .irs-from:before, .irs--modern .irs-single:before {
  border-top-color: #64b0f2; }

.irs--modern .irs-line {
  background: #efefef;
  border-color: #efefef; }

.irs--modern .irs-min, .irs--modern .irs-max {
  color: #adb5bd;
  background: #efefef; }

.irs--modern .irs-grid-text {
  font-size: 12px;
  color: #ced4da; }

.irs--modern .irs-handle > i:nth-child(1) {
  width: 8px;
  height: 8px; }

/* =============
   Rating
============= */
.rating-star i {
  color: #6c757d; }

.rating-md i {
  font-size: 16px; }

.rating-lg i {
  font-size: 22px; }

.jstree-default .jstree-node,
.jstree-default .jstree-icon {
  background-image: url("../images/plugins/jstree.png"); }

.jstree-default .jstree-node {
  background-position: -292px -4px;
  background-repeat: repeat-y; }

.jstree-default .jstree-themeicon-custom {
  background-color: transparent;
  background-image: none;
  background-position: 0 0; }

.jstree-default .jstree-anchor {
  line-height: 28px;
  height: 28px; }

.jstree-default > .jstree-container-ul .jstree-loading > .jstree-ocl {
  background: url("../images/plugins/loading.gif") center center no-repeat; }

.jstree-default .jstree-icon:empty {
  width: 24px;
  height: 28px;
  line-height: 28px;
  font-size: 15px;
  color: #6c757d; }

.jstree-default .jstree-clicked,
.jstree-default .jstree-wholerow-clicked {
  background: rgba(100, 176, 242, 0.2);
  -webkit-box-shadow: none;
          box-shadow: none; }

.jstree-default .jstree-hovered,
.jstree-default .jstree-wholerow-hovered {
  background: rgba(100, 176, 242, 0.25);
  -webkit-box-shadow: none;
          box-shadow: none; }

.jstree-default .jstree-last {
  background: transparent; }

.jstree-default .jstree-wholerow {
  height: 28px; }

div.hopscotch-bubble {
  border: 3px solid #64b0f2;
  border-radius: 5px; }
  div.hopscotch-bubble .hopscotch-next,
  div.hopscotch-bubble .hopscotch-prev {
    background-color: #64b0f2 !important;
    background-image: none !important;
    border-color: #64b0f2 !important;
    text-shadow: none !important;
    margin: 0 0 0 5px !important;
    font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, "Noto Sans", sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol", "Noto Color Emoji";
    color: #fff !important; }
  div.hopscotch-bubble .hopscotch-bubble-number {
    background: #1bb99a;
    padding: 0;
    border-radius: 50%; }
  div.hopscotch-bubble .hopscotch-bubble-arrow-container.left .hopscotch-bubble-arrow-border {
    border-right: 19px solid #64b0f2; }
  div.hopscotch-bubble .hopscotch-bubble-arrow-container.left .hopscotch-bubble-arrow {
    border: none; }
  div.hopscotch-bubble .hopscotch-bubble-arrow-container.right .hopscotch-bubble-arrow {
    border-left: 19px solid #64b0f2;
    left: -2px; }
  div.hopscotch-bubble .hopscotch-bubble-arrow-container.right .hopscotch-bubble-arrow-border {
    border-left: 0 solid #64b0f2; }
  div.hopscotch-bubble .hopscotch-bubble-arrow-container.up .hopscotch-bubble-arrow {
    border-bottom: 19px solid #64b0f2;
    top: 0; }
  div.hopscotch-bubble .hopscotch-bubble-arrow-container.up .hopscotch-bubble-arrow-border {
    border-bottom: 0 solid rgba(0, 0, 0, 0.5); }
  div.hopscotch-bubble .hopscotch-bubble-arrow-container.down .hopscotch-bubble-arrow {
    border-top: 19px solid #64b0f2;
    top: -2px; }
  div.hopscotch-bubble .hopscotch-bubble-arrow-container.down .hopscotch-bubble-arrow-border {
    border-top: 0 solid rgba(0, 0, 0, 0.5); }
  div.hopscotch-bubble h3 {
    font-family: "Roboto", sans-serif;
    margin-bottom: 10px;
    font-weight: 600; }
  div.hopscotch-bubble .hopscotch-content {
    font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, "Noto Sans", sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol", "Noto Color Emoji"; }

.calendar {
  float: left;
  margin-bottom: 0; }

.fc-view {
  margin-top: 30px; }

.none-border .modal-footer {
  border-top: none; }

.fc-toolbar h2 {
  font-size: 1.25rem;
  line-height: 1.875rem;
  text-transform: uppercase; }

.fc-day-grid-event .fc-time {
  font-weight: 500; }

.fc-day {
  background: transparent; }

.fc-toolbar .fc-state-active,
.fc-toolbar .ui-state-active,
.fc-toolbar button:focus,
.fc-toolbar button:hover,
.fc-toolbar .ui-state-hover {
  z-index: 0; }

.fc th.fc-widget-header {
  background: #efefef;
  font-size: 13px;
  line-height: 20px;
  padding: 10px 0;
  text-transform: uppercase;
  font-weight: 600; }

.fc-unthemed th,
.fc-unthemed td,
.fc-unthemed thead,
.fc-unthemed tbody,
.fc-unthemed .fc-divider,
.fc-unthemed .fc-row,
.fc-unthemed .fc-popover {
  border-color: #efefef; }

.fc-unthemed td.fc-today, .fc-unthemed .fc-divider {
  background: #efefef !important; }

.fc-button {
  background: #efefef;
  border: none;
  color: #495057;
  text-transform: capitalize;
  -webkit-box-shadow: none;
          box-shadow: none;
  border-radius: 3px !important;
  margin: 0 3px !important;
  padding: 6px 12px !important;
  height: auto !important; }

.fc-text-arrow {
  font-family: inherit;
  font-size: 1rem; }

.fc-state-hover {
  background: #efefef; }

.fc-state-highlight {
  background: #efefef; }

.fc-state-down,
.fc-state-active,
.fc-state-disabled {
  background-color: #64b0f2;
  color: #fff;
  text-shadow: none; }

.fc-cell-overlay {
  background: #efefef; }

.fc-unthemed td.fc-today {
  background: #fff; }

.fc-event {
  border-radius: 2px;
  border: none;
  cursor: move;
  font-size: 0.8125rem;
  margin: 5px 7px;
  padding: 5px 5px;
  text-align: center; }

.external-event {
  cursor: move;
  margin: 10px 0;
  padding: 8px 10px;
  color: #fff;
  border-radius: 4px; }

.fc-basic-view td.fc-week-number span {
  padding-right: 8px; }

.fc-basic-view td.fc-day-number {
  padding-right: 8px; }

.fc-basic-view .fc-content {
  color: #fff; }

.fc-time-grid-event .fc-content {
  color: #fff; }

@media (max-width: 767.98px) {
  .fc-toolbar .fc-left, .fc-toolbar .fc-right, .fc-toolbar .fc-center {
    float: none;
    display: block;
    clear: both;
    margin: 10px 0; }
  .fc .fc-toolbar > * > * {
    float: none; }
  .fc-today-button {
    display: none; } }

/* Bootstrap tagsinput */
.bootstrap-tagsinput {
  -webkit-box-shadow: none;
          box-shadow: none;
  padding: 4px 7px 4px;
  width: 100%;
  background-color: #fff;
  border-color: #ced4da; }
  .bootstrap-tagsinput input {
    color: #495057; }
  .bootstrap-tagsinput .label-info {
    background-color: #64b0f2;
    display: inline-block;
    font-size: 11px;
    margin: 3px 1px;
    padding: 0 5px;
    border-radius: 3px;
    font-weight: 500; }

.ms-container {
  background: transparent url("../images/plugins/multiple-arrow.png") no-repeat 50% 50%;
  width: auto;
  max-width: 370px; }
  .ms-container .ms-list {
    -webkit-box-shadow: none;
            box-shadow: none;
    border: 1px solid #ced4da; }
    .ms-container .ms-list.ms-focus {
      -webkit-box-shadow: none;
              box-shadow: none;
      border: 1px solid #b1bbc4; }
  .ms-container .ms-selectable {
    background-color: #fff; }
    .ms-container .ms-selectable li.ms-elem-selectable {
      border: none;
      padding: 5px 10px;
      color: #6c757d; }
    .ms-container .ms-selectable li.ms-hover {
      background-color: #64b0f2;
      color: #fff; }
  .ms-container .ms-selection {
    background-color: #fff; }
    .ms-container .ms-selection li.ms-elem-selection {
      border: none;
      padding: 5px 10px;
      color: #6c757d; }
    .ms-container .ms-selection li.ms-hover {
      background-color: #64b0f2;
      color: #fff; }

.search-input {
  margin-bottom: 10px; }

.ms-selectable {
  -webkit-box-shadow: none;
          box-shadow: none;
  outline: none !important; }

.ms-optgroup-label {
  font-weight: 500;
  font-family: "Roboto", sans-serif;
  color: #343a40 !important;
  font-size: 13px; }

.select2-container .select2-selection--single {
  background-color: #fff;
  border: 1px solid #ced4da;
  height: 38px;
  outline: none; }
  .select2-container .select2-selection--single .select2-selection__rendered {
    line-height: 36px;
    padding-left: 12px;
    color: #6c757d; }
  .select2-container .select2-selection--single .select2-selection__arrow {
    height: 34px;
    width: 34px;
    right: 3px; }
    .select2-container .select2-selection--single .select2-selection__arrow b {
      border-color: #6c757d transparent transparent transparent;
      border-width: 6px 6px 0 6px; }

.select2-container--open .select2-selection--single .select2-selection__arrow b {
  border-color: transparent transparent #6c757d transparent !important;
  border-width: 0 6px 6px 6px !important; }

.select2-results__option {
  padding: 6px 12px; }

.select2-dropdown {
  border: rgba(0, 0, 0, 0.15);
  background-color: #fff;
  -webkit-box-shadow: 0px 0px 35px 0px rgba(73, 80, 87, 0.15);
          box-shadow: 0px 0px 35px 0px rgba(73, 80, 87, 0.15); }

.select2-container--default .select2-search--dropdown {
  padding: 10px;
  background-color: #fff; }
  .select2-container--default .select2-search--dropdown .select2-search__field {
    border: 1px solid #ced4da;
    background-color: #fff;
    color: #6c757d;
    outline: none; }

.select2-container--default .select2-results__group {
  font-weight: 500; }

.select2-container--default .select2-results__option--highlighted[aria-selected] {
  background-color: #64b0f2; }

.select2-container--default .select2-results__option[aria-selected=true] {
  background-color: #f8f9fa;
  color: #16181b; }
  .select2-container--default .select2-results__option[aria-selected=true]:hover {
    background-color: #64b0f2;
    color: #fff; }

.select2-container .select2-selection--multiple {
  min-height: 38px;
  background-color: #fff;
  border: 1px solid #ced4da !important; }
  .select2-container .select2-selection--multiple .select2-selection__rendered {
    padding: 1px 10px; }
  .select2-container .select2-selection--multiple .select2-search__field {
    border: 0;
    color: #6c757d; }
    .select2-container .select2-selection--multiple .select2-search__field::-webkit-input-placeholder {
      color: #6c757d; }
    .select2-container .select2-selection--multiple .select2-search__field::-moz-placeholder {
      color: #6c757d; }
    .select2-container .select2-selection--multiple .select2-search__field:-ms-input-placeholder {
      color: #6c757d; }
    .select2-container .select2-selection--multiple .select2-search__field::-ms-input-placeholder {
      color: #6c757d; }
    .select2-container .select2-selection--multiple .select2-search__field::placeholder {
      color: #6c757d; }
  .select2-container .select2-selection--multiple .select2-selection__choice {
    background-color: #64b0f2;
    border: none;
    color: #fff;
    border-radius: 3px;
    padding: 0 7px;
    margin-top: 7px; }
  .select2-container .select2-selection--multiple .select2-selection__choice__remove {
    color: #fff;
    margin-right: 5px; }
    .select2-container .select2-selection--multiple .select2-selection__choice__remove:hover {
      color: #fff; }

.autocomplete-suggestions {
  border: 1px solid #e9ecef;
  background: #eff1f3;
  cursor: default;
  overflow: auto;
  max-height: 200px !important;
  -webkit-box-shadow: 0px 0px 35px 0px rgba(73, 80, 87, 0.15);
          box-shadow: 0px 0px 35px 0px rgba(73, 80, 87, 0.15); }
  .autocomplete-suggestions strong {
    font-weight: 600;
    color: #343a40; }

.autocomplete-suggestion {
  padding: 5px 10px;
  white-space: nowrap;
  overflow: hidden; }

.autocomplete-no-suggestion {
  padding: 5px; }

.autocomplete-selected {
  background: #e9ecef;
  cursor: pointer; }

.autocomplete-group {
  padding: 5px;
  font-weight: 500;
  font-family: "Roboto", sans-serif; }
  .autocomplete-group strong {
    font-weight: 600;
    font-size: 16px;
    color: #343a40;
    display: block; }

/* =============
   Form validation
============= */
.parsley-error {
  border-color: #ff5d48 !important; }

.parsley-errors-list {
  margin: 0;
  padding: 0; }
  .parsley-errors-list > li {
    font-size: 12px;
    list-style: none;
    color: #ff5d48;
    margin-top: 5px; }

.bootstrap-timepicker-widget table td input {
  border: 1px solid rgba(52, 58, 64, 0.3);
  width: 35px;
  background-color: #fff;
  color: #6c757d; }

.bootstrap-timepicker-widget table td a {
  color: #adb5bd; }
  .bootstrap-timepicker-widget table td a:hover {
    background-color: #eff1f3;
    border-color: #eff1f3; }

.bootstrap-timepicker-widget.dropdown-menu:after {
  border-bottom-color: #e9ecef; }

.bootstrap-timepicker-widget.timepicker-orient-bottom:after {
  border-top-color: #e9ecef; }

.datepicker {
  padding: 10px !important; }
  .datepicker td,
  .datepicker th {
    width: 30px;
    height: 30px;
    padding: 5px; }
  .datepicker th {
    font-weight: 600; }
  .datepicker table tr td.active.active, .datepicker table tr td.active.disabled, .datepicker table tr td.active.disabled.active, .datepicker table tr td.active.disabled.disabled, .datepicker table tr td.active.disabled:active, .datepicker table tr td.active.disabled:hover, .datepicker table tr td.active.disabled:hover.active, .datepicker table tr td.active.disabled:hover.disabled, .datepicker table tr td.active.disabled:hover:active, .datepicker table tr td.active.disabled:hover:hover,
  .datepicker table tr td .active.disabled:hover[disabled],
  .datepicker table tr td .active.disabled[disabled],
  .datepicker table tr td .active:active,
  .datepicker table tr td .active:hover,
  .datepicker table tr td .active:hover.active,
  .datepicker table tr td .active:hover.disabled,
  .datepicker table tr td .active:hover:active,
  .datepicker table tr td .active:hover:hover,
  .datepicker table tr td .active:hover[disabled],
  .datepicker table tr td .active[disabled],
  .datepicker table tr td span.active.active,
  .datepicker table tr td span.active.disabled,
  .datepicker table tr td span.active.disabled.active,
  .datepicker table tr td span.active.disabled.disabled,
  .datepicker table tr td span.active.disabled:active,
  .datepicker table tr td span.active.disabled:hover,
  .datepicker table tr td span.active.disabled:hover.active,
  .datepicker table tr td span.active.disabled:hover.disabled,
  .datepicker table tr td span.active.disabled:hover:active,
  .datepicker table tr td span.active.disabled:hover:hover,
  .datepicker table tr td span.active.disabled:hover[disabled],
  .datepicker table tr td span.active.disabled[disabled],
  .datepicker table tr td span.active:active,
  .datepicker table tr td span.active:hover,
  .datepicker table tr td span.active:hover.active,
  .datepicker table tr td span.active:hover.disabled,
  .datepicker table tr td span.active:hover:active,
  .datepicker table tr td span.active:hover:hover,
  .datepicker table tr td span.active:hover[disabled],
  .datepicker table tr td span.active[disabled], .datepicker table tr td.today, .datepicker table tr td.today.disabled, .datepicker table tr td.today.disabled:hover, .datepicker table tr td.today:hover, .datepicker table tr td.selected {
    background-color: #64b0f2 !important;
    background-image: none !important;
    color: #fff; }
  .datepicker table tr td.day.focused, .datepicker table tr td.day:hover,
  .datepicker table tr td span.focused,
  .datepicker table tr td span:hover {
    background: #e9ecef; }
  .datepicker table tr td.new, .datepicker table tr td.old,
  .datepicker table tr td span.new,
  .datepicker table tr td span.old {
    color: #adb5bd;
    opacity: 0.6; }
  .datepicker table tr td.range, .datepicker table tr td.range.disabled, .datepicker table tr td.range.disabled:hover, .datepicker table tr td.range:hover {
    background-color: #f5f6f8; }
  .datepicker .datepicker-switch:hover,
  .datepicker .next:hover,
  .datepicker .prev:hover,
  .datepicker tfoot tr th:hover {
    background: #e9ecef; }
  .datepicker .datepicker-switch:hover {
    background: none; }

.datepicker-inline {
  border: 2px solid rgba(52, 58, 64, 0.1); }

.datepicker-dropdown:after {
  border-bottom: 6px solid #fff; }

.datepicker-dropdown:before {
  border-bottom-color: rgba(0, 0, 0, 0.15); }

.datepicker-dropdown.datepicker-orient-top:before {
  border-top: 7px solid rgba(0, 0, 0, 0.15); }

.datepicker-dropdown.datepicker-orient-top:after {
  border-top: 6px solid #fff; }

.daterangepicker {
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, "Noto Sans", sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol", "Noto Color Emoji";
  background-color: #fff;
  border-color: #efefef; }
  .daterangepicker .calendar-table {
    background-color: #fff;
    border-color: #efefef; }
    .daterangepicker .calendar-table .next span, .daterangepicker .calendar-table .prev span {
      border-color: #adb5bd; }
  .daterangepicker th, .daterangepicker td {
    padding: 5px; }
    .daterangepicker th.week, .daterangepicker td.week {
      color: #ced4da; }
    .daterangepicker th.available:hover, .daterangepicker td.available:hover {
      background-color: #f5f6f8; }
  .daterangepicker td.active, .daterangepicker td.active:hover, .daterangepicker .ranges li.active {
    background-color: #64b0f2; }
  .daterangepicker .ranges li:hover {
    background-color: #efefef; }
  .daterangepicker .month select, .daterangepicker .calendar-time select {
    background-color: #fff;
    border-color: #ced4da;
    color: #495057; }
  .daterangepicker td.off, .daterangepicker td.off.in-range, .daterangepicker td.off.start-date, .daterangepicker td.off.end-date {
    background-color: transparent;
    color: rgba(206, 212, 218, 0.7); }
  .daterangepicker td.in-range {
    background-color: #f5f6f8;
    color: #6c757d; }
  .daterangepicker.show-ranges.ltr .drp-calendar.left {
    border-left-color: #efefef; }
  .daterangepicker .drp-buttons {
    border-top-color: #efefef; }
    .daterangepicker .drp-buttons .btn {
      font-weight: 500; }

.clockpicker-popover .popover-title {
  font-size: 16px;
  font-weight: 600;
  background-color: white; }

.clockpicker-popover .popover-content {
  background-color: #f5f6f8; }

.clockpicker-popover .clockpicker-plate {
  background-color: white;
  border-color: #e9ecef; }

.clockpicker-popover .clockpicker-tick {
  color: #adb5bd; }

.clockpicker-popover .btn-default {
  background-color: #64b0f2;
  color: #fff; }

.wizard > .steps {
  position: relative;
  display: block;
  width: 100%; }
  .wizard > .steps > ul > li {
    width: 25%; }
  .wizard > .steps a {
    font-size: 16px;
    margin: 0 0.5em 0.5em; }
  .wizard > .steps a, .wizard > .steps a:hover, .wizard > .steps a:active {
    display: block;
    width: auto;
    padding: 1em 1em;
    text-decoration: none;
    border-radius: 2px; }
  .wizard > .steps .disabled a {
    background: #f5f6f8;
    border: 1px solid #e9ecef;
    color: #343a40;
    cursor: default; }
    .wizard > .steps .disabled a:hover, .wizard > .steps .disabled a:active {
      background: #eff1f3; }
  .wizard > .steps .current a, .wizard > .steps .current a:hover, .wizard > .steps .current a:active {
    background: #64b0f2;
    color: #fff;
    cursor: default; }
  .wizard > .steps .done a, .wizard > .steps .done a:hover, .wizard > .steps .done a:active {
    background: #e9ecef;
    color: #343a40; }

.wizard > .steps > ul > li, .wizard > .actions > ul > li {
  float: left;
  position: relative; }

.wizard > .content {
  display: block;
  margin: 0.5em;
  min-height: 240px;
  overflow: hidden;
  position: relative;
  width: auto;
  padding: 20px;
  border: 1px solid #e9ecef; }
  .wizard > .content > .body {
    padding: 0;
    position: relative; }
    .wizard > .content > .body ul {
      list-style: disc !important; }
      .wizard > .content > .body ul > li {
        display: block;
        line-height: 30px; }
    .wizard > .content > .body > iframe {
      border: 0 none;
      width: 100%;
      height: 100%; }
    .wizard > .content > .body input {
      display: block;
      border-color: #efefef; }
      .wizard > .content > .body input:focus {
        border-color: #efefef; }
      .wizard > .content > .body input[type="checkbox"] {
        display: inline-block; }
      .wizard > .content > .body input.error {
        background: rgba(255, 93, 72, 0.1);
        border: 1px solid #ffedeb;
        color: #ff5d48; }
    .wizard > .content > .body label {
      display: inline-block;
      margin-bottom: 0.5em;
      margin-top: 10px; }
      .wizard > .content > .body label.error {
        color: #ff5d48;
        font-size: 12px; }

.wizard > .actions {
  position: relative;
  display: block;
  text-align: right;
  width: 100%;
  margin-top: 15px; }
  .wizard > .actions > ul {
    display: inline-block;
    text-align: right; }
    .wizard > .actions > ul > li {
      margin: 0 0.5em; }
  .wizard > .actions a, .wizard > .actions a:hover, .wizard > .actions a:active {
    background: #64b0f2;
    color: #fff;
    display: block;
    padding: 0.5em 1em;
    text-decoration: none;
    border-radius: 2px; }
  .wizard > .actions .disabled a, .wizard > .actions .disabled a:hover, .wizard > .actions .disabled a:active {
    background: #f5f6f8;
    color: #343a40; }

.wizard.vertical > .steps {
  display: inline;
  float: left;
  width: 30%; }
  .wizard.vertical > .steps > ul > li {
    float: none;
    width: 100%; }

.wizard.vertical > .content {
  width: 65%;
  margin: 0 2.5% 0.5em;
  display: inline;
  float: left; }

.wizard.vertical > .actions {
  display: inline;
  float: right;
  width: 95%;
  margin: 0 2.5%;
  margin-top: 15px !important; }
  .wizard.vertical > .actions > ul > li {
    margin: 0 0 0 1em; }

/*
  Common 
*/
.wizard, .tabcontrol {
  display: block;
  width: 100%;
  overflow: hidden;
  /* Accessibility */ }
  .wizard a, .tabcontrol a {
    outline: 0; }
  .wizard ul, .tabcontrol ul {
    list-style: none !important;
    padding: 0;
    margin: 0; }
    .wizard ul > li, .tabcontrol ul > li {
      display: block;
      padding: 0; }
  .wizard > .steps .current-info, .tabcontrol > .steps .current-info {
    position: absolute;
    left: -999em; }
  .wizard > .content > .title, .tabcontrol > .content > .title {
    position: absolute;
    left: -999em; }

@media (max-width: 767.98px) {
  .wizard > .steps > ul > li, .wizard.vertical > .steps, .wizard.vertical > .content {
    width: 100%; } }

@font-face {
  font-family: "summernote";
  font-style: normal;
  font-weight: normal;
  src: url("../fonts/summernote.eot");
  src: url("../fonts/summernote.eot?#iefix") format("embedded-opentype"), url("../fonts/summernote.woff?") format("woff"), url("../fonts/summernote.ttf?") format("truetype"); }

.note-editor.note-frame {
  border: 1px solid #dde2e6;
  -webkit-box-shadow: none;
          box-shadow: none;
  margin: 0; }
  .note-editor.note-frame .note-statusbar {
    background-color: #eff1f3;
    border-top: 1px solid #e9ecef; }
  .note-editor.note-frame .note-editable {
    border: none; }

.note-status-output {
  display: none; }

.note-editable p:last-of-type {
  margin-bottom: 0; }

.note-popover .popover-content .note-color .dropdown-menu,
.card-header.note-toolbar .note-color .dropdown-menu {
  min-width: 344px; }

.note-toolbar {
  z-index: 1; }

@font-face {
  font-family: 'dropify';
  src: url("../fonts/dropify.eot");
  src: url("../fonts/dropify.eot#iefix") format("embedded-opentype"), url("../fonts/dropify.woff") format("woff"), url("../fonts/dropify.ttf") format("truetype"), url("../fonts/dropify.svg#dropify") format("svg");
  font-weight: normal;
  font-style: normal; }

.dropify-wrapper {
  border: 2px dashed #efefef;
  background-color: transparent;
  border-radius: 6px;
  color: #ced4da; }
  .dropify-wrapper:hover {
    background-image: linear-gradient(-45deg, #f5f6f8 25%, transparent 25%, transparent 50%, #f5f6f8 50%, #f5f6f8 75%, transparent 75%, transparent); }
  .dropify-wrapper .dropify-preview {
    background-color: #f5f6f8; }

.editable-clear-x {
  background: url("../images/plugins/clear.png") center center no-repeat; }

.editableform-loading {
  background: url("../images/plugins/loading.gif") center center no-repeat; }

.editable-checklist label {
  display: block; }

.editable-clear-x {
  background: url("../images/plugins/clear.png") center center no-repeat; }

.editableform-loading {
  background: url("../images/plugins/loading.gif") center center no-repeat; }

.editable-checklist label {
  display: block; }

.dataTables_wrapper.container-fluid {
  padding: 0; }

table.dataTable {
  border-collapse: collapse !important;
  margin-bottom: 15px !important; }
  table.dataTable tbody > tr.selected, table.dataTable tbody > tr > .selected {
    background-color: #64b0f2; }
    table.dataTable tbody > tr.selected td, table.dataTable tbody > tr > .selected td {
      border-color: #64b0f2; }
  table.dataTable tbody td:focus {
    outline: none !important; }
  table.dataTable tbody th.focus, table.dataTable tbody td.focus {
    outline: 2px solid #64b0f2 !important;
    outline-offset: -1px;
    color: #64b0f2;
    background-color: rgba(100, 176, 242, 0.15); }

.dataTables_info {
  font-weight: 500; }

table.dataTable.dtr-inline.collapsed > tbody > tr[role=row] > td:first-child:before, table.dataTable.dtr-inline.collapsed > tbody > tr[role=row] > th:first-child:before {
  -webkit-box-shadow: 0 1rem 3rem rgba(0, 0, 0, 0.175);
          box-shadow: 0 1rem 3rem rgba(0, 0, 0, 0.175);
  background-color: #1bb99a;
  top: 0.75rem; }

table.dataTable.dtr-inline.collapsed > tbody > tr.parent > td:first-child:before, table.dataTable.dtr-inline.collapsed > tbody > tr.parent > th:first-child:before {
  background-color: #ff5d48;
  top: 0.75rem; }

.dt-buttons, .dataTables_length {
  float: left; }

div.dt-button-info {
  background-color: #64b0f2;
  border: none;
  color: #fff;
  -webkit-box-shadow: none;
          box-shadow: none;
  border-radius: 3px;
  text-align: center;
  z-index: 21; }
  div.dt-button-info h2 {
    border-bottom: none;
    background-color: rgba(255, 255, 255, 0.2);
    color: #fff; }

@media (max-width: 767.98px) {
  li.paginate_button.previous, li.paginate_button.next {
    display: inline-block;
    font-size: 1.5rem; }
  li.paginate_button {
    display: none; }
  .dataTables_paginate ul {
    text-align: center;
    display: block;
    margin: 1rem 0 0 !important; }
  div.dt-buttons {
    display: inline-table;
    margin-bottom: 1rem; } }

.activate-select .sorting_1 {
  background-color: #f8f9fa; }

/* ColVid Tables */
div.ColVis {
  float: none;
  margin-right: 30px; }

button.ColVis_Button, button.ColVis_Button:hover {
  float: none !important;
  border-radius: 3px;
  outline: none !important;
  -webkit-box-shadow: none !important;
          box-shadow: none !important;
  color: #fff !important;
  background: #64b0f2 !important;
  border: 1px solid #64b0f2 !important; }

div.ColVis_collectionBackground {
  background-color: transparent; }

ul.ColVis_collection {
  padding: 10px 0 0 0;
  background-color: #fff;
  -webkit-box-shadow: 0px 0px 35px 0px rgba(73, 80, 87, 0.15);
          box-shadow: 0px 0px 35px 0px rgba(73, 80, 87, 0.15);
  border: none; }
  ul.ColVis_collection li {
    background: transparent !important;
    padding: 3px 10px !important;
    border: none !important;
    -webkit-box-shadow: none !important;
            box-shadow: none !important; }

#datatable-colvid_info {
  float: left; }

.responsive-table-plugin .dropdown-menu li.checkbox-row {
  padding: 7px 15px;
  color: #6c757d; }
  .responsive-table-plugin .dropdown-menu li.checkbox-row:hover, .responsive-table-plugin .dropdown-menu li.checkbox-row:focus {
    background-color: #f5f6f8;
    color: #6c757d; }

.responsive-table-plugin .table-responsive {
  border: none;
  margin-bottom: 0; }

.responsive-table-plugin .btn-toolbar {
  display: block; }

.responsive-table-plugin tbody th {
  font-size: 14px;
  font-weight: normal; }

.responsive-table-plugin .checkbox-row {
  padding-left: 40px; }
  .responsive-table-plugin .checkbox-row label {
    display: inline-block;
    padding-left: 5px;
    position: relative;
    margin-bottom: 0; }
    .responsive-table-plugin .checkbox-row label::before {
      background-color: #f5f6f8;
      border-radius: 3px;
      border: 1px solid #ced4da;
      content: "";
      display: inline-block;
      height: 17px;
      left: 0;
      margin-left: -20px;
      position: absolute;
      -webkit-transition: 0.3s ease-in-out;
      transition: 0.3s ease-in-out;
      width: 17px;
      outline: none; }
    .responsive-table-plugin .checkbox-row label::after {
      color: #ced4da;
      display: inline-block;
      font-size: 11px;
      height: 16px;
      left: 0;
      margin-left: -20px;
      padding-left: 3px;
      padding-top: 1px;
      position: absolute;
      top: -1px;
      width: 16px; }
  .responsive-table-plugin .checkbox-row input[type="checkbox"] {
    cursor: pointer;
    opacity: 0;
    z-index: 1;
    outline: none; }
    .responsive-table-plugin .checkbox-row input[type="checkbox"]:disabled + label {
      opacity: 0.65; }
  .responsive-table-plugin .checkbox-row input[type="checkbox"]:focus + label::before {
    outline-offset: -2px;
    outline: none; }
  .responsive-table-plugin .checkbox-row input[type="checkbox"]:checked + label::after {
    content: "\f00c";
    font-family: 'Font Awesome 5 Free';
    font-weight: 900; }
  .responsive-table-plugin .checkbox-row input[type="checkbox"]:disabled + label::before {
    background-color: #efefef;
    cursor: not-allowed; }
  .responsive-table-plugin .checkbox-row input[type="checkbox"]:checked + label::before {
    background-color: #f5f6f8;
    border-color: #64b0f2; }
  .responsive-table-plugin .checkbox-row input[type="checkbox"]:checked + label::after {
    color: #64b0f2; }

.responsive-table-plugin table.focus-on tbody tr.focused th,
.responsive-table-plugin table.focus-on tbody tr.focused td,
.responsive-table-plugin .sticky-table-header {
  background: #64b0f2;
  border-color: #64b0f2;
  color: #fff; }
  .responsive-table-plugin table.focus-on tbody tr.focused th table,
  .responsive-table-plugin table.focus-on tbody tr.focused td table,
  .responsive-table-plugin .sticky-table-header table {
    color: #fff; }

.responsive-table-plugin .fixed-solution .sticky-table-header {
  top: 70px !important; }

.responsive-table-plugin .btn-default {
  background-color: #f5f6f8;
  color: #343a40;
  border: 1px solid #e9ecef; }
  .responsive-table-plugin .btn-default.btn-primary {
    background-color: #64b0f2;
    border-color: #64b0f2;
    color: #fff;
    -webkit-box-shadow: 0 0 0 2px rgba(100, 176, 242, 0.5);
            box-shadow: 0 0 0 2px rgba(100, 176, 242, 0.5); }

.responsive-table-plugin .btn-group.pull-right {
  float: right; }
  .responsive-table-plugin .btn-group.pull-right .dropdown-menu {
    left: auto;
    right: 0; }

.tablesaw thead {
  background: #f5f6f8;
  background-image: none;
  border: none; }
  .tablesaw thead th {
    text-shadow: none; }
  .tablesaw thead tr:first-child th {
    border: none;
    font-weight: 500;
    font-family: "Roboto", sans-serif; }

.tablesaw td {
  border-top: 1px solid #f5f6f8 !important; }

.tablesaw td, .tablesaw tbody th {
  font-size: inherit;
  line-height: inherit;
  padding: 10px !important; }

.tablesaw-stack tbody tr, .tablesaw tbody tr {
  border-bottom: none; }

.tablesaw-bar .btn-select .btn-small:after, .tablesaw-bar .btn-select .btn-micro:after {
  font-size: 8px;
  padding-right: 10px; }

.tablesaw-swipe .tablesaw-cell-persist {
  -webkit-box-shadow: none;
          box-shadow: none;
  border-color: #f8f9fa; }

.tablesaw-swipe .tablesaw-swipe-cellpersist {
  border-right: 2px solid #f5f6f8; }

.tablesaw-bar-section label {
  color: #6c757d; }

.tablesaw-enhanced .tablesaw-bar .btn {
  text-shadow: none;
  background-image: none;
  text-transform: none;
  border: 1px solid #efefef;
  padding: 3px 10px;
  color: #343a40; }
  .tablesaw-enhanced .tablesaw-bar .btn:after {
    display: none; }
  .tablesaw-enhanced .tablesaw-bar .btn.btn-select:hover {
    background: #fff; }
  .tablesaw-enhanced .tablesaw-bar .btn:hover, .tablesaw-enhanced .tablesaw-bar .btn:focus, .tablesaw-enhanced .tablesaw-bar .btn:active {
    color: #64b0f2 !important;
    background-color: #f8f9fa;
    outline: none !important;
    -webkit-box-shadow: none !important;
            box-shadow: none !important;
    background-image: none; }

.tablesaw-columntoggle-popup .btn-group {
  display: block; }

.tablesaw-sortable-btn {
  cursor: pointer; }

.tablesaw-swipe-cellpersist {
  width: auto !important; }

.flotTip {
  padding: 8px 12px;
  background-color: rgba(52, 58, 64, 0.9);
  z-index: 100;
  color: #f8f9fa;
  opacity: 1;
  border-radius: 3px; }

.legend tr {
  height: 30px;
  font-family: "Roboto", sans-serif; }

.legendLabel {
  padding-left: 5px;
  line-height: 10px;
  padding-right: 20px;
  font-size: 13px;
  font-weight: 500;
  color: #6c757d; }

.legendColorBox div {
  border-radius: 3px; }
  .legendColorBox div div {
    border-radius: 3px; }

@media (max-width: 767.98px) {
  .legendLabel {
    display: none; } }

.morris-chart text {
  font-family: "Roboto", sans-serif !important;
  font-weight: 600 !important;
  fill: #adb5bd; }

.morris-hover {
  position: absolute;
  z-index: 10; }
  .morris-hover.morris-default-style {
    font-size: 12px;
    text-align: center;
    border-radius: 5px;
    padding: 10px 12px;
    background: rgba(248, 249, 250, 0.8);
    color: #343a40;
    border: 2px solid #e9ecef;
    font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, "Noto Sans", sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol", "Noto Color Emoji"; }
    .morris-hover.morris-default-style .morris-hover-row-label {
      font-weight: bold;
      margin: 0.25em 0;
      font-family: "Roboto", sans-serif; }
    .morris-hover.morris-default-style .morris-hover-point {
      white-space: nowrap;
      margin: 0.1em 0;
      color: #fff; }

.ct-golden-section:before {
  float: none; }

.ct-chart {
  max-height: 300px; }
  .ct-chart .ct-label {
    fill: #adb5bd;
    color: #adb5bd;
    font-size: 12px;
    line-height: 1; }

.ct-chart.simple-pie-chart-chartist .ct-label {
  color: #fff;
  fill: #fff;
  font-size: 16px; }

.ct-grid {
  stroke: rgba(52, 58, 64, 0.1); }

.ct-chart .ct-series.ct-series-a .ct-bar,
.ct-chart .ct-series.ct-series-a .ct-line,
.ct-chart .ct-series.ct-series-a .ct-point,
.ct-chart .ct-series.ct-series-a .ct-slice-donut {
  stroke: #64b0f2; }

.ct-chart .ct-series.ct-series-b .ct-bar,
.ct-chart .ct-series.ct-series-b .ct-line,
.ct-chart .ct-series.ct-series-b .ct-point,
.ct-chart .ct-series.ct-series-b .ct-slice-donut {
  stroke: #1bb99a; }

.ct-chart .ct-series.ct-series-c .ct-bar,
.ct-chart .ct-series.ct-series-c .ct-line,
.ct-chart .ct-series.ct-series-c .ct-point,
.ct-chart .ct-series.ct-series-c .ct-slice-donut {
  stroke: #ffff48; }

.ct-chart .ct-series.ct-series-d .ct-bar,
.ct-chart .ct-series.ct-series-d .ct-line,
.ct-chart .ct-series.ct-series-d .ct-point,
.ct-chart .ct-series.ct-series-d .ct-slice-donut {
  stroke: #ff7aa3; }

.ct-chart .ct-series.ct-series-e .ct-bar,
.ct-chart .ct-series.ct-series-e .ct-line,
.ct-chart .ct-series.ct-series-e .ct-point,
.ct-chart .ct-series.ct-series-e .ct-slice-donut {
  stroke: #343a40; }

.ct-chart .ct-series.ct-series-f .ct-bar,
.ct-chart .ct-series.ct-series-f .ct-line,
.ct-chart .ct-series.ct-series-f .ct-point,
.ct-chart .ct-series.ct-series-f .ct-slice-donut {
  stroke: #3db9dc; }

.ct-chart .ct-series.ct-series-g .ct-bar,
.ct-chart .ct-series.ct-series-g .ct-line,
.ct-chart .ct-series.ct-series-g .ct-point,
.ct-chart .ct-series.ct-series-g .ct-slice-donut {
  stroke: #ff5d48; }

.ct-series-a .ct-area,
.ct-series-a .ct-slice-pie {
  fill: #64b0f2; }

.ct-series-b .ct-area,
.ct-series-b .ct-slice-pie {
  fill: #1bb99a; }

.ct-series-c .ct-area,
.ct-series-c .ct-slice-pie {
  fill: #ffff48; }

.ct-series-d .ct-area,
.ct-series-d .ct-slice-pie {
  fill: #1bb99a; }

.ct-area {
  fill-opacity: .33; }

.chartist-tooltip {
  position: absolute;
  display: inline-block;
  opacity: 0;
  min-width: 10px;
  padding: 2px 10px;
  border-radius: 3px;
  background: #343a40;
  color: #efefef;
  text-align: center;
  pointer-events: none;
  z-index: 1;
  -webkit-transition: opacity .2s linear;
  transition: opacity .2s linear; }
  .chartist-tooltip.tooltip-show {
    opacity: 1; }

.chartjs-chart {
  margin: auto;
  position: relative;
  width: 100%; }

.chartjs-chart-example {
  height: 300px; }

.c3-tooltip {
  -webkit-box-shadow: 0 1rem 3rem rgba(0, 0, 0, 0.175);
          box-shadow: 0 1rem 3rem rgba(0, 0, 0, 0.175);
  opacity: 1; }
  .c3-tooltip td {
    border-left: none;
    font-family: "Roboto", sans-serif; }
    .c3-tooltip td > span {
      background: #343a40; }
  .c3-tooltip tr {
    border: none !important; }
  .c3-tooltip th {
    background-color: #343a40;
    color: #f8f9fa; }

.c3-chart-arcs-title {
  font-size: 18px;
  font-weight: 600; }

.c3 text {
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, "Noto Sans", sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol", "Noto Color Emoji";
  fill: #6c757d; }

.c3 line, .c3 path {
  stroke: #ced4da; }

.c3-chart-arc.c3-target g path {
  stroke: #fff; }

.jqstooltip {
  -webkit-box-sizing: content-box;
          box-sizing: content-box;
  width: auto !important;
  height: auto !important;
  background-color: #343a40 !important;
  -webkit-box-shadow: 0 1rem 3rem rgba(0, 0, 0, 0.175);
          box-shadow: 0 1rem 3rem rgba(0, 0, 0, 0.175);
  padding: 5px 10px !important;
  border-radius: 3px;
  border-color: #212529 !important; }

.jqsfield {
  color: #e9ecef !important;
  font-size: 12px !important;
  line-height: 18px !important;
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, "Noto Sans", sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol", "Noto Color Emoji" !important;
  font-weight: 500 !important; }

.button-list {
  margin-left: -8px;
  margin-bottom: -12px; }
  .button-list .btn {
    margin-bottom: 12px;
    margin-left: 8px; }

.icons-list-demo div {
  cursor: pointer;
  line-height: 45px;
  white-space: nowrap;
  text-overflow: ellipsis;
  display: block;
  overflow: hidden; }
  .icons-list-demo div p {
    margin-bottom: 0;
    line-height: inherit; }

.icons-list-demo i {
  text-align: center;
  vertical-align: middle;
  font-size: 22px;
  width: 50px;
  height: 50px;
  line-height: 50px;
  margin-right: 12px;
  color: #6c757d;
  border: 1px solid #efefef;
  border-radius: 3px;
  display: inline-block;
  -webkit-transition: all 0.2s;
  transition: all 0.2s; }

.icons-list-demo .col-lg-4 {
  background-clip: padding-box;
  margin-top: 10px; }
  .icons-list-demo .col-lg-4:hover i {
    color: #64b0f2; }

.grid-structure .grid-container {
  background-color: #f8f9fa;
  margin-top: 10px;
  font-size: .8rem;
  font-weight: 500;
  padding: 10px 20px; }

.demos-show-btn {
  position: fixed;
  top: 50%;
  right: 0;
  -webkit-writing-mode: vertical-rl;
      -ms-writing-mode: tb-rl;
          writing-mode: vertical-rl;
  font-weight: 600;
  background-color: #ff5d48;
  color: #fff !important;
  line-height: 36px;
  padding: 15px 3px;
  border-radius: 6px 0 0 6px;
  -webkit-transform: translateY(-50%);
          transform: translateY(-50%);
  text-transform: uppercase; }

@media (max-width: 600px) {
  .demos-show-btn {
    display: none; } }

.authentication-bg {
  background-color: #64b0f2; }

.account-card-box {
  background-color: #fff;
  padding: 7px;
  border-radius: 8px; }
  .account-card-box .card {
    border: 4px solid #64b0f2; }

.card-pricing {
  -webkit-box-shadow: 0 0.75rem 6rem rgba(73, 80, 87, 0.03);
          box-shadow: 0 0.75rem 6rem rgba(73, 80, 87, 0.03); }
  .card-pricing .card-pricing-features li {
    padding: 15px; }
  .card-pricing .card-price {
    font-size: 48px;
    font-weight: 300; }
  .card-pricing.active {
    margin: 0 -24px;
    -webkit-box-shadow: 0px 0px 35px 0px rgba(73, 80, 87, 0.15);
            box-shadow: 0px 0px 35px 0px rgba(73, 80, 87, 0.15); }

.maintenance-icon {
  height: 220px;
  width: 220px;
  margin: 0 auto; }

.line1 {
  opacity: 0;
  -webkit-animation: fadeInLeft both 1s 0.4s, coding1 ease 6s 4s infinite;
          animation: fadeInLeft both 1s 0.4s, coding1 ease 6s 4s infinite; }

.line2 {
  opacity: 0;
  -webkit-animation: fadeInLeft both 1s 0.6s, coding2 ease 6s 4s infinite;
          animation: fadeInLeft both 1s 0.6s, coding2 ease 6s 4s infinite; }

.line3 {
  opacity: 0;
  -webkit-animation: fadeInLeft both 1s 0.8s, coding3 ease 6s 4s infinite;
          animation: fadeInLeft both 1s 0.8s, coding3 ease 6s 4s infinite; }

.line4 {
  opacity: 0;
  -webkit-animation: fadeInLeft both 1s 1.0s, coding4 ease 6s 4s infinite;
          animation: fadeInLeft both 1s 1.0s, coding4 ease 6s 4s infinite; }

.line5 {
  opacity: 0;
  -webkit-animation: fadeInLeft both 1s 1.2s, coding5 ease 6s 4s infinite;
          animation: fadeInLeft both 1s 1.2s, coding5 ease 6s 4s infinite; }

.line6 {
  opacity: 0;
  -webkit-animation: fadeInLeft both 1s 1.4s, coding6 ease 6s 4s infinite;
          animation: fadeInLeft both 1s 1.4s, coding6 ease 6s 4s infinite; }

.line7 {
  opacity: 0;
  -webkit-animation: fadeInLeft both 1s 1.6s, coding6 ease 6s 4s infinite;
          animation: fadeInLeft both 1s 1.6s, coding6 ease 6s 4s infinite; }

@-webkit-keyframes coding1 {
  0% {
    -webkit-transform: translate(0, 0);
            transform: translate(0, 0);
    opacity: 1; }
  14% {
    -webkit-transform: translate(0, -10px);
            transform: translate(0, -10px);
    opacity: 0; }
  15% {
    -webkit-transform: translate(0, 45px);
            transform: translate(0, 45px); }
  30% {
    -webkit-transform: translate(0, 40px);
            transform: translate(0, 40px);
    opacity: 1; }
  45% {
    -webkit-transform: translate(0, 30px);
            transform: translate(0, 30px); }
  60% {
    -webkit-transform: translate(0, 20px);
            transform: translate(0, 20px); }
  75% {
    -webkit-transform: translate(0, 10px);
            transform: translate(0, 10px); }
  90% {
    -webkit-transform: translate(0, 5px);
            transform: translate(0, 5px); }
  100% {
    -webkit-transform: translate(0, 0);
            transform: translate(0, 0);
    opacity: 1; } }

@keyframes coding1 {
  0% {
    -webkit-transform: translate(0, 0);
            transform: translate(0, 0);
    opacity: 1; }
  14% {
    -webkit-transform: translate(0, -10px);
            transform: translate(0, -10px);
    opacity: 0; }
  15% {
    -webkit-transform: translate(0, 45px);
            transform: translate(0, 45px); }
  30% {
    -webkit-transform: translate(0, 40px);
            transform: translate(0, 40px);
    opacity: 1; }
  45% {
    -webkit-transform: translate(0, 30px);
            transform: translate(0, 30px); }
  60% {
    -webkit-transform: translate(0, 20px);
            transform: translate(0, 20px); }
  75% {
    -webkit-transform: translate(0, 10px);
            transform: translate(0, 10px); }
  90% {
    -webkit-transform: translate(0, 5px);
            transform: translate(0, 5px); }
  100% {
    -webkit-transform: translate(0, 0);
            transform: translate(0, 0);
    opacity: 1; } }

@-webkit-keyframes coding2 {
  0% {
    -webkit-transform: translate(0, 0);
            transform: translate(0, 0);
    opacity: 1; }
  15% {
    -webkit-transform: translate(0, -5px);
            transform: translate(0, -5px);
    opacity: 1; }
  29% {
    -webkit-transform: translate(0, -10px);
            transform: translate(0, -10px);
    opacity: 0; }
  30% {
    -webkit-transform: translate(0, 40px);
            transform: translate(0, 40px); }
  45% {
    -webkit-transform: translate(0, 30px);
            transform: translate(0, 30px);
    opacity: 1; }
  60% {
    -webkit-transform: translate(0, 20px);
            transform: translate(0, 20px); }
  75% {
    -webkit-transform: translate(0, 10px);
            transform: translate(0, 10px); }
  90% {
    -webkit-transform: translate(0, 5px);
            transform: translate(0, 5px); }
  100% {
    -webkit-transform: translate(0, 0);
            transform: translate(0, 0);
    opacity: 1; } }

@keyframes coding2 {
  0% {
    -webkit-transform: translate(0, 0);
            transform: translate(0, 0);
    opacity: 1; }
  15% {
    -webkit-transform: translate(0, -5px);
            transform: translate(0, -5px);
    opacity: 1; }
  29% {
    -webkit-transform: translate(0, -10px);
            transform: translate(0, -10px);
    opacity: 0; }
  30% {
    -webkit-transform: translate(0, 40px);
            transform: translate(0, 40px); }
  45% {
    -webkit-transform: translate(0, 30px);
            transform: translate(0, 30px);
    opacity: 1; }
  60% {
    -webkit-transform: translate(0, 20px);
            transform: translate(0, 20px); }
  75% {
    -webkit-transform: translate(0, 10px);
            transform: translate(0, 10px); }
  90% {
    -webkit-transform: translate(0, 5px);
            transform: translate(0, 5px); }
  100% {
    -webkit-transform: translate(0, 0);
            transform: translate(0, 0);
    opacity: 1; } }

@-webkit-keyframes coding3 {
  0% {
    -webkit-transform: translate(0, 0);
            transform: translate(0, 0);
    opacity: 1; }
  15% {
    -webkit-transform: translate(0, -5px);
            transform: translate(0, -5px); }
  30% {
    -webkit-transform: translate(0, -10px);
            transform: translate(0, -10px);
    opacity: 1; }
  44% {
    -webkit-transform: translate(0, -20px);
            transform: translate(0, -20px);
    opacity: 0; }
  45% {
    -webkit-transform: translate(0, 30px);
            transform: translate(0, 30px); }
  60% {
    -webkit-transform: translate(0, 20px);
            transform: translate(0, 20px);
    opacity: 1; }
  75% {
    -webkit-transform: translate(0, 10px);
            transform: translate(0, 10px); }
  90% {
    -webkit-transform: translate(0, 5px);
            transform: translate(0, 5px); }
  100% {
    -webkit-transform: translate(0, 0);
            transform: translate(0, 0);
    opacity: 1; } }

@keyframes coding3 {
  0% {
    -webkit-transform: translate(0, 0);
            transform: translate(0, 0);
    opacity: 1; }
  15% {
    -webkit-transform: translate(0, -5px);
            transform: translate(0, -5px); }
  30% {
    -webkit-transform: translate(0, -10px);
            transform: translate(0, -10px);
    opacity: 1; }
  44% {
    -webkit-transform: translate(0, -20px);
            transform: translate(0, -20px);
    opacity: 0; }
  45% {
    -webkit-transform: translate(0, 30px);
            transform: translate(0, 30px); }
  60% {
    -webkit-transform: translate(0, 20px);
            transform: translate(0, 20px);
    opacity: 1; }
  75% {
    -webkit-transform: translate(0, 10px);
            transform: translate(0, 10px); }
  90% {
    -webkit-transform: translate(0, 5px);
            transform: translate(0, 5px); }
  100% {
    -webkit-transform: translate(0, 0);
            transform: translate(0, 0);
    opacity: 1; } }

@-webkit-keyframes coding4 {
  0% {
    -webkit-transform: translate(0, 0);
            transform: translate(0, 0);
    opacity: 1; }
  15% {
    -webkit-transform: translate(0, -5px);
            transform: translate(0, -5px); }
  30% {
    -webkit-transform: translate(0, -10px);
            transform: translate(0, -10px); }
  45% {
    -webkit-transform: translate(0, -20px);
            transform: translate(0, -20px);
    opacity: 1; }
  59% {
    -webkit-transform: translate(0, -30px);
            transform: translate(0, -30px);
    opacity: 0; }
  60% {
    -webkit-transform: translate(0, 20px);
            transform: translate(0, 20px); }
  75% {
    -webkit-transform: translate(0, 10px);
            transform: translate(0, 10px);
    opacity: 1; }
  90% {
    -webkit-transform: translate(0, 5px);
            transform: translate(0, 5px); }
  100% {
    -webkit-transform: translate(0, 0);
            transform: translate(0, 0);
    opacity: 1; } }

@keyframes coding4 {
  0% {
    -webkit-transform: translate(0, 0);
            transform: translate(0, 0);
    opacity: 1; }
  15% {
    -webkit-transform: translate(0, -5px);
            transform: translate(0, -5px); }
  30% {
    -webkit-transform: translate(0, -10px);
            transform: translate(0, -10px); }
  45% {
    -webkit-transform: translate(0, -20px);
            transform: translate(0, -20px);
    opacity: 1; }
  59% {
    -webkit-transform: translate(0, -30px);
            transform: translate(0, -30px);
    opacity: 0; }
  60% {
    -webkit-transform: translate(0, 20px);
            transform: translate(0, 20px); }
  75% {
    -webkit-transform: translate(0, 10px);
            transform: translate(0, 10px);
    opacity: 1; }
  90% {
    -webkit-transform: translate(0, 5px);
            transform: translate(0, 5px); }
  100% {
    -webkit-transform: translate(0, 0);
            transform: translate(0, 0);
    opacity: 1; } }

@-webkit-keyframes coding5 {
  0% {
    -webkit-transform: translate(0, 0);
            transform: translate(0, 0);
    opacity: 1; }
  15% {
    -webkit-transform: translate(0, -5px);
            transform: translate(0, -5px); }
  30% {
    -webkit-transform: translate(0, -10px);
            transform: translate(0, -10px); }
  45% {
    -webkit-transform: translate(0, -20px);
            transform: translate(0, -20px); }
  60% {
    -webkit-transform: translate(0, -30px);
            transform: translate(0, -30px);
    opacity: 1; }
  74% {
    -webkit-transform: translate(0, -40px);
            transform: translate(0, -40px);
    opacity: 0; }
  75% {
    -webkit-transform: translate(0, 10px);
            transform: translate(0, 10px); }
  90% {
    -webkit-transform: translate(0, 5px);
            transform: translate(0, 5px);
    opacity: 1; }
  100% {
    -webkit-transform: translate(0, 0);
            transform: translate(0, 0);
    opacity: 1; } }

@keyframes coding5 {
  0% {
    -webkit-transform: translate(0, 0);
            transform: translate(0, 0);
    opacity: 1; }
  15% {
    -webkit-transform: translate(0, -5px);
            transform: translate(0, -5px); }
  30% {
    -webkit-transform: translate(0, -10px);
            transform: translate(0, -10px); }
  45% {
    -webkit-transform: translate(0, -20px);
            transform: translate(0, -20px); }
  60% {
    -webkit-transform: translate(0, -30px);
            transform: translate(0, -30px);
    opacity: 1; }
  74% {
    -webkit-transform: translate(0, -40px);
            transform: translate(0, -40px);
    opacity: 0; }
  75% {
    -webkit-transform: translate(0, 10px);
            transform: translate(0, 10px); }
  90% {
    -webkit-transform: translate(0, 5px);
            transform: translate(0, 5px);
    opacity: 1; }
  100% {
    -webkit-transform: translate(0, 0);
            transform: translate(0, 0);
    opacity: 1; } }

@-webkit-keyframes coding6 {
  0% {
    -webkit-transform: translate(0, 0);
            transform: translate(0, 0);
    opacity: 1; }
  15% {
    -webkit-transform: translate(0, -5px);
            transform: translate(0, -5px); }
  30% {
    -webkit-transform: translate(0, -10px);
            transform: translate(0, -10px); }
  45% {
    -webkit-transform: translate(0, -20px);
            transform: translate(0, -20px); }
  60% {
    -webkit-transform: translate(0, -30px);
            transform: translate(0, -30px); }
  75% {
    -webkit-transform: translate(0, -40px);
            transform: translate(0, -40px);
    opacity: 1; }
  89% {
    -webkit-transform: translate(0, -50px);
            transform: translate(0, -50px);
    opacity: 0; }
  90% {
    -webkit-transform: translate(0, 10px);
            transform: translate(0, 10px); }
  100% {
    -webkit-transform: translate(0, 0);
            transform: translate(0, 0);
    opacity: 1; } }

@keyframes coding6 {
  0% {
    -webkit-transform: translate(0, 0);
            transform: translate(0, 0);
    opacity: 1; }
  15% {
    -webkit-transform: translate(0, -5px);
            transform: translate(0, -5px); }
  30% {
    -webkit-transform: translate(0, -10px);
            transform: translate(0, -10px); }
  45% {
    -webkit-transform: translate(0, -20px);
            transform: translate(0, -20px); }
  60% {
    -webkit-transform: translate(0, -30px);
            transform: translate(0, -30px); }
  75% {
    -webkit-transform: translate(0, -40px);
            transform: translate(0, -40px);
    opacity: 1; }
  89% {
    -webkit-transform: translate(0, -50px);
            transform: translate(0, -50px);
    opacity: 0; }
  90% {
    -webkit-transform: translate(0, 10px);
            transform: translate(0, 10px); }
  100% {
    -webkit-transform: translate(0, 0);
            transform: translate(0, 0);
    opacity: 1; } }

.diamond {
  margin: 50px auto;
  height: 90px;
  width: 120px; }
  .diamond:after {
    content: "";
    position: absolute;
    height: 14px;
    width: 44px;
    background: rgba(52, 58, 64, 0.1);
    border-radius: 50%;
    margin-top: 0;
    margin-left: 38px;
    z-index: 11; }
  .diamond .top {
    height: 30px;
    border-left: 27px solid transparent;
    border-right: 27px solid transparent;
    border-bottom: 24px solid #1ecba9; }
    .diamond .top:after {
      content: "";
      position: absolute;
      height: 24px;
      width: 32px;
      margin-top: 6px;
      margin-left: 47px;
      background: #1bb99a;
      -webkit-transform: skew(30deg, 20deg);
      transform: skew(48deg); }
    .diamond .top:before {
      content: "";
      position: absolute;
      height: 24px;
      width: 32px;
      margin-top: 7px;
      margin-left: -13px;
      background: #1bb99a;
      -webkit-transform: skew(-48deg);
              transform: skew(-48deg); }
  .diamond .bot {
    height: 60px;
    border-left: 60px solid transparent;
    border-right: 60px solid transparent;
    border-top: 60px solid #1ecba9; }
    .diamond .bot:before {
      content: "";
      position: absolute;
      height: 60px;
      margin-top: -60px;
      margin-left: -27px;
      border-left: 27px solid transparent;
      border-right: 26px solid transparent;
      border-top: 60px solid #1bb99a; }

.timeline {
  border-collapse: collapse;
  border-spacing: 0;
  display: table;
  margin-bottom: 50px;
  position: relative;
  table-layout: fixed;
  width: 100%; }
  .timeline .time-show {
    margin-bottom: 30px;
    margin-right: -75px;
    margin-top: 30px;
    position: relative;
    text-align: right; }
    .timeline .time-show a {
      color: #fff; }
  .timeline:before {
    background-color: rgba(173, 181, 189, 0.3);
    bottom: 0px;
    content: "";
    left: 50%;
    position: absolute;
    top: 30px;
    width: 2px;
    z-index: 0; }
  .timeline .timeline-icon {
    background: #6c757d;
    border-radius: 50%;
    color: #fff;
    display: block;
    height: 21px;
    left: -54px;
    margin-top: -11px;
    position: absolute;
    text-align: center;
    top: 50%;
    width: 21px; }
    .timeline .timeline-icon i {
      color: #e9ecef; }
  .timeline .time-icon:before {
    font-size: 16px;
    margin-top: 5px; }

h3.timeline-title {
  color: #6c757d;
  font-size: 20px;
  font-weight: 400;
  margin: 0 0 5px;
  text-transform: uppercase; }

.timeline-item {
  display: table-row; }
  .timeline-item:before {
    content: "";
    display: block;
    width: 50%; }
  .timeline-item .timeline-desk .arrow {
    border-bottom: 8px solid transparent;
    border-right: 8px solid #fff !important;
    border-top: 8px solid transparent;
    display: block;
    height: 0;
    left: -7px;
    margin-top: -10px;
    position: absolute;
    top: 50%;
    width: 0; }

.timeline-item.alt:after {
  content: "";
  display: block;
  width: 50%; }

.timeline-item.alt .timeline-desk .arrow-alt {
  border-bottom: 8px solid transparent;
  border-left: 8px solid #fff !important;
  border-top: 8px solid transparent;
  display: block;
  height: 0;
  left: auto;
  margin-top: -10px;
  position: absolute;
  right: -7px;
  top: 50%;
  width: 0; }

.timeline-item.alt .timeline-desk .album {
  float: right;
  margin-top: 20px; }
  .timeline-item.alt .timeline-desk .album a {
    float: right;
    margin-left: 5px; }

.timeline-item.alt .timeline-icon {
  left: auto;
  right: -58px; }

.timeline-item.alt:before {
  display: none; }

.timeline-item.alt .panel {
  margin-left: 0;
  margin-right: 45px; }
  .timeline-item.alt .panel .panel-body p + p {
    margin-top: 10px !important; }

.timeline-item.alt h4 {
  text-align: right; }

.timeline-item.alt p {
  text-align: right; }

.timeline-item.alt .timeline-date {
  text-align: right; }

.timeline-desk {
  display: table-cell;
  vertical-align: top;
  width: 50%; }
  .timeline-desk h4 {
    font-size: 16px;
    font-weight: 300;
    margin: 0; }
  .timeline-desk .panel {
    background: #fff;
    display: block;
    margin-bottom: 5px;
    margin-left: 45px;
    position: relative;
    text-align: left;
    padding: 20px;
    border-radius: 7px;
    -webkit-box-shadow: 0 0.75rem 6rem rgba(73, 80, 87, 0.03);
            box-shadow: 0 0.75rem 6rem rgba(73, 80, 87, 0.03); }
  .timeline-desk h5 span {
    color: #e9ecef;
    display: block;
    font-size: 12px;
    margin-bottom: 4px; }
  .timeline-desk p {
    color: #6c757d;
    font-size: 14px;
    margin-bottom: 0; }
  .timeline-desk .album {
    margin-top: 12px; }
    .timeline-desk .album a {
      float: left;
      margin-right: 5px; }
    .timeline-desk .album img {
      height: 36px;
      width: auto;
      border-radius: 3px; }
  .timeline-desk .notification {
    background: none repeat scroll 0 0 #fff;
    margin-top: 20px;
    padding: 8px; }

.text-error {
  color: #fff;
  font-size: 98px;
  line-height: 150px; }
  .text-error.shadow-text {
    text-shadow: rgba(255, 255, 255, 0.3) 5px 1px, rgba(255, 255, 255, 0.2) 12px 3px, rgba(255, 255, 255, 0.1) 6px 4px; }

.portfolioFilter a {
  -webkit-transition: all 0.3s ease-out;
  transition: all 0.3s ease-out;
  color: #343a40;
  border-radius: 3px;
  padding: 5px 10px;
  display: inline-block;
  font-size: 13px;
  font-weight: 500;
  text-transform: uppercase; }
  .portfolioFilter a:hover {
    color: #1bb99a; }

.portfolioFilter a.current {
  background-color: #1bb99a;
  color: #fff; }

.gallery-box {
  background-color: #fff;
  margin-top: 24px;
  border-radius: 4px;
  overflow: hidden; }
  .gallery-box a {
    display: block;
    background-color: #060708;
    overflow: hidden; }
  .gallery-box:hover .thumb-img {
    position: relative;
    -webkit-transform: scale(1.05);
            transform: scale(1.05);
    opacity: 0.7; }

.thumb-img {
  overflow: hidden;
  width: 100%;
  -webkit-transition: all 0.2s ease-out;
  transition: all 0.2s ease-out; }

.counter-number {
  font-size: 60px;
  font-weight: 600;
  text-align: center;
  color: #343a40; }
  .counter-number span {
    font-size: 16px;
    font-weight: 400;
    display: block;
    text-transform: uppercase;
    padding-top: 5px;
    color: #1bb99a; }

.coming-box {
  float: left;
  width: 25%; }
