<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\Site;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Str;

class SiteController extends Controller
{
    public function index()
    {
        $sites = Site::all();
        return view('warehouse.sitemanagemen', compact('sites'));
    }

    public function getSites(Request $request)
    {
        $perPage = $request->input('per_page', 5); // Default to 5 items per page
        $page = $request->input('page', 1);

        // Get total count
        $total = Site::count();

        // Get paginated data
        $sites = Site::skip(($page - 1) * $perPage)
                    ->take($perPage)
                    ->get();

        return response()->json([
            'data' => $sites,
            'current_page' => (int)$page,
            'per_page' => (int)$perPage,
            'last_page' => ceil($total / $perPage),
            'total' => $total
        ]);
    }

    public function store(Request $request)
    {
        try {
            $validator = Validator::make($request->all(), [
                'site_name' => 'required|string|max:255',
                'address' => 'nullable|string|max:255',
            ]);

            if ($validator->fails()) {
                return response()->json(['errors' => $validator->errors()], 422);
            }
            // Generate Site ID
            $siteId = 'SID' . Str::random(10); // Awalan SID + 10 karakter random

            // Pastikan ID unik (opsional, tapi disarankan)
            while (Site::where('site_id', $siteId)->exists()) {
                $siteId = 'SID' . Str::random(10); // Generate lagi jika sudah ada
            }
            $site = Site::create([
                'site_id' => $siteId,
                'site_name' => strtoupper($request->input('site_name')),
                'address' =>strtoupper($request->input('address')),
            ]);

            return response()->json(['data' => $site, 'message' => 'Site created successfully'], 201); // Respon sukses
        } catch (\Exception $e) {
            return response()->json(['error' => 'Failed to create site: ' . $e->getMessage()], 500);
        }
    }

    public function edit($id)
    {
        $site = Site::find($id);

        if (!$site) {
            return response()->json(['error' => 'Site not found'], 404);
        }

        return response()->json(['data' => $site]); // Respon dengan struktur data
    }

    public function update(Request $request, $id)
    {
        try {
            $site = Site::findOrFail($id);

            $validator = Validator::make($request->all(), [
                'site_name' => 'required|string|max:255',
                'address' => 'nullable|string|max:255',
            ]);

            if ($validator->fails()) {
                return response()->json(['errors' => $validator->errors()], 422);
            }

            $site->update([
                'site_name' => strtoupper($request->input('site_name')),
                'address' => strtoupper($request->input('address')),
            ]);

            return response()->json(['data' => $site, 'message' => 'Site updated successfully']); // Respon sukses
        } catch (\Exception $e) {
            return response()->json(['error' => 'Failed to update site: ' . $e->getMessage()], 500);
        }
    }

    public function destroy($id)
    {
        try {
            $site = Site::findOrFail($id);
            $site->delete();

            return response()->json(['message' => 'Site deleted successfully'], 200); // Respon sukses
        } catch (\Exception $e) {
            return response()->json(['error' => 'Failed to delete site: ' . $e->getMessage()], 500);
        }
    }
}
