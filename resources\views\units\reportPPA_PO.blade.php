<!DOCTYPE html>
<html>

<head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
    <title>Berita Acara Pemakaian Part</title>
    <style>
        body {
            font-family: 'Times New Roman', Times, serif;
            font-size: 11px;
            line-height: 1.4;
            margin: 20px;
        }

        /* Header styles */
        .header-container {
            width: 100%;
            margin-bottom: 15px;
        }

        .logo-left {
            float: left;
            width: 20%;
        }

        .title-center {
            float: left;
            width: 60%;
            text-align: center;
        }

        .number-right {
            float: right;
            width: 20%;
            text-align: right;
        }

        .header-title {
            background-color:rgb(29, 87, 138);
            color: white;
            padding: 8px;
            font-size: 14px;
            font-weight: bold;
            text-align: center;
            margin-bottom: 15px;
        }

        /* Customer info table */
        .customer-table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 15px;
        }

        .customer-table td {
            border: 1px solid #000;
            padding: 1px;
            padding-left: 4px;
        }

        .customer-table .label {
            width: 10%;
            font-weight: normal;
        }

        /* Main table styles */
        .main-table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 20px;
        }

        .main-table th,
        .main-table td {
            border: 1px solid #000;
            padding: 1px;
            text-align: center;
            font-size: 10px;
        }

        .main-table th {
            background-color:rgb(29, 87, 138);
            color: white;
            font-weight: bold;
        }

        /* Total section */
        .total-section {
            width: 30%;
            float: right;
            margin-bottom: 20px;
        }

        .total-table {
            width: 100%;
            border-collapse: collapse;
        }

        .total-table td {
            border: 1px solid #000;
            padding: 1px;
            text-align: left;
        }

        .total-table .total-label {
            font-weight: bold;
        }

        /* Signature section */
        .signature-table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 10px;
        }

        .signature-table td {
            width: 50%;
            text-align: center;
            vertical-align: bottom;
            font-weight: bold;
            font-size: 10px;
        }

        .signature-stamp {
            width: 100px;
            height: 100px;
            margin: 0 auto;
            position: relative;
        }

        .clearfix::after {
            content: "";
            clear: both;
            display: table;
        }

        /* Adjust for landscape orientation */
        @page {
            size: landscape;
        }
    </style>
</head>

<body>
    <!-- Header with logo, title and number -->
    <div class="header-container clearfix">
        <div class="logo-left" style="text-align: center;">
            <img src="{{ public_path('assets/images/logo-small.png') }}" alt="PWB LOGO" style="width: 80px;">
            <p><strong>PT PUTERA WIBOWO BORNEO</strong></p>
        </div>
        <div class="title-center">
            <div class="header-title">BERITA ACARA PEMAKAIAN PART SITE PPA (DELIVERY ORDER)</div>
        </div>
        <div class="number-right" style="padding-top: 60px;">
            <div style="font-size: 15px; font-weight: bold; border: 1px solid #000; padding: 10px; text-align: center; background-color:rgb(29, 87, 138); color: white;">{{ $transactions[0]->do_number ?? '' }}
            </div>
        </div>
    </div>


    <!-- Customer Information -->
    <table class="customer-table">
        <tr style="width: 20%;">
            <td class="label">CUSTOMER</td>
            <td>PT. PUTRA PERKASA ABADI</td>
            <td class="label">CONTACT</td>
            <td style="width: 20%;">{{ $transactions[0]->contact ?? '' }}</td>
        </tr>
        <tr style="width: 100%;">
            <td class="label">CODE AREA</td>
            <td>SITE BIB</td>
            <td class="label">PHONE</td>
            <td>{{ $transactions[0]->phone ?? '' }}</td>
        </tr>
        <tr>
            <td class="label">PAYMENT</td>
            <td>60 DAYS</td>
            <td class="label">DATE CREATED</td>
            <td style="width: 20%;">{{ \Carbon\Carbon::parse($transactions[0]->do_date ?? \Carbon\Carbon::now())->locale('id')->isoFormat('D MMMM Y') }}</td>
        </tr>
    </table>

    <!-- Main Content Table -->
    <table class="main-table">
        <thead>
            <tr>
                <th rowspan="2" style="width:3%;">NO</th>
                <th colspan="2" style="width:18%;">SLIP STORE</th> <!-- Gabungan WO dan DATE -->
                <th rowspan="2" style="width:10%;">UNIT</th>
                <th rowspan="2" style="width:14%;">PART NUMBER</th>
                <th rowspan="2" style="width:28%;">DESCRIPTION</th>
                <th rowspan="2" style="width:4%;">QTY</th>
                <th rowspan="2" style="width:4%;">UOM</th>
                <th rowspan="2" style="width:10%;">UNIT PRICE</th>
                <th rowspan="2" style="width:13%;">TOTAL PRICE</th>
            </tr>
            <tr>
                <th style="width:10%;">WO</th>
                <th style="width:8%;">DATE</th>
            </tr>
        </thead>

        <tbody>
            @if(count($transactions) > 0)
            @php
            $no = 1;
            $totalAmount = 0;
            $currentWO = null;
            $currentDate = null;
            $currentUnit = null;
            $woRowspan = 0;
            $dateRowspan = 0;
            $unitRowspan = 0;
            $groupedData = [];

            // First, group the data by WO, Date, and Unit
            foreach($transactions as $transaction) {
                $mrDate = $transaction->mr_date ? \Carbon\Carbon::parse($transaction->mr_date)->locale('id')->isoFormat('D MMMM Y') : '';
                $woKey = $transaction->wo_number ?? '';
                $dateKey = $mrDate;
                $unitKey = $transaction->unit->unit_code ?? '';

                // Create a unique key for this combination
                $groupKey = $woKey . '|' . $dateKey . '|' . $unitKey;

                if (!isset($groupedData[$groupKey])) {
                    $groupedData[$groupKey] = [
                        'wo' => $woKey,
                        'date' => $dateKey,
                        'unit' => $unitKey,
                        'parts' => [],
                        'rowspan' => 0
                    ];
                }
                foreach($transaction->parts as $part) {
                    $subtotal = $part->price * $part->quantity;
                    $totalAmount += $subtotal;

                    $groupedData[$groupKey]['parts'][] = [
                        'part_code' => $part->partInventory->part->part_code,
                        'part_name' => $part->partInventory->part->part_name,
                        'quantity' => $part->quantity,
                        'eum' => $part->eum,
                        'price' => $part->price,
                        'subtotal' => $subtotal,
                        'id'  => $part->id,
                    ];
                    $groupedData[$groupKey]['rowspan']++;
                }
                // After all parts for the current $groupKey are added, sort them by 'id'
                if (isset($groupedData[$groupKey]['parts']) && is_array($groupedData[$groupKey]['parts'])) {
                    usort($groupedData[$groupKey]['parts'], function ($a, $b) {
                        return $a['id'] <=> $b['id'];
                    });
                }
            }
            @endphp

            @foreach($groupedData as $group)
                @foreach($group['parts'] as $index => $part)
                    <tr>
                        <td>{{ $no++ }}</td>

                        @if($index === 0)
                            <td rowspan="{{ $group['rowspan'] }}">{{ $group['wo'] }}</td>
                            <td rowspan="{{ $group['rowspan'] }}">{{ $group['date'] }}</td>
                            <td rowspan="{{ $group['rowspan'] }}">{{ $group['unit'] }}</td>
                        @endif

                        <td>{{ $part['part_code'] }}</td>
                        <td style="text-align: center;">{{ $part['part_name'] }}</td>
                        <td>{{ $part['quantity'] }}</td>
                        <td>{{ $part['eum'] }}</td>
                        <td style="text-align: right;"><span style="float: left; padding-left: 4px">Rp</span> {{ number_format($part['price'], 0, ',', '.') }}</td>
                        <td style="text-align: right;"><span style="float: left; padding-left: 4px">Rp</span> {{ number_format($part['subtotal'], 0, ',', '.') }}</td>
                    </tr>
                @endforeach
            @endforeach

            <!-- Add empty rows to make the table look complete -->
            @for($i = $no; $i <= 10; $i++)
                <tr>
                <td>{{ $i }}</td>
                <td></td>
                <td></td>
                <td></td>
                <td></td>
                <td></td>
                <td></td>
                <td></td>
                <td></td>
                <td></td>
                </tr>
                @endfor
                @else
                @for($i = 1; $i <= 10; $i++)
                    <tr>
                    <td>{{ $i }}</td>
                    <td></td>
                    <td></td>
                    <td></td>
                    <td></td>
                    <td></td>
                    <td></td>
                    <td></td>
                    <td></td>
                    <td></td>
                    </tr>
                    @endfor
                    @endif
                    <tr>
                        <td style="border: none;"></td>
                        <td style="border: none;"></td>
                        <td style="border: none;"></td>
                        <td style="border: none;"></td>
                        <td style="border: none;"></td>
                        <td style="border: none;"></td>
                        <td style="border: none;"></td>
                        <td style="border: none;"></td>
                        <td style="text-align: left;">TOTAL</td>
                        <td style="text-align: right;"><span style="float: left; padding-left: 4px">Rp</span> {{ number_format($totalAmount ?? 0, 0, ',', '.') }}</td>
                    </tr>
                    <tr style="background-color: #ffff">
                        <td style="border: none;"></td>
                        <td style="border: none;"></td>
                        <td style="border: none;"></td>
                        <td style="border: none;"></td>
                        <td style="border: none;"></td>
                        <td style="border: none;"></td>
                        <td style="border: none;"></td>
                        <td style="border: none;"></td>
                        <td style="text-align: left;">PPN 11%</td>
                        <td style="text-align: right;"><span style="float: left; padding-left: 4px">Rp</span> {{ number_format(($totalAmount ?? 0) * 0.11, 0, ',', '.') }}</td>
                    </tr>
                    <tr>
                        <td style="border: none;"></td>
                        <td style="border: none;"></td>
                        <td style="border: none;"></td>
                        <td style="border: none;"></td>
                        <td style="border: none;"></td>
                        <td style="border: none;"></td>
                        <td style="border: none;"></td>
                        <td style="border: none;"></td>
                        <td style="text-align: left;">GRAND TOTAL</td>
                        <td style="text-align: right;"><span style="float: left; padding-left: 4px">Rp</span> {{ number_format(($totalAmount ?? 0) * 1.11, 0, ',', '.') }}</td>
                    </tr>
        </tbody>
    </table>

    <!-- Signature Section -->
    <div class="clearfix">
        <table class="signature-table">
            <tr>
                <td>
                    <div>Dibuat Oleh</div>
                </td>
                <td>
                    <div>Diterima Oleh</div>
                </td>
            </tr>
            <tr>
                <td style="padding-top: 60px;">
                    <div>Admin Site PT. Putera Wibowo Borneo</div>
                </td>
                <td style="padding-top: 60px;">
                    <div>Logistik PT. Putera Perkasa Abadi</div>
                </td>
            </tr>
        </table>
    </div>
</body>

</html>