@extends('sales.contentsales')
@section('title', 'Daftar Invoice Selesai')
@section('resourcesales')
@vite(['resources/js/sales/completed-invoices.js'])
<style>
    .w-fit-content {
        width: fit-content;
    }
    .shadow-kit {
        border: 1px solid rgb(42, 105, 168);
        box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
        border-radius: 0.5rem;
        background-color: #fff;
    }
    .btn{
        font-size: 11px;
    }
    .btn-warning{
        background-color:rgb(242, 215, 132);
        color: #000;
    }
    .bg-warning{
        background-color:rgb(240, 250, 150);
        color: #0f0187;
    }

    /* Sortable table styles */
    .sortable {
        cursor: pointer;
        position: relative;
    }

    .sortable:hover {
        background-color: rgba(0, 0, 0, 0.05);
    }

    .sort-icon {
        font-size: 11px;
        margin-left: 5px;
        opacity: 0.5;
    }

    .sortable.asc .sort-icon {
        opacity: 1;
        transform: rotate(180deg);
    }

    .sortable.desc .sort-icon {
        opacity: 1;
        transform: rotate(0deg);
    }
</style>
@endsection
@section('contentsales')

<div class="bgwhite mb-2 p-2 pr-2">
    <div class="d-flex justify-content-right">
        <div class="nav-links">
            <a href="{{ route('sales.dashboard') }}" class="btn btn-sm {{ request()->routeIs('sales.dashboard') ? 'btn-primary' : 'btn-light' }} mx-1">
                <i class="mdi mdi-view-dashboard"></i> Dashboard
            </a>
            <a href="{{ route('sales.penawaran') }}" class="btn btn-sm {{ request()->routeIs('sales.penawaran') ? 'btn-primary' : 'btn-light' }} mx-1">
                <i class="mdi mdi-file-document-edit"></i> Buat Penawaran
            </a>
            <a href="{{ route('sales.completed-invoices') }}" class="btn btn-sm {{ request()->routeIs('sales.completed-invoices') ? 'btn-primary' : 'btn-light' }} mx-1">
                <i class="mdi mdi-file-check"></i> Invoice Selesai
            </a>
            <a href="{{ route('sales.jasa-karyawan') }}" class="btn btn-sm {{ request()->routeIs('sales.jasa-karyawan') ? 'btn-primary' : 'btn-light' }} mx-1">
                <i class="mdi mdi-account-cash"></i>Monthly Report
            </a>
            <a class="btn btn-sm btn-secondary mx-1" href="{{ route(name: 'logout') }}">
                <i class="mdi mdi-logout-variant"></i>
                <span> Logout</span>
            </a>
        </div>
    </div>
</div>

<div class="content">
    <div class="container-fluid">
        <!-- <div class="row">
            <div class="col-12">
                <div class="page-title-box">
                    <div class="page-title-right">
                        <ol class="breadcrumb m-0">
                            <li class="breadcrumb-item"><a href="{{ route('sales.dashboard') }}">Dashboard</a></li>
                            <li class="breadcrumb-item active">Invoice Selesai</li>
                        </ol>
                    </div>
                    <h4 class="page-title">Daftar Invoice Selesai</h4>
                </div>
            </div>
        </div> -->

        <div class="row">
            <div class="col-md-12">
                <div class="bgwhite shadow-kit rounded-lg">
                    <div class="card-header bg-success text-white d-flex justify-content-between align-items-center">
                        <div class="d-flex align-items-center">
                            <h5 class="mb-0 mr-3 font-bold text-uppercase text-white">Daftar Invoice Selesai</h5>
                        </div>
                        <div class="d-flex">
                            <div class="input-group input-group-sm mr-2">
                                <input type="date" id="date-from" class="form-control form-control-sm">
                            </div>
                            <div class="input-group input-group-sm mr-2">
                                <input type="date" id="date-to" class="form-control form-control-sm">
                            </div>
                            <button id="filter-button" class="btn btn-sm btn-light mr-2">Filter</button>
                            <input type="text" id="search-input" class="form-control form-control-sm" placeholder="Search...">
                        </div>
                    </div>
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table table-bordered w-100" id="completed-invoices-table" style="font-size: 11px;">
                                <thead class="bg-light">
                                    <tr>
                                        <th>No</th>
                                        <th class="sortable" data-sort="no_invoice">Nomor Invoice <i class="sort-icon mdi mdi-sort"></i></th>
                                        <th class="sortable" data-sort="unit_list">Daftar Unit <i class="sort-icon mdi mdi-sort"></i></th>
                                        <th class="sortable" data-sort="tanggal_invoice">Tanggal <i class="sort-icon mdi mdi-sort"></i></th>
                                        <th class="sortable" data-sort="customer">Customer <i class="sort-icon mdi mdi-sort"></i></th>
                                        <th class="sortable" data-sort="total_amount">Total <i class="sort-icon mdi mdi-sort"></i></th>
                                        <th class="sortable" data-sort="payment_date">Tanggal Pembayaran <i class="sort-icon mdi mdi-sort"></i></th>
                                        <th>Lampiran</th>
                                    </tr>
                                </thead>
                                <tbody id="completed-invoices-table-body">
                                    <!-- Data will be loaded dynamically -->
                                </tbody>
                            </table>
                        </div>
                        <div class="d-flex justify-content-between align-items-center mt-3">
                            <div>
                                <span id="showing-text">Menampilkan 0 dari 0 invoice</span>
                            </div>
                            <div>
                                <nav aria-label="Page navigation">
                                    <ul class="pagination pagination-sm" id="pagination">
                                        <!-- Pagination will be generated dynamically -->
                                    </ul>
                                </nav>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Invoice Detail Modal -->
<div class="modal fade" id="invoice-detail-modal" tabindex="-1" role="dialog" aria-labelledby="invoice-detail-modal-label" aria-hidden="true">
    <div class="modal-dialog modal-lg" role="document">
        <div class="modal-content">
            <div class="modal-header bg-success text-white">
                <h5 class="modal-title" id="invoice-detail-modal-label">Detail Invoice</h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <div id="invoice-detail-content">
                    <!-- Invoice details will be loaded here -->
                </div>
            </div>
            <div class="modal-footer">
                <!-- <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Tutup</button> -->
            </div>
        </div>
    </div>
</div>

<!-- Document Viewer Modal -->
<div class="modal fade" id="document-viewer-modal" tabindex="-1" role="dialog" aria-labelledby="document-viewer-modal-label" aria-hidden="true">
    <div class="modal-dialog modal-xl" role="document" style="max-width: 90%; max-height: 90vh;">
        <div class="modal-content h-100">
            <div class="modal-header bg-primary text-white">
                <h5 class="modal-title" id="document-viewer-modal-label">Dokumen Invoice</h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body p-0" style="height: 80vh;">
                <div id="document-viewer" class="h-100">
                    <!-- Document will be embedded here -->
                </div>
            </div>
            <div class="modal-footer">
                <!-- <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Tutup</button> -->
                <a id="download-document" href="#" target="_blank" class="btn btn-primary">
                    <i class="mdi mdi-download"></i> Download
                </a>
            </div>
        </div>
    </div>
</div>
@endsection
