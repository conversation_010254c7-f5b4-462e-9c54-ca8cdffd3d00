<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Supplier extends Model
{
    use HasFactory;

    protected $table = 'suppliers';
    protected $primaryKey = 'supplier_id';
    public $incrementing = false;
    protected $keyType = 'string';


    protected $fillable = [
        'supplier_id',
        'supplier_name',
        'address',
        'contact_person',
    ];

    public function warehouseInStocks()
    {
        return $this->hasMany(WarehouseInStock::class, 'supplier_id', 'supplier_id');
    }

    public function siteInStocks()
    {
        return $this->hasMany(SiteInStock::class, 'supplier_id', 'supplier_id');
    }
}
