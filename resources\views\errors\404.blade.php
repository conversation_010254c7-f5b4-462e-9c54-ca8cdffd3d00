<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="utf-8" />
    <title>Halaman Tidak Ditemukan - PWB</title>
    <link rel="shortcut icon" href="{{ asset('assets/images/logo-small.png') }}">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="csrf-token" content="{{ csrf_token() }}">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    @vite("resources/assets/css/bootstrap.min.css")
    @vite("resources/assets/css/icons.min.css")
    @vite("resources/assets/css/app.min.css")
    @vite("resources/css/app.css")
    <style>
        body {
            background-color: #e0e5ec;
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 0;
        }

        .container {
            width: 100%;
            max-width: 1200px;
            margin: 0 auto;
            display: flex;
            justify-content: center;
            align-items: center;
        }

        .row {
            width: 100%;
            display: flex;
            justify-content: center;
        }

        .col-md-8 {
            width: 100%;
            max-width: 800px;
            display: flex;
            justify-content: center;
        }

        .error-container {
            text-align: center;
            padding: 50px 40px;
            background-color: #e0e5ec;
            border-radius: 30px;
            box-shadow: 20px 20px 60px #bec5d0, -20px -20px 60px #ffffff;
            max-width: 500px;
            width: 100%;
            position: relative;
            overflow: hidden;
        }

        .logo-wrapper {
            display: flex;
            justify-content: center;
            align-items: center;
            margin-bottom: 30px;
            width: 100%;
        }

        .logo-img {
            max-width: 120px;
            filter: drop-shadow(3px 3px 5px rgba(0, 0, 0, 0.3));
            transition: all 0.4s ease;
            border-radius: 50%;
            padding: 15px;
            background-color: #e0e5ec;
            box-shadow: 8px 8px 16px #bec5d0, -8px -8px 16px #ffffff;
            margin: 0 auto;
        }

        .logo-img:hover {
            transform: scale(1.05) rotate(5deg);
            box-shadow: 6px 6px 12px #bec5d0, -6px -6px 12px #ffffff;
        }

        .error-code {
            font-size: 90px;
            font-weight: 700;
            color: #eb3124;
            margin-bottom: 15px;
            text-shadow: 3px 3px 6px rgba(0, 0, 0, 0.2);
            position: relative;
            letter-spacing: 2px;
            text-align: center;
        }

        .error-code:before {
            content: "404";
            position: absolute;
            left: 50%;
            transform: translateX(-50%);
            color: rgba(235, 49, 36, 0.08);
            font-size: 140px;
            z-index: -1;
            top: -25px;
        }

        .error-title {
            font-size: 32px;
            font-weight: 600;
            margin-bottom: 25px;
            color: #225297;
            position: relative;
            text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.1);
            text-align: center;
            width: 100%;
        }

        .error-message {
            color: #510968;
            margin-bottom: 35px;
            font-size: 17px;
            line-height: 1.7;
            padding: 0 15px;
            text-align: center;
            width: 100%;
        }

        .btn-back {
            border: none;
            position: relative;
            background-color: #e0e5ec;
            border-radius: 50px;
            padding: 15px 35px;
            font-weight: 500;
            color: #225297;
            box-shadow: 8px 8px 16px #bec5d0, -8px -8px 16px #ffffff;
            transition: all 0.3s ease;
            font-size: 16px;
            text-decoration: none;
            display: inline-block;
            letter-spacing: 0.5px;
            margin: 0 auto;
        }

        .btn-back:hover {
            box-shadow: inset 6px 6px 10px #bec5d0, inset -6px -6px 10px #ffffff;
            color: #eb3124;
            transform: translateY(2px);
        }

        .btn-back:active {
            box-shadow: inset 8px 8px 16px #bec5d0, inset -8px -8px 16px #ffffff;
        }

        .error-container:after {
            content: "";
            position: absolute;
            top: -50%;
            left: -50%;
            width: 200%;
            height: 200%;
            background: linear-gradient(
                to bottom right,
                rgba(255, 255, 255, 0) 0%,
                rgba(255, 255, 255, 0.05) 50%,
                rgba(255, 255, 255, 0) 100%
            );
            transform: rotate(30deg);
            pointer-events: none;
        }

        @media (max-width: 768px) {
            .error-container {
                margin: 0 20px;
                padding: 40px 30px;
                border-radius: 25px;
            }

            .logo-img {
                max-width: 100px;
                padding: 10px;
            }

            .error-code {
                font-size: 70px;
            }

            .error-code:before {
                font-size: 100px;
                top: -15px;
            }

            .error-title {
                font-size: 26px;
            }

            .btn-back {
                padding: 12px 30px;
            }
        }
    </style>
</head>

<body>
    <div class="container">
        <div class="row justify-content-center">
            <div class="col-md-8">
                <div class="error-container">
                    <div class="logo-wrapper">
                        <img src="{{ asset('assets/images/logo-small.png') }}" alt="PWB Logo" class="logo-img">
                    </div>
                    <div class="error-code">404</div>
                    <h1 class="error-title">Halaman Tidak Ditemukan</h1>
                    <p class="error-message">
                        Maaf, halaman yang Anda cari tidak dapat ditemukan.
                        Halaman mungkin telah dipindahkan atau dihapus.
                    </p>
                    <a href="{{ url('/') }}" class="btn-back">
                        <i class="mdi mdi-home-outline me-1"></i> Kembali ke Beranda
                    </a>
                </div>
            </div>
        </div>
    </div>

    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.min.js"></script>
</body>

</html>
