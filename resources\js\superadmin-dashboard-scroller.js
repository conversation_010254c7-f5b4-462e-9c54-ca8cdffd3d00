/**
 * Superadmin Dashboard Scroller
 * Controls the scrolling animation for the site invoice display
 */

document.addEventListener('DOMContentLoaded', function() {
    // Get DOM elements
    const scrollerContent = document.querySelector('.scroller-content');
    const scrollerContainer = document.querySelector('.scroller-container');
    const pauseBtn = document.getElementById('pauseScrollBtn');
    const resumeBtn = document.getElementById('resumeScrollBtn');
    const scrollUpBtn = document.getElementById('scrollUpBtn');
    const scrollDownBtn = document.getElementById('scrollDownBtn');
    const siteInvoiceItems = document.querySelectorAll('.site-invoice-item');

    if (!scrollerContent || !pauseBtn || !resumeBtn) return;

    // Add click event to site invoice items to scroll to target achievement section
    siteInvoiceItems.forEach(item => {
        item.addEventListener('click', function() {
            // Get the site ID from the data attribute
            const siteId = this.getAttribute('data-site-id');

            // Find the corresponding target achievement section
            const targetSection = document.getElementById(`target-achievement-${siteId}`);

            if (targetSection) {
                // Pause the scroller animation
                scrollerContent.classList.add('paused');
                pauseBtn.style.display = 'none';
                resumeBtn.style.display = 'flex';

                // Scroll to the target section with smooth behavior
                targetSection.scrollIntoView({
                    behavior: 'smooth',
                    block: 'start'
                });

                // Add a temporary highlight effect to the target section
                targetSection.classList.add('highlight-section');
                setTimeout(() => {
                    targetSection.classList.remove('highlight-section');
                }, 2000);
            }
        });
    });

    // Clone the content multiple times to create a continuous scroll effect
    const originalItems = document.querySelectorAll('.site-invoice-item');
    const originalContent = scrollerContent.innerHTML;

    // Create a duplicate set of items to ensure continuous scrolling
    scrollerContent.innerHTML = originalContent + originalContent;

    // Calculate animation duration based on number of items
    const siteItems = document.querySelectorAll('.site-invoice-item');
    const animationDuration = Math.max(15, siteItems.length * 2.5); // At least 15 seconds, or 2.5 seconds per item

    // Set animation duration dynamically
    scrollerContent.style.animationDuration = `${animationDuration}s`;

    // Set initial transform value
    scrollerContent.style.transform = 'translateY(0)';

    // Pause button click handler
    pauseBtn.addEventListener('click', function() {
        scrollerContent.classList.add('paused');
        pauseBtn.style.display = 'none';
        resumeBtn.style.display = 'flex';
    });

    // Resume button click handler
    resumeBtn.addEventListener('click', function() {
        scrollerContent.classList.remove('paused');
        resumeBtn.style.display = 'none';
        pauseBtn.style.display = 'flex';
    });

    // Manual scroll up (only if the button exists)
    if (scrollUpBtn) {
        scrollUpBtn.addEventListener('click', function() {
            // Pause the animation temporarily
            const wasPlaying = !scrollerContent.classList.contains('paused');
            scrollerContent.classList.add('paused');

            // Get current position and scroll up by one item height
            const itemHeight = siteItems[0].offsetHeight;

            // Get current transform value using a more compatible approach
            let currentY = 0;
            const transformValue = scrollerContent.style.transform;

            if (transformValue && transformValue !== 'none') {
                // Extract the Y value from the transform string using regex
                const match = transformValue.match(/translateY\(([^)]+)\)/);
                if (match && match[1]) {
                    currentY = parseInt(match[1], 10) || 0;
                }
            }

            // Scroll up (decrease negative Y value)
            const newY = Math.min(0, currentY + itemHeight);
            scrollerContent.style.transform = `translateY(${newY}px)`;

            // If we're at the top and trying to scroll further up, jump to the bottom of the first set
            if (newY === 0) {
                setTimeout(() => {
                    scrollerContent.style.transition = 'none';
                    const totalHeight = originalItems.length * itemHeight;
                    scrollerContent.style.transform = `translateY(${-totalHeight}px)`;

                    // Force reflow
                    scrollerContent.offsetHeight;

                    // Restore transition
                    scrollerContent.style.transition = '';
                }, 50);
            }

            // Resume animation if it was playing before
            if (wasPlaying) {
                setTimeout(() => {
                    scrollerContent.classList.remove('paused');
                }, 1000);
            }
        });
    }

    // Manual scroll down (only if the button exists)
    if (scrollDownBtn) {
        scrollDownBtn.addEventListener('click', function() {
            // Pause the animation temporarily
            const wasPlaying = !scrollerContent.classList.contains('paused');
            scrollerContent.classList.add('paused');

            // Get current position and scroll down by one item height
            const itemHeight = siteItems[0].offsetHeight;

            // Get current transform value using a more compatible approach
            let currentY = 0;
            const transformValue = scrollerContent.style.transform;

            if (transformValue && transformValue !== 'none') {
                // Extract the Y value from the transform string using regex
                const match = transformValue.match(/translateY\(([^)]+)\)/);
                if (match && match[1]) {
                    currentY = parseInt(match[1], 10) || 0;
                }
            }

            // Calculate total height of original items
            const totalHeight = originalItems.length * itemHeight;

            // Scroll down (increase negative Y value)
            const newY = currentY - itemHeight;
            scrollerContent.style.transform = `translateY(${newY}px)`;

            // If we're at the bottom of the duplicated content, jump back to the top
            if (Math.abs(newY) >= totalHeight * 2) {
                setTimeout(() => {
                    scrollerContent.style.transition = 'none';
                    scrollerContent.style.transform = `translateY(0)`;

                    // Force reflow
                    scrollerContent.offsetHeight;

                    // Restore transition
                    scrollerContent.style.transition = '';
                }, 50);
            }

            // Resume animation if it was playing before
            if (wasPlaying) {
                setTimeout(() => {
                    scrollerContent.classList.remove('paused');
                }, 1000);
            }
        });
    }

    // Handle animation loop to ensure continuous scrolling
    scrollerContent.addEventListener('animationiteration', function() {
        // When animation completes one cycle, reset to beginning smoothly
        setTimeout(() => {
            scrollerContent.style.transition = 'none';
            scrollerContent.style.transform = 'translateY(0)';

            // Force reflow
            scrollerContent.offsetHeight;

            // Restore transition
            scrollerContent.style.transition = '';
        }, 50);
    });

    // Pause animation on hover
    if (scrollerContainer) {
        scrollerContainer.addEventListener('mouseenter', function() {
            if (!scrollerContent.classList.contains('paused')) {
                scrollerContent.classList.add('paused');
            }
        });

        scrollerContainer.addEventListener('mouseleave', function() {
            if (resumeBtn.style.display === 'none') {
                scrollerContent.classList.remove('paused');
            }
        });
    }
});
