<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\PartInventory;
use App\Models\LogAktivitas;
use App\Models\Part;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Validator;

class StockReconciliationController extends Controller
{
    /**
     * Display a listing of parts with negative stock quantities.
     *
     * @return \Illuminate\Http\Response
     */
    public function index()
    {
        $site_id = session('site_id');
        
        // Get parts with negative stock quantities for the current site
        $negativeStockParts = PartInventory::where('site_id', $site_id)
            ->where('stock_quantity', '<', 0)
            ->with('part')
            ->get();
            
        return view('stock.reconciliation', compact('negativeStockParts'));
    }
    
    /**
     * Fix negative stock quantities for a specific part inventory.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function fixNegativeStock(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'part_inventory_id' => 'required|exists:part_inventories,part_inventory_id',
            'new_quantity' => 'required|integer|min:0',
        ]);
        
        if ($validator->fails()) {
            return response()->json(['error' => $validator->errors()], 422);
        }
        
        DB::beginTransaction();
        
        try {
            // Get the part inventory with a lock to prevent race conditions
            $partInventory = PartInventory::lockForUpdate()
                ->where('part_inventory_id', $request->part_inventory_id)
                ->first();
                
            if (!$partInventory) {
                return response()->json(['error' => 'Part inventory not found'], 404);
            }
            
            // Check if the user has permission to modify this part inventory
            if ($partInventory->site_id !== session('site_id')) {
                return response()->json(['error' => 'You do not have permission to modify this part inventory'], 403);
            }
            
            $oldQuantity = $partInventory->stock_quantity;
            $newQuantity = $request->new_quantity;
            
            // Update the stock quantity
            $partInventory->stock_quantity = $newQuantity;
            $partInventory->save();
            
            // Log the activity
            $part = Part::where('part_code', $partInventory->part_code)->first();
            
            LogAktivitas::create([
                'site_id' => session('site_id'),
                'name' => session('name'),
                'action' => 'Rekonsiliasi Stock',
                'decription' => "User " . session('name') . " melakukan rekonsiliasi stock " . $part->part_name . 
                    " dari " . $oldQuantity . " menjadi " . $newQuantity,
                'ip_address' => $request->ip(),
                'table' => "Part Inventories",
            ]);
            
            DB::commit();
            
            return response()->json([
                'success' => true,
                'message' => 'Stock quantity updated successfully',
                'data' => $partInventory
            ]);
            
        } catch (\Exception $e) {
            DB::rollback();
            return response()->json(['error' => 'Failed to update stock quantity: ' . $e->getMessage()], 500);
        }
    }
    
    /**
     * Fix all negative stock quantities for the current site.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function fixAllNegativeStock(Request $request)
    {
        $site_id = session('site_id');
        
        DB::beginTransaction();
        
        try {
            // Get all part inventories with negative stock for the current site
            $negativeStockParts = PartInventory::where('site_id', $site_id)
                ->where('stock_quantity', '<', 0)
                ->lockForUpdate()
                ->get();
                
            if ($negativeStockParts->isEmpty()) {
                return response()->json(['message' => 'No negative stock quantities found'], 200);
            }
            
            foreach ($negativeStockParts as $partInventory) {
                $oldQuantity = $partInventory->stock_quantity;
                
                // Update the stock quantity to 0
                $partInventory->stock_quantity = 0;
                $partInventory->save();
                
                // Log the activity
                $part = Part::where('part_code', $partInventory->part_code)->first();
                
                LogAktivitas::create([
                    'site_id' => session('site_id'),
                    'name' => session('name'),
                    'action' => 'Rekonsiliasi Stock Massal',
                    'decription' => "User " . session('name') . " melakukan rekonsiliasi stock " . $part->part_name . 
                        " dari " . $oldQuantity . " menjadi 0",
                    'ip_address' => $request->ip(),
                    'table' => "Part Inventories",
                ]);
            }
            
            DB::commit();
            
            return response()->json([
                'success' => true,
                'message' => 'All negative stock quantities have been fixed',
                'count' => $negativeStockParts->count()
            ]);
            
        } catch (\Exception $e) {
            DB::rollback();
            return response()->json(['error' => 'Failed to fix negative stock quantities: ' . $e->getMessage()], 500);
        }
    }
}
