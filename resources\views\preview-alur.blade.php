<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>Preview Alur Sistem Inventory</title>
    <meta name="viewport" content="width=device-width, initial-scale=1.0">

    <style>
        body {
            margin: 0;
            overflow: hidden;
            font-family: Arial, sans-serif;
        }

        .container {
            width: 100vw;
            height: 100vh;
            overflow: hidden;
            position: relative;
        }

        .controls {
            margin-top: 40px;
            position: absolute;
            top: 10px;
            left: 10px;
            z-index: 10;
            background: rgba(255, 255, 255, 0.7);
            border-radius: 8px;
            padding: 8px;
        }

        .controls button {
            margin-right: 8px;
        }

        .panzoom-wrapper {
            width: 100%;
            height: 100%;
            cursor: grab;
        }

        .panzoom-content {
            display: flex;
            align-items: center;
            justify-content: center;
            height: 100%;
        }

        img {
            max-width: none;
            max-height: none;
        }
        .btnhome{
            background-color: #2a1b89;
            color: white;
            border-radius: 10px;
            padding: 10px 20px 10px 20px;
            text-decoration: none;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="controls">
            <a class="btnhome" href="{{ route('sites.dashboard') }}">Home</a>
        </div>

        <div id="panzoom" class="panzoom-wrapper">
            <div class="panzoom-content">
                <img src="{{ asset('assets/images/alur-sistem.jpg') }}" alt="Alur Sistem Inventory">
            </div>
        </div>
    </div>

    <!-- Tambahkan Panzoom dari CDN -->
    <script src="https://unpkg.com/@panzoom/panzoom@9.4.0/dist/panzoom.min.js"></script>
    <script>
        const element = document.getElementById('panzoom');
        const panzoom = Panzoom(element, {
            maxScale: 5,
            minScale: 0.5,
            contain: 'outside'
        });

        // Enable mouse wheel zoom
        element.addEventListener('wheel', panzoom.zoomWithWheel);
    </script>
</body>
</html>
