<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('requisition_details', function (Blueprint $table) {
            // Drop the existing foreign key constraint
            $table->dropForeign(['part_code']);

            // Add the foreign key constraint with CASCADE
            $table->foreign('part_code')
                ->references('part_code')
                ->on('parts')
                ->onUpdate('cascade');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('requisition_details', function (Blueprint $table) {
            // Drop the CASCADE foreign key constraint
            $table->dropForeign(['part_code']);

            // Add back the original foreign key constraint without CASCADE
            $table->foreign('part_code')
                ->references('part_code')
                ->on('parts');
        });
    }
};
