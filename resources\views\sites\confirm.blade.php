
<div class="container">
    <h2>Daftar Transaksi Masuk</h2>
    <div class="table-responsive">
        <table class="table table-striped">
            <thead>
                <tr>
                    <th>Tanggal</th>
                    <th>Part Code</th>
                    <th>Nama Part</th>
                    <th>Qty <PERSON></th>
                    <th>Status</th>
                    <th>Aksi</th>
                </tr>
            </thead>
            <tbody>
                @foreach($transactions as $transaction)
                <tr>
                    <td>{{ $transaction->created_at->format('d/m/Y H:i') }}</td>
                    <td>{{ $transaction->part_code }}</td>
                    <td>{{ $transaction->part->part_name }}</td>
                    <td>{{ $transaction->quantity_sent }}</td>
                    <td>
                        <span class="badge 
                            @if($transaction->status == 'intransit') bg-secondary text-dark
                            @elseif($transaction->status == 'complete') bg-success
                            @else bg-secondary
                            @endif">
                            {{ $transaction->status }}
                        </span>
                    </td>
                    <td>
                        @if($transaction->status == 'intransit')
                        <a href="{{ route('transactions.single', $transaction->stock_transaction_id) }}" 
                           class="btn btn-sm btn-primary">
                           <i class="fas fa-check-circle"></i> Konfirmasi
                        </a>
                        @else
                        <button class="btn btn-sm btn-secondary" disabled>
                            <i class="fas fa-check-double"></i> Selesai
                        </button>
                        @endif
                    </td>
                </tr>
                @endforeach
            </tbody>
        </table>
    </div>
</div>