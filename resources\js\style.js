
const body = document.body;
const backgrounds = []; 
// const backgrounds = ['bg1', 'bg2', 'bg3', 'bg4']; 
let currentBackgroundIndex = 0;
function changeBackground() {
  body.classList.remove(backgrounds[currentBackgroundIndex]);
  currentBackgroundIndex = (currentBackgroundIndex + 1) % backgrounds.length;
  body.classList.add(backgrounds[currentBackgroundIndex]);
}

setInterval(changeBackground, 10000);
body.classList.add(backgrounds[0]);