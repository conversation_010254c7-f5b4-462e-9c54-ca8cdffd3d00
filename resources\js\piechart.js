import Chart from 'chart.js/auto';
import ChartDataLabels from 'chartjs-plugin-datalabels';

Chart.register(ChartDataLabels); // Registrasi global, !! Penting !! jangan panggil difile lain

document.addEventListener("DOMContentLoaded", function () {
    window.inventoryData.forEach((data) => {
        const pieChartId = document
            .getElementById(`pieChart${slugify(data.site_name)}`)
            .getContext("2d");

        const pieChart = new Chart(pieChartId, {
            type: "pie",
            data: {
                labels: ["Not Ready", "Ready"],
                datasets: [
                    {
                        label: "Status Inventory",
                        data: [
                            data.not_ready_percentage,
                            data.ready_percentage,
                        ],
                        backgroundColor: [
                            "rgba(235, 49, 36, 0.8)",  // Red (left side) - using the danger color #eb3124
                            "rgba(40, 167, 69, 0.8)",  // Green (right side)
                        ],
                        borderWidth: 1,
                    },
                ],
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        position: "bottom",
                    },
                    title: {
                        display: true,
                        text: "Persentase Status Inventory",
                    },
                    tooltip: {
                        callbacks: {
                            label: function (context) {
                                let label = context.label || "";
                                if (label) {
                                    label += ": ";
                                }
                                if (context.parsed !== null) {
                                    label += context.parsed.toFixed(2) + "%";
                                }
                                return label;
                            },
                        },
                    },
                    datalabels: {
                        formatter: (value, context) => {
                            return value.toFixed(2) + "%";
                        },
                        color: "#fff",
                        font: {
                            weight: "bold",
                            size: 12,
                        },
                    },
                },
            },
            plugins: [ChartDataLabels],
        });
    });

    function slugify(str) {
        return String(str)
            .normalize("NFKD")
            .replace(/[\u0300-\u036f]/g, "")
            .trim()
            .toLowerCase()
            .replace(/[^a-z0-9 -]/g, "")
            .replace(/\s+/g, "-")
            .replace(/-+/g, "-");
    }
});