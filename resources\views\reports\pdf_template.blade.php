<!DOCTYPE html>
<html>
<head>
    <title>{{ $title }}</title>
    <style>
        @page {
            margin: 120px 50px 80px 50px;
        }
        :root {
            --primary-color: #1a3c5a;
            --secondary-color: #2980b9;
            --accent-color: #f5f8fa;
            --text-color: #333;
            --border-color: #e0e0e0;
        }
        body {
            font-family: Arial, Helvetica, sans-serif;
            font-size: 11px;
            line-height: 1.4;
            color: var(--text-color);
            margin: 0;
            padding: 0;
        }
        .header {
            position: fixed;
            top: -80px;
            left: 0;
            right: 0;
            height: 70px;
            text-align: center;
            border-bottom: 1px solid var(--border-color);
            padding-bottom: 10px;
            background-color: white;
        }
        .header-logo {
            text-align: left;
            margin-bottom: 5px;
        }
        .header-title {
            margin: 0;
            font-size: 18px;
            font-weight: bold;
            color: var(--primary-color);
            text-transform: uppercase;
            letter-spacing: 1px;
        }
        .header-subtitle {
            font-size: 12px;
            color: var(--secondary-color);
            margin: 5px 0;
        }
        .period-info {
            background: var(--accent-color);
            padding: 6px 10px;
            border-radius: 3px;
            display: inline-block;
            margin: 5px 0;
            font-size: 10px;
            border: 1px solid var(--border-color);
        }
        table {
            width: 100%;
            border-collapse: collapse;
            margin: 0;
        }
        th {
            background-color: var(--primary-color);
            color: white;
            padding: 8px 6px;
            font-size: 10px;
            text-align: left;
            border: 1px solid var(--border-color);
            font-weight: normal;
        }
        td {
            padding: 6px;
            border: 1px solid var(--border-color);
            vertical-align: top;
        }
        tr:nth-child(even) {
            background-color: var(--accent-color);
        }
        .footer {
            position: fixed;
            bottom: -60px;
            left: 0;
            right: 0;
            height: 40px;
            text-align: center;
            font-size: 9px;
            color: #777;
            border-top: 1px solid var(--border-color);
            padding: 10px 0;
            background: white;
        }
        .page-number {
            text-align: right;
            font-size: 9px;
            color: #777;
        }
        .main-content {
            margin: 0;
            padding: 0;
        }
    </style>
</head>
<body>
    <div class="header">
        <div class="header-logo">PT. Putera Wibowo Borneo</div>
        <h1 class="header-title">{{ $title }}</h1>
        <div class="header-subtitle">
            Site: {{ $siteName ?? 'Semua Site' }}  Periode: {{ $startDate ? date('d F Y', strtotime($startDate)) : 'Awal' }} s/d {{ $endDate ? date('d F Y', strtotime($endDate)) : 'Akhir' }}
        </div>
    </div>

    <div class="main-content">
        <table>
            <thead>
                <tr>
                    <th>No</th>
                    <th>Tanggal</th>
                    <th>Code Part</th>
                    <th>Nama Part</th>
                    <th>Qty</th>
                </tr>
            </thead>
            <tbody>
                @foreach($data as $key => $row)
                <tr>
                    <td>{{ $key + 1 }}</td>
                    <td>{{ date('d F Y', strtotime($row->date_in ?? $row->date_out)) }}</td>
                    <td>{{ $row->part_code }}</td>
                    <td>{{ $row->part_name }}</td>
                    <td>{{ $row->quantity }}</td>
                </tr>
                @endforeach
            </tbody>
        </table>
    </div>
    <div class="footer">
        <div>Dicetak pada: {{ now()->format('d F Y H:i:s') }} | © {{ date('Y') }} PT. Putera Wibowo Borneo</div>
        <div class="page-number">Halaman {PAGE_NUM} dari {PAGE_COUNT}</div>
    </div>
</body>
</html>
