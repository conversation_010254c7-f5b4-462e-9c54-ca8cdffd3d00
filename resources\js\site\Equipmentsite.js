import Swal from "sweetalert2";
import bootstrap from '../bootstrap-init';

window.loadEquipments = function () {
    loadEquipmentStockData();
};

// Global variables for pagination
let currentPage = 1;
const itemsPerPage = 5; // 5 items per page

// Function to create pagination item
function createPaginationItem(page, text, isActive = false) {
    const li = document.createElement('li');
    li.className = `page-item ${isActive ? 'active' : ''}`;

    const a = document.createElement('a');
    a.className = 'page-link';
    a.href = '#';
    a.textContent = text;
    a.dataset.page = page;

    li.appendChild(a);
    return li;
}

// Function to render pagination
function renderPagination(data) {
    const container = document.getElementById('pagination-container');
    container.innerHTML = '';

    if (data.last_page > 1) {
        const pagination = document.createElement('ul');
        pagination.className = 'pagination pagination-rounded  justify-content-center';

        // Previous button
        if (data.current_page > 1) {
            pagination.appendChild(createPaginationItem(data.current_page - 1, '«'));
        }

        // Page numbers
        for (let i = 1; i <= data.last_page; i++) {
            pagination.appendChild(createPaginationItem(i, i, i === data.current_page));
        }

        // Next button
        if (data.current_page < data.last_page) {
            pagination.appendChild(createPaginationItem(data.current_page + 1, '»'));
        }

        container.appendChild(pagination);

        // Add event listeners to pagination links
        container.querySelectorAll('.page-link').forEach(link => {
            link.addEventListener('click', function(e) {
                e.preventDefault();
                currentPage = parseInt(this.dataset.page);
                loadEquipmentStockData();
            });
        });
    }
}

window.loadEquipmentStockData = function (tryLastDate = true) {
    const status = document.getElementById("status").value;
    const startDate = document.getElementById("start_date").value;
    const endDate = document.getElementById("end_date").value;

    let url = `/site/equipment/stocks/data?status=${status}&page=${currentPage}&per_page=${itemsPerPage}`;

    // Add date filters if provided
    if (startDate && endDate) {
        url += `&start_date=${startDate}&end_date=${endDate}`;
    }

    // Add parameter to use last date with data if no data found for selected date
    if (tryLastDate) {
        url += `&use_last_date=true`;
    }

    fetch(url, {
        method: "GET",
        headers: {
            "Content-Type": "application/json",
            "X-CSRF-TOKEN": document
                .querySelector('meta[name="csrf-token"]')
                .getAttribute("content"),
        },
    })
        .then((response) => {
            if (!response.ok) {
                throw new Error("Network response was not ok");
            }
            return response.json();
        })
        .then((data) => {
            // Update the table with the data
            updateEquipmentStockTable(data.data);
            renderPagination(data);

            // If we used the last date with data, update the date inputs
            if (data.used_last_date) {
                document.getElementById("start_date").value = data.start_date;
                document.getElementById("end_date").value = data.end_date;

                // Show a notification that we're showing data from a different date
                Swal.fire({
                    icon: "info",
                    title: "Informasi",
                    text: `Tidak ada data untuk tanggal yang dipilih. Menampilkan data dari tanggal ${data.start_date}.`,
                    toast: true,
                    position: "top-end",
                    showConfirmButton: false,
                    timer: 5000,
                    timerProgressBar: true
                });
            }

            // If no data at all, show a message
            if (data.data.length === 0) {
                Swal.fire({
                    icon: "info",
                    title: "Tidak Ada Data",
                    text: "Tidak ada data peralatan yang tersedia."
                });
            }
        })
        .catch((error) => {
            Swal.fire({
                icon: "error",
                title: "Kesalahan!",
                text: "Gagal memuat data stok peralatan: " + error.message,
            });
        });
};

window.updateEquipmentStockTable = function (equipmentStocks) {
    const tableBody = document.getElementById("equipmentStockTableBody");
    tableBody.innerHTML = "";

    // Check if we have data
    if (!equipmentStocks || equipmentStocks.length === 0) {
        // Display a message in the table when no data is available
        const emptyRow = `
            <tr>
                <td colspan="6" class="text-center py-3">Tidak ada data peralatan yang tersedia.</td>
            </tr>
        `;
        tableBody.innerHTML = emptyRow;
        return;
    }

    // If we have data, display it
    let i = 1;
    equipmentStocks.forEach((stock) => {
        // Format the date for better display
        const receivedDate = new Date(stock.received_at);
        const formattedDate = receivedDate.toLocaleDateString('id-ID', {
            year: 'numeric',
            month: 'long',
            day: 'numeric'
        });

        // Create status badge with color based on status
        let statusClass = '';
        switch(stock.status) {
            case 'Baik':
                statusClass = 'bg-success';
                break;
            case 'Cukup Baik':
                statusClass = 'bg-info';
                break;
            case 'Kurang Baik':
                statusClass = 'bg-warning';
                break;
            case 'Rusak':
                statusClass = 'bg-danger';
                break;
            default:
                statusClass = 'bg-secondary';
        }

        const row = `
            <tr>
                <td>${i++}</td>
                <td>${stock.equipment.name}</td>
                <td>${stock.site.site_name}</td>
                <td><span class="badge ${statusClass}">${stock.status}</span></td>
                <td>${formattedDate}</td>
                <td>
                    <button class="btn btn-sm btn-primary" onclick="openEditEquipmentStock(${stock.id})">Edit</button>
                </td>
            </tr>
        `;
        tableBody.innerHTML += row;
    });
};

window.applyFilters = function () {
    currentPage = 1; // Reset to first page when filters change
    loadEquipmentStockData();
};

window.openEditEquipmentStock = function (id) {
    fetch(`/site/equipment-stock/${id}`, {
        method: "GET",
        headers: {
            "Content-Type": "application/json",
            "X-CSRF-TOKEN": document
                .querySelector('meta[name="csrf-token"]')
                .getAttribute("content"),
        },
    })
        .then((response) => response.json())
        .then((data) => {
            // Populate form fields with existing data
            document.getElementById("stock_id").value = data.id;
            document.getElementById("status_stock").value = data.status;
            document.getElementById("received_at_stock").value =
                data.received_at;

            // Show the modal using Bootstrap 5
            const editModal = new bootstrap.Modal(document.getElementById('editStockModal'));
            editModal.show();
        })
        .catch((error) => {
            Swal.fire({
                icon: "error",
                title: "Kesalahan!",
                text: "Gagal membuka formulir edit stok.",
            });
        });
};

window.updateEquipmentStock = function () {
    const stockId = document.getElementById("stock_id").value;
    const status = document.getElementById("status_stock").value;
    const receivedAt = document.getElementById("received_at_stock").value;

    const data = {
        status: status,
        received_at: receivedAt,
    };

    // Laravel doesn't support PUT requests directly, so we need to use POST with _method=PUT
    const formData = new FormData();
    formData.append('_method', 'PUT');
    formData.append('status', status);
    formData.append('received_at', receivedAt);

    fetch(`/site/equipment-stock/${stockId}`, {
        method: "POST",
        headers: {
            "X-CSRF-TOKEN": document
                .querySelector('meta[name="csrf-token"]')
                .getAttribute("content"),
        },
        body: formData,
    })
        .then((response) => response.json())
        .then((data) => {
            if (data.message) {
                window.loadEquipmentStockData();
                // Hide the modal using Bootstrap 5
                const editModal = bootstrap.Modal.getInstance(document.getElementById('editStockModal'));
                if (editModal) {
                    editModal.hide();
                }
                Swal.fire({
                    icon: "success",
                    title: "Berhasil!",
                    text: "Data Stok Peralatan berhasil disimpan.",
                });
            } else {
                Swal.fire({
                    icon: "error",
                    title: "Gagal!",
                    text: "Data Stok Peralatan Gagal disimpan.",
                });
            }
        })
        .catch((error) => {
            Swal.fire({
                icon: "error",
                title: "Kesalahan!",
                text: "Gagal menyimpan data stok peralatan.",
            });
        });
};

window.confirmDeleteEquipmentStock = function (id) {
    Swal.fire({
        title: "Apakah Anda yakin?",
        text: "Anda tidak akan dapat mengembalikan ini!",
        icon: "warning",
        showCancelButton: true,
        confirmButtonColor: "#3085d6",
        cancelButtonColor: "#d33",
        confirmButtonText: "Ya, hapus!",
        cancelButtonText: "Batal",
    }).then((result) => {
        if (result.isConfirmed) {
            deleteEquipmentStock(id);
        }
    });
};

window.deleteEquipmentStock = function (id) {
    fetch(`/site/equipment-stock/${id}`, {
        method: "DELETE",
        headers: {
            "Content-Type": "application/json",
            "X-CSRF-TOKEN": document
                .querySelector('meta[name="csrf-token"]')
                .getAttribute("content"),
        },
    })
        .then((response) => {
            if (response.ok) {
                window.loadEquipmentStockData();
                Swal.fire(
                    "Terhapus!",
                    "Data stok peralatan telah dihapus.",
                    "success"
                );
            } else {
                Swal.fire({
                    icon: "error",
                    title: "Kesalahan!",
                    text: "Gagal menghapus data stok peralatan.",
                });
            }
        })
        .catch((error) => {
            Swal.fire({
                icon: "error",
                title: "Kesalahan!",
                text: "Gagal menghapus data stok peralatan.",
            });
        });
};

document.addEventListener("DOMContentLoaded", function () {
    // Set default date values to today
    const today = new Date().toISOString().split('T')[0];
    document.getElementById("start_date").value = today;
    document.getElementById("end_date").value = today;

    window.loadEquipments();
});
