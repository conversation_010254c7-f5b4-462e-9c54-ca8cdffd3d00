document.addEventListener("DOMContentLoaded", function () {
    !(function (n) {
        "use strict";
        var t = function () {};
        (t.prototype.initTooltipPlugin = function () {
            n.fn.tooltip && n('[data-toggle="tooltip"]').tooltip();
        }),
            (t.prototype.initPopoverPlugin = function () {
                n.fn.popover && n('[data-toggle="popover"]').popover();
            }),
            (t.prototype.initSlimScrollPlugin = function () {
                n.fn.slimScroll &&
                    n(".slimscroll").slimScroll({
                        height: "auto",
                        position: "right",
                        size: "5px",
                        touchScrollStep: 20,
                        color: "#9ea5ab",
                    });
            }),
            (t.prototype.initCustomModalPlugin = function () {
                n('[data-plugin="custommodal"]').on("click", function (t) {
                    t.preventDefault(),
                        new Custombox.modal({
                            content: {
                                target: n(this).attr("href"),
                                effect: n(this).attr("data-animation"),
                            },
                            overlay: {
                                color: n(this).attr("data-overlayColor"),
                            },
                        }).open();
                });
            }),
            (t.prototype.initCounterUp = function () {
                n(this).attr("data-delay") && n(this).attr("data-delay"),
                    n(this).attr("data-time") && n(this).attr("data-time");
                n('[data-plugin="counterup"]').each(function (t, i) {
                    n(this).counterUp({ delay: 100, time: 1200 });
                });
            }),
            (t.prototype.initPeityCharts = function () {
                n('[data-plugin="peity-pie"]').each(function (t, i) {
                    var e = n(this).attr("data-colors")
                            ? n(this).attr("data-colors").split(",")
                            : [],
                        a = n(this).attr("data-width")
                            ? n(this).attr("data-width")
                            : 20,
                        o = n(this).attr("data-height")
                            ? n(this).attr("data-height")
                            : 20;
                    n(this).peity("pie", { fill: e, width: a, height: o });
                }),
                    n('[data-plugin="peity-donut"]').each(function (t, i) {
                        var e = n(this).attr("data-colors")
                                ? n(this).attr("data-colors").split(",")
                                : [],
                            a = n(this).attr("data-width")
                                ? n(this).attr("data-width")
                                : 20,
                            o = n(this).attr("data-height")
                                ? n(this).attr("data-height")
                                : 20;
                        n(this).peity("donut", {
                            fill: e,
                            width: a,
                            height: o,
                        });
                    }),
                    n('[data-plugin="peity-donut-alt"]').each(function (t, i) {
                        n(this).peity("donut");
                    }),
                    n('[data-plugin="peity-line"]').each(function (t, i) {
                        n(this).peity("line", n(this).data());
                    }),
                    n('[data-plugin="peity-bar"]').each(function (t, i) {
                        var e = n(this).attr("data-colors")
                                ? n(this).attr("data-colors").split(",")
                                : [],
                            a = n(this).attr("data-width")
                                ? n(this).attr("data-width")
                                : 20,
                            o = n(this).attr("data-height")
                                ? n(this).attr("data-height")
                                : 20;
                        n(this).peity("bar", { fill: e, width: a, height: o });
                    });
            }),
            (t.prototype.initKnob = function () {
                n('[data-plugin="knob"]').each(function (t, i) {
                    n(this).knob();
                });
            }),
            (t.prototype.init = function () {
                this.initTooltipPlugin(),
                    this.initPopoverPlugin(),
                    this.initSlimScrollPlugin(),
                    this.initCustomModalPlugin(),
                    this.initCounterUp(),
                    this.initPeityCharts(),
                    this.initKnob();
            }),
            (n.Components = new t()),
            (n.Components.Constructor = t);
    })(window.jQuery),
        (function (i) {
            "use strict";
            var t = function () {
                (this.$bootstrapStylesheet = i("#bootstrap-stylesheet")),
                    (this.$appStylesheet = i("#app-stylesheet")),
                    (this.$originalBSStylesheet = i(
                        "#bootstrap-stylesheet"
                    ).attr("href")),
                    (this.$originalAppStylesheet =
                        i("#app-stylesheet").attr("href"));
            };
            (t.prototype.init = function () {
                var t = this;
                i("#light-mode-switch").on("change", function () {
                    this.checked &&
                        (t.$bootstrapStylesheet.attr(
                            "href",
                            t.$originalBSStylesheet
                        ),
                        t.$appStylesheet.attr("href", t.$originalAppStylesheet),
                        i("#dark-mode-switch").prop("checked", !1),
                        i("#rtl-mode-switch").prop("checked", !1));
                }),
                    i("#dark-mode-switch").on("change", function () {
                        this.checked &&
                            (t.$bootstrapStylesheet.attr(
                                "href",
                                i(this).data("bsstyle")
                            ),
                            t.$appStylesheet.attr(
                                "href",
                                i(this).data("appstyle")
                            ),
                            i("#light-mode-switch").prop("checked", !1),
                            i("#rtl-mode-switch").prop("checked", !1));
                    }),
                    i("#rtl-mode-switch").on("change", function () {
                        this.checked &&
                            (t.$bootstrapStylesheet.attr(
                                "href",
                                t.$originalBSStylesheet
                            ),
                            t.$appStylesheet.attr(
                                "href",
                                i(this).data("appstyle")
                            ),
                            i("#light-mode-switch").prop("checked", !1),
                            i("#dark-mode-switch").prop("checked", !1));
                    });
            }),
                (i.RightSidebar = new t()),
                (i.RightSidebar.Constructor = t);
        })(window.jQuery),
        (function (e) {
            "use strict";
            var t = function () {
                (this.$body = e("body")), (this.$window = e(window));
            };
            (t.prototype._resetSidebarScroll = function () {
                e(".slimscroll-menu").slimscroll({
                    height: "auto",
                    position: "right",
                    size: "5px",
                    color: "#9ea5ab",
                    wheelStep: 5,
                    touchScrollStep: 20,
                });
            }),
                (t.prototype.initMenu = function () {
                    var i = this;
                    e(".button-menu-mobile").on("click", function (t) {
                        t.preventDefault(),
                            i.$body.toggleClass("sidebar-enable"),
                            768 <= i.$window.width()
                                ? i.$body.toggleClass("enlarged")
                                : i.$body.removeClass("enlarged"),
                            i._resetSidebarScroll();
                    }),
                        e("#side-menu").metisMenu(),
                        i._resetSidebarScroll(),
                        e(".right-bar-toggle").on("click", function (t) {
                            e("body").toggleClass("right-bar-enabled");
                        }),
                        e(document).on("click", "body", function (t) {
                            0 <
                                e(t.target).closest(
                                    ".right-bar-toggle, .right-bar"
                                ).length ||
                                0 <
                                    e(t.target).closest(
                                        ".left-side-menu, .side-nav"
                                    ).length ||
                                e(t.target).hasClass("button-menu-mobile") ||
                                0 <
                                    e(t.target).closest(".button-menu-mobile")
                                        .length ||
                                (e("body").removeClass("right-bar-enabled"),
                                e("body").removeClass("sidebar-enable"));
                        }),
                        e("#side-menu a").each(function () {
                            var t = window.location.href.split(/[?#]/)[0];
                            this.href == t &&
                                (e(this).addClass("active"),
                                e(this).parent().addClass("mm-active"),
                                e(this).parent().parent().addClass("mm-show"),
                                e(this)
                                    .parent()
                                    .parent()
                                    .prev()
                                    .addClass("active"),
                                e(this)
                                    .parent()
                                    .parent()
                                    .parent()
                                    .addClass("mm-active"),
                                e(this)
                                    .parent()
                                    .parent()
                                    .parent()
                                    .parent()
                                    .addClass("mm-show"),
                                e(this)
                                    .parent()
                                    .parent()
                                    .parent()
                                    .parent()
                                    .parent()
                                    .addClass("mm-active"));
                        }),
                        e(".navbar-toggle").on("click", function (t) {
                            e(this).toggleClass("open"),
                                e("#navigation").slideToggle(400);
                        });
                }),
                (t.prototype.initLayout = function () {
                    768 <= this.$window.width() && this.$window.width() <= 1024
                        ? this.$body.addClass("enlarged")
                        : 1 != this.$body.data("keep-enlarged") &&
                          this.$body.removeClass("enlarged");
                }),
                (t.prototype.init = function () {
                    var i = this;
                    this.initLayout(),
                        this.initMenu(),
                        e.Components.init(),
                        e.RightSidebar.init(),
                        i.$window.on("resize", function (t) {
                            t.preventDefault(),
                                i.initLayout(),
                                i._resetSidebarScroll();
                        });
                }),
                (e.App = new t()),
                (e.App.Constructor = t);
        })(window.jQuery),
        (function (t) {
            "use strict";
            window.jQuery.App.init();
        })()
    //# sourceMappingURL=app.min.js.map
});
