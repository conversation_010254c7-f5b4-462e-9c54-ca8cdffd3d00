@foreach($parts as $part)
<tr>
    <td>{{ $part->part_code }}</td>
    <td>{{ $part->part_name }}</td>
    <td>{{ $part->bin_location }}</td>
    <td>{{ $part->part_type }}</td>
    <td>
        <button class="btn btn-sm btn-primary edit-part-btn" data-part-code="{{ $part->part_code }}"
            data-part-name="{{ $part->part_name }}" data-bin-location="{{ $part->bin_location }}"
            data-part-type="{{ $part->part_type }}">
            Edit
        </button>
        <form action="{{ route('parts.destroy', $part->part_code) }}" class="d-inline deleteForm">
            @csrf
            @method('DELETE')
            <button type="submit" class="btn btn-sm btn-danger delete-part-btn">Hapus</button>
        </form>
    </td>
</tr>
@endforeach

<div class="d-flex justify-content-center mt-3">
    {{ $parts->links() }}
</div>