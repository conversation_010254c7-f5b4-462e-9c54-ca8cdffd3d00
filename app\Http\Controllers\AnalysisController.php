<?php

namespace App\Http\Controllers;

use App\Models\Part;
use App\Models\Site;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;

class AnalysisController extends Controller
{
    public function index()
    {
        $sites = Site::all();
        $partCategories = Part::distinct('part_type')->pluck('part_type')->toArray(); // Ambil daftar kategori part unik
        return view('inventory.analisis', compact('sites', 'partCategories'));
    }

    private function getTopUsageData(string $tableName, string $startDate, string $endDate, string $partCategory = null, int $limit = 10, string $siteId = null): array
    {
        $baseQuery = DB::table($tableName)
            ->whereBetween('date_out', [$startDate, $endDate])
            ->join('part_inventories', $tableName . '.part_inventory_id', '=', 'part_inventories.part_inventory_id')
            ->join('parts', 'part_inventories.part_code', '=', 'parts.part_code');
        if ($partCategory) {
            $baseQuery->where('parts.part_type', $partCategory);
        }
        if ($siteId) { 
            $baseQuery->where($tableName . '.site_id', $siteId);
        }
        $topUsageByQuantity = $baseQuery->clone()
            ->select(
                'parts.part_code',
                'parts.part_name',
                DB::raw('SUM(' . $tableName . '.quantity) as total_quantity')
            )
            ->groupBy('parts.part_code', 'parts.part_name')
            ->orderByDesc('total_quantity')
            ->limit($limit)
            ->get();
        $topUsageByFrequency = $baseQuery
            ->select(
                'parts.part_code',
                'parts.part_name',
                DB::raw('COUNT(*) as total_frequency')
            )
            ->groupBy('parts.part_code', 'parts.part_name')
            ->orderByDesc('total_frequency')
            ->limit($limit)
            ->get();

        return [
            'top_quantity' => $topUsageByQuantity,
            'top_frequency' => $topUsageByFrequency,
        ];
    }

    public function analyze(Request $request)
    {
        $request->validate([
            'start_date' => 'required|date',
            'end_date' => 'required|date|after_or_equal:start_date',
            'site_id' => 'nullable|exists:sites,site_id',
            'part_category' => 'nullable|in:' . implode(',', Part::distinct('part_type')->pluck('part_type')->toArray()),
            'limit' => 'nullable|integer|min:1', 
        ]);

        $startDate = $request->input('start_date');
        $endDate = $request->input('end_date');
        $siteId = $request->input('site_id');
        $limit = $request->input('limit', 10);
        $partCategory = $request->input('part_category');
        $results = [];
        $sites = $siteId ? Site::where('site_id', $siteId)->get() : Site::all();

        foreach ($sites as $site) {
            $tableName = ($site->site_id == session('site_id')) ? 'warehouse_out_stocks' : 'site_out_stocks';
            $siteIdForQuery = ($tableName == 'site_out_stocks') ? $site->site_id : null;
            $usageData = $this->getTopUsageData($tableName, $startDate, $endDate, $partCategory, $limit, $siteIdForQuery);
            $results[$site->site_id] = [
                'site_name' => $site->site_name,
                'top_quantity' => $usageData['top_quantity'],
                'top_frequency' => $usageData['top_frequency'],
            ];
        }

        return response()->json($results);
    }
}
