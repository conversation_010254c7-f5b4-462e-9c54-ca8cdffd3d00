@extends('sites.content')
@section('contentsite')
<!-- mulai content disin -->
<div class="row bgwhite page-title-box shadow-kit mb-1">
    <div class="col d-flex align-items-center" style="max-width: fit-content;">
        <button class="button-menu-mobile text-blue-500 btn-sm">
            <i style="font-size: 30px;" class="mdi mdi-menu"></i>
        </button>
        <div class="text">
            <p class="font-bold m-0">{{ session('name') }}</p>
        </div>
    </div>

    <div class="col">
        <ul class="list-unstyled topnav-menu float-right mb-0">
            <li class="dropdown notification-list">
                <a class="nav-link dropdown-toggle" href="#" data-bs-toggle="dropdown" aria-expanded="false">
                    <i class="mdi mdi-bell-outline noti-icon"></i>
                    <span class="badge badge-danger badge-pill float-right badgenotif"></span>
                </a>
                <div class="dropdown-menu dropdown-menu-end dropdown-lg">
                    <div class="dropdown-item noti-title bg-primary">
                        <h5 class="m-0 text-white d-flex justify-content-between align-items-center">
                            Notifikasi
                            <a href="javascript:void(0);" class="text-white" id="clear-all">
                                <small></small>
                            </a>
                        </h5>
                    </div>
                    <div class="noti-scroll" id="notification-list">
                        <!-- Notifications will be dynamically inserted here -->
                    </div>
                </div>
            </li>
        </ul>
    </div>
</div>

<div class="row">
    <div class="col">
        <div class="shadow-kit bgwhite p-3">
            <h1 class="h4 font-bold text-uppercase">Pengajuan part</h1>
            <table class="table" id="pengajuanTable">
                <thead class="table-dark text-white">
                    <tr>
                        <th class="p-2 font-bold">Judul Pengajuan</th>
                        <th class="p-2 font-bold">Status</th>
                        <th class="p-2 font-bold">Type</th>
                        <th class="p-2 font-bold">Tanggal</th>
                        <th class="p-2 font-bold">Keterangan</th>
                        <th class="p-2 font-bold">Aksi</th>
                    </tr>
                </thead>
                <tbody>
                </tbody>
            </table>
            <div id="pengajuan-pagination" class="mt-3">
                <!-- Custom pagination will be rendered here by JavaScript -->
            </div>
        </div>
    </div>

    <div class="col">
        <div id="detailsDiv" class=" mt-0" style="min-width: 100%;">
            <div class="p-4 mb-4">
                <button class="btn float-right btn-sm btn-secondary" id="tutupDetailsBtn">Tutup</button>
                <h4 class="h4 text-uppercase">Detail Pengajuan Part</h4>
                <div id="detailContent"></div>
            </div>
        </div>
        <div class="bgwhite p-4 shadow-kit">
            <div class="row">
                <div class="col">
                    <h4 class="font-bold h4 text-uppercase">Isi Detail Pengajuan </h4>
                    <hr class="mt-3 mb-2">
                    <form id="pengajuanDetailsForm" style="min-width: 200px;">
                        <div class="form-group">
                            <label for="title">Judul Pengajuan</label>
                            <input type="text" class="form-control" id="title" name="title" placeholder="Masukkan judul pengajuan" autocomplete="off" required>
                        </div>
                        <div class="form-group">
                            <input type="hidden" class="form-control" id="site_id" name="site_id" placeholder="Masukkan Site ID" required value="{{ session('site_id') }}">
                        </div>
                        <!-- <div class="form-group">
                            <label for="requisition_date">Tanggal Pengajuan</label>
                            <input type="date" class="form-control" id="requisition_date" name="requisition_date" autocomplete="off" required>
                        </div> -->
                        <input type="hidden" class="form-control" id="modified_by" name="modified_by" required d value="{{ session('name') }}">
                        <!-- <div class="form-group">
                            <select class="form-control" id="requisition_type" name="requisition_type" onchange="toggleJumlah()">
                                <option value="Pembelian Part">Pembelian Part</option>
                            </select>
                        </div> -->
                        <div class="form-group" id="jumlahGroup" style="display: none;">
                            <label for="jumlah">Jumlah</label>
                            <input type="number" class="form-control" id="jumlah" placeholder="Masukkan jumlah">
                        </div>
                        <div class="form-group">
                            <label for="notes">Keterangan Pengajuan</label>
                            <textarea style="min-height: 130px;" class="form-control" id="notes" name="notes" placeholder="Masukkan keterangan"></textarea>
                        </div>
                </div>
                <div class="col">
                    <div class="form-group mt-5">
                        <label for="part_name">Pilih Part</label>
                        <input type="text" class="form-control" id="part_name" placeholder="Masukkan nama part" autocomplete="off">
                        <input type="hidden" id="selected_part_code" name="selected_part_code">
                        <div id="suggestions" class="suggestions-dropdown" style="display: none;"></div>
                    </div>
                    <div class="form-group">
                        <label for="quantity">Jumlah</label>
                        <input type="number" class="form-control" id="quantity" placeholder="Masukkan jumlah">
                    </div>
                    <div class="form-group">
                        <label for="note">Catatan part ini</label>
                        <input type="text" class="form-control" id="note" placeholder="Jika Ada keterangan tambahan">
                    </div>
                    <button type="button" class="btn btn-primary" id="addItemToListBtn">Tambah</button>
                    </form>
                </div>
            </div>
            <hr class="mt-2">
            <h5 class="pt-4 pb-4">Daftar List Part</h5>
            <ul id="listPengajuan" class="list-group">
                <li id="pesanKosong" class="list-group-item text-muted">- belum terdapat list pengajuan -</li>
            </ul>
            <button type="button" class="mt-4 btn btn-success" id="simpanPengajuanBtn">Simpan Pengajuan Part</button>
        </div>
    </div>
</div>

@endsection
@section('resourcesite')
@vite('resources/js/pengajuan/Pengajuansite.js')
<style>
    /* Pagination styling */
    .pagination {
        display: flex;
        justify-content: center;
        list-style: none;
        padding: 0;
        margin-top: 15px;
    }
    .pagination .page-item {
        margin: 0 2px;
    }
    .pagination .page-item .page-link {
        color: #333;
        background-color: #fff;
        border: 1px solid #ddd;
        padding: 6px 12px;
        text-decoration: none;
        border-radius: 4px;
        cursor: pointer;
    }
    .pagination .page-item.active .page-link {
        color: #fff;
        background-color: #28a745;
        border-color: #28a745;
    }
    .pagination .page-item .page-link:hover {
        background-color: #f8f9fa;
    }
    #pengajuan-pagination {
        margin-top: 15px;
    }

    /* Badge styling */
    .badge {
        font-size: 0.85em;
        padding: 0.35em 0.65em;
        border-radius: 0.25rem;
        display: inline-block;
        font-weight: 700;
        line-height: 1;
        text-align: center;
        white-space: nowrap;
        vertical-align: baseline;
    }
    .bg-info {
        background-color: #17a2b8 !important;
        color: white;
    }
    .bg-secondary {
        background-color: #ffc107 !important;
        color: #212529;
    }
    .bg-success {
        background-color: #28a745 !important;
        color: white;
    }
    .bg-primary {
        background-color: #007bff !important;
        color: white;
    }
    .bg-secondary {
        background-color: #6c757d !important;
        color: white;
    }
</style>
<script>
    // Initial pagination data
    window.pengajuanPaginationData = {
        current_page: 1,
        per_page: 15,
        last_page: 1,
        total: 0
    };
</script>
@endsection