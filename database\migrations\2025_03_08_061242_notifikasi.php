<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::create('notifications', function (Blueprint $table) {
            $table->id();
            $table->string('title');
            $table->text('message');
            $table->enum('type', ['stock', 'part_request', 'other']);
            $table->string('routes', 100); 
            $table->string('from_site', 100)->nullable(); 
            $table->string('site_id', 50); 
            $table->foreign('site_id')
                ->references('site_id')
                ->on('sites')
                ->onUpdate('cascade');
            $table->boolean('is_read')->default(false);
            $table->timestamps();
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('notifications');
    }
};
