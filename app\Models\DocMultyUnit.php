<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class DocMultyUnit extends Model
{
    protected $table = 'docmultyunits';

    protected $fillable = [
        'tanggalstart',
        'tanggalend',
        'noBA',
        'unit_transaction_ids',
        'site_id',
        'document_path'
    ];

    protected $casts = [
        'unit_transaction_ids' => 'array',
        'tanggalstart' => 'date',
        'tanggalend' => 'date',
    ];

    /**
     * Get the site that owns the document
     */
    public function site()
    {
        return $this->belongsTo(Site::class, 'site_id', 'site_id');
    }

    /**
     * Get the unit transactions associated with this document
     */
    public function unitTransactions()
    {
        return $this->belongsToMany(UnitTransaction::class, 'unit_transaction_ids');
    }
}
