<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Concerns\HasUuids;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class OnHandQuantity extends Model
{
    use HasFactory, HasUuids;

    protected $table = 'on_hand_quantities';
    protected $primaryKey = 'on_hand_id';
    public $incrementing = false;
    protected $keyType = 'string';

    protected $fillable = [
        'part_inventory_id',
        'month',
        'quantity',
    ];

    public function partInventory()
    {
        return $this->belongsTo(PartInventory::class, 'part_inventory_id', 'part_inventory_id');
    }
}