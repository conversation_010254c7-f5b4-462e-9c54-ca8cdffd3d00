import Swal from "sweetalert2";

// Global variables for pagination
let currentSupplierPage = 1;
const supplierPerPage = 10; // 10 items per page for supplier table

document.addEventListener("DOMContentLoaded", function () {
    const supplierForm = document.getElementById("supplierForm");
    const supplierTableBody = document.querySelector("#supplierTable tbody");
    const supplierPaginationContainer = document.getElementById('supplier-pagination');
    const supplierIdhidden = document.getElementById("supplierIdhidden");
    const supplierIdInput = document.getElementById("supplierIdInput");
    const supplierNameInput = document.getElementById("supplierName");
    const addressInput = document.getElementById("alamat");
    const contactPersonInput = document.getElementById("contactPerson");

    // Function to create pagination item
    function createPaginationItem(page, text, isActive = false) {
        const li = document.createElement('li');
        li.className = `page-item ${isActive ? 'active' : ''}`;

        const a = document.createElement('a');
        a.className = 'page-link';
        a.href = '#';
        a.textContent = text;
        a.dataset.page = page;

        li.appendChild(a);
        return li;
    }

    // Function to render pagination
    function renderSupplierPagination(data) {
        try {
            let paginationContainer = document.getElementById('supplier-pagination');

            // If the container doesn't exist, create it
            if (!paginationContainer) {
                paginationContainer = document.createElement('div');
                paginationContainer.id = 'supplier-pagination';
                paginationContainer.className = 'mt-3';

                // Find the supplier table and append the pagination container after it
                const supplierTable = document.getElementById('supplierTable');
                if (supplierTable && supplierTable.parentNode) {
                    supplierTable.parentNode.insertBefore(paginationContainer, supplierTable.nextSibling);
                } else {
                    console.error('Could not find supplier table to append pagination');
                    return;
                }
            }

            paginationContainer.innerHTML = '';

            if (data.last_page > 1) {
                const pagination = document.createElement('ul');
                pagination.className = 'pagination pagination-rounded  justify-content-center';

                // Previous button
                if (data.current_page > 1) {
                    pagination.appendChild(createPaginationItem(data.current_page - 1, '\u00ab'));
                }

                // Page numbers
                for (let i = 1; i <= data.last_page; i++) {
                    pagination.appendChild(createPaginationItem(i, i, i === data.current_page));
                }

                // Next button
                if (data.current_page < data.last_page) {
                    pagination.appendChild(createPaginationItem(data.current_page + 1, '\u00bb'));
                }

                paginationContainer.appendChild(pagination);

                // Add event listeners to pagination links
                paginationContainer.querySelectorAll('.page-link').forEach(link => {
                    link.addEventListener('click', function(e) {
                        e.preventDefault();
                        const page = parseInt(this.dataset.page);
                        loadSuppliers(page);
                    });
                });
            }

            // Update global pagination data
            window.supplierPaginationData = data;
        } catch (error) {
            console.error('Error rendering pagination:', error);
        }
    }

    function loadSuppliers(page = 1) {
        // Update current page
        currentSupplierPage = page;

        // Show loading indicator
        supplierTableBody.innerHTML = '<tr><td colspan="5" class="text-center">Loading...</td></tr>';

        fetch(`/suppliers/paginated?page=${page}&per_page=${supplierPerPage}`)
            .then((response) => {
                if (!response.ok) {
                    throw new Error("Failed to load data");
                }
                return response.json();
            })
            .then((data) => {
                supplierTableBody.innerHTML = "";

                if (data.data && data.data.length > 0) {
                    data.data.forEach((supplier) => {
                        const row = document.createElement("tr");
                        row.innerHTML = `
                            <td class="py-1">${supplier.supplier_id}</td>
                            <td class="py-1">${supplier.supplier_name}</td>
                            <td class="py-1">${supplier.address}</td>
                            <td class="py-1">${supplier.contact_person}</td>
                            <td class="py-1">
                                <button class="edit-btn btn-primary text-white px-2 py-1 rounded" data-id="${supplier.supplier_id}">Edit</button>
                                <button class="delete-btn btn-danger text-white px-2 py-1 rounded" data-id="${supplier.supplier_id}">Delete</button>
                            </td>
                        `;
                        supplierTableBody.appendChild(row);
                    });
                } else {
                    supplierTableBody.innerHTML = '<tr><td colspan="5" class="text-center">No suppliers found</td></tr>';
                }

                // Render pagination
                renderSupplierPagination({
                    current_page: data.current_page,
                    per_page: data.per_page,
                    last_page: data.last_page,
                    total: data.total
                });
            })
            .catch((error) => {
                console.error("Error loading suppliers:", error);
                supplierTableBody.innerHTML = '<tr><td colspan="5" class="text-center text-danger">Failed to load data. Please try again.</td></tr>';
                Swal.fire({
                    icon: "error",
                    title: "Oops...",
                    text: "Failed to load suppliers! " + error,
                });
            });
    }

    supplierForm.addEventListener("submit", function (e) {
        e.preventDefault();

        const supplierId = supplierIdhidden.value;
        const url = supplierId ? `/suppliers/${supplierId}` : "/suppliers";
        const method = supplierId ? "PUT" : "POST";

        const formData = {
            supplier_id: supplierIdInput.value,
            supplier_name: supplierNameInput.value,
            address: addressInput.value,
            contact_person: contactPersonInput.value,
        };

        fetch(url, {
            method: method,
            headers: {
                "Content-Type": "application/json",
                "X-CSRF-TOKEN": document
                    .querySelector('meta[name="csrf-token"]')
                    .getAttribute("content"),
            },
            body: JSON.stringify(formData),
        })
            .then((response) => {
                if (!response.ok) {
                    return response.json().then((error) => {
                        throw new Error(
                            error.message || "Gagal menyimpan pemasok"
                        );
                    });
                }
                return response.json();
            })
            .then((data) => {
                Swal.fire({
                    icon: "success",
                    title: "Berhasil!",
                    text: supplierId
                        ? "Pemasok berhasil diperbarui!"
                        : "Pemasok berhasil ditambahkan!",
                });
                loadSuppliers(1); // Reload data tabel and reset to page 1
                supplierForm.reset();
                supplierIdInput.value = "";
            })
            .catch((error) => {
                console.error("Error:", error);
                Swal.fire({
                    icon: "error",
                    title: "Oops...",
                    text: "Gagal menyimpan pemasok! " + error,
                });
            });
    });

    window.editSupplier = async function (id) {
        try {
            const response = await fetch(`/suppliers/${id}`);
            if (!response.ok) {
                const error = await response.json();
                throw new Error(error.message || "Gagal memuat data pemasok");
            }
            const data = await response.json();
            supplierIdhidden.value = data.supplier_id;
            supplierIdInput.value = data.supplier_id;
            supplierNameInput.value = data.supplier_name;
            addressInput.value = data.address;
            contactPersonInput.value = data.contact_person;
        } catch (error) {
            console.error("Error:", error);
            Swal.fire({
                icon: "error",
                title: "Oops...",
                text: "Gagal memuat data pemasok! " + error,
            });
        }
    };

    window.deleteSupplier = function (supplierId) {
        Swal.fire({
            title: "Apakah Anda yakin?",
            text: "Anda tidak akan dapat mengembalikan ini!",
            icon: "warning",
            showCancelButton: true,
            confirmButtonColor: "#3085d6",
            cancelButtonColor: "#d33",
            confirmButtonText: "Ya, hapus!",
        }).then((result) => {
            if (result.isConfirmed) {
                fetch(`/suppliers/${supplierId}`, {
                    method: "DELETE",
                    headers: {
                        "X-CSRF-TOKEN": document
                            .querySelector('meta[name="csrf-token"]')
                            .getAttribute("content"),
                    },
                })
                    .then((response) => {
                        if (response.ok) {
                            loadSuppliers(1); // Reset to page 1 after delete
                            Swal.fire({
                                icon: "success",
                                title: "Dihapus!",
                                text: "Pemasok telah dihapus.",
                            });
                        } else {
                            console.error(
                                "Error deleting supplier:",
                                response.statusText
                            );
                            Swal.fire({
                                icon: "error",
                                title: "Oops...",
                                text: "Gagal menghapus pemasok!",
                            });
                        }
                    })
                    .catch((error) => {
                        console.error("Error:", error); // Tambahkan logging error
                        Swal.fire({
                            icon: "error",
                            title: "Oops...",
                            text: "Terjadi kesalahan saat menghapus!",
                        });
                    });
            }
        });
    };

    supplierTableBody.addEventListener("click", function (e) {
        if (e.target.classList.contains("edit-btn")) {
            const supplierId = e.target.getAttribute("data-id");
            editSupplier(supplierId);
        } else if (e.target.classList.contains("delete-btn")) {
            const supplierId = e.target.getAttribute("data-id");
            deleteSupplier(supplierId);
        }
    });

    // Initialize with page 1
    loadSuppliers(1);
});
