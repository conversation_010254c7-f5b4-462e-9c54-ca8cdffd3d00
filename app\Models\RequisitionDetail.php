<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class RequisitionDetail extends Model
{
    use HasFactory;

    protected $table = 'requisition_details';
    public $incrementing = false;
    protected $primaryKey = 'requisition_details_id';

    protected $fillable = [
        'requisition_id',
        'part_code',
        'quantity',
        'surat_jalan_path',
        'status_details',
        'quantity_send',
        'quantity_confirm',
        'notes',
        'notes_ho'
    ];

    protected function setKeysForSaveQuery($query)
    {
        return $query->where([
            'requisition_id' => $this->requisition_id,
            'part_code' => $this->part_code
        ]);
    }

    public function requisition()
    {
        return $this->belongsTo(Requisition::class, 'requisition_id', 'requisition_id');
    }

    public function part()
    {
        return $this->belongsTo(Part::class, 'part_code');
    }
}

