<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Concerns\HasUuids;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class PartMerge extends Model
{
    use HasFactory;

    protected $table = 'part_merges';
    protected $primaryKey = 'part_merge_id';
    public $incrementing = false;
    protected $keyType = 'string';

    protected $fillable = [
        'part_merge_name',
        'quantity',
        'part_code_old',
        'part_code_new',
        'notes',
        'site_id',
        'merge_date',
    ];

    protected $casts = [
        'merge_date' => 'datetime',
    ];

    public function mergedPartParts()
    {
        return $this->hasMany(MergedPartPart::class, 'part_merge_id', 'part_merge_id');
    }

    public function mergedPartSites()
    {
        return $this->hasMany(MergedPartSite::class, 'part_merge_id', 'part_merge_id');
    }
}