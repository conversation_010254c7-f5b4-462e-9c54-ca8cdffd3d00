@extends('sites.content')
@section('contentsite');
@section('title', 'Withdraw Part')
<div class="row bgwhite page-title-box shadow-kit mb-1">
    <div class="col d-flex align-items-center" style="max-width: fit-content;">
        <button class="button-menu-mobile text-blue-500 btn-sm">
            <i style="font-size: 30px;" class="mdi mdi-menu"></i>
        </button>
        <div class="text">
            <p class="font-bold m-0">{{ session('name') }}</p>
        </div>
    </div>
    <div class="col">
        <ul class="list-unstyled topnav-menu float-right mb-0">
            <li class="dropdown notification-list">
                <a class="nav-link dropdown-toggle  waves-effect waves-light" data-toggle="dropdown" href="#"
                    role="button" aria-haspopup="false" aria-expanded="false">
                    <i class="mdi mdi-bell-outline noti-icon"></i>
                    <span class="badge badge-danger badge-pill float-right"></span>
                </a>
                <div class="dropdown-menu dropdown-menu-right dropdown-lg">
                    <div class="dropdown-item noti-title">
                        <h5 class="font-16 text-white m-0">
                            <span class="float-right">
                                <a href="javascript:void(0);" class="text-white" id="clear-all">
                                    <small></small>
                                </a>
                            </span>Notifikasi
                        </h5>
                    </div>
                    <div class="noti-scroll" id="notification-list">
                    </div>

                    </a>
                </div>
            </li>
        </ul>
    </div>
</div>
<div id="container" class="p-1">
    <div class="row">
        <div class="col-md-8">
            <div class="shadow-kit bgwhite p-4">
                <div id="filters" class="mb-3">
                    <h2 class="h5 font-bold text-uppercase mb-3">Filters</h2>
                    <div class="d-flex flex-wrap gap-2 align-items-end">
                        <div>
                            <label for="statusFilter" class="form-label">Status</label>
                            <select class="form-control" id="statusFilter">
                                <option value="">Semua status</option>
                                <option value="Pending">Pending</option>
                                <option value="Approved">Approved</option>
                                <option value="Rejected">Rejected</option>
                                <option value="In Transit">In Transit</option>
                                <option value="Completed">Completed</option>
                            </select>
                        </div>

                        <div>
                            <label for="start_date" class="form-label">Tanggal Awal</label>
                            <input type="date" class="form-control" id="start_date">
                        </div>

                        <div>
                            <label for="end_date" class="form-label">Tanggal Akhir</label>
                            <input type="date" class="form-control" id="end_date">
                        </div>

                        <div>
                            <label for="siteFilter" class="form-label">Site</label>
                            <select style="display: none;" class="form-control" id="siteFilter">
                                <option value="">Semua Site</option>
                                <option value="{{session('site_id')}}">{{session('site_id')}}</option>
                            </select>
                        </div>
                    </div>
                </div>
                <div id="table-container">
                    <h2 class="h5 text-center text-uppercase">Return Part Ke HO</h2>
                    <table id="withdrawals-table" class="table">
                        <thead class="table-dark text-white p-2">
                            <tr>
                                <th class="p-2">ID</th>
                                <th class="p-2">Part Name</th>
                                <th class="p-2">From Site</th>
                                <th class="p-2">Keterangan</th>
                                <th class="p-2">Jumlah Permintaan</th>
                                <th class="p-2">Keterangan HO</th>
                                <th class="p-2">Status</th>
                                <th class="p-2">Aksi</th>
                            </tr>
                        </thead>
                        <tbody></tbody>
                    </table>
                    <div id="withdrawals-pagination" class="mt-3">
                        <!-- Pagination will be rendered here by JavaScript -->
                    </div>
                </div>
            </div>
            <div class="shadow-kit bgwhite p-4 mt-2">
                <div id="completed-table-container">
                    <h2 class="h5 text-center text-uppercase mt-4">Completed Requests</h2>
                    <table id="completed-table" class="table table-success">
                        <thead class="table-dark text-white p-2">
                            <tr>
                                <th class="p-2">ID</th>
                                <th class="p-2">Part Name</th>
                                <th class="p-2">From Site</th>
                                <th class="p-2">Keterangan</th>
                                <th class="p-2">Jumlah Permintaan</th>
                                <th class="p-2">Keterangan HO</th>
                                <th class="p-2">Status</th>
                            </tr>
                        </thead>
                        <tbody></tbody>
                    </table>
                    <div id="completed-pagination" class="mt-3">
                        <!-- Pagination will be rendered here by JavaScript -->
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-4">
            <div class="shadow-kit bgwhite p-4 mb-3">
                <div id="create-form" style="position: relative;">
                    <h2 class="h5 text-uppercase font-bold mb-3">Buat Permintaan Return Part</h2>
                    <form id="create-withdrawal-form">
                        <input type="hidden" id="withdrawal_id_edit" name="withdrawal_id_edit" value="">
                        <input type="hidden" id="from_site_id" name="from_site_id" value="{{session('site_id')}}">

                        <div class="form-group mb-3">
                            <label for="part_code">Kode Part</label>
                            <div class="position-relative">
                                <input type="text" class="form-control" id="part_code" name="part_code" placeholder="Cari part..." autocomplete="off" required>
                                <div id="part-suggestions" class="position-absolute w-100" style="display: none; z-index: 1000;">
                                    <ul class="list-group"></ul>
                                </div>
                            </div>
                            <small id="part-name-display" class="form-text text-muted"></small>
                        </div>

                        <div class="form-group mb-3">
                            <label for="requested_quantity">Jumlah Part</label>
                            <input type="number" class="form-control" id="requested_quantity" name="requested_quantity" min="1" required>
                        </div>

                        <div class="form-group mb-3">
                            <label for="withdrawal_reason">Keterangan </label>
                            <textarea class="form-control" id="withdrawal_reason" name="withdrawal_reason" rows="3" required>-</textarea>
                        </div>

                        <div class="form-group mt-4 d-flex justify-content-end">
                            <button type="button" id="submit-button" class="btn btn-primary">Kirim Permintaan</button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
        <div class="col max500" id="colhidden" style="display: none;">
            <button type="button" class="btn close-btn position-absolute" id="btnclosed" style="top: 10px; right: 10px; z-index: 1000;">×</button>
            <div class="shadow-kit bgwhite p-4">
                <div id="form" style="position: relative;">
                    <h2 class="h5 text-uppercase font-bold">Ubah Status Pengajuan Return</h2>
                    <form id="update-withdrawal-form" style="display: none;">
                        <div class="form-group mb-3">
                            <label for="withdrawal_id_status">ID Permintaan</label>
                            <input disabled class="form-control" type="text" id="withdrawal_id_status" name="withdrawal_id_status" required>
                        </div>

                        <div class="form-group mb-3">
                            <label for="approved_quantity">Jumlah Part Yang Bisa Return</label>
                            <input class="form-control" type="number" id="approved_quantity" name="approved_quantity">
                        </div>

                        <div class="form-group mb-3">
                            <label for="status">Ubah Status Permintaan</label>
                            <select class="form-control" id="status" name="status">
                                <option value="Pending">Pending</option>
                                <option value="Rejected">Tolak</option>
                                <option value="In Transit">In Transit</option>
                                <option value="Completed">Completed</option>
                            </select>
                        </div>

                        <div class="form-group mb-3">
                            <label for="notes">Tambahkan Keterangan</label>
                            <textarea class="form-control" id="notes" name="notes" rows="3"></textarea>
                        </div>

                        <div class="form-group mt-4 d-flex justify-content-end">
                            <button type="button" class="btn btn-secondary" onclick="document.getElementById('btnclosed').click();">Batal</button>
                            <button type="button" class="btn btn-success ml-2">Update Request</button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
<style>
    /* Pagination styling */
    .pagination {
        display: flex;
        justify-content: center;
        list-style: none;
        padding: 0;
        margin-top: 15px;
    }

    .pagination .page-item {
        margin: 0 2px;
    }

    .pagination .page-item .page-link {
        color: #333;
        background-color: #fff;
        border: 1px solid #ddd;
        padding: 6px 12px;
        text-decoration: none;
        border-radius: 4px;
        cursor: pointer;
    }

    .pagination .page-item.active .page-link {
        color: #fff;
        background-color: #28a745;
        border-color: #28a745;
    }

    .pagination .page-item .page-link:hover {
        background-color: #f8f9fa;
    }

    /* Part suggestions styling */
    #part-suggestions {
        background-color: white;
        border: 1px solid #ddd;
        border-radius: 4px;
        box-shadow: 0 2px 5px rgba(0,0,0,0.2);
        max-height: 200px;
        overflow-y: auto;
    }

    #part-suggestions ul {
        list-style: none;
        padding: 0;
        margin: 0;
    }

    #part-suggestions li {
        padding: 8px 12px;
        cursor: pointer;
        border-bottom: 1px solid #f0f0f0;
    }

    #part-suggestions li:hover {
        background-color: #f8f9fa;
    }
</style>
@endsection
@section('resourcesite')
@vite(['resources/js/site/Withrawals.js'])
@endsection