<?php

namespace App\Http\Controllers\Sites;

use App\Http\Controllers\Controller;
use App\Models\LogAktivitas;
use App\Models\Notification;
use App\Models\Part;
use App\Models\Requisition;
use App\Models\RequisitionDetail;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Validator;

class PengajuanController extends Controller
{
    public function index()
    {
        $pengajuans = Requisition::with('details')->where('site_id', session('site_id'))->get();
        return view('pengajuan.viewpengajuan', compact('pengajuans'));
    }
    public function getcount()
    {
        $pengajuans = Requisition::with('details')
            ->whereNotIn('status', ['selesai', 'disetujui'])
            ->get();
        $jumlah = count($pengajuans);
        return  response()->json(['jumlah' => $jumlah]);
    }

    public function autocompleteParts(Request $request)
    {
        $query = $request->input('query');
        $siteId = session('site_id');

        $parts = Part::join('part_inventories', 'parts.part_code', '=', 'part_inventories.part_code')
            ->where('part_inventories.site_id', $siteId)
            ->where(function($q) use ($query) {
                $q->where('parts.part_name', 'like', '%' . $query . '%')
                  ->orWhere('parts.part_code', 'like', '%' . $query . '%');
            })
            ->select('parts.part_code', 'parts.part_name')
            ->groupBy('parts.part_code', 'parts.part_name')
            ->limit(10)
            ->get();

        return response()->json($parts);
    }
    public function getdata(Request $request)
    {
        $page = $request->input('page', 1);
        $perPage = $request->input('per_page', 15); // Default to 15 items per page

        $query = Requisition::with('details')
            ->where('site_id', session('site_id'));

        // Get total count for pagination
        $total = $query->count();

        // Get paginated data
        $pengajuans = $query->orderBy('created_at', 'desc')
                          ->skip(($page - 1) * $perPage)
                          ->take($perPage)
                          ->get();

        return response()->json([
            'data' => $pengajuans,
            'current_page' => (int)$page,
            'per_page' => (int)$perPage,
            'last_page' => ceil($total / $perPage),
            'total' => $total
        ]);
    }

    public function store(Request $request)
    {
        // Log the incoming request data for debugging
        Log::info('Pengajuan store request data:', [
            'request_data' => $request->all()
        ]);

        $validator = Validator::make($request->all(), [
            'title' => 'required',
            'details' => 'required|array',
            'details.*.part_code' => 'required|exists:parts,part_code',
            'details.*.quantity' => 'required|integer|min:1',
            'details.*.note' => 'nullable|string',
        ]);
        if ($validator->fails()) {
            Log::warning('Pengajuan validation failed:', [
                'errors' => $validator->errors()->toArray()
            ]);
            return response()->json(['errors' => $validator->errors()], 422);
        }
        try {
            $requisition = Requisition::create([
                'title' => $request->title,
                'site_id' => session('site_id'),
                'requisition_type' => 'Pembelian Part',
                'requisition_date' => now()->toDateString(),
                'modified_by' => session('name'),
                'status' => 'diajukan',
                'notes' => $request->notes,
            ]);
            $reqid = $requisition->requisition_id;
            if ($reqid) {
                foreach ($request->details as $detail) {
                    RequisitionDetail::create([
                        'requisition_id' => $reqid,
                        'part_code' => $detail['part_code'],
                        'status_details' => 'pending',
                        'quantity' => $detail['quantity'],
                        'notes' => $detail['note'] ?? null,
                    ]);
                }
            }
            // Log Aktivitas (CREATE)
            LogAktivitas::create([
                'site_id' => session('site_id'),
                'name' => session('name'),
                'action' => 'Menambahkan Pengajuan',
                'description' => "User " . session('name') . " menambahkan Pengajuan: " . $request->title,
                'table' => "Requisitions",
                'ip_address' => $request->ip(),
            ]);

            $notification = new Notification();
            $notification->title = "Pengajuan Pembelian Part";
            $notification->message = "Judul Pengajuan: {$request->title}";
            $notification->type = "part_request";
            $notification->routes = "requisitionsho.index";
            $notification->site_id = 'WHO'; // Send to warehouse (WHO) instead of the site that made the request
            $notification->from_site = session('site_id'); // Set the source site correctly
            $notification->is_read = false;
            $notification->save();

            Log::info("Notification created", [
                "title" => $notification->title,
                "message" => $notification->message,
                "type" => $notification->type,
                "routes" => $notification->routes,
                "site_id" => $notification->site_id,
                "from_site" => $notification->from_site,
            ]);

            return response()->json(['success' => true, 'message' => 'Requisition berhasil disimpan!', 'data' => $requisition], 201);
        } catch (\Exception $e) {
            Log::error("Gagal membuat Requisition!", [
                "error" => $e->getMessage(),
                "trace" => $e->getTraceAsString()
            ]);
            return response()->json(['success' => false, 'message' => 'Gagal membuat Requisition! ' . $e->getMessage()], 400);
        }
    }

    public function show($id)
    {
        try {
            $requisition = Requisition::with(['details.part'])->findOrFail($id);
            return response()->json($requisition);
        } catch (\Exception $e) {
            return response()->json(['message' => 'Requisition tidak ditemukan', 'error' => $e->getMessage()], 400);
        }
    }

    public function destroy(Request $request, $id)
    {
        // Start a database transaction to ensure all operations are atomic
        DB::beginTransaction();

        try {
            // Find the requisition with its details
            $requisition = Requisition::with('details')->findOrFail($id);

            // Check if the requisition status is 'diajukan'
            if ($requisition->status !== 'diajukan') {
                DB::rollBack();
                return response()->json([
                    'success' => false,
                    'message' => 'Gagal menghapus Requisition! Hanya pengajuan dengan status "diajukan" yang dapat dihapus.'
                ], 400);
            }

            // Check if all details have status 'pending'
            foreach ($requisition->details as $detail) {
                if ($detail->status_details !== 'pending') {
                    DB::rollBack();
                    return response()->json([
                        'success' => false,
                        'message' => 'Gagal menghapus Requisition! Beberapa item sudah diproses oleh warehouse.'
                    ], 400);
                }

                // Check if there are any related stock transactions
                $hasTransactions = DB::table('stock_transactions')
                    ->where('requisition_details_id', $detail->requisition_details_id)
                    ->exists();

                if ($hasTransactions) {
                    DB::rollBack();
                    return response()->json([
                        'success' => false,
                        'message' => 'Gagal menghapus Requisition! Terdapat transaksi stok terkait dengan pengajuan ini.'
                    ], 400);
                }
            }

            // Delete all requisition details first
            foreach ($requisition->details as $detail) {
                $detail->delete();
            }

            // Then delete the requisition
            $requisition->delete();

            // Log the deletion
            LogAktivitas::create([
                'site_id' => session('site_id'),
                'name' => session('name'),
                'action' => 'Menghapus Pengajuan',
                'description' => "User " . session('name') . " menghapus Pengajuan: " . $requisition->title,
                'table' => "Requisitions",
                'ip_address' => $request->ip(),
            ]);

            Log::info("Requisition deleted", [
                "requisition_id" => $id,
                "deleted_by" => session('name'),
                "site_id" => session('site_id')
            ]);

            // Commit the transaction
            DB::commit();

            return response()->json([
                'success' => true,
                'message' => 'Requisition berhasil dihapus!'
            ], 200);
        } catch (\Exception $e) {
            // Roll back the transaction in case of error
            DB::rollBack();

            Log::error("Error deleting requisition", [
                "requisition_id" => $id,
                "error" => $e->getMessage(),
                "trace" => $e->getTraceAsString()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Gagal menghapus Requisition!',
                'error' => $e->getMessage()
            ], 400);
        }
    }

    public function getRequisitionDetails($id)
    {
        try {
            $details = RequisitionDetail::where('requisition_id', $id)->get();
            return response()->json($details);
        } catch (\Exception $e) {
            return response()->json(['message' => 'Gagal mengambil detail requisition', 'error' => $e->getMessage()], 400);
        }
    }
}


