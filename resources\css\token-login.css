:root {
    --primary-color: rgba(255, 255, 255, 0.9);
    --secondary-color: rgba(255, 255, 255, 0.3);
    --accent-color: rgb(40, 21, 211);
    --accent-hover-color: rgb(60, 41, 231);
    --highlight-color: rgb(251, 255, 0);
    --danger-bg-color: rgba(255, 93, 72, 0.2);
    --danger-border-color: rgba(255, 93, 72, 0.5);
}

body {
    margin: 0;
    padding: 0;
    min-height: 100vh;
    display: flex;
    justify-content: center;
    align-items: center;
    background-size: cover;
    background-position: center;
    font-family: 'Segoe UI', sans-serif;
    position: relative;
    overflow: hidden;
}

.token-container {
    position: relative;
    z-index: 2;
    display: flex;
    backdrop-filter: blur(10px);
    background-color: rgba(15, 18, 179, 0.24);
    border-radius: 25px;
    border: 1px solid var(--secondary-color);
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
    overflow: hidden;
    flex-direction: row;
    width: 80%;
    max-width: 900px;
}

.token-section {
    padding: 40px;
    width: 50%;
    box-sizing: border-box;
}

.token-box {
    text-align: center;
}

.token-box h2 {
    color: var(--primary-color);
    margin-bottom: 30px;
    font-size: 2.5em;
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.1);
}

.token-box p {
    color: rgba(255, 255, 255, 0.7);
    margin-bottom: 25px;
}

.input-group {
    position: relative;
    margin-bottom: 25px;
}

.input-group i {
    position: absolute;
    left: 15px;
    top: 50%;
    transform: translateY(-50%);
    color: var(--primary-color);
}

input {
    width: 100%;
    padding: 15px 15px 15px 45px;
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid var(--secondary-color);
    border-radius: 8px;
    color: var(--primary-color);
    font-size: 1em;
    backdrop-filter: blur(5px);
    transition: all 0.3s ease;
    box-sizing: border-box;
}

input::placeholder {
    color: rgba(255, 255, 255, 0.7);
}

input:focus {
    outline: none;
    background: rgba(15, 5, 77, 0.445);
    color: var(--highlight-color);
    border-color: var(--primary-color);
}

button {
    width: 100%;
    padding: 15px;
    background: var(--accent-color);
    border: 1px solid var(--secondary-color);
    border-radius: 8px;
    color: var(--primary-color);
    font-size: 1.1em;
    cursor: pointer;
    transition: all 0.3s ease;
    margin-top: 15px;
    backdrop-filter: blur(5px);
    box-sizing: border-box;
}

button:hover {
    background: var(--accent-hover-color);
    transform: translateY(-2px);
}

.back-link {
    margin-top: 20px;
    text-align: center;
}

.back-link a {
    color: var(--primary-color);
    text-decoration: none;
    font-size: 0.9em;
    transition: all 0.3s ease;
}

.back-link a:hover {
    color: var(--highlight-color);
}

.alert-danger {
    background-color: var(--danger-bg-color);
    border: 1px solid var(--danger-border-color);
    color: var(--primary-color);
    border-radius: 8px;
    padding: 10px;
    margin-bottom: 20px;
}

.alert-danger ul {
    padding-left: 20px;
    margin: 0;
}

.chart-section {
    width: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
}

.chart-section img {
    max-width: 100%;
    height: auto;
}

.footer {
    position: absolute;
    bottom: 20px;
    width: 100%;
    text-align: center;
    color: rgba(255, 255, 255, 0.5);
    font-size: 0.8em;
}

/* Media query for smaller screens */
@media (max-width: 768px) {
    .token-container {
        flex-direction: column;
        width: 95%;
    }

    .token-section {
        width: 100%;
        padding: 20px;
    }

    .chart-section {
        display: none;
    }
}
