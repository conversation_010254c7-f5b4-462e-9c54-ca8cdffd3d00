<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('invoices', function (Blueprint $table) {
            // Add po_number column if it doesn't exist
            if (!Schema::hasColumn('invoices', 'po_number')) {
                $table->string('po_number')->nullable()->after('no_invoice');
            }
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('invoices', function (Blueprint $table) {
            if (Schema::hasColumn('invoices', 'po_number')) {
                $table->dropColumn('po_number');
            }
        });
    }
};
