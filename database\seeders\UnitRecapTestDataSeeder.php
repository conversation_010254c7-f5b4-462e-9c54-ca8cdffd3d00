<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\DailyReport;
use App\Models\Unit;
use App\Models\Job;
use App\Models\Technician;
use Illuminate\Support\Facades\DB;
use Carbon\Carbon;

class UnitRecapTestDataSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Define test data parameters
        $startDate = Carbon::create(2025, 6, 1);
        $endDate = Carbon::create(2025, 6, 3);
        $problemComponents = ['COMPRESSOR', 'THERMOSTAT', 'ADJUST V-BELT', 'AIR FILTER'];
        $shifts = ['DAY', 'NIGHT'];
        
        // Get or create test units
        $units = $this->createTestUnits();
        
        // Get or create test jobs
        $jobs = $this->createTestJobs();
        
        // Get or create test technicians
        $technicians = $this->createTestTechnicians();
        
        // Generate daily reports for each date
        $currentDate = $startDate->copy();
        
        while ($currentDate <= $endDate) {
            // Generate 3-5 reports per day
            $reportsPerDay = rand(3, 5);
            
            for ($i = 0; $i < $reportsPerDay; $i++) {
                $this->createDailyReport(
                    $currentDate,
                    $units,
                    $problemComponents,
                    $shifts,
                    $jobs,
                    $technicians
                );
            }
            
            $currentDate->addDay();
        }
        
        $this->command->info('Unit Recap test data created successfully!');
    }
    
    /**
     * Create or get test units
     */
    private function createTestUnits()
    {
        $unitData = [
            ['unit_code' => 'EX001', 'unit_type' => 'Excavator CAT 320D'],
            ['unit_code' => 'EX002', 'unit_type' => 'Excavator CAT 330D'],
            ['unit_code' => 'DT001', 'unit_type' => 'Dump Truck CAT 777D'],
            ['unit_code' => 'DT002', 'unit_type' => 'Dump Truck CAT 785C'],
            ['unit_code' => 'LD001', 'unit_type' => 'Loader CAT 980K'],
            ['unit_code' => 'LD002', 'unit_type' => 'Loader CAT 966K'],
            ['unit_code' => 'GR001', 'unit_type' => 'Grader CAT 140M'],
            ['unit_code' => 'BD001', 'unit_type' => 'Bulldozer CAT D8T'],
            ['unit_code' => 'WL001', 'unit_type' => 'Water Truck CAT 777D'],
            ['unit_code' => 'FL001', 'unit_type' => 'Fuel Truck CAT 777D'],
        ];
        
        $units = collect();
        
        foreach ($unitData as $data) {
            $unit = Unit::firstOrCreate(
                ['unit_code' => $data['unit_code']],
                [
                    'unit_code' => $data['unit_code'],
                    'unit_type' => $data['unit_type'],
                    'site_id' => 'PPA', // Default site
                    'nopr' => rand(1000, 9999),
                    'noqtn' => rand(1000, 9999),
                ]
            );
            $units->push($unit);
        }
        
        return $units;
    }
    
    /**
     * Create or get test jobs
     */
    private function createTestJobs()
    {
        $jobDescriptions = [
            'Replace hydraulic filter',
            'Check engine oil level',
            'Inspect air conditioning system',
            'Clean air filter',
            'Adjust V-belt tension',
            'Replace thermostat',
            'Check compressor operation',
            'Lubricate grease points',
            'Inspect hydraulic hoses',
            'Check tire pressure',
            'Replace fuel filter',
            'Inspect electrical connections',
            'Check cooling system',
            'Replace worn parts',
            'Perform preventive maintenance',
        ];
        
        $jobs = collect();
        
        foreach ($jobDescriptions as $description) {
            $job = Job::firstOrCreate(
                ['job_description' => $description],
                [
                    'job_description' => $description,
                    'highlight' => rand(0, 1) == 1, // Random highlight
                ]
            );
            $jobs->push($job);
        }
        
        return $jobs;
    }
    
    /**
     * Create or get test technicians
     */
    private function createTestTechnicians()
    {
        $technicianNames = [
            'Ahmad Susanto',
            'Budi Prasetyo',
            'Candra Wijaya',
            'Dedi Kurniawan',
            'Eko Saputra',
            'Fajar Nugroho',
            'Gunawan Santoso',
            'Hendra Setiawan',
            'Indra Permana',
            'Joko Widodo',
        ];
        
        $technicians = collect();
        
        foreach ($technicianNames as $name) {
            $technician = Technician::firstOrCreate(
                ['name' => $name],
                ['name' => $name]
            );
            $technicians->push($technician);
        }
        
        return $technicians;
    }
    
    /**
     * Create a single daily report
     */
    private function createDailyReport($date, $units, $problemComponents, $shifts, $jobs, $technicians)
    {
        // Random selections
        $unit = $units->random();
        $problemComponent = $problemComponents[array_rand($problemComponents)];
        $shift = $shifts[array_rand($shifts)];
        
        // Generate random times
        $hourIn = $shift === 'DAY' ? rand(6, 8) : rand(18, 20);
        $hourOut = $shift === 'DAY' ? rand(14, 17) : rand(2, 5);
        if ($shift === 'NIGHT' && $hourOut < $hourIn) {
            $hourOut += 24; // Handle overnight shift
        }

        // Create full datetime for hour_in and hour_out
        $hourInFormatted = $date->copy()->setTime($hourIn, rand(0, 59), 0);
        $hourOutFormatted = $date->copy()->setTime($hourOut % 24, rand(0, 59), 0);

        // If overnight shift, add a day to hour_out
        if ($shift === 'NIGHT' && $hourOut >= 24) {
            $hourOutFormatted->addDay();
        }
        
        // Create daily report
        $dailyReport = DailyReport::create([
            'unit_id' => $unit->id,
            'hm' => rand(1000, 9999) + (rand(0, 99) / 100), // Random hour meter
            'problem' => 'UNSCHEDULED', // Set to UNSCHEDULED as requested
            'problem_component' => $problemComponent,
            'problem_description' => $this->generateProblemDescription($problemComponent),
            'date_in' => $date->format('Y-m-d'),
            'hour_in' => $hourInFormatted,
            'hour_out' => $hourOutFormatted,
            'shift' => $shift,
        ]);
        
        // Attach random jobs (1-3 jobs per report)
        $selectedJobs = $jobs->random(rand(1, 3));
        foreach ($selectedJobs as $job) {
            DB::table('daily_report_jobs')->insert([
                'daily_report_id' => $dailyReport->daily_report_id,
                'job_description_id' => $job->job_description_id,
                'created_at' => now(),
                'updated_at' => now(),
            ]);
        }
        
        // Attach random technicians (1-2 technicians per report)
        $selectedTechnicians = $technicians->random(rand(1, 2));
        foreach ($selectedTechnicians as $technician) {
            DB::table('daily_report_technicians')->insert([
                'daily_report_id' => $dailyReport->daily_report_id,
                'technician_id' => $technician->technician_id,
                'created_at' => now(),
                'updated_at' => now(),
            ]);
        }
        
        return $dailyReport;
    }
    
    /**
     * Generate problem description based on component
     */
    private function generateProblemDescription($component)
    {
        $descriptions = [
            'COMPRESSOR' => [
                'Compressor not working properly',
                'Low compression detected',
                'Compressor making unusual noise',
                'Compressor overheating issue',
            ],
            'THERMOSTAT' => [
                'Thermostat stuck in closed position',
                'Engine overheating due to faulty thermostat',
                'Thermostat needs replacement',
                'Temperature regulation issue',
            ],
            'ADJUST V-BELT' => [
                'V-belt tension too loose',
                'V-belt showing signs of wear',
                'V-belt alignment issue',
                'V-belt needs adjustment',
            ],
            'AIR FILTER' => [
                'Air filter clogged with dust',
                'Air filter needs replacement',
                'Reduced airflow due to dirty filter',
                'Air filter maintenance required',
            ],
        ];
        
        $componentDescriptions = $descriptions[$component] ?? ['General maintenance required'];
        return $componentDescriptions[array_rand($componentDescriptions)];
    }
}
