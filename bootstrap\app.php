<?php

use App\Http\Middleware\CheckAdminRole;
use App\Http\Middleware\CheckAdminSite;
use App\Http\Middleware\CheckSales;
use App\Http\Middleware\CheckSuperAdmin;
use App\Http\Middleware\CheckSuperadminPin;
use App\Http\Middleware\CheckAccessMode;
use App\Http\Middleware\CheckKasir;
use Illuminate\Foundation\Application;
use Illuminate\Foundation\Configuration\Exceptions;
use Illuminate\Foundation\Configuration\Middleware;
use Symfony\Component\HttpKernel\Exception\HttpException;
// use Throwable;

return Application::configure(basePath: dirname(__DIR__))
    ->withRouting(
        web: __DIR__.'/../routes/web.php',
        commands: __DIR__.'/../routes/console.php',
        health: '/up',
    )
    ->withMiddleware(function (Middleware $middleware) {
        $middleware->alias([
            'admin' => CheckAdminRole::class,
            'adminsite' => CheckAdminSite::class,
            'sales' => CheckSales::class,
            'superadmin' => CheckSuperAdmin::class,
            'superadmin.pin' => CheckSuperadminPin::class,
            'access.mode' => CheckAccessMode::class,
            'kasir' => CheckKasir::class,
       ]);
    })
    ->withExceptions(function (Exceptions $exceptions) {
        // Register custom error pages for HTTP exceptions
        $exceptions->renderable(function (\Symfony\Component\HttpKernel\Exception\HttpException $e) {
            if ($e->getStatusCode() === 419) {
                return response()->view('errors.419', [], 419);
            }

            if ($e->getStatusCode() === 404) {
                return response()->view('errors.404', [], 404);
            }

            if ($e->getStatusCode() === 403) {
                return response()->view('errors.403', [], 403);
            }

            if ($e->getStatusCode() === 401) {
                return response()->view('errors.401', [], 401);
            }
            if ($e->getStatusCode() === 500) {
                return response()->view('errors.500', [], 500);
            }
            // For any other HTTP exception, use the generic error page
            return response()->view('errors.generic', ['exception' => $e], $e->getStatusCode());
        });

        // Handle other exceptions (non-HTTP)
        $exceptions->renderable(function (\Throwable $e) {
            if (!$e instanceof \Symfony\Component\HttpKernel\Exception\HttpException) {
                return response()->view('errors.500', [], 500);
            }
            return null;
        });
    })->create();
