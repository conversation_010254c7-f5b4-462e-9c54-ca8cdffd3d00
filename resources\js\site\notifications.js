// Function to update the count of incoming transactions for the site
function updateJumlahInSite() {
    fetch("/transactions/site/count", {
        method: "GET",
        headers: {
            "X-CSRF-TOKEN": document.querySelector('meta[name="csrf-token"]').content,
            "Content-Type": "application/json",
        },
    })
        .then((response) => response.json())
        .then((data) => {
            const jumlahinsite = document.getElementById("jumlahinsite");
            if (jumlahinsite) { // Check if element exists
                if (data.count > 0) {
                    jumlahinsite.textContent = data.count;
                    jumlahinsite.style.display = "flex";
                } else {
                    jumlahinsite.style.display = "none";
                }
            }
        })
        .catch((error) => {
            console.error("Error fetching transaction count:", error);
        });
}

// Update the count every 30 seconds
setInterval(updateJumlahInSite, 30000);

// Initial update when the page loads
document.addEventListener("DOMContentLoaded", updateJumlahInSite);

// Export the function for use in other files if needed
export { updateJumlahInSite };
