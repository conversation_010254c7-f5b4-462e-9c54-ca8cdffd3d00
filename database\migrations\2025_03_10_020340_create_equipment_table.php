<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('equipment', function (Blueprint $table) {
            $table->id();
            $table->string('name');
            $table->text('description')->nullable();
            $table->timestamps();
        });

        Schema::create('equipment_stocks', function (Blueprint $table) {
            $table->id();
            $table->integer('quantity')->unsigned();
            $table->foreignId('equipment_id')->constrained('equipment');
            $table->string('site_id', 50)->nullable(); // Changed to string
            $table->foreign('site_id')
                ->references('site_id')
                ->on('sites');
            $table->enum('status', ['Baik', 'Cukup Baik', 'Kurang Baik', 'Rusak'])->default('Baik');
            $table->timestamp('received_at')->useCurrent();
            $table->timestamps();
        });

        Schema::create('equipment_requests', function (Blueprint $table) {
            $table->id();
            $table->string('site_id', 50)->nullable(); // Changed to string
            $table->foreign('site_id')
                ->references('site_id')
                ->on('sites');
            $table->enum('status', ['pending', 'approved', 'rejected', 'completed'])->default('pending');
            $table->timestamps();
        });

        Schema::create('equipment_request_items', function (Blueprint $table) {
            $table->id();
            $table->foreignId('request_id')->constrained('equipment_requests');
            $table->foreignId('equipment_id')->constrained('equipment');
            $table->integer('quantity')->unsigned();
            $table->enum('required_status', ['Baik', 'Cukup Baik', 'Kurang Baik'])->default('Baik');
            $table->enum('status', ['pending', 'approved', 'rejected'])->default('pending');
            $table->timestamps();
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('equipment');
        Schema::dropIfExists('equipment_stock');
        Schema::dropIfExists('equipment_requests');
        Schema::dropIfExists('equipment_request_items');
    }
};
