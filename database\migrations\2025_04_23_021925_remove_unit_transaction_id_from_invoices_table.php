<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('invoices', function (Blueprint $table) {
            // Remove the unit_transaction_id column
            $table->dropForeign(['unit_transaction_id']);
            $table->dropColumn('unit_transaction_id');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('invoices', function (Blueprint $table) {
            $table->foreignId('unit_transaction_id')->nullable()->constrained('unit_transactions')->onUpdate('cascade')->onDelete('cascade');
        });
    }
};
