1. Add this to the imports at the top of the file:
use App\Http\Controllers\StockReconciliationController;

2. Add these routes inside the admin middleware group, before the "pengjuan notif" comment:
    // Stock Reconciliation
    Route::get('/stock-reconciliation', [StockReconciliationController::class, 'index'])->name('stock.reconciliation');
    Route::post('/stock-reconciliation/fix-negative', [StockReconciliationController::class, 'fixNegativeStock'])->name('stock.fix-negative');
    Route::post('/stock-reconciliation/fix-all-negative', [StockReconciliationController::class, 'fixAllNegativeStock'])->name('stock.fix-all-negative');

3. Add these routes at the end of the adminsite middleware group:
    // Stock Reconciliation for site admins
    Route::get('/stock-reconciliation', [StockReconciliationController::class, 'index'])->name('stock.reconciliation');
    Route::post('/stock-reconciliation/fix-negative', [StockReconciliationController::class, 'fixNegativeStock'])->name('stock.fix-negative');
    Route::post('/stock-reconciliation/fix-all-negative', [StockReconciliationController::class, 'fixAllNegativeStock'])->name('stock.fix-all-negative');
