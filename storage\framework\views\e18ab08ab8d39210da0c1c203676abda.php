
<?php $__env->startSection('contentho'); ?>
<?php $__env->startSection('title', 'Daftar Part'); ?>
<div class="row bgwhite page-title-box shadow-kit mb-1">
    <div class="col d-flex align-items-center" style="max-width: fit-content;">
        <button class="button-menu-mobile text-blue-500 btn-sm">
            <i style="font-size: 30px;" class="mdi mdi-menu"></i>
        </button>
        <div class="text">
            <p class="font-bold m-0"><?php echo e(session('name')); ?></p>
        </div>
    </div>
    <div class="col">
        <ul class="list-unstyled topnav-menu float-right mb-0">
            <li class="dropdown notification-list">
                <a class="nav-link dropdown-toggle" href="#" data-bs-toggle="dropdown" aria-expanded="false">
                    <i class="mdi mdi-bell-outline noti-icon"></i>
                    <span class="badge badge-danger badge-pill float-right badgenotif"></span>
                </a>
                <div class="dropdown-menu dropdown-menu-end dropdown-lg">
                    <div class="dropdown-item noti-title bg-primary">
                        <h5 class="m-0 text-white d-flex justify-content-between align-items-center">
                            Notifikasi
                            <a href="javascript:void(0);" class="text-white" id="clear-all">
                                <small></small>
                            </a>
                        </h5>
                    </div>
                    <div class="noti-scroll" id="notification-list">
                        <!-- Notifications will be dynamically inserted here -->
                    </div>
                </div>
            </li>
        </ul>
    </div>
</div>

<div class="content p-2 m-0">
    <div class="row">
        <div class="col">
            <div class="bgwhite mb-3 shadow-kit p-4 rounded-lg">
                <div class="d-flex justify-content-between align-items-center px-4">
                    <h4 class="text-uppercase font-bold h4">Tabel Daftar Part</h4>
                    <div class="d-flex">
                        <select id="typeFilter" class="form-control mr-2">
                            <option value="" <?php echo e(empty($typeFilter) ? 'selected' : ''); ?>>Semua Tipe</option>
                            <?php $__currentLoopData = $partTypes; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $type): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                            <option value="<?php echo e($type); ?>" <?php echo e($type == $typeFilter ? 'selected' : ''); ?>><?php echo e($type); ?></option>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                        </select>
                        <div class="input-group max500">
                            <input type="text" id="searchInput" class="form-control"
                                placeholder="Cari Part..." value="<?php echo e($search ?? ''); ?>">
                        </div>
                    </div>
                </div>

                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-bordered table-hover" id="partsTable">
                            <thead class="table-dark text-white">
                                <tr>
                                    <th class="p-2">No</th>
                                    <th class="p-2">Kode Part</th>
                                    <th class="p-2">Nama Part</th>
                                    <th class="p-2">Lokasi</th>
                                    <th class="p-2">Tipe Part</th>
                                    <th class="p-2">Harga</th>
                                    <th class="p-2">EUM</th>
                                    <th class="p-2">Aksi</th>
                                </tr>
                            </thead>
                            <tbody id="partsTableBody">
                                <!-- Table data will be loaded here via AJAX -->
                                <tr>
                                    <td colspan="6" class="text-center">
                                        <div class="spinner-border text-primary" role="status">
                                            <span class="visually-hidden">Loading...</span>
                                        </div>
                                        <p>Loading data...</p>
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                    <div class="mt-3">
                        <div id="pagination-info" class="text-center mb-2 text-muted small">
                            <!-- Page info will be displayed here -->
                        </div>
                        <div id="parts-pagination">
                            <!-- Custom pagination will be rendered here by JavaScript -->
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col max500 mr-2 ml-0 p-0">
            <div class="bgwhite  shadow-kit rounded-lg">
                <div class="card-header rounded-lg">
                    <button type="button" class="btn btn-sm btn-secondary float-right" id="cancelButton" style="display: none;">Cancel</button>
                    <h4 class="text-uppercase h5 #mb-0" id="formTitle">Tambah Part Baru</h4>
                </div>
                <div class="card-body p-4">
                    <form id="partForm">
                        <?php echo csrf_field(); ?>
                        <input type="hidden" name="part_code" id="part_code" value="">
                        <div class="form-group">
                            <label>Kode Part * </label>
                            <small id="code_name_hint" class="text-red-400" style="display: none;">* Harap Code tidak mengandung spasi, dan Unik</small>
                            <div class="input-group">
                                <input type="text" name="part_code_display" id="part_code_display" class="form-control" autocomplete="off" required>
                            </div>
                        </div>
                        <div class="form-group">
                            <label>Nama Part *</label>
                            <small id="part_name_hint" class="text-red-400" style="display: none;">
                                Gunakan nama yang mudah dan tidak sama dengan part yang lain.
                            </small>
                            <input type="text" name="part_name" id="part_name" class="form-control" autocomplete="off" required>
                        </div>
                        <div class="form-group">
                            <label>Tipe Part</label>
                            <select name="part_type" id="part_type" class="form-control" autocomplete="off" required>
                                <option value="">Pilih Tipe Part</option>
                                <?php $__currentLoopData = App\Models\Part::PART_TYPES; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $type): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                <option value="<?php echo e($type); ?>"><?php echo e($type); ?></option>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                            </select>
                        </div>
                        <div class="form-group">
                            <label>Lokasi BIN</label>
                            <input type="text" name="bin_location" id="bin_location" class="form-control">
                        </div>
                        <div class="form-group">
                            <label>Harga *</label>
                            <div class="input-group">
                                <div class="input-group-prepend">
                                    <span class="input-group-text">Rp</span>
                                </div>
                                <input type="text" name="price" id="price" class="form-control" placeholder="0" value="0" required>
                            </div>
                            <small class="text-muted">Format: 1.000.000</small>
                        </div>
                        <div class="form-group">
                            <label>EUM (End User Measurement) *</label>
                            <input type="text" name="eum" id="eum" class="form-control" placeholder="AE" value="AE" required>
                            <small class="text-muted">Contoh: AE, PCS, SET, dll</small>
                        </div>
                        <button type="submit" class="btn btn-primary btn-block" id="submitButton">Simpan</button>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
<?php $__env->stopSection(); ?>
<?php $__env->startSection('resource'); ?>
<meta name="csrf-token" content="<?php echo e(csrf_token()); ?>">
<?php echo app('Illuminate\Foundation\Vite')(['resources/js/part/Crudpart.js','resources/js/style.js']); ?>
<style>
    /* Pagination styling */
    .pagination {
        display: flex;
        flex-wrap: wrap;
        justify-content: center;
        list-style: none;
        padding: 0;
        margin-top: 15px;
        max-width: 100%;
    }
    .pagination .page-item {
        margin: 2px;
    }
    .pagination .page-item .page-link {
        color: #333;
        background-color: #fff;
        border: 1px solid #ddd;
        padding: 6px 10px;
        text-decoration: none;
        border-radius: 4px;
        cursor: pointer;
        font-size: 0.9rem;
        min-width: 36px;
        text-align: center;
    }
    .pagination .page-item.active .page-link {
        color: #fff;
        background-color: #28a745;
        border-color: #28a745;
    }
    .pagination .page-item .page-link:hover {
        background-color: #f8f9fa;
    }
    #parts-pagination {
        margin-top: 15px;
        width: 100%;
        overflow: hidden;
    }
</style>
<?php $__env->stopSection(); ?>
<?php echo $__env->make('warehouse.content', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\xampp\htdocs\portalpwb\resources\views/warehouse/buatdaftarpart.blade.php ENDPATH**/ ?>