<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Document</title>
</head>
@vite(['resources/js/part/Crudpart.js','resources/js/style.js'])

<body>
    <div class="content mt-4 p-4">
        <div class="container">
            <div class="row">
                <div class="col-md-8">
                    <div class="card mb-3">
                        <div class="card-body">
                            <div class="row">
                                <div class="col-9">
                                    <div class="input-group">
                                        <input type="text" id="searchInput" class="form-control"
                                            placeholder="Cari berdasarkan Kode Part, Nama Part, atau Lokasi...">
                                        <div class="input-group-append">
                                            <button class="btn btn-primary" type="button" id="searchButton">
                                                <i class="fas fa-search"></i> Cari
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="card-body">
                            <div class="table-responsive">
                                <table class="table table-bordered table-hover" id="partsTable">
                                    <thead class="table-dark text-white">
                                        <tr>
                                            <th class="p-2">No</th>
                                            <th class="p-2">Kode Part</th>
                                            <th class="p-2">Nama Part</th>
                                            <th class="p-2">Lokasi</th>
                                            <th class="p-2">Tipe Part</th>
                                            <th class="p-2">Aksi</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <!-- Isi tabel akan dimuat oleh AJAX -->
                                    </tbody>
                                </table>
                            </div>
                            <div class="d-flex justify-content-center mt-3" id="paginationLinks">
                                <!-- Link pagination akan dimuat oleh AJAX -->
                            </div>
                        </div>
                    </div>
                </div>

                <div class="col-md-4">
                    <div class="card">
                        <div class="card-header">
                            <h4 class="mb-0" id="formTitle">Tambah Part Baru</h4>
                        </div>
                        <div class="card-body">
                            <form id="partForm">
                                @csrf
                                <input type="hidden" name="part_code" id="part_code" value="">
                                <div class="form-group">
                                    <div class="d-flex justify-content-between align-items-center">
                                        <label>Kode Part *</label>
                                        <button type="button" class="btn btn-sm btn-secondary" id="cancelButton" style="display: none;">Cancel</button>
                                    </div>
                                    <div class="input-group">
                                        <input type="text" name="part_code_display" id="part_code_display" class="form-control"
                                            autocomplete="off" required>
                                        <!-- <div class="input-group-append">
                                            <button class="btn btn-outline-secondary" type="button" id="activateCodeButton">Aktifkan</button>
                                        </div> -->
                                    </div>
                                </div>
                                <div class="form-group">
                                    <label>Nama Part *</label>
                                    <input type="text" name="part_name" id="part_name" class="form-control" autocomplete="off" required>
                                </div>

                                <div class="form-group">
                                    <label>Tipe Part</label>
                                    <select name="part_type" id="part_type" class="form-control">
                                        <option value="">Pilih Tipe Part</option>
                                        @foreach(App\Models\Part::PART_TYPES as $type)
                                        <option value="{{ $type }}">{{ $type }}</option>
                                        @endforeach
                                    </select>
                                </div>

                                <div class="form-group">
                                    <label>Lokasi BIN</label>
                                    <input type="text" name="bin_location" id="bin_location" class="form-control">
                                </div>

                                <button type="submit" class="btn btn-primary btn-block" id="submitButton">Simpan</button>
                            </form>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</body>

</html>