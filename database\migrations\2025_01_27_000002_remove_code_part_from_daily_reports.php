<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('daily_reports', function (Blueprint $table) {
            // Drop foreign key constraint first
            $table->dropForeign(['code_part']);
            // Drop the column
            $table->dropColumn('code_part');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('daily_reports', function (Blueprint $table) {
            $table->string('code_part', 50)->nullable()->default(null)->after('unit_id');
            $table->foreign('code_part')
                ->references('part_code')
                ->on('parts')
                ->onUpdate('cascade')
                ->onDelete('set null');
        });
    }
};
