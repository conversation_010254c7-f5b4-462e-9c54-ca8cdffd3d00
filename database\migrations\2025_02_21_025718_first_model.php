<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::create('parts', function (Blueprint $table) {
            $table->string('part_code', 50)->primary();
            $table->string('part_name', 255);
            $table->decimal('price', 15, 2)->nullable();
            $table->decimal('purchase_price', 15, 2)->nullable();
            $table->string('bin_location', 50)->nullable();
            $table->enum('part_type', ['AC', 'TYRE', 'FABRIKASI','PERLENGKAPAN AC','PERSEDIAAN LAINNYA'])->nullable();
            $table->timestamps();
        });

        Schema::create('suppliers', function (Blueprint $table) {
            $table->string('supplier_id', 50)->primary();
            $table->string('supplier_name')->nullable();
            $table->string('address')->nullable();
            $table->string('contact_person')->nullable();
            $table->timestamps();
        });

        Schema::create('sites', function (Blueprint $table) {
            $table->string('site_id', 50)->primary();
            $table->string('site_name');
            $table->string('address')->nullable();
            $table->timestamps();
        });

        Schema::create('customers', function (Blueprint $table) {
            $table->string('customer_id', 50)->primary();
            $table->string('customer_name');
            $table->string('address')->nullable();
            $table->string('contact_person')->nullable();
            $table->string('phone_number')->nullable();
            $table->string('email')->nullable();
            $table->timestamps();
        });

        Schema::create('requisitions', function (Blueprint $table) {
            $table->id('requisition_id');
            $table->string('site_id', 50);
            $table->foreign('site_id')->references('site_id')->on('sites');
            $table->string('requisition_type');
            $table->enum('status', ['diajukan', 'pending', 'disetujui','selesai'])->default('diajukan');
            $table->date('requisition_date');
            $table->text('title');
            $table->string('surat_jalan_path')->nullable();
            $table->date('confirmation_date')->nullable();
            $table->string('modified_by');
            $table->string('notes')->nullable();
            $table->timestamps();
        });

        Schema::create('requisition_details', function (Blueprint $table) {
            $table->id('requisition_details_id')->primary();
            $table->foreignId('requisition_id')
            ->constrained('requisitions', 'requisition_id')
            ->onUpdate('cascade');
            $table->enum('status_details', ['disetujui', 'ditolak', 'pending', 'dikirim sebagian', 'intransit','selesai'])->default('pending');
            $table->integer('quantity_send')->nullable()->default(0);
            $table->integer('quantity_confirm')->nullable()->default(0);
            $table->string('part_code', 50);
            $table->float('quantity');
            $table->longText('surat_jalan_path')->nullable();
            $table->foreign('part_code')->references('part_code')->on('parts');
            $table->string('notes_ho')->nullable();
            $table->string('notes')->nullable();
            $table->timestamps();
        });

        Schema::create('users', function (Blueprint $table) {
            $table->string('employee_id', 50)->primary();
            $table->string('site_id', 50)->nullable();
            $table->foreign('site_id')
                ->references('site_id')
                ->on('sites')
                ->onUpdate('cascade');
            $table->string('name', 255);
            $table->string('username', 255)->unique();
            $table->string('password', 255);
            $table->string('email', 50)->nullable()->index();
            $table->enum('role', ['adminho', 'adminsite', 'superadmin','sales', 'karyawan', 'kasir'])->default('karyawan');
            $table->rememberToken();
            $table->timestamps();
        });

        Schema::create('part_inventories', function (Blueprint $table) {
            $table->id('part_inventory_id');
            $table->string('part_code', 50);
            $table->foreign('part_code')
                ->references('part_code')
                ->on('parts')
                ->onUpdate('cascade');
            $table->decimal('price', 15, 2)->default(0)->nullable();
            $table->string('site_id', 50);
            $table->decimal('price', 15, 2)->default(0)->nullable();
            $table->foreign('site_id')
                ->references('site_id')
                ->on('sites')
                ->onUpdate('cascade');
            $table->boolean('priority')->default(false);
            $table->integer('min_stock');
            $table->integer('max_stock');
            $table->float('stock_quantity')->default(0);
            $table->timestamps();
        });


        Schema::create('on_hand_quantities', function (Blueprint $table) {
            $table->uuid('on_hand_id')->primary();
            $table->foreignId('part_inventory_id')
                ->constrained('part_inventories', 'part_inventory_id')
                ->onUpdate('cascade');
            $table->string('month');
            $table->float('quantity');
            $table->timestamps();
        });

        Schema::create('warehouse_in_stocks', function (Blueprint $table) {
            $table->id('warehouse_in_stock_id');
            $table->foreignId('part_inventory_id')
                ->constrained('part_inventories', 'part_inventory_id')
                ->onUpdate('cascade');
            $table->string('supplier_id', 50)->nullable();
            $table->foreign('supplier_id')
                ->references('supplier_id')
                ->on('suppliers')
                ->onUpdate('cascade');
            $table->string('employee_id', 50);
            $table->decimal('price', 15, 2)->default(0)->nullable();
            $table->foreign('employee_id')
                ->references('employee_id')
                ->on('users')
                ->onUpdate('cascade');
            $table->timestamp('date_in')->useCurrent();
            $table->float('quantity');
            $table->string('notes')->nullable();
            $table->timestamps();
        });

        Schema::create('warehouse_out_stocks', function (Blueprint $table) {
            $table->id('warehouse_out_stock_id');
            $table->foreignId('part_inventory_id')
                ->constrained('part_inventories', 'part_inventory_id');
            $table->string('employee_id', 50);
            $table->decimal('price', 15, 2)->default(0)->nullable();
            $table->foreign('employee_id')
                ->references('employee_id')
                ->on('users')->nullable();
            $table->timestamp('date_out')->useCurrent();
            $table->float('quantity');
            $table->decimal('price', 15, 2)->default(0)->nullable();
            $table->string('status');
            $table->string('notes')->nullable();

            $table->string('destination_type')->nullable();
            $table->String('destination_id')->nullable();
            $table->timestamps();
            $table->index(['destination_type', 'destination_id']);
        });

        Schema::create('return_stocks', function (Blueprint $table) {
            $table->id('return_stock_id');
            $table->string('from_site_id', 50)->nullable();
            $table->foreign('from_site_id')
                ->references('site_id')
                ->on('sites');
            $table->string('to_site_id', 50)->nullable();
            $table->foreign('to_site_id')
                ->references('site_id')
                ->on('sites');
            $table->string('employee_id', 50);
            $table->foreign('employee_id')
                ->references('employee_id')
                ->on('users')
                ->onUpdate('cascade');
            $table->foreignId('part_inventory_id')
                ->constrained('part_inventories', 'part_inventory_id')
                ->onUpdate('cascade');
            $table->enum('part_type', ['AC', 'TYRE', 'FABRIKASI','PERLENGKAPAN AC','PERSEDIAAN LAINNYA']);
            $table->float('quantity');
            $table->timestamp('timestamp')->useCurrent();
        });

        Schema::create('part_merges', function (Blueprint $table) {
            $table->id('part_merge_id');
            $table->string('part_merge_name');
            $table->string('part_code_old', 50);
            $table->string('part_code_new', 50);
            $table->foreign('part_code_old')
                ->references('part_code')
                ->on('parts')
                ->onUpdate('cascade');
            $table->foreign('part_code_new')
                ->references('part_code')
                ->on('parts')
                ->onUpdate('cascade');
            $table->string('site_id', 50)->nullable();
            $table->foreign('site_id')
                ->references('site_id')
                ->on('sites');
            $table->float('quantity')->default(1);
            $table->string('notes')->nullable();
            $table->timestamp('merge_date')->useCurrent();
            $table->timestamps();
        });

        Schema::create('part_trackings', function (Blueprint $table) {
            $table->id('part_tracking_id');
            $table->foreignId('part_inventory_id')
                ->constrained('part_inventories', 'part_inventory_id');
            $table->string('requestor_id', 50);
            $table->foreign('requestor_id')
                ->references('employee_id')
                ->on('users')
                ->onUpdate('cascade');
            $table->string('receiver_id', 50);
            $table->foreign('receiver_id')
                ->references('employee_id')
                ->on('users')
                ->onUpdate('cascade');
            $table->enum('transaction_type', ['in', 'out', 'order', 'po', 'transferpart']);
            $table->timestamp('date_created')->useCurrent();
            $table->timestamp('date_updated')->useCurrent();
            $table->enum('status', ['menunggu', 'selesai']);
        });

        // NEW TABLES FOR SITE IN/OUT
        Schema::create('site_in_stocks', function (Blueprint $table) {
            $table->id('site_in_stock_id');
            $table->foreignId('part_inventory_id')
                ->constrained('part_inventories', 'part_inventory_id')
                ->onUpdate('cascade');
            $table->string('supplier_id', 50)->nullable();
            $table->foreign('supplier_id')
                ->references('supplier_id')
                ->on('suppliers')
                ->onUpdate('cascade');
                $table->decimal('price', 15, 2)->default(0)->nullable();
                $table->string('employee_id', 50);
                $table->foreign('employee_id')
                    ->references('employee_id')
                    ->on('users')
                    ->onUpdate('cascade');
            $table->timestamp('date_in')->useCurrent();
            $table->float('quantity');
            $table->string('notes')->nullable();
            $table->timestamps();
        });

        Schema::create('site_out_stocks', function (Blueprint $table) {
            $table->id('site_out_stock_id');
            $table->foreignId('part_inventory_id')
                ->constrained('part_inventories', 'part_inventory_id')
                ->onUpdate('cascade');
            $table->string('site_id', 50);
            $table->foreign('site_id')
                ->references('site_id')
                ->on('sites')
                ->onUpdate('cascade');
            $table->decimal('price', 15, 2)->default(0)->nullable();
            $table->string('employee_id', 50);
            $table->foreign('employee_id')
                ->references('employee_id')
                ->on('users')
                ->onUpdate('cascade');
            $table->integer('unit_transaction_parts_id');
            $table->foreign('unit_transaction_parts_id')
                    ->references('id')
                    ->on('units_transaction_parts')
                    ->onUpdate('cascade')->nullable();
            $table->timestamp('date_out')->useCurrent();
            $table->float('quantity');
            $table->enum('status',['out stock','Proses Out','Done','Proses Return','Proses PO','Proses MR'])->default('out stock')->nullable();
            $table->string('notes')->nullable();
            $table->timestamps();
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('site_in_stocks');
        Schema::dropIfExists('site_out_stocks');
        Schema::dropIfExists('part_trackings');
        Schema::dropIfExists('part_merges');
        Schema::dropIfExists('return_stocks');
        Schema::dropIfExists('warehouse_out_stocks');
        Schema::dropIfExists('warehouse_in_stocks');
        Schema::dropIfExists('on_hand_quantities');
        Schema::dropIfExists('part_inventories');
        Schema::dropIfExists('sites');
        Schema::dropIfExists('suppliers');
        Schema::dropIfExists('users');
        Schema::dropIfExists('customers');
        Schema::dropIfExists('parts');
        Schema::dropIfExists('requisitions');
        Schema::dropIfExists('requisition_details');
    }
};