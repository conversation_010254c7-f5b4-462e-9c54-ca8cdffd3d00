<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class SiteInStock extends Model
{
    use HasFactory;

    protected $table = 'site_in_stocks';
    protected $primaryKey = 'site_in_stock_id';
    
    protected $fillable = [
        'part_inventory_id',
        'supplier_id',
        'employee_id',
        'price',
        'date_in',
        'quantity',
        'notes',
    ];

    protected $casts = [
        'date_in' => 'datetime',
        'quantity' => 'float',
    ];

    public function partInventory()
    {
        return $this->belongsTo(PartInventory::class, 'part_inventory_id', 'part_inventory_id');
    }

    public function supplier()
    {
        return $this->belongsTo(Supplier::class, 'supplier_id', 'supplier_id');
    }

    public function employee()
    {
        return $this->belongsTo(User::class, 'employee_id', 'employee_id');
    }

}
