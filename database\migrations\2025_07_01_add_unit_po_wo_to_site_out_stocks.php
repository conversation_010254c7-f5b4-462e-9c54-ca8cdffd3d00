<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('site_out_stocks', function (Blueprint $table) {
            // Add unit_id as a foreign key to units table
            $table->foreignId('unit_id')->nullable()->after('employee_id');
            $table->foreign('unit_id')
                ->references('id')
                ->on('units')
                ->onUpdate('cascade')
                ->onDelete('set null');
            
            // Add po_number and wo_number columns
            $table->string('po_number')->nullable()->after('status');
            $table->string('wo_number')->nullable()->after('po_number');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('site_out_stocks', function (Blueprint $table) {
            $table->dropForeign(['unit_id']);
            $table->dropColumn(['unit_id', 'po_number', 'wo_number']);
        });
    }
};
