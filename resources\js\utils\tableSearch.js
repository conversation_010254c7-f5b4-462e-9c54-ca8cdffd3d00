export function setupTableSearch(inputId, tableId) {
    const searchInput = document.getElementById(inputId);
    const table = document.getElementById(tableId);
    
    if (!searchInput || !table) return;

    searchInput.addEventListener("keyup", function () {
        const filter = searchInput.value.toLowerCase();
        const rows = table.getElementsByTagName("tbody")[0].getElementsByTagName("tr");

        for (let row of rows) {
            let found = false;
            const cells = row.getElementsByTagName("td");

            for (let cell of cells) {
                if (cell.textContent.toLowerCase().includes(filter)) {
                    found = true;
                    break;
                }
            }

            row.style.display = found ? "" : "none";
        }
    });
}
