<?php
namespace App\Models;
use Illuminate\Database\Eloquent\Model;
class PartWithdrawal extends Model
{
    protected $primaryKey = 'withdrawal_id';
    protected $fillable = [
        'part_code',
        'from_site_id',
        'requested_quantity',
        'approved_quantity',
        'status',
        'withdrawal_reason',
        'notes',
        'previous_stock',
        'new_stock',
        'updated_by'
    ];

    public function part()
    {
        return $this->belongsTo(Part::class, 'part_code', 'part_code');
    }
    public function fromSite()
    {
        return $this->belongsTo(Site::class, 'from_site_id', 'site_id');
    }
}
