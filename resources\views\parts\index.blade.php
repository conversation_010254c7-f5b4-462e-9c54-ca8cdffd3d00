@extends('warehouse.layoutwho')
@section('contentwho')
<div class="container">
    <h1>Manajemen Part</h1>
    <div class="d-flex justify-content-between align-items-center mb-3">
        <button class="btn btn-success" data-toggle="modal" data-target="#addPartModal">
            <i class="fas fa-plus"></i> Tambah Part Baru
        </button>
    </div>
    <!-- Tabel Part -->
    <div class="card">
        <div class="card-body">
            <div class="table-responsive">
                <input type="text" id="searchInputtabelpartsTable" class="form-control mb-3" placeholder="Cari data...">
                <table class="table table-bordered table-hover" id="partsTable">
                    <thead class="thead-dark text-white">
                        <tr>
                            <th class="p-2">Kode Part</th>
                            <th class="p-2">Nama Part</th>
                            <th class="p-2">Tipe</th>
                            <th class="p-2">Bin</th>
                            <th class="p-2">Aksi</th>
                        </tr>
                    </thead>
                    <tbody>
                        @foreach($parts as $part)
                        <tr>
                            <td>{{ $part->code_part }}</td>
                            <td>{{ $part->part_name }}</td>
                            <td>{{ $part->type }}</td>
                            <td>{{ $part->bin }}</td>
                            <td>
                                <button class="btn btn-sm btn-warning"
                                    data-toggle="modal"
                                    data-target="#editPartModal"
                                    data-code="{{ $part->code_part }}"
                                    data-name="{{ $part->part_name }}"
                                    data-type="{{ $part->type }}"
                                    data-bin="{{ $part->bin }}">
                                    <i class="fas fa-edit"></i> Edit
                                </button>
                                <form action="{{ route('parts.destroy', $part->code_part) }}" method="POST" style="display:inline;">
                                    @csrf
                                    @method('DELETE')
                                    <button type="button" class="btn btn-sm btn-danger delete-part-btn">
                                        <i class="fas fa-trash"></i> Hapus
                                    </button>
                                </form>
                            </td>
                        </tr>
                        @endforeach
                    </tbody>
                </table>
            </div>
            <div class="d-flex justify-content-center">
                {{ $parts->links() }}
            </div>
        </div>
    </div>
</div>
<!-- Modal Edit Part -->
<div class="modal fade" id="editPartModal" tabindex="-1" role="dialog">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <form method="POST" id="editPartForm">
                @csrf
                @method('PUT') <!-- Method Spoofing untuk PUT -->
                <div class="modal-header">
                    <h5 class="modal-title">Edit Part</h5>
                    <button type="button" class="close" data-dismiss="modal">
                        <span>&times;</span>
                    </button>
                </div>
                <div class="modal-body">
                    <div class="form-group">
                        <label>Kode Part</label>
                        <input type="text" id="edit_code_part" class="form-control" readonly>
                    </div>
                    <div class="form-group">
                        <label>Nama Part *</label>
                        <input type="text" name="part_name" id="edit_part_name" class="form-control" autocomplete="off" required>
                    </div>
                    <div class="form-group">
                        <label>Tipe Part *</label>
                        <select name="type" id="edit_type" class="form-control" autocomplete="off" required>
                            <option value="AC">AC</option>
                            <option value="TYRE">TYRE</option>
                            <option value="FABRIKASI">FABRIKASI</option>
                            <option value="PERLENGKAPAN AC">PERLENGKAPAN AC</option>
                            <option value="PERSEDIAAN LAINNYA">PERSEDIAAN LAINNYA</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label>Bin</label>
                        <input type="text" name="bin" id="edit_bin" class="form-control">
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-dismiss="modal">Tutup</button>
                    <button type="submit" class="btn btn-primary">Simpan Perubahan</button>
                </div>
            </form>
        </div>
    </div>
</div>
<!-- Modal Tambah Part -->
<div class="modal fade" id="addPartModal" tabindex="-1" role="dialog">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <form method="POST" action="{{ route('parts.store') }}">
                @csrf
                <div class="modal-header">
                    <h5 class="modal-title">Tambah Part Baru</h5>
                    <button type="button" class="close" data-dismiss="modal">
                        <span>&times;</span>
                    </button>
                </div>
                <div class="modal-body">
                    <div class="form-group">
                        <label>Kode Part *</label>
                        <input type="text" name="code_part" class="form-control" autocomplete="off" required>
                    </div>
                    <div class="form-group">
                        <label>Nama Part *</label>
                        <input type="text" name="part_name" class="form-control" autocomplete="off" required>
                    </div>
                    <div class="form-group">
                        <label>Tipe Part *</label>
                        <select name="type" class="form-control" autocomplete="off" required>
                            <option value="AC">AC</option>
                            <option value="TYRE">TYRE</option>
                            <option value="FABRIKASI">FABRIKASI</option>
                            <option value="PERLENGKAPAN AC">PERLENGKAPAN AC</option>
                            <option value="PERSEDIAAN LAINNYA">PERSEDIAAN LAINNYA</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label>Bin</label>
                        <input type="text" name="bin" class="form-control">
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-dismiss="modal">Tutup</button>
                    <button type="submit" class="btn btn-primary">Simpan</button>
                </div>
            </form>
        </div>
    </div>
</div>
@vite(['resources/js/part/Partgroup.js'])
@vite(['resources/js/part/part.js'])
@endsection