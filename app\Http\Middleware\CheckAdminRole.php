<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Symfony\Component\HttpFoundation\Response;

class CheckAdminRole
{
    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle(Request $request, Closure $next): Response
    {
        if (session('role') !== 'adminho') {
            // abort(403, 'Unauthorized.'); // Atau redirect ke halaman error/home
            // redirect(route('/login'));
            return redirect('/login')->withErrors(['username' => '<PERSON><PERSON><PERSON> !!']);
        }
        return $next($request);
    }
}
