<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class ManualInvoicePart extends Model
{
    use HasFactory;

    protected $fillable = [
        'invoice_id',
        'part_code',
        'part_name',
        'quantity',
        'price',
        'total',
        'eum'
    ];

    protected $casts = [
        'quantity' => 'integer',
        'price' => 'float',
        'total' => 'float'
    ];

    /**
     * Get the invoice that owns this part
     */
    public function invoice()
    {
        return $this->belongsTo(Invoice::class);
    }

    /**
     * Get the part details
     */
    public function part()
    {
        return $this->belongsTo(Part::class, 'part_code', 'part_code');
    }
}
