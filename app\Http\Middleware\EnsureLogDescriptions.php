<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Symfony\Component\HttpFoundation\Response;
use App\Models\LogAktivitas;

class EnsureLogDescriptions
{
    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle(Request $request, Closure $next): Response
    {
        // Process the request
        $response = $next($request);

        // After the request is processed, check for any log entries with empty descriptions
        // This is a fallback in case any direct LogAktivitas::create calls bypass our model boot method
        try {
            // Get the latest log entry for the current request
            $latestLog = LogAktivitas::where('ip_address', $request->ip())
                ->latest()
                ->first();

            // If we found a log entry with an empty description, update it
            if ($latestLog) {
                // If both description fields are empty, create a specific description
                if (empty($latestLog->description) && empty($latestLog->decription)) {
                    $action = $latestLog->action ?? 'aktivitas';
                    $table = $latestLog->table ?? 'sistem';
                    $name = $latestLog->name ?? session('name');

                    // Get additional context information
                    $ipAddress = $latestLog->ip_address ?? $request->ip();
                    $timestamp = now()->format('Y-m-d H:i:s');
                    $url = $request->fullUrl();

                    // Try to extract specific item information from the request
                    $itemInfo = "";

                    // Check for common item identifiers in the request
                    $possibleItemFields = ['part_name', 'part_code', 'unit_code', 'unit_type', 'item_name', 'name'];
                    foreach ($possibleItemFields as $field) {
                        if ($request->has($field) && !empty($request->input($field))) {
                            $itemInfo = ": " . $request->input($field);
                            break;
                        }
                    }

                    // If we couldn't find an item name in the request, try to extract it from the URL
                    if (empty($itemInfo)) {
                        $urlParts = explode('/', $url);
                        $lastPart = end($urlParts);
                        if (is_numeric($lastPart) || (strlen($lastPart) > 3 && $lastPart !== 'index.php')) {
                            $itemInfo = " (ID: {$lastPart})";
                        }
                    }

                    // Create a more specific description based on common action patterns
                    if (strpos($action, 'Menambahkan') !== false || strpos($action, 'Membuat') !== false) {
                        $latestLog->description = "User {$name} menambahkan {$table}{$itemInfo}. IP: {$ipAddress}, Timestamp: {$timestamp}";
                    }
                    elseif (strpos($action, 'Mengubah') !== false) {
                        $latestLog->description = "User {$name} mengubah {$table}{$itemInfo}. IP: {$ipAddress}, Timestamp: {$timestamp}";
                    }
                    elseif (strpos($action, 'Menghapus') !== false || strpos($action, 'Hapus') !== false) {
                        $latestLog->description = "User {$name} menghapus {$table}{$itemInfo}. IP: {$ipAddress}, Timestamp: {$timestamp}";
                    }
                    else {
                        $latestLog->description = "User {$name} melakukan {$action} pada {$table}{$itemInfo}. IP: {$ipAddress}, Timestamp: {$timestamp}";
                    }

                    $latestLog->save();
                }
                // If description is empty but decription has value, copy it
                elseif (empty($latestLog->description) && !empty($latestLog->decription)) {
                    $latestLog->description = $latestLog->decription;
                    $latestLog->save();
                }
            }
        } catch (\Exception $e) {
            // Just log the error, don't interrupt the response
            \Illuminate\Support\Facades\Log::error('Error in EnsureLogDescriptions middleware: ' . $e->getMessage());
        }

        return $response;
    }
}
