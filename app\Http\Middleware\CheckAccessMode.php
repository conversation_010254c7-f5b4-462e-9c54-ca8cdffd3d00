<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Symfony\Component\HttpFoundation\Response;

class CheckAccessMode
{
    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle(Request $request, Closure $next, $mode): Response
    {
        // Check if user is logged in and is an admin site
        if (session('role') !== 'adminsite') {
            return redirect('/login')->withErrors(['username' => 'Aks<PERSON> !!']);
        }
        
        // Check if the user has selected a mode
        if (!session()->has('access_mode')) {
            return redirect()->route('adminsite.mode.select');
        }
        
        // Check if the user has the correct mode
        if (session('access_mode') !== $mode) {
            return redirect()->route('adminsite.mode.select')
                ->withErrors(['mode' => 'Anda tidak memiliki akses ke fitur ini. Silakan pilih mode yang sesuai.']);
        }
        
        return $next($request);
    }
}
