<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('stock_transactions', function (Blueprint $table) {
            $table->id('stock_transaction_id');
            $table->string('part_code', 50);
            $table->foreign('part_code')
                ->references('part_code')
                ->on('parts')
                ->onUpdate('cascade');
            $table->string('transaction_type'); // 'outbound', 'inbound', 'return', 'adjustment'
            $table->string('destination_siteid', 50)->nullable();  //dikirim kemana? NULL jika adjustment
            $table->foreign('destination_siteid')
                ->references('site_id')
                ->on('sites')
                ->onUpdate('cascade');
            $table->foreignId('requisition_details_id')->nullable();
            $table->foreign('requisition_details_id')
                ->references('requisition_details_id')
                ->on('requisition_details')
                ->onUpdate('cascade')
                ->nullable()
                ->default(null);
            $table->longText('surat_jalan_path')->nullable();
            $table->timestamp('transaction_date')->useCurrent(); //tanggal dikirim
            $table->integer('quantity_sent')->default(0);   //jumlah part yang dikirim (default 0 untuk transaksi internal)

            $table->integer('quantity_received')->nullable(); // Diisi saat site konfirmasi, jumlah diterima
            $table->integer('quantity_returned')->nullable(); // Diisi saat return/pengembalian
            $table->enum('status', allowed: ['intransit', 'pending', 'selesai','return','hilang'])
                ->default('intransit');
            // Kolom untuk melacak selisi
            $table->string('discrepancy_reason')->nullable(); // Alasan return dari site/warehouse
            $table->string('discrepancy_resolution')->nullable(); // Tindakan/penjelasan warehouse terkait selisih
            $table->integer('quantity_discrepancy')->nullable(); // Jumlah selisih (quantity_sent - quantity_received)
            $table->string('discrepancy_status')->nullable(); // open, investigation, resolved, missing
            $table->string('discrepancy_type')->nullable(); // Kekurangan, Kelebihan, Kerusakan, Hilang

            $table->string('notes')->nullable(); // Catatan tambahan
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('stock_transactions');
    }
};
