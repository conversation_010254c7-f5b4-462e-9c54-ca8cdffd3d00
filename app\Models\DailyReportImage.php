<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class DailyReportImage extends Model
{
    use HasFactory;

    protected $fillable = [
        'daily_report_id',
        'image_path',
        'type',
    ];

    /**
     * Get the daily report that owns the image.
     */
    public function dailyReport()
    {
        return $this->belongsTo(DailyReport::class, 'daily_report_id', 'daily_report_id');
    }
}
