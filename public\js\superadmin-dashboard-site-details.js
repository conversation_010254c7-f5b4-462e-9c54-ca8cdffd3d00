/**
 * Superadmin Dashboard Site Details
 * Site details modal functionality has been removed as requested
 */

document.addEventListener('DOMContentLoaded', function() {
    // Site cards click event listeners removed as requested

    // Make site cards non-clickable by adding a CSS class
    const siteCards = document.querySelectorAll('.site-card');
    siteCards.forEach(card => {
        card.style.cursor = 'default';
    });

    // Format number function is kept for other parts of the application
});

/**
 * Format number with thousand separators
 *
 * @param {number} number
 * @returns {string}
 */
function formatNumber(number) {
    return new Intl.NumberFormat('id-ID').format(number);
}
