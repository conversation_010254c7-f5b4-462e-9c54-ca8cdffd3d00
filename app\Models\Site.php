<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Site extends Model
{
    use HasFactory;

    protected $table = 'sites';
    protected $primaryKey = 'site_id';
    public $incrementing = false;
    protected $keyType = 'string';

    protected $fillable = [
        'site_id',
        'site_name',
        'address',
    ];

    public function equipmentStocks()
    {
        return $this->hasMany(EquipmentStock::class, 'site_id', 'site_id');
    }                                     

    public function notifications()
    {
        return $this->hasMany(Notification::class, 'site_id', 'site_id');
    }
    public function users()
    {
        return $this->hasMany(User::class, 'site_id', 'site_id');
    }

    public function partInventories()
    {
        return $this->hasMany(PartInventory::class, 'site_id', 'site_id');
    }

    public function equipment()
    {
        return $this->hasMany(Equipment::class, 'site_id', 'site_id');
    }

    public function siteOutStocks()
    {
        return $this->hasMany(SiteOutStock::class, 'site_id', 'site_id');
    }

    public function mergedPartSites()
    {
        return $this->hasMany(MergedPartSite::class, 'site_id', 'site_id');
    }

    public function returnStocksFrom()
    {
        return $this->hasMany(ReturnStock::class, 'from_site_id', 'site_id');
    }

    public function returnStocksTo()
    {
        return $this->hasMany(ReturnStock::class, 'to_site_id', 'site_id');
    }
    public function logs()
    {
        return $this->hasMany(LogAktivitas::class, 'site_id', 'site_id');
    }
    public function withdrawalsTo()
    {
        return $this->hasMany(PartWithdrawal::class, 'to_site_id', 'site_id');
    }
}
