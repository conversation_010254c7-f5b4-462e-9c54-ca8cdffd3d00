<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    // public function up(): void
    // {
    //     Schema::create('customersales', function (Blueprint $table) {
    //         $table->id();
    //         $table->string('code')->unique(); // Changed from 'kode' to 'code'
    //         $table->string('nama_customer');
    //         $table->text('alamat')->nullable();
    //         // Removed total_pending as we'll calculate from invoices
    //         $table->timestamps();
    //     });
    // }

    /**
     * Reverse the migrations.
     */
    // public function down(): void
    // {
    //     Schema::dropIfExists('customersales');
    // }
};
