<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Dashboard</title>
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
</head>

<body>
    <h1>Dashboard</h1>
    <p>Selamat datang, {{ Auth::user()->username }} ({{ Auth::user()->email }})</p>
    <a href="{{ route('logout') }}">Logout</a>

    <h2>Daftar User</h2>
    <table id="users-table">
        <thead>
            <tr>
                <th>Username</th>
                <th>Email</th>
                <th>Role</th>
                <th>Aksi</th>
            </tr>
        </thead>
        <tbody>
            @foreach ($users as $user)
            <tr>
                <td>{{ $user->username }}</td>
                <td>{{ $user->email }}</td>
                <td>{{ $user->role }}</td>
                <td>
                    <button class="edit-btn" data-id="{{ $user->employee_id }}">Edit</button>
                    <button class="delete-btn" data-id="{{ $user->employee_id }}">Delete</button>
                </td>
            </tr>
            @endforeach
        </tbody>
    </table>

    <h2>Tambah User</h2>
    <form id="add-user-form">
        @csrf
        <label for="site_id">Site ID:</label>
        <input type="text" id="site_id" name="site_id" autocomplete="off" required>
        <br>
        <label for="username">Username:</label>
        <input type="text" id="username" name="username" autocomplete="off" required>
        <br>
        <label for="password">Password:</label>
        <input type="password" id="password" name="password" autocomplete="off" required>
        <br>
        <label for="email">Email:</label>
        <input type="email" id="email" name="email">
        <br>
        <label for="role">Role:</label>
        <select id="role" name="role">
            <option value="adminho">Admin HO</option>
            <option value="adminsite">Admin Site</option>
            <option value="superadmin">Super Admin</option>
            <option value="karyawan">Karyawan</option>
            <option value="sales">Sales</option>
        </select>
        <br>
        <button type="submit">Tambah</button>
    </form>

    <!-- Modal Edit User -->
    <div id="edit-user-modal" style="display:none;">
        <form id="edit-user-form">
            @csrf
            @method('PUT')
            <input type="hidden" id="edit-employee_id" name="employee_id">
            <label for="edit-site_id">Site ID:</label>
            <input type="text" id="edit-site_id" name="site_id" autocomplete="off" required>
            <br>
            <label for="edit-username">Username:</label>
            <input type="text" id="edit-username" name="username" autocomplete="off" required>
            <br>
            <label for="edit-password">Password (kosongkan jika tidak diubah):</label>
            <input type="password" id="edit-password" name="password">
            <br>
            <label for="edit-email">Email:</label>
            <input type="email" id="edit-email" name="email">
            <br>
            <label for="edit-role">Role:</label>
            <select id="edit-role" name="role">
                <option value="adminho">Admin HO</option>
                <option value="adminsite">Admin Site</option>
                <option value="superadmin">Super Admin</option>
                <option value="karyawan">Karyawan</option>
                <option value="sales">Sales</option>
            </select>
            <br>
            <button type="submit">Update</button>
            <button type="button" id="close-modal">Tutup</button>
        </form>
    </div>

    <script>
        $(document).ready(function() {
            // Load users
            function loadUsers() {
                $.get('{{ route("users.index") }}', function(data) {
                    $('#users-table tbody').empty();
                    data.forEach(function(user) {
                        $('#users-table tbody').append(`
                        <tr>
                            <td>${user.username}</td>
                            <td>${user.email}</td>
                            <td>${user.role}</td>
                            <td>
                                <button class="edit-btn" data-id="${user.employee_id}">Edit</button>
                                <button class="delete-btn" data-id="${user.employee_id}">Delete</button>
                            </td>
                        </tr>
                    `);
                    });
                });
            }
            loadUsers();

            // Tambah user
            $('#add-user-form').submit(function(e) {
                e.preventDefault();
                $.ajax({
                    type: 'POST',
                    url: '{{ route("users.store") }}',
                    data: $(this).serialize(),
                    success: function(response) {
                        loadUsers();
                        $('#add-user-form')[0].reset();
                    },
                    error: function(xhr) {
                        alert('Error: ' + xhr.responseJSON.message);
                    }
                });
            });

            // Delete user
            $(document).on('click', '.delete-btn', function() {
                var id = $(this).data('id');
                if (confirm('Yakin hapus user ini?')) {
                    $.ajax({
                        type: 'DELETE',
                        url: '{{ url("/users") }}/' + id,
                        data: {
                            "_token": "{{ csrf_token() }}"
                        },
                        success: function() {
                            loadUsers();
                        },
                        error: function(xhr) {
                            alert('Error: ' + xhr.responseJSON.message);
                        }
                    });
                }
            });

            // Edit user
            $(document).on('click', '.edit-btn', function() {
                var id = $(this).data('id');
                $.get('{{ url("/users") }}/' + id, function(user) {
                    $('#edit-employee_id').val(user.employee_id);
                    $('#edit-site_id').val(user.site_id);
                    $('#edit-username').val(user.username);
                    $('#edit-email').val(user.email);
                    $('#edit-role').val(user.role);
                    $('#edit-user-modal').show();
                });
            });

            // Update user
            $('#edit-user-form').submit(function(e) {
                e.preventDefault();
                var id = $('#edit-employee_id').val();
                $.ajax({
                    type: 'PUT',
                    url: '{{ url("/users") }}/' + id,
                    data: $(this).serialize(),
                    success: function() {
                        loadUsers();
                        $('#edit-user-modal').hide();
                    },
                    error: function(xhr) {
                        alert('Error: ' + xhr.responseJSON.message);
                    }
                });
            });

            // Tutup modal
            $('#close-modal').click(function() {
                $('#edit-user-modal').hide();
            });
        });
    </script>
</body>

</html>