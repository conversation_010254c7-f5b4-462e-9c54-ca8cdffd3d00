<?php

namespace App\Http\Controllers\Sales;

use App\Http\Controllers\Controller;
use App\Models\CustomerSales;
use App\Models\Penawaran;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;

class CustomerSalesController extends Controller
{
    /**
     * Display a listing of the customers.
     */
    public function index(Request $request)
    {
        // If this is an AJAX request, return JSON data for DataTable
        if ($request->ajax()) {
            return $this->getCustomersData($request);
        }

        return view('sales.customer');
    }

    /**
     * Get customers data for AJAX DataTable
     */
    public function getCustomersData(Request $request)
    {
        $query = CustomerSales::query();

        // Apply search filter
        if ($request->has('search') && !empty($request->search)) {
            $query->search($request->search);
        }

        // Get total count before pagination
        $totalRecords = $query->count();

        // Apply pagination
        $perPage = 10; // As specified in requirements
        $page = $request->get('page', 1);
        $customers = $query->orderBy('nama_customer', 'asc')
            ->skip(($page - 1) * $perPage)
            ->take($perPage)
            ->get();

        // Calculate invoice totals for each customer
        $customersWithTotals = $customers->map(function ($customer) {
            return [
                'id' => $customer->id,
                'code' => $customer->code,
                'nama_customer' => $customer->nama_customer,
                'alamat' => $customer->alamat,
                'total_invoice' => $customer->total_invoice,
                'total_pembayaran' => $customer->total_pembayaran,
                'saldo_piutang' => $customer->saldo_piutang,
            ];
        });

        return response()->json([
            'data' => $customersWithTotals,
            'current_page' => $page,
            'last_page' => ceil($totalRecords / $perPage),
            'per_page' => $perPage,
            'total' => $totalRecords,
        ]);
    }

    /**
     * Store a newly created customer in storage.
     */
    public function store(Request $request)
    {
        $request->validate([
            'code' => 'required|string|max:50|unique:customersales,code',
            'nama_customer' => 'required|string|max:255',
            'alamat' => 'nullable|string',
        ]);

        try {
            $customer = CustomerSales::create([
                'code' => strtoupper($request->code),
                'nama_customer' => strtoupper($request->nama_customer),
                'alamat' => $request->alamat ? strtoupper($request->alamat) : null,
            ]);

            return response()->json([
                'success' => true,
                'message' => 'Customer berhasil ditambahkan',
                'data' => $customer
            ]);
        } catch (\Exception $e) {
            Log::error("Error creating customer: {$e->getMessage()}");
            return response()->json([
                'success' => false,
                'message' => "Gagal menambahkan customer: {$e->getMessage()}"
            ], 500);
        }
    }

    /**
     * Display the specified customer.
     */
    public function show($id)
    {
        $customer = CustomerSales::findOrFail($id);
        return response()->json($customer);
    }

    /**
     * Update the specified customer in storage.
     */
    public function update(Request $request, $id)
    {
        $customer = CustomerSales::findOrFail($id);

        $request->validate([
            'code' => 'required|string|max:50|unique:customersales,code,' . $id,
            'nama_customer' => 'required|string|max:255',
            'alamat' => 'nullable|string',
        ]);

        try {
            $customer->update([
                'code' => strtoupper($request->code),
                'nama_customer' => strtoupper($request->nama_customer),
                'alamat' => $request->alamat ? strtoupper($request->alamat) : null,
            ]);

            return response()->json([
                'success' => true,
                'message' => 'Customer berhasil diperbarui',
                'data' => $customer
            ]);
        } catch (\Exception $e) {
            Log::error("Error updating customer: {$e->getMessage()}");
            return response()->json([
                'success' => false,
                'message' => "Gagal memperbarui customer: {$e->getMessage()}"
            ], 500);
        }
    }

    /**
     * Remove the specified customer from storage.
     */
    public function destroy($id)
    {
        try {
            $customer = CustomerSales::findOrFail($id);

            // Check if customer has any pending offers
            $pendingOffers = Penawaran::where('customer', $customer->nama_customer)
                ->whereIn('status', ['Draft', 'Dikirim ke customer', 'PO customer', 'Proses penyediaan'])
                ->count();

            if ($pendingOffers > 0) {
                return response()->json([
                    'success' => false,
                    'message' => 'Customer tidak dapat dihapus karena masih memiliki penawaran yang aktif'
                ], 400);
            }

            $customer->delete();

            return response()->json([
                'success' => true,
                'message' => 'Customer berhasil dihapus'
            ]);
        } catch (\Exception $e) {
            Log::error('Error deleting customer: ' . $e->getMessage());
            return response()->json([
                'success' => false,
                'message' => 'Gagal menghapus customer: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Search for customers for autocomplete.
     */
    public function search(Request $request)
    {
        $query = $request->input('query');

        $customers = CustomerSales::where('nama_customer', 'LIKE', "%{$query}%")
            ->orWhere('code', 'LIKE', "%{$query}%")
            ->limit(10)
            ->get(['id', 'code', 'nama_customer', 'alamat']);

        return response()->json($customers);
    }


}
