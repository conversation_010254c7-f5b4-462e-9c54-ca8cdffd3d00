@foreach($parts as $part)
<tr>
    <td>{{ $loop->iteration }}</td>
    <td>{{ $part->part->part_code }}</td>
    <td>{{ $part->part->part_name }}</td>
    <td>{{ $part->site->site_name }}</td>
    <td>{{ $part->min_stock }}</td>
    <td>{{ $part->max_stock }}</td>
    <td>
        <button class="btn btn-danger btn-sm delete-part" 
                data-id="{{ $part->part_inventory_id }}"
                data-name="{{ $part->part->part_name }}">
            Hapus
        </button>
    </td>
</tr>
@endforeach