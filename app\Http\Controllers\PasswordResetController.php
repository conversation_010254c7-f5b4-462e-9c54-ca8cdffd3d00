<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Str;
use App\Models\User;
use App\Models\LogAktivitas;
use App\Helpers\LogHelper;

class PasswordResetController extends Controller
{
    /**
     * Show the password reset form.
     *
     * @return \Illuminate\View\View
     */
    public function showResetForm()
    {
        return view('auth.reset-password');
    }

    /**
     * Reset the user's password.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\RedirectResponse
     */
    public function reset(Request $request)
    {
        $request->validate([
            'current_password' => 'required',
            'password' => 'required|min:8|confirmed',
            'token' => 'required',
        ]);

        $user = Auth::user();

        // Verify current password
        if (!Hash::check($request->current_password, $user->password)) {
            return back()->withErrors(['current_password' => 'Password lama tidak sesuai.']);
        }

        // Verify token
        if ($request->token !== $user->remember_token) {
            return back()->withErrors(['token' => 'Token tidak valid. Silahkan hubungi admin untuk mendapatkan token yang benar.']);
        }

        // Update password
        $user->password = Hash::make($request->password);
        $user->remember_token = Str::random(60); // Generate new token after reset
        $user->save();

        // Log the password change
        LogHelper::logPasswordChange($user->username, $request);

        return redirect()->route('password.reset')->with('success', 'Password berhasil diubah.');
    }

    /**
     * Show the token generation page.
     *
     * @return \Illuminate\View\View
     */
    public function showTokenGenerationForm()
    {
        // Check if current user is admin
        if (!in_array(Auth::user()->role, ['adminho', 'superadmin'])) {
            return redirect()->route('adminho.dashboard')->withErrors(['unauthorized' => 'Anda tidak memiliki akses untuk fungsi ini.']);
        }

        $users = User::with('site')->get();
        return view('admin.generate-token', compact('users'));
    }

    /**
     * Generate a new token for a user (admin function).
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  string  $employee_id
     * @return \Illuminate\Http\RedirectResponse
     */
    public function generateToken(Request $request, $employee_id)
    {
        // Check if current user is admin
        if (!in_array(Auth::user()->role, ['adminho', 'superadmin'])) {
            return back()->withErrors(['unauthorized' => 'Anda tidak memiliki akses untuk fungsi ini.']);
        }

        $user = User::findOrFail($employee_id);
        $token = Str::random(10); // Generate a shorter, more user-friendly token
        $user->remember_token = $token;
        $user->save();

        // Log the token generation
        LogHelper::logTokenGeneration($user->username, Auth::user()->name, $request);

        return redirect()->route('password.token.generate')
            ->with('token', $token)
            ->with('success', "Token reset password berhasil dibuat untuk {$user->name}");
    }
}
