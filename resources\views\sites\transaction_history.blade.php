@extends('sites.content')
@section('contentsite')
@section('title', 'History Transaksi Site')
<div class="row bgwhite page-title-box shadow-kit mb-1">
    <div class="col d-flex align-items-center" style="max-width: fit-content;">
        <button class="button-menu-mobile text-blue-500 btn-sm">
            <i style="font-size: 30px;" class="mdi mdi-menu"></i>
        </button>
        <div class="text">
            <p class="font-bold m-0">{{ session('name') }}</p>
        </div>
    </div>

    <div class="col">
        <ul class="list-unstyled topnav-menu float-right mb-0">
            <li class="dropdown notification-list">
                <a class="nav-link dropdown-toggle" href="#" data-bs-toggle="dropdown" aria-expanded="false">
                    <i class="mdi mdi-bell-outline noti-icon"></i>
                    <span class="badge badge-danger badge-pill float-right badgenotif"></span>
                </a>
                <div class="dropdown-menu dropdown-menu-end dropdown-lg">
                    <div class="dropdown-item noti-title bg-primary">
                        <h5 class="m-0 text-white d-flex justify-content-between align-items-center">
                            Notifikasi
                            <a href="javascript:void(0);" class="text-white" id="clear-all">
                                <small></small>
                            </a>
                        </h5>
                    </div>
                    <div class="noti-scroll" id="notification-list">
                        <!-- Notifications will be dynamically inserted here -->
                    </div>
                </div>
            </li>
        </ul>
    </div>
</div>

<div class="row p-2">
    <div class="col bgwhite shadow-kit p-4 rounded-lg">
        <h1 class="text-xl font-bold mb-4 text-uppercase">History Transaksi Site</h1>
        <div class="mb-4">
            <div class="d-flex flex-wrap gap-3">
                <div>
                    <label for="status_filter" class="form-label">Status:</label>
                    <select class="form-control" id="status_filter">
                        <option value="">Semua Status</option>
                        <option value="intransit">In Transit</option>
                        <option value="return">Return Ke HO</option>
                        <option value="pending">Pending</option>
                        <option value="selesai">Selesai</option>
                    </select>
                </div>
                <div>
                    <label class="form-label">Periode Transaksi:</label>
                    <div class="d-flex gap-2">
                        <input type="date" class="form-control" id="start_date">
                        <input type="date" class="form-control" id="end_date">
                    </div>
                </div>
                <div>
                    <label for="search" class="form-label">Cari Part:</label>
                    <input type="text" class="form-control" id="search" placeholder="Cari part...">
                </div>
            </div>
        </div>

        <div id="last-data-alert" class="alert alert-info mb-3" style="display: none;">
            <i class="mdi mdi-information-outline me-2"></i>
            <span>Tidak ada data untuk periode yang dipilih. Menampilkan data dari tanggal terakhir yang tersedia.</span>
        </div>

        <div class="table-responsive">
            <table class="table table-bordered w-100">
                <thead class="table-dark text-white">
                    <tr>
                        <th>ID</th>
                        <th>Part Code</th>
                        <th>Part Name</th>
                        <th>Tanggal</th>
                        <th>Qty Kirim Warehouse</th>
                        <th>Qty Terima Site</th>
                        <th>Status</th>
                        <th>Lampiran</th>
                    </tr>
                </thead>
                <tbody id="transaction-table-body">
                </tbody>
            </table>
            <div id="pagination-container" class="mt-3"></div>
        </div>
    </div>
</div>
@endsection
@section('resourcesite')
@vite(['resources/js/site/Transactionhistory.js'])
@endsection