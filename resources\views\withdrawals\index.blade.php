@extends('warehouse.content')
@section('resource')
@vite(['resources/js/adminho/withrawals.js','resources/js/style.js'])
<style>
    /* Pagination styling */
    .pagination {
        display: flex;
        justify-content: center;
        list-style: none;
        padding: 0;
        margin-top: 15px;
    }
    .pagination .page-item {
        margin: 0 2px;
    }
    .pagination .page-item .page-link {
        color: #333;
        background-color: #fff;
        border: 1px solid #ddd;
        padding: 6px 12px;
        text-decoration: none;
        border-radius: 4px;
        cursor: pointer;
    }
    .pagination .page-item.active .page-link {
        color: #fff;
        background-color: #28a745;
        border-color: #28a745;
    }
    .pagination .page-item .page-link:hover {
        background-color: #f8f9fa;
    }
    #withdrawals-pagination {
        margin-top: 15px;
    }
</style>
<script>
    // Initial pagination data
    window.withdrawalsPaginationData = {
        current_page: 1,
        per_page: 15,
        last_page: 1,
        total: 0
    };
</script>
@endsection
@section('contentho')
<div class="row bgwhite page-title-box shadow-kit mb-1">
    <div class="col d-flex align-items-center" style="max-width: fit-content;">
        <button class="button-menu-mobile text-blue-500 btn-sm">
            <i style="font-size: 30px;" class="mdi mdi-menu"></i>
        </button>
        <div class="text">
            <p class="font-bold m-0">{{ session('name') }}</p>
        </div>
    </div>
    <div class="col">
        <ul class="list-unstyled topnav-menu float-right mb-0">
            <li class="dropdown notification-list">
                <a class="nav-link dropdown-toggle" href="#" data-bs-toggle="dropdown" aria-expanded="false">
                    <i class="mdi mdi-bell-outline noti-icon"></i>
                    <span class="badge badge-danger badge-pill float-right badgenotif"></span>
                </a>
                <div class="dropdown-menu dropdown-menu-end dropdown-lg">
                    <div class="dropdown-item noti-title bg-primary">
                        <h5 class="m-0 text-white d-flex justify-content-between align-items-center">
                            Notifikasi
                            <a href="javascript:void(0);" class="text-white" id="clear-all">
                                <small></small>
                            </a>
                        </h5>
                    </div>
                    <div class="noti-scroll" id="notification-list">
                        <!-- Notifications will be dynamically inserted here -->
                    </div>
                </div>
            </li>
        </ul>
    </div>
</div>
<div id="container" class="p-1">
    <div class="row">
        <div class="col">
            <div class="shadow-kit bgwhite p-4">
                <div id="filters">
                    <h2 class="h5 font-bold text-uppercase">Filter</h2>
                    <select class="btn btn-primary" id="statusFilter">
                        <option value="">Semua status</option>
                        <option value="Pending">Pending</option>
                        <option value="Rejected">Ditolak</option>
                        <option value="In Transit">In Transit</option>
                        <option value="Completed">Selesai</option>
                    </select>
                    <select class="btn btn-primary" id="siteFilter">
                        <option value="">Semua Site</option>
                        @foreach ($sites as $site)
                        <option value="{{ $site->site_id }}">{{ $site->site_name }}</option>
                        @endforeach
                    </select>
                </div>

                <div id="table-container">
                    <h2 class="h5 text-center text-uppercase">Return Part dari Site</h2>
                    <table id="withdrawals-table" class="table">
                        <thead class="table-dark text-white p-2">
                            <tr>
                                <th class="p-2">No</th>
                                <th class="p-2">Nama Part</th>
                                <th class="p-2">Return Dari</th>
                                <th class="p-2">Keterangan</th>
                                <th class="p-2">Jumlah Pengajuan Return</th>
                                <th class="p-2">Status</th>
                                <th class="p-2">Aksi</th>
                            </tr>
                        </thead>
                        <tbody>
                        </tbody>
                    </table>
                    <div id="withdrawals-pagination" class="mt-3">
                        <!-- Custom pagination will be rendered here by JavaScript -->
                    </div>
                </div>
            </div>
        </div>
        <div class="col max500">
            <div class="shadow-kit bgwhite p-4">
                <div id="form" style="position: relative;">
                    <h2 class="h5 font-bold">Buat Permintaan Return</h2>
                    <form id="withdrawal-form">
                        <div>
                            <input type="hidden" id="withdrawal_id_edit" name="withdrawal_id_edit" value="">
                            <input placeholder="Cari Nama Part" class="form-control" type="text" id="part_code" name="part_code" required>
                            <div id="partsuggestions" class="autocomplete-suggestions hidden">
                                <ul></ul>
                            </div>
                            <div id="part_name_display" style="margin-top: 5px; font-style: italic; color: grey;"></div> <!-- Elemen baru untuk menampilkan nama part -->
                        </div>
                        <input type="hidden" id="maxstock" name="maxstock">
                        <input type="hidden" id="from_site_id" name="from_site_id">
                        <input placeholder="Jumlah Penarikan" class="form-control mt-4" type="number" id="requested_quantity" name="requested_quantity" required><br>
                        <textarea placeholder="Alasan Penarikan" class="form-control" type="text" id="withdrawal_reason" name="withdrawal_reason"></textarea>
                        <button class="btn btn-primary mt-4" type="button" id="submit-button" onclick="createWithdrawal()">Simpan</button> <!-- Ubah id tombol -->
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Detail Modal -->
<div class="modal fade" id="detailModal" tabindex="-1" role="dialog" aria-labelledby="detailModalLabel" aria-hidden="true">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="detailModalLabel">Detail Pengajuan</h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">×</span>
                </button>
            </div>
            <div class="modal-body" id="detailModalBody">
                <!-- Data akan ditampilkan di sini -->
            </div>
        </div>
    </div>
</div>

<!-- Edit Status Modal -->
<div class="modal fade" id="editStatusModal" tabindex="-1" role="dialog" aria-labelledby="editStatusModalLabel" aria-hidden="true">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="editStatusModalLabel">Edit Status Return</h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">×</span>
                </button>
            </div>
            <div class="modal-body">
                <form id="edit-status-form">
                    <input type="hidden" id="edit_withdrawal_id" name="withdrawal_id">

                    <div class="form-group mb-3">
                        <label for="part_name_display">Nama Part</label>
                        <div id="edit_part_name" class="form-control-static"></div>
                    </div>

                    <div class="form-group mb-3">
                        <label for="edit_status">Status</label>
                        <select class="form-control" id="edit_status" name="status" required>
                            <option value="Pending">Pending</option>
                            <option value="Rejected">Ditolak</option>
                        </select>
                    </div>

                    <div class="form-group mb-3">
                        <label for="edit_notes">Catatan</label>
                        <textarea class="form-control" id="edit_notes" name="notes" rows="3" placeholder="Tambahkan catatan (opsional)"></textarea>
                    </div>

                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-dismiss="modal">Batal</button>
                        <button type="button" class="btn btn-primary" id="update-status-btn">Simpan Perubahan</button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>
@endsection