<!DOCTYPE html>
<html lang="id">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>TAR Daily Report - <?php echo e($dailyReport->unit->unit_code ?? 'N/A'); ?></title>
    <style>
        @page {
            margin: 0;
            padding: 0;
            size: A4 portrait;
        }

        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 0;
            font-size: 14px;
            line-height: 1.2;
            font-weight: bold;
            color: #000;
            position: relative;
        }

        .template-background {
            position: absolute;
            top: 0;
            left: 0;
            width: 210mm;
            height: 297mm;
            z-index: 1;
        }

        .template-background img {
            width: 100%;
            height: 100%;
            object-fit: fill;
        }

        .content-overlay {
            position: relative;
            z-index: 2;
            width: 210mm;
            height: 297mm;
            padding: 0;
            margin: 0;
        }

        /* Header Right Form Fields */
        .header-form {
            position: absolute;
            top: 19mm;
            right: 24mm;
            width: 40mm;
        }

        .form-field {
            font-size: 9px;
            margin-bottom: 2mm;
            color: #000;
        }

        /* Data Unit Fields */
        .data-fields {
            position: absolute;
            top: 37.5mm;
            left: 23mm;
            right: 15mm;
        }

        .field-left {
            position: absolute;
            left: 0;
            width: 85mm;
        }

        .field-right {
            position: absolute;
            right: 0;
            width: 100mm;
        }

        .field-item {
            font-size: 10px;
            font-weight: bold;
            color: #000;
        }

        .field-value {
            display: inline-block;
            margin-left: 2mm;
            font-weight: normal;
        }

        /* Problem Issue Section */
        .problem-section {
            position: absolute;
            top: 145mm;
            left: 15mm;
            right: 15mm;
            height: 25mm;
        }

        .problem-content {
            font-size: 9px;
            color: #000;
            padding: 2mm;
            line-height: 1.3;
        }

        /* Parts Failure Analysis Section */
        .analysis-section {
            position: absolute;
            top: 175mm;
            left: 15mm;
            right: 15mm;
            height: 30mm;
        }
        .analysis-content {
            font-size: 9px;
            color: #000;
            padding: 2mm;
            line-height: 1.3;
        }
        .parts-table-section {
            position: absolute;
            top: 210mm;
            left: 15mm;
            right: 15mm;
            height: 20mm;
        }

        .parts-table {
            width: 100%;
            border-collapse: collapse;
            font-size: 8px;
        }

        .parts-table td {
            padding: 1mm;
            text-align: center;
            color: #000;
            vertical-align: middle;
        }

        /* Picture Component Failure Section */
        .picture-section {
            position: absolute;
            top: 235mm;
            left: 15mm;
            right: 15mm;
            height: 25mm;
        }

        .picture-content {
            display: flex;
            flex-wrap: wrap;
            gap: 2mm;
            padding: 2mm;
        }

        .picture-item {
            display: flex;
            flex-direction: column;
            width: 40mm;
        }

        .picture-label {
            font-size: 6px;
            font-weight: bold;
            text-align: center;
            margin-bottom: 1mm;
            color: #000;
        }

        .picture-content img {
            width: 40mm;
            height: 18mm;
            object-fit: cover;
            border: 1px solid #ccc;
        }

        /* Correction Action Section */
        .correction-section {
            position: absolute;
            top: 265mm;
            left: 15mm;
            right: 15mm;
            height: 12mm;
        }

        .correction-content {
            font-size: 8px;
            color: #000;
            padding: 1mm;
            line-height: 1.2;
        }

        /* Recommendation Section */
        .recommendation-section {
            position: absolute;
            top: 280mm;
            left: 15mm;
            right: 15mm;
            height: 12mm;
        }

        .recommendation-content {
            font-size: 8px;
            color: #000;
            padding: 1mm;
            line-height: 1.2;
        }
    </style>
</head>
<body>
    <!-- Background Template Image -->
    <div class="template-background">
        <img src="<?php echo e(public_path('assets/images/tartemplate.png')); ?>" alt="TAR Template">
    </div>

    <!-- Content Overlay -->
    <div class="content-overlay">

        <!-- Header Form Fields (Top Right) -->
        <div class="header-form">
            <div class="form-field">PT. PUTERA WIBOWO BORNEO</div>
            <div class="form-field">AC UNIT</div>
        </div>

        <!-- Data Unit Fields -->
        <div class="data-fields">
            <table style="font-size: 10px; font-weight: bold;">
                <tr>
                    <td style="width: 90px; padding-left: 5px; padding-top: 0px;"><?php echo e($dailyReport->unit->unit_code ?? ''); ?></td>
                    <td style="width: 50px; padding: 20px 10px 0px 10px;"></td>
                    <td style="width: 330px;"></td>
                    <td style="width: 90px;"><?php echo e($dailyReport->problem_component ?? ''); ?></td>
                </tr>
                <tr>
                    <td style="width: 90px; padding-left: 5px;padding-top: 4px; "><?php echo e($dailyReport->unit->unit_code ?? ''); ?></td>
                    <td><?php echo e($dailyReport->technicians[0]['name'] ?? ''); ?></td>
                    <td><?php echo e($dailyReport->technicians[1]['name'] ?? ''); ?></td>
                </tr>
                <tr>
                    <td style="width: 90px; padding-left: 5px; padding-top: 12px;"><?php echo e($dailyReport->hm ?? ''); ?></td>
                    <td style="width: 50px; padding-top: 12px;"><?php echo e($dailyReport->technicians[2]['name'] ?? ''); ?></td>
                    <td style="width: 90px; padding-top: 12px;"><?php echo e($dailyReport->technicians[3]['name'] ?? ''); ?></td>
                    <?php
                        $date = \Carbon\Carbon::parse($dailyReport->date_in);
                        $formattedDate = $date->format('d/m/Y');
                    ?>
                    <td style="width: 90px; padding-top: 10px;"><?php echo e($formattedDate); ?></td>
                </tr>
            </table>

            <div class="field-right">
                <div class="field-item">
                    Component: <span class="field-value"><?php echo e($dailyReport->problem_component ?? ''); ?></span>
                </div>
                <div class="field-item">
                    Life Time Component: <span class="field-value"></span>
                </div>
                <div class="field-item">
                    Trouble Date: <span class="field-value">
                    </span>
                </div>
            </div>
        </div>

        <!-- Problem Issue Section -->
        <div class="problem-section">
            <div class="problem-content">
                <?php echo e($dailyReport->problem_description ?? ''); ?>

                <?php if($dailyReport->jobs->count() > 0): ?>
                    <br><strong>Job Description:</strong><br>
                    <?php $__currentLoopData = $dailyReport->jobs; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $job): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                        • <?php echo e($job->job_description); ?><br>
                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                <?php endif; ?>
            </div>
        </div>

        <!-- Parts Failure Analysis Section -->
        <div class="analysis-section">
            <div class="analysis-content">
                <?php echo e($dailyReport->problem ?? ''); ?>

            </div>
        </div>

        <!-- Main Parts Problem Table -->
        <div class="parts-table-section">
            <table class="parts-table">
                <tbody>
                    <!-- Empty rows for manual filling - positioned to align with template table -->
                    <tr><td style="width: 25%;">&nbsp;</td><td style="width: 25%;">&nbsp;</td><td style="width: 15%;">&nbsp;</td><td style="width: 35%;">&nbsp;</td></tr>
                    <tr><td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td></tr>
                    <tr><td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td></tr>
                </tbody>
            </table>
        </div>

        <!-- Picture Component Failure Section -->
        <div class="picture-section">
            <div class="picture-content">
                <?php if($dailyReport->images->count() > 0): ?>
                    <?php $__currentLoopData = $dailyReport->images->take(4); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $image): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                        <div class="picture-item">
                            <div class="picture-label">
                                <?php if($image->type == 'before'): ?>
                                    Gambar Sebelum
                                <?php elseif($image->type == 'after'): ?>
                                    Gambar Sesudah
                                <?php elseif($image->type == 'unit'): ?>
                                    Gambar Unit
                                <?php else: ?>
                                    Gambar
                                <?php endif; ?>
                            </div>
                            <img src="<?php echo e(public_path('assets/daily_reports/' . $image->image_path)); ?>" alt="Component Image">
                        </div>
                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                <?php endif; ?>
            </div>
        </div>

        <!-- Correction Action Section -->
        <div class="correction-section">
            <div class="correction-content">
                <?php if($dailyReport->jobs->count() > 0): ?>
                    <?php $__currentLoopData = $dailyReport->jobs; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $job): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                        <?php if($job->highlight): ?>
                            <?php echo e($job->job_description); ?><br>
                        <?php endif; ?>
                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                <?php endif; ?>
            </div>
        </div>

        <!-- Recommendation Preventive Action Section -->
        <div class="recommendation-section">
            <div class="recommendation-content">
                <!-- This section can be filled manually or from additional data -->
            </div>
        </div>

    </div>
</body>
</html>
<?php /**PATH C:\xampp\htdocs\portalpwb\resources\views/daily-reports/single-pdf.blade.php ENDPATH**/ ?>