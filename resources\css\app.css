@tailwind base;
@tailwind components;
@tailwind utilities;

/* Pagination Styles */
.pagination {
    display: flex;
    list-style: none;
    padding: 0;
    margin: 0;
}

.pagination li {
    margin: 0 2px;
}

.pagination li a {
    color: #333;
    background-color: #fff;
    border: 1px solid #ddd;
    padding: 6px 12px;
    text-decoration: none;
    border-radius: 4px;
    cursor: pointer;
    display: block;
}

.pagination li.active a {
    color: #fff;
    background-color: #28a745;
    border-color: #28a745;
}

.pagination li a:hover {
    background-color: #f8f9fa;
}

.pagination li.disabled a {
    color: #6c757d;
    pointer-events: none;
    background-color: #fff;
    border-color: #ddd;
}

body {
    background-size: cover;
    background-position: center center;
    background-repeat: no-repeat;
    background-attachment: fixed;
    min-height: 100vh;
    margin: 0;
    transition: background-image 1s ease-in;
}

html {
    min-height: 100vh;
}

.bg1 {
    background-image: url("https://images.pexels.com/photos/869258/pexels-photo-869258.jpeg?auto=compress&cs=tinysrgb&w=1260&h=750&dpr=1");
}

.bg2 {
    background-image: url("https://images.pexels.com/photos/2391/dirty-industry-stack-factory.jpg?auto=compress&cs=tinysrgb&w=1260&h=750&dpr=1");
}

.bg3 {
    background-image: url("https://images.pexels.com/photos/5775880/pexels-photo-5775880.jpeg?auto=compress&cs=tinysrgb&w=1260&h=750&dpr=1");
}

.bg4 {
    background-image: url("https://images.pexels.com/photos/3089685/pexels-photo-3089685.jpeg?auto=compress&cs=tinysrgb&w=1260&h=750&dpr=1");
}

.bg-warning {
    background-color: yellow !important;
}

.wrapper {
    background-color: rgba(254, 255, 219, 0) !important;
}

.content-page {
    background-color: rgba(254, 255, 219, 0) !important;
}

.table-hover tbody tr:hover {
    background-color: #f5f5f5;
    cursor: pointer;
}

#detailModal .table-responsive {
    max-height: 300px;
    overflow-y: auto;
}

.noti-icon-badge {
    position: absolute;
    top: -5px;
    right: -5px;
    background: red;
    color: white;
    border-radius: 50%;
    padding: 2px 6px;
    font-size: 12px;
}

#notification-list {
    max-height: 400px;
    overflow-y: auto;
}

.maxforminput {
    max-width: 500px;
}

.max300 {
    max-width: 300px !important;
}

.max500 {
    max-width: 500px !important;
}

.max400 {
    max-width: 400px !important;
}

.bgchart {
    background: rgb(254, 255, 219);
    background: -moz-linear-gradient(150deg, rgba(254, 255, 219, 1) 0%, rgba(255, 255, 255, 1) 100%);
    background: -webkit-linear-gradient(150deg, rgba(254, 255, 219, 1) 0%, rgba(255, 255, 255, 1) 100%);
    background: linear-gradient(150deg, rgba(254, 255, 219, 1) 0%, rgba(255, 255, 255, 1) 100%);
    filter: progid:DXImageTransform.Microsoft.gradient(startColorstr="#feffdb", endColorstr="#ffffff", GradientType=1);
}

.bgchart2 {
    background: rgb(160, 237, 255);
    background: linear-gradient(150deg, rgba(160, 237, 255, 1) 0%, rgba(255, 255, 255, 1) 100%);
}

.chart-container {
    position: relative;
    /* Important: Needed for absolute positioning */
    min-height: 400px;
}

.chart-container h5 {
    position: absolute;
    top: 10px;
    left: 20px;
    color: #333;
    font-size: 1.2em;
    margin: 0;
    padding: 0;
    background-color: rgba(255, 255, 255, 0.8);
    z-index: 10;
}

.left-side-menu {
    box-shadow: 0px 0px 1px 1px rgba(0, 0, 0, 0.2);
    -webkit-box-shadow: 0px 0px 1px 1px rgba(0, 0, 0, 0.2);
    -moz-box-shadow: 0px 0px 1px 1px rgba(0, 0, 0, 0.1) !important;
}


.shadow-kit {
    box-shadow: 0px 0px 1px 1px rgba(0, 0, 0, 0.2);
    -webkit-box-shadow: 0px 0px 1px 1px rgba(0, 0, 0, 0.2);
    -moz-box-shadow: 0px 0px 1px 1px rgba(0, 0, 0, 0.1);
}

.bgwhite {
    background-color: rgb(255, 255, 255);
}

.custom-radio {
    width: 18px;
    height: 18px;
    accent-color: #007bff;
    cursor: pointer;
}

.form-check-label {
    font-size: 16px;
    font-weight: 500;
    cursor: pointer;
}

.alert {
    padding: 15px;
    margin: 10px 0;
    border: 1px solid transparent;
    border-radius: 4px;
    position: fixed;
    top: 20px;
    right: 20px;
    z-index: 9999;
    min-width: 300px;
}

.alert-success {
    color: #3c763d;
    background-color: #dff0d8;
    border-color: #d6e9c6;
}

.max200 {
    max-width: 200px;
}

.max100 {
    max-width: 100px;
}

#detailsDiv {
    display: none;
    border: 1px solid #ccc;
    padding: 10px;
    margin-top: 10px;
    width: 300px;
    float: right;
}

.alert-error {
    color: #a94442;
    background-color: #f2dede;
    border-color: #ebccd1;
}

.autocomplete-list {
    list-style: none;
    padding: 0;
    margin: 0;
    border: 1px solid #ccc;
    position: absolute;
    background-color: #fff;
    width: 100%;
    z-index: 1;
    display: none;
}

.autocomplete-list li {
    padding: 5px;
    cursor: pointer;
}

.autocomplete-list li:hover {
    background-color: #f0f0f0;
}

#part-suggestions {
    border: 1px solid #ccc;
    position: absolute;
    /* Adjust as needed */
    background-color: white;
    width: 200px;
    z-index: 10;
}

#part-suggestions ul {
    list-style: none;
    padding: 0;
    margin: 0;
}

#part-suggestions li {
    padding: 5px;
    cursor: pointer;
}

#part-suggestions li:hover {
    background-color: #f0f0f0;
}

#update-withdrawal-form, #colhidden {
    display: none;
}

/* Suggestions dropdown styling */
.suggestions-dropdown {
    position: absolute;
    background-color: white;
    border: 1px solid #ccc;
    width: 95%;
    max-height: 200px;
    overflow-y: auto;
    z-index: 1000;
    box-shadow: 0 4px 8px rgba(0,0,0,0.1);
}

.suggestion-item {
    padding: 8px 10px;
    cursor: pointer;
    border-bottom: 1px solid #f0f0f0;
}

.suggestion-item:hover {
    background-color: #f0f0f0;
}

.suggestion-limit-message {
    padding: 5px 10px;
    font-size: 12px;
    font-style: italic;
    background-color: #f8f9fa;
    border-bottom: 1px solid #ddd;
    color: #6c757d;
}

.maxfit {
    max-width: fit-content;
}

.btnclosed {
    position: absolute;
    color: #a94442;
    top: 10px;
    right: 10px;
    background: transparent;
    border: none;
    font-size: 20px;
    cursor: pointer
}

#iframesk {
    width: 100%;
}

#resolve-form {
    display: none;
    /* Sembunyikan form secara default */
}

/* Modal styles */
.modal {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    z-index: 1050;
    overflow-x: hidden;
    overflow-y: auto;
    outline: 0;
}

.modal-dialog {
    position: relative;
    width: auto;
    margin: 0.5rem;
    pointer-events: none;
}

@media (min-width: 576px) {
    .modal-dialog {
        max-width: 500px;
        margin: 1.75rem auto;
    }
}

.modal-content {
    position: relative;
    display: flex;
    flex-direction: column;
    width: 100%;
    background-color: #fff;
    background-clip: padding-box;
    border: 1px solid rgba(0, 0, 0, 0.2);
    border-radius: 0.3rem;
    outline: 0;
    pointer-events: auto;
}

.modal-header {
    display: flex;
    align-items: flex-start;
    justify-content: space-between;
    padding: 1rem;
    border-bottom: 1px solid #dee2e6;
    border-top-left-radius: 0.3rem;
    border-top-right-radius: 0.3rem;
}

.modal-body {
    position: relative;
    flex: 1 1 auto;
    padding: 1rem;
}

.modal-footer {
    display: flex;
    align-items: center;
    justify-content: flex-end;
    padding: 1rem;
    border-top: 1px solid #dee2e6;
}

.close {
    float: right;
    font-size: 1.5rem;
    font-weight: 700;
    line-height: 1;
    color: #000;
    text-shadow: 0 1px 0 #fff;
    opacity: 0.5;
    padding: 0;
    background-color: transparent;
    border: 0;
    appearance: none;
    cursor: pointer;
}

.close:hover {
    color: #000;
    text-decoration: none;
    opacity: 0.75;
}

/* Button styles */
.modal-buttons {
    margin-top: 20px;
    display: flex;
    gap: 10px;
    justify-content: flex-end;
}

.modal-button {
    padding: 8px 16px;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    font-weight: 500;
}

.modal-button.primary {
    background-color: #4CAF50;
    color: white;
}

.modal-button.secondary {
    background-color: #2196F3;
    color: white;
}

.modal-button:hover {
    opacity: 0.9;
}

/* Modal fixes */
.modal-backdrop {
    position: fixed;
    top: 0;
    left: 0;
    z-index: 1040;
    width: 100vw;
    height: 100vh;
    background-color: #000;
}

.modal-backdrop.fade {
    opacity: 0;
}

.modal-backdrop.show {
    opacity: 0.5;
}

/* Ensure modal is clickable */
.modal.show {
    display: block;
    pointer-events: auto;
}

.modal.fade .modal-dialog {
    transition: transform 0.3s ease-out;
    transform: translate(0, -50px);
}

.modal.show .modal-dialog {
    transform: none;
    pointer-events: auto;
}

/* Pagination Styles */
.pagination {
    display: flex;
    padding-left: 0;
    list-style: none;
    border-radius: 0.25rem;
}

.page-link {
    position: relative;
    display: block;
    padding: 0.5rem 0.75rem;
    margin-left: -1px;
    line-height: 1.25;
    color: #64b0f2;
    background-color: #fff;
    border: 1px solid #dee2e6;
}

.page-item.active .page-link {
    z-index: 1;
    color: #fff;
    background-color: #64b0f2;
    border-color: #64b0f2;
}

.page-item.disabled .page-link {
    color: #6c757d;
    pointer-events: none;
    cursor: auto;
    background-color: #fff;
    border-color: #dee2e6;
}