<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Models\PartInventory;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class FixNegativeStockQuantities extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'inventory:fix-negative-stock';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Fix negative stock quantities in part_inventories table';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('Starting to fix negative stock quantities...');
        
        // Get all part inventories with negative stock
        $negativeStockParts = PartInventory::where('stock_quantity', '<', 0)
            ->with('part', 'site')
            ->get();
            
        $count = $negativeStockParts->count();
        
        if ($count === 0) {
            $this->info('No negative stock quantities found. All good!');
            return 0;
        }
        
        $this->info("Found {$count} part inventories with negative stock quantities.");
        
        // Ask for confirmation before proceeding
        if (!$this->confirm('Do you want to set these negative stock quantities to 0?', true)) {
            $this->info('Operation cancelled.');
            return 1;
        }
        
        // Start a transaction
        DB::beginTransaction();
        
        try {
            foreach ($negativeStockParts as $partInventory) {
                $oldQuantity = $partInventory->stock_quantity;
                
                // Log the change
                $this->info("Fixing part: {$partInventory->part->part_name} (Code: {$partInventory->part_code}) at site: {$partInventory->site->site_name}");
                $this->info("  Current quantity: {$oldQuantity} -> New quantity: 0");
                
                // Update the stock quantity to 0
                $partInventory->stock_quantity = 0;
                $partInventory->save();
                
                // Log the change to the application log
                Log::warning("Fixed negative stock quantity for part: {$partInventory->part->part_name} (Code: {$partInventory->part_code}) at site: {$partInventory->site->site_name}. Changed from {$oldQuantity} to 0.");
            }
            
            // Commit the transaction
            DB::commit();
            
            $this->info('Successfully fixed all negative stock quantities!');
            return 0;
            
        } catch (\Exception $e) {
            // Rollback the transaction if something goes wrong
            DB::rollBack();
            
            $this->error('An error occurred while fixing negative stock quantities: ' . $e->getMessage());
            Log::error('Error fixing negative stock quantities: ' . $e->getMessage());
            
            return 1;
        }
    }
}
