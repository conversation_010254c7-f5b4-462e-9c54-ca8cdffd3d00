import Swal from "sweetalert2";

// Global variables for pagination
let currentPage = 1;
const itemsPerPage = 20; // 20 items per page for instock table

document.addEventListener("DOMContentLoaded", function () {
    const supplierFilter = document.getElementById("supplierFilter");
    const inStockTableBody = document.getElementById("inStockTableBody");
    const partNameInput = document.getElementById("partName");
    const partSuggestionsDiv = document.getElementById("partSuggestions");
    const partCodeInput = document.getElementById("partCode");
    const addInStockForm = document.getElementById("addInStockForm");
    const startDateInput = document.getElementById("startDate");
    const endDateInput = document.getElementById("endDate");
    const totalid = document.getElementById("total");

    // Fungsi untuk membuat elemen tabel row
    function formatTanggal(dateString) {
        const bulan = [
            "<PERSON><PERSON><PERSON>",
            "<PERSON><PERSON><PERSON>",
            "<PERSON><PERSON>",
            "April",
            "<PERSON>",
            "<PERSON><PERSON>",
            "<PERSON><PERSON>",
            "Agustus",
            "September",
            "Oktober",
            "November",
            "Desember",
        ];

        const date = new Date(dateString);
        if (isNaN(date.getTime())) return "Tanggal tidak valid";

        const tanggal = date.getDate();
        const bulanNama = bulan[date.getMonth()];
        const tahun = date.getFullYear();

        return `${tanggal} ${bulanNama} ${tahun}`;
    }

    function createInStockRow(item) {
        const row = document.createElement("tr");
        row.id = `row_${item.site_in_stock_id}`;
        row.innerHTML = `
            <td>${item.part_inventory.part.part_code}</td>
            <td>${item.part_inventory.part.part_name}</td>
            <td>${
                item.supplier
                    ? item.supplier.supplier_name
                    : "Tidak ada customer"
            }</td>
            <td>${item.quantity}</td>
            <td>${formatTanggal(item.date_in)}</td>
            <td>${item.notes}</td>
            <td>
                <button class="btn btn-danger btn-sm delete-btn" data-id="${item.site_in_stock_id}">Delete</button>
            </td>
        `;
        return row;
    }

    function loadLastActivities() {
        fetch("/last-activities")
            .then((response) => response.json())
            .then((data) => {
                const ul = document.getElementById("activityList");

                if (!ul) {
                    console.error('Element with ID "activityList" not found!');
                    return;
                }
                ul.innerHTML = "";
                data.forEach((activity) => {
                    const li = document.createElement("li");
                    const a = document.createElement("a");
                    a.href = "#";
                    a.textContent = activity.description;
                    li.appendChild(a);
                    ul.appendChild(li);
                });
            })
            .catch((error) => {
                console.error("Error fetching activities:", error);
                const ul = document.getElementById("activityList");
                if (ul) {
                    ul.innerHTML = "<li>Error loading activities.</li>";
                }
            });
    }

    // Filter Supplier
    supplierFilter.addEventListener("change", function () {
        const supplierId = this.value;
        const startDate = startDateInput.value;
        const endDate = endDateInput.value;
        // Reset to page 1 when filter changes
        loadInStockData(supplierId, startDate, endDate, 1);
    });
    // Filter Date Range
    startDateInput.addEventListener("change", function () {
        const supplierId = supplierFilter.value;
        const startDate = this.value;
        const endDate = endDateInput.value;
        // Reset to page 1 when filter changes
        loadInStockData(supplierId, startDate, endDate, 1);
    });

    endDateInput.addEventListener("change", function () {
        const supplierId = supplierFilter.value;
        const startDate = startDateInput.value;
        const endDate = this.value;
        // Reset to page 1 when filter changes
        loadInStockData(supplierId, startDate, endDate, 1);
    });

    // Function to create pagination item
    function createPaginationItem(page, text, isActive = false) {
        const li = document.createElement('li');
        li.className = `page-item ${isActive ? 'active' : ''}`;

        const a = document.createElement('a');
        a.className = 'page-link';
        a.href = '#';
        a.textContent = text;
        a.dataset.page = page;

        li.appendChild(a);
        return li;
    }

    // Function to render pagination
    function renderPagination(data) {
        try {
            const paginationContainer = document.getElementById('instock-pagination');
            if (!paginationContainer) {
                console.error('Pagination container not found');
                return;
            }

            paginationContainer.innerHTML = '';

            if (data.last_page > 1) {
                const pagination = document.createElement('ul');
                pagination.className = 'pagination pagination-rounded  justify-content-center';

                // Previous button
                if (data.current_page > 1) {
                    pagination.appendChild(createPaginationItem(data.current_page - 1, '\u00ab'));
                }

                // Page numbers
                for (let i = 1; i <= data.last_page; i++) {
                    pagination.appendChild(createPaginationItem(i, i, i === data.current_page));
                }

                // Next button
                if (data.current_page < data.last_page) {
                    pagination.appendChild(createPaginationItem(data.current_page + 1, '\u00bb'));
                }

                paginationContainer.appendChild(pagination);

                // Add event listeners to pagination links
                paginationContainer.querySelectorAll('.page-link').forEach(link => {
                    link.addEventListener('click', function(e) {
                        e.preventDefault();
                        const page = parseInt(this.dataset.page);
                        currentPage = page;
                        const currentSupplierId = supplierFilter.value;
                        const currentStartDate = startDateInput.value;
                        const currentEndDate = endDateInput.value;
                        loadInStockData(currentSupplierId, currentStartDate, currentEndDate, page);
                    });
                });
            }

            // Update global pagination data
            window.instockPaginationData = data;
        } catch (error) {
            console.error('Error rendering pagination:', error);
        }
    }

    // Fungsi untuk memuat data in stock (digunakan saat filter atau setelah tambah data)
    function loadInStockData(supplierId = "", startDate = "", endDate = "", page = 1) {
        loadLastActivities();
        currentPage = page;

        // Show loading indicator
        inStockTableBody.innerHTML = '<tr><td colspan="7" class="text-center">Loading...</td></tr>';

        let url = `/instockwho/filter`;
        const params = new URLSearchParams();
        if (supplierId) {
            params.append("supplier_id", supplierId);
        }
        if (startDate) {
            params.append("start_date", startDate);
        }
        if (endDate) {
            params.append("end_date", endDate);
        }

        // Add pagination parameters
        params.append("page", page);
        params.append("per_page", itemsPerPage);

        const queryString = params.toString();
        if (queryString) {
            url += `?${queryString}`;
        }

        fetch(url, {
            method: "GET",
            headers: {
                "Content-Type": "application/json",
                "X-CSRF-TOKEN": document
                    .querySelector('meta[name="csrf-token"]')
                    .getAttribute("content"),
            },
        })
            .then((response) => {
                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }
                return response.json();
            })
            .then((data) => {
                inStockTableBody.innerHTML = "";
                let total = 0;

                if (data.data && data.data.length > 0) {
                    data.data.forEach((item) => {
                        total += item.quantity;
                        const row = createInStockRow(item);
                        inStockTableBody.appendChild(row);
                    });
                } else {
                    inStockTableBody.innerHTML = '<tr><td colspan="7" class="text-center">No data found</td></tr>';
                }

                totalid.innerText = total;
                attachDeleteEventListeners();

                // Render pagination
                renderPagination({
                    current_page: data.current_page,
                    per_page: data.per_page,
                    last_page: data.last_page,
                    total: data.total
                });
            })
            .catch((error) => {
                console.error("Error fetching data:", error);
                inStockTableBody.innerHTML = '<tr><td colspan="7" class="text-center text-danger">Failed to load data. Please try again.</td></tr>';
                Swal.fire({
                    icon: "error",
                    title: "Kesalahan!",
                    text: "Gagal memuat data. Silakan periksa konsol.",
                });
            });
    }

    // Delete Data
    function attachDeleteEventListeners() {
        document.querySelectorAll(".delete-btn").forEach((button) => {
            const currentSupplierId = supplierFilter.value;
            const currentStartDate = startDateInput.value;
            const currentEndDate = endDateInput.value;
            button.addEventListener("click", function () {
                const id = this.dataset.id;

                Swal.fire({
                    title: "Apakah anda yakin ?",
                    text: "Kamu tidak dapat mengambalikan data ini !",
                    icon: "warning",
                    showCancelButton: true,
                    confirmButtonColor: "#3085d6",
                    cancelButtonColor: "#d33",
                    confirmButtonText: "Ya, hapus!",
                }).then((result) => {
                    if (result.isConfirmed) {
                        fetch(`/instockwho/${id}`, {
                            method: "DELETE",
                            headers: {
                                "Content-Type": "application/json",
                                "X-CSRF-TOKEN": document
                                    .querySelector('meta[name="csrf-token"]')
                                    .getAttribute("content"),
                            },
                        })
                            .then((response) => {
                                if (!response.ok) {
                                    throw new Error(
                                        `HTTP error! status: ${response.status}`
                                    );
                                }
                                return response.json();
                            })
                            .then((data) => {
                                if (data.success) {
                                    Swal.fire(
                                        "Terhapus!",
                                        data.message,
                                        "success"
                                    );
                                    const row = document.getElementById(
                                        `row_${id}`
                                    );
                                    if (row) {
                                        row.remove();
                                    }
                                    // Reset to page 1 after deleting an item
                                    loadInStockData(
                                        currentSupplierId,
                                        currentStartDate,
                                        currentEndDate,
                                        1
                                    );
                                } else {
                                    Swal.fire({
                                        icon: "error",
                                        title: "Oops...",
                                        text: data.message,
                                    });
                                }
                            })
                            .catch((error) => {
                                console.error("Error deleting:", error);
                                Swal.fire({
                                    icon: "error",
                                    title: "Kesalahan!",
                                    text: "Gagal menghapus. silahkan coba beberapa saat lagi",
                                });
                            });
                    }
                });
            });
        });
    }

    attachDeleteEventListeners();
    loadLastActivities();

    partNameInput.addEventListener("input", function () {
        const query = this.value;
        if (query.length >= 3) {
            fetch(`/instockwho/parts?query=${query}`, {
                method: "GET",
                headers: {
                    "Content-Type": "application/json",
                    "X-CSRF-TOKEN": document
                        .querySelector('meta[name="csrf-token"]')
                        .getAttribute("content"),
                },
            })
                .then((response) => {
                    if (!response.ok) {
                        throw new Error(
                            `HTTP error! status: ${response.status}`
                        );
                    }

                    return response.json();
                })
                .then((data) => {
                    partSuggestionsDiv.innerHTML = "";
                    data.forEach((part) => {
                        const suggestion = document.createElement("a");
                        suggestion.href = "#";
                        suggestion.classList.add(
                            "list-group-item",
                            "list-group-item-action"
                        );
                        suggestion.textContent = `${part.part_name} (${part.part_code})`;
                        suggestion.addEventListener("click", function (e) {
                            e.preventDefault();
                            partNameInput.value = part.part_name;
                            partCodeInput.value = part.part_code; // Set the part code
                            partSuggestionsDiv.innerHTML = "";
                        });
                        partSuggestionsDiv.appendChild(suggestion);
                    });
                })
                .catch((error) => {
                    console.error("Error fetching part suggestions:", error);
                    Swal.fire({
                        icon: "error",
                        title: "Kesalahan!",
                        text: "Gagal mendapatkan supplier.",
                    });
                });
        } else {
            partSuggestionsDiv.innerHTML = "";
        }
    });

    // Tambah In Stock
    addInStockForm.addEventListener("submit", function (e) {
        const currentSupplierId = supplierFilter.value;
        const currentStartDate = startDateInput.value;
        const currentEndDate = endDateInput.value;
        e.preventDefault();
        Swal.fire({
            title: "Apakah anda yakin ?",
            text: "Data tidak dapat diperbaharui, namun bisa dihapus",
            icon: "warning",
            showCancelButton: true,
            confirmButtonColor: "#3085d6",
            cancelButtonColor: "#d33",
            confirmButtonText: "Ya, tambahkan!",
        }).then((result) => {
            if (result.isConfirmed) {
                const formData = new FormData(addInStockForm);
                fetch("/instockwho/store", {
                    method: "POST",
                    body: formData,
                    headers: {
                        "X-CSRF-TOKEN": document
                            .querySelector('meta[name="csrf-token"]')
                            .getAttribute("content"),
                    },
                })
                    .then((response) => {
                        if (!response.ok) {
                            return response.json().then((data) => {
                                // Parse error message
                                throw new Error(
                                    `HTTP error! status: ${response.status}, message: ${data.message}`
                                );
                            });
                        }
                        return response.json();
                    })
                    .then((data) => {
                        if (data.success) {
                            Swal.fire(
                                "Ditambahkan!",
                                "Stok berhasil ditambahkan!",
                                "success"
                            );
                            addInStockForm.reset();
                            partCodeInput.value = "";
                            // Reset to page 1 after adding new item
                            loadInStockData(
                                currentSupplierId,
                                currentStartDate,
                                currentEndDate,
                                1
                            );
                        } else {
                            Swal.fire({
                                icon: "error",
                                title: "Oops...",
                                text: "Gagal : " + data.message,
                            });
                        }
                    })
                    .catch((error) => {
                        console.error("Error adding in stock:", error);
                        Swal.fire({
                            icon: "error",
                            title: "Kesalahan!",
                            text: "Gagal menambahkan stok. pastikan data diinput dengan benar !",
                        });
                    });
            }
        });
    });

    // Set today's date as default if not already set
    const today = new Date().toISOString().split('T')[0];
    if (!startDateInput.value) {
        startDateInput.value = today;
    }
    if (!endDateInput.value) {
        endDateInput.value = today;
    }

    // Load initial data with pagination and today's date
    loadInStockData(
        "",
        startDateInput.value,
        endDateInput.value,
        1
    );
});
