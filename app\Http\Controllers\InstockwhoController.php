<?php

namespace App\Http\Controllers;

use App\Models\LogAktivitas;
use App\Models\SiteInStock;
use App\Models\Part;
use App\Models\PartInventory;
use App\Models\Site;
use App\Models\Supplier;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Facades\Auth; // Pastikan ini di-import
use Illuminate\Support\Facades\DB;

class InstockwhoController extends Controller
{
    public function index()
    {
        $siteId = session('site_id');
        $suppliers = Supplier::all();
        $sites = Site::all(); // Get all sites for the dropdown
        $siteInStocks = SiteInStock::with(['partInventory.part', 'supplier', 'employee'])
            ->whereHas('partInventory', function ($query) use ($siteId) {
                $query->where('site_id', $siteId);
            })
            ->get();
        $totalQuantity = $siteInStocks->sum('quantity');
        return view('warehouse.instockwho', compact('siteInStocks', 'suppliers', 'totalQuantity', 'sites'));
    }

    public function filter(Request $request)
    {
        $supplierId = $request->input('supplier_id');
        $startDate = $request->input('start_date');
        $endDate = $request->input('end_date');
        $page = $request->input('page', 1);
        $perPage = $request->input('per_page', 20); // Default to 20 items per page
        $requestedSiteId = $request->input('site_id');

        // If a specific site is requested and it's not 'all', use that site
        // Otherwise, default to the session site_id
        $siteId = ($requestedSiteId && $requestedSiteId !== 'all') ? $requestedSiteId : session('site_id');

        $query = SiteInStock::with(['partInventory.part', 'supplier', 'employee']);

        // Only filter by site if we're not showing all sites
        if ($requestedSiteId !== 'all') {
            $query->whereHas('partInventory', function ($query) use ($siteId) {
                $query->where('site_id', $siteId);
            });
        }

        if ($supplierId) {
            $query->where('supplier_id', $supplierId);
        }

        // Default to today's date if no dates are provided
        $today = now()->toDateString();
        $startDate = $startDate ?: $today;
        $endDate = $endDate ?: $today;

        // Apply date filter
        $query->whereBetween('date_in', [$startDate, $endDate]);

        // Get total count for pagination
        $total = $query->count();

        // Get paginated data
        $siteInStocks = $query->skip(($page - 1) * $perPage)
                            ->take($perPage)
                            ->get();

        return response()->json([
            'data' => $siteInStocks,
            'current_page' => (int)$page,
            'per_page' => (int)$perPage,
            'last_page' => ceil($total / $perPage),
            'total' => $total
        ]);
    }

    public function destroy(Request $request, $id)
    {
        $siteInStockId = $id;
        $siteInStock = SiteInStock::find($siteInStockId);

        if (!$siteInStock) {
            return response()->json(['success' => false, 'message' => 'In-stock record not found.'], 404);
        }
        $quantity = $siteInStock->quantity;
        $partInventoryId = $siteInStock->part_inventory_id;
        $partInventory = PartInventory::find($partInventoryId);

        if (!$partInventory) {
            return response()->json(['success' => false, 'message' => 'Part inventory not found.'], 404);
        }

        DB::beginTransaction();

        try {
            if ($partInventory->stock_quantity >= $quantity) {
                $partInventory->stock_quantity -= $quantity;
                $partInventory->save();
            } else {
                return response()->json([
                    'success' => false,
                    'message' => 'Gagal! Beberapa stok telah out, tidak dapat menghapus in-stock'
                ], 200);
            }

            // Hapus in-stock
            $siteInStock->delete();

            // Ambil kembali data Part Inventory setelah penghapusan
            $partInventory = PartInventory::findOrFail($partInventoryId);
            $part_code = $partInventory->part_code;

            // Ambil nama part
            $part = Part::where('part_code', $part_code)->first();
            $partName = $part->part_name;

            // Catat log aktivitas
            LogAktivitas::create([
                'site_id' => session('site_id'),
                'name' => session('name'),
                'action' => 'Menghapus Data In HO',
                'decription' => "Admin HO " . session('name') . " Menghapus Data In Stock " . $partName . " sebanyak " . $quantity, // Perbaiki pesan log dan tambahkan kuantitas
                'ip_address' => $request->ip(),
                'table' => "In Stock HO",
            ]);

            DB::commit();
            return response()->json(['success' => true, 'message' => 'Data berhasil dihapus.']);

        } catch (\Exception $e) {
            DB::rollBack();
            return response()->json([
                'success' => false,
                'message' => 'Gagal menghapus data. ' . $e->getMessage()
            ], 500);
        }
    }

    public function suggestParts(Request $request)
    {
        $query = $request->input('query');
        $siteId = session('site_id');

        $parts = Part::where('part_name', 'like', '%' . $query . '%')
            ->whereHas('partInventories', function ($q) use ($siteId) {
                $q->where('site_id', $siteId);
            })
            ->get();

        return response()->json($parts);
    }

    public function store(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'part_code' => 'required|string',
            'supplier_id' => 'nullable|string',
            'date_in' => 'required|date',
            'quantity' => 'required|numeric|min:0.1',
            'notes' => 'nullable|string',
        ]);

        if ($validator->fails()) {
            return response()->json(['success' => false, 'message' => $validator->errors()->first()], 201);
        }
        $siteId = session('site_id');

        $partCode = $request->input('part_code');

        $partInventory = PartInventory::where('part_code', $partCode)
            ->where('site_id', $siteId)
            ->first();

        if (!$partInventory) {
            return response()->json(['success' => false, 'message' => 'Part not found in your inventory.'], 201);
        }

        DB::beginTransaction();

        try {
            $siteInStock = new SiteInStock();
            $siteInStock->part_inventory_id = $partInventory->part_inventory_id;
            $siteInStock->supplier_id = $request->input('supplier_id');
            // $siteInStock->employee_id = Auth::user()->employee_id;
            $siteInStock->employee_id = session('employee_id');
            $siteInStock->date_in = $request->input('date_in');
            $siteInStock->quantity = $request->input('quantity');
            $siteInStock->notes = $request->input('notes');
            $siteInStock->save();
            $partInventory->stock_quantity += $request->input('quantity');
            $partInventory->save();
            DB::commit();

            $partname = Part::where('part_code', $partCode)->first()->part_name;
            $quantity = $request->input('quantity');

            LogAktivitas::create([
                'site_id' => session('site_id'),
                'name' => session('name'),
                'action' => 'Menambahkan data In Part',
                'decription' => "Admin HO " . session('name') . " Menambahkan " . $partname . " sebanyak " . $quantity,
                'ip_address' => $request->ip(),
                'table' => "In HO Stock",
            ]);

            return response()->json(['success' => true, 'message' => 'In Stock added successfully!'], 201); // 201 Created
        } catch (\Exception $e) {
            DB::rollback(); // Rollback transaction
            return response()->json(['success' => false, 'message' => 'Failed to add In Stock: ' . $e->getMessage()], 201);
        }
    }
}
