<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Concerns\HasUuids;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Part extends Model
{
    use HasFactory, HasUuids;

    protected $table = 'parts';
    protected $primaryKey = 'part_code';
    public $incrementing = false;
    protected $keyType = 'string';

    protected $fillable = [
        'part_code',
        'part_name',
        'bin_location',
        'part_type',
        'price',
        'purchase_price',
        'eum',
    ];

    public function withdrawals()
    {
        return $this->hasMany(PartWithdrawal::class, 'part_code', 'part_code');
    }

    public function partInventories()
    {
        return $this->hasMany(PartInventory::class, 'part_code', 'part_code');
    }

    public function mergedPartParts()
    {
        return $this->hasMany(MergedPartPart::class, 'part_code', 'part_code');
    }
    public function requisitionDetails()
    {
        return $this->hasMany(RequisitionDetail::class, 'part_code', 'part_code');
    }
    protected $casts = [
        'part_code' => 'string',
        'part_type' => 'string',
        'price' => 'float',
        'purchase_price' => 'float',
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
    ];

    public const PART_TYPES = ['AC', 'TYRE', 'FABRIKASI','PERLENGKAPAN AC','PERSEDIAAN LAINNYA'];
}