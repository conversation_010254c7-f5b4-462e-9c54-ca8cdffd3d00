<?php

namespace App\Http\Controllers;

use App\Models\DailyReport;
use App\Models\Job;
use App\Models\Technician;
use App\Models\Unit;
use PhpOffice\PhpSpreadsheet\IOFactory;

class DailyReportViewController extends Controller
{
public function show()
{
    $filePath = storage_path('app/public/testmei.xlsx');
    if (!file_exists($filePath)) {
        return "File not found: " . $filePath;
    }
    $spreadsheet = IOFactory::load($filePath);
    $sheet = $spreadsheet->getActiveSheet();
    $rows = $sheet->toArray();

    foreach ($rows as $index => $row) {
        if ($index === 0) continue; // skip header

        $jobs = $this->extractJobDescriptions($row[7]);  // kolom 8 (0-based index)
        $manpower = $this->extractJobDescriptions($row[12]);    // kolom 13 (0-based index)

        $data = [
            'tanggal' => $row[1],
            'unit' => $row[2],
            'hm' => $row[3],
            'problem' => $row[4],
            'problem component' => $row[5],
            'problem description' => $row[6],
            'start' => $row[8],
            'finish' => $row[9],
            'shift' => $row[11],
            'jobs' => $jobs,
            'manpower' => $manpower,
        ];

        echo '<pre>';
        print_r($data);
        echo '</pre><br>';

        try {
            $unitid = Unit::where('unit_codes', $row[2])->first()->unit_id;
            $dailyReport = DailyReport::create([
                'unit_id' => $unitid,
                'hm' => $row[3],
                'problem' => $row[4],
                'problem_component' => $row[5],
                'problem_description' => $row[6],
                'date_in' => $row[1],
                'hour_in' => $row[8],
                'hour_out' => $row[9],
                'shift' => $row[11],
            ]);
            $jobIds = [];
            foreach ($jobs as $jobData) {
                $job = Job::where('job_description', $jobData['description'])->first();
                if (!$job) {
                    $job = Job::create([
                        'job_description' => $jobData['description'],
                        'highlight' => isset($jobData['highlight']) && $jobData['highlight'] == '1',
                    ]);
                }
                $jobIds[] = $job->job_description_id;
            }
            $dailyReport->jobs()->attach($jobIds);
            $technicianIds = [];
            foreach ($manpower as $technicianData) {
                $technician = Technician::where('name', $technicianData['name'])->first();
                if (!$technician) {
                    $technician = Technician::create([
                        'name' => $technicianData['name'],
                    ]);
                }
                $technicianIds[] = $technician->technician_id;
            }
            $dailyReport->technicians()->attach($technicianIds);
        } catch (\Exception $e) {
           continue;
        }
    }
}

    private function extractJobDescriptions($rawText)
    {
        if (!is_string($rawText)) return [];

        // Ganti newline/tab/karakter aneh jadi spasi
        $text = str_replace(["\n", "\r", "\t"], ' ', $rawText);
        $text = preg_replace('/[^\x20-\x7E\xA0-\xFF]/u', '', $text);

        // Ganti delimiter ke '|'
        $text = preg_replace('/[\-*•\.]\s*/', '|', $text); // tangani simbol
        $text = preg_replace('/\s+/', ' ', $text); // rapikan spasi

        // Pisah berdasarkan '|'
        $items = explode('|', $text);

        // Bersihkan per item
        $cleaned = [];
        foreach ($items as $item) {
            $item = trim($item);

            // Jika masih ada titik gabung nama seperti "Yusup . Iyus"
            if (preg_match('/[a-zA-Z]\s*\.\s*[a-zA-Z]/', $item)) {
                $item = str_replace('.', '|', $item);
                $parts = explode('|', $item);
                foreach ($parts as $p) {
                    $p = ucfirst(trim($p));
                    if (!empty($p)) {
                        $cleaned[] = $p;
                    }
                }
            } else {
                $item = preg_replace('/^[\-\*\•\.\s]+/', '', $item); // hapus simbol awal
                $item = ucfirst(trim($item));
                if (!empty($item)) {
                    $cleaned[] = $item;
                }
            }
        }
        return $cleaned;
    }
}
