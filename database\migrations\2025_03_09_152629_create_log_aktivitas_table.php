<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    public function up(): void
    {
        Schema::create('log_aktivitas', function (Blueprint $table) {
            $table->id();
            $table->string('name');
            $table->string('site_id', 50)->nullable(); // Changed to string
            $table->foreign('site_id')
            ->references('site_id')
            ->on('sites');
            $table->string('action');
            $table->text('decription')->nullable();
            $table->text('table')->nullable();
            $table->string('ip_address', 45);
            $table->timestamps();
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('log_aktivitas');
    }
};
