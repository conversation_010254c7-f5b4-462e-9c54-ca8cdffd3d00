<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class WarehouseOutStock extends Model
{
    use HasFactory;

    protected $table = 'warehouse_out_stocks';
    protected $primaryKey = 'warehouse_out_stock_id';

    protected $fillable = [
        'part_inventory_id',
        'employee_id',
        'price',
        'date_out',
        'quantity',
        'status',
        'notes',
        'destination_type',
        'destination_id',
    ];

    protected $casts = [
        'date_out' => 'datetime',
        'quantity' => 'float',
    ];

    public function getDestinationNameAttribute()
    {
        $destination = $this->destination();
        if ($destination instanceof Site) {
            return $destination->site_name;
        } elseif ($destination instanceof Customer) {
            return $destination->customer_name;
        } else {
            return 'Unknown Destination'; // Atau nilai default lainnya
        }
    }

    public function partInventory()
    {
        return $this->belongsTo(PartInventory::class, 'part_inventory_id', 'part_inventory_id');
    }

    public function user()
    {
        return $this->belongsTo(User::class, 'employee_id', 'employee_id');
    }

    public function site()
    {
        return $this->belongsTo(Site::class, 'destination_id', 'site_id'); // Pastikan 'site_id' benar
    }

    public function customer()
    {
        return $this->belongsTo(Customer::class, 'destination_id', 'customer_id'); // Sesuaikan 'customer_id' jika perlu
    }

    public function destination()
    {
        $site = Site::find($this->destination_id);
        $customer = Customer::find($this->destination_id);

        if ($site) {
            return $site;
        } elseif ($customer) {
            return $customer;
        } else {
            return null; // Atau berikan nilai default jika perlu
        }
    }
}
