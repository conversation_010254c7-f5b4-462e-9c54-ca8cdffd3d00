<?php

namespace App\Http\Controllers;

use App\Models\Site;
use Illuminate\Http\Request;
use App\Models\User;
use Illuminate\Support\Facades\Hash;
use App\Helpers\LogHelper;

class UserController extends Controller
{
    public function index()
    {
        $user = User::with('site')->get();
        return response()->json($user); // Return users as JSON
    }

    public function create()
    {
        $users = User::all();
        $sites = Site::all();
        return view("users.create", compact('users','sites'));
    }

    public function store(Request $request)
    {
        $validated = $request->validate([
            'employee_id' => 'required|unique:users,employee_id',
            'site_id' => 'nullable|exists:sites,site_id',
            'name' => 'required',
            'username' => 'required|unique:users,username',
            'password' => 'required',
            'email' => 'nullable|email|unique:users,email',
            'role' => 'required|in:adminho,adminsite,sales,karyawan',
        ]);

        $user = new User();
        $user->employee_id = $validated['employee_id'];
        $user->site_id = $validated['site_id'];
        $user->name = $validated['name'];
        $user->username = $validated['username'];
        $user->password = Hash::make($validated['password']);
        $user->email = $validated['email'];
        $user->role = $validated['role'];
        $user->save();

        // Log the user creation
        LogHelper::logCreate('User', $validated['name'] . ' (' . $validated['username'] . ')', 'Users', $request);

        return response()->json(['success' => 'User created successfully.']);
    }

    public function show($employee_id)
    {
        $user = User::findOrFail($employee_id);
        return response()->json($user);
    }

    public function update(Request $request, $employee_id)
    {
        $user = User::findOrFail($employee_id);

        $validated = $request->validate([
            'site_id' => 'nullable|exists:sites,site_id',
            'name' => 'required',
            'username' => 'required|unique:users,username,' . $user->employee_id . ',employee_id',
            'password' => 'sometimes|nullable',
            'email' => 'nullable|email|unique:users,email,' . $user->employee_id . ',employee_id',
            'role' => 'required|in:adminho,adminsite,sales,karyawan',
        ]);

        $user->site_id = $validated['site_id'];
        $user->name = $validated['name'];
        $user->username = $validated['username'];

        if (isset($validated['password'])) {
            $user->password = Hash::make($validated['password']);
        }

        $user->email = $validated['email'];
        $user->role = $validated['role'];

        // Track changes for logging
        $changes = [];
        if ($user->isDirty('name')) {
            $changes[] = "name: {$user->getOriginal('name')} → {$validated['name']}";
        }
        if ($user->isDirty('username')) {
            $changes[] = "username: {$user->getOriginal('username')} → {$validated['username']}";
        }
        if ($user->isDirty('role')) {
            $changes[] = "role: {$user->getOriginal('role')} → {$validated['role']}";
        }
        if ($user->isDirty('site_id')) {
            $changes[] = "site_id: {$user->getOriginal('site_id')} → {$validated['site_id']}";
        }
        if (isset($validated['password'])) {
            $changes[] = "password: [changed]";
        }

        $user->save();

        // Log the user update
        LogHelper::logUpdate('User', $user->name . ' (' . $user->username . ')', 'Users', $request, $changes);

        return response()->json(['success' => 'User updated successfully.']);
    }

    public function destroy(Request $request, $employee_id)
    {
        $user = User::findOrFail($employee_id);

        // Log the user deletion before deleting
        LogHelper::logDelete('User', $user->name . ' (' . $user->username . ')', 'Users', $request);

        $user->delete();
        return response()->json(['success' => 'User deleted successfully.']);
    }
}