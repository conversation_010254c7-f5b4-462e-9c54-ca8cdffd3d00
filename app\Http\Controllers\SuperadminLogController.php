<?php

namespace App\Http\Controllers;

use App\Models\LogAktivitas;
use App\Models\Site;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Session;

class SuperadminLogController extends Controller
{
    // Default PIN for superadmin log access
    private const DEFAULT_PIN = '20212021';

    /**
     * Show the PIN verification form
     */
    public function showPinForm()
    {
        return view('superadmin.log.pin');
    }

    /**
     * Verify the PIN and grant access to logs
     */
    public function verifyPin(Request $request)
    {
        $request->validate([
            'pin' => 'required|string',
        ]);

        if ($request->pin === self::DEFAULT_PIN) {
            // Store PIN verification in session
            Session::put('superadmin_log_access', true);

            // Tidak perlu mencatat log akses

            return redirect()->route('superadmin.log.index');
        }

        return back()->withErrors(['pin' => 'PIN tidak valid.']);
    }

    /**
     * Show the log activity page
     */
    public function index()
    {
        // Check if PIN has been verified
        if (!Session::get('superadmin_log_access')) {
            return redirect()->route('superadmin.log.pin');
        }

        $sites = Site::all();
        return view('superadmin.log.index', compact('sites'));
    }

    /**
     * Get log data with filters
     */
    public function getData(Request $request)
    {
        // Check if PIN has been verified
        if (!Session::get('superadmin_log_access')) {
            return response()->json(['error' => 'Unauthorized'], 401);
        }

        $siteId = $request->input('site_id');
        $search = $request->input('search');
        $startDate = $request->input('start_date');
        $endDate = $request->input('end_date');
        $page = $request->input('page', 1);
        $perPage = $request->input('per_page', 20);

        $query = LogAktivitas::query();

        // Apply site filter if provided and not 'all'
        if ($siteId && $siteId != 'all') {
            $query->where('site_id', $siteId);
        }

        // Apply date range filter
        if ($startDate && $endDate) {
            $query->whereDate('created_at', '>=', $startDate)
                  ->whereDate('created_at', '<=', $endDate);
        } else {
            // Default to today if no date range is provided
            $query->whereDate('created_at', now()->toDateString());
        }

        // Apply search filter
        if ($search) {
            $query->where(function($q) use ($search) {
                $q->where('name', 'like', "%{$search}%")
                  ->orWhere('action', 'like', "%{$search}%")
                  ->orWhere('description', 'like', "%{$search}%")
                  ->orWhere('table', 'like', "%{$search}%");
            });
        }

        // Order by most recent first
        $query->orderBy('created_at', 'desc');

        // Get total count for pagination
        $total = $query->count();

        // Get paginated results
        $logs = $query->skip(($page - 1) * $perPage)
            ->take($perPage)
            ->get()
            ->map(function ($log) {
                $log->formatted_created_at = $log->created_at->format('Y-m-d H:i:s');
                $log->site_name = $log->site ? $log->site->site_name : '-';
                return $log;
            });

        return response()->json([
            'data' => $logs,
            'current_page' => (int)$page,
            'per_page' => (int)$perPage,
            'last_page' => ceil($total / $perPage),
            'total' => $total
        ]);
    }

    /**
     * Clear the PIN verification from session
     */
    public function logout(Request $request)
    {
        Session::forget('superadmin_log_access');

        // Tidak perlu mencatat log logout

        return redirect()->route('superadmin.dashboard');
    }
}
