@extends('sites.content')
@section('contentsite')
@section('title', 'History Out Part')
<div class="row bgwhite page-title-box shadow-kit mb-1">
    <div class="col d-flex align-items-center" style="max-width: fit-content;">
        <button class="button-menu-mobile text-blue-500 btn-sm">
            <i style="font-size: 30px;" class="mdi mdi-menu"></i>
        </button>
        <div class="text">
            <p class="font-bold m-0">{{ session('name') }}</p>
        </div>
    </div>
    <div class="col">
        <ul class="list-unstyled topnav-menu float-right mb-0">
            <li class="dropdown notification-list">
                <a class="nav-link dropdown-toggle" href="#" data-bs-toggle="dropdown" aria-expanded="false">
                    <i class="mdi mdi-bell-outline noti-icon"></i>
                    <span class="badge badge-danger badge-pill float-right badgenotif"></span>
                </a>
                <div class="dropdown-menu dropdown-menu-end dropdown-lg">
                    <div class="dropdown-item noti-title bg-primary">
                        <h5 class="m-0 text-white d-flex justify-content-between align-items-center">
                            Notifikasi
                            <a href="javascript:void(0);" class="text-white" id="clear-all">
                                <small></small>
                            </a>
                        </h5>
                    </div>
                    <div class="noti-scroll" id="notification-list">
                        <!-- Notifications will be dynamically inserted here -->
                    </div>
                </div>
            </li>
        </ul>
    </div>
</div>
<div class="row p-1">
    <div class="col bgwhite shadow-kit">
        <div class=" p-4">
            <h1 class="h4 text-uppercase">Site Out-Stock Management</h1>
            <input type="hidden" id="site_id" value="{{ $site_id }}">
            <!-- Form pencarian & filter dalam satu baris -->
            <div class="d-flex flex-wrap align-items-end gap-3 mb-3">
                <div>
                    <label for="start_date" class="form-label">Start Date</label>
                    <input type="date" class="form-control" id="start_date">
                </div>
                <div>
                    <label for="end_date" class="form-label">End Date</label>
                    <input type="date" class="form-control" id="end_date">
                </div>
                <div>
                    <label for="search_input" class="form-label">Cari Nama / Code</label>
                    <input type="text" class="form-control" id="search_input" placeholder="Search by Part Name/Code">
                </div>
            </div>
            <div id="outstock-table-container">
                <table class="table table-bordered">
                    <thead class="table-dark text-white">
                        <tr>
                            <th class="p-2">Code Part</th>
                            <th class="p-2">Nama Part</th>
                            <th class="p-2">Tanggal</th>
                            <th class="p-2">Jumlah</th>
                            <th class="p-2">Nama Unit</th>
                            <th class="p-2">HM / KM</th>
                            <th class="p-2">Lokasi</th>
                            <th class="p-2">Status</th>
                            <th class="p-2">Detail</th>
                            <th class="p-2">Aksi</th>
                        </tr>
                    </thead>
                    <tbody>
                    </tbody>
                </table>
                <div id="pagination-container" class="mt-3">
                    <!-- Pagination will be rendered here by JavaScript -->
                </div>
            </div>
        </div>
    </div>
    <div class="col max500">
        <div class="shadow-kit bgwhite p-4">
            <h2 class="font-bold text-uppercase">Add Out-Stock</h2>
            <form id="create-form">
                <div class="mb-3">
                    <label for="part_code" class="form-label">Part Code</label>
                    <input type="text" class="form-control" id="part_code" autocomplete="off" required>
                    <div id="part_code_suggestions" class="autocomplete-suggestions hidden"></div>
                </div>
                <div class="mb-3">
                    <label for="unit_code" class="form-label">Unit</label>
                    <input type="text" class="form-control" id="unit_code" autocomplete="off">
                    <input type="hidden" id="unit_id">
                    <div id="unit_code_suggestions" class="autocomplete-suggestions hidden"></div>
                </div>
                <div class="mb-3">
                    <label for="quantity" class="form-label">Quantity</label>
                    <input type="number" class="form-control" id="quantity" autocomplete="off" step="0.1" min="0.1" required>
                </div>
                <div class="mb-3">
                    <label for="price" class="form-label">Harga</label>
                    <div class="input-group">
                        <input type="text" class="form-control" id="price" autocomplete="off" required>
                        <div class="input-group-append">
                            <span class="input-group-text">IDR</span>
                        </div>
                    </div>
                    <small class="form-text text-muted">Harga akan otomatis terisi saat memilih part</small>
                    <div class="mt-1">
                        <small class="text-muted">Format: <span id="formatted_create_price">Rp 0</span></small>
                    </div>
                </div>
                <div class="mb-3">
                    <label for="date_out" class="form-label">Date Out</label>
                    <input value="{{ date('Y-m-d') }}" type="date" class="form-control" id="date_out" autocomplete="off" required>
                </div>
                <div class="mb-3">
                    <label for="create_status" class="form-label">Status</label>
                    <select class="form-control" id="create_status">
                        <!-- <option value="out stock" selected>Out Stock</option> -->
                        <option value="Proses Out">Proses Out</option>
                        <option value="Proses Return">Proses Return</option>
                        <option value="Proses PO">Proses PO</option>
                        <option value="Proses MR">Proses MR</option>
                        <option value="Garansi">Garansi</option>
                        <option value="Done">Done</option>
                    </select>
                </div>
                <div id="po_wo_fields" style="display: none;">
                    <div class="mb-3">
                        <label for="po_number" class="form-label">PO Number</label>
                        <input type="text" class="form-control" id="po_number">
                    </div>
                    <div class="mb-3">
                        <label for="wo_number" class="form-label">WO Number</label>
                        <input type="text" class="form-control" id="wo_number">
                    </div>
                </div>
                <div class="mb-3">
                    <label for="notes" class="form-label">Keterangan <span class="text-blue-300">*Opsional</span></label>
                    <textarea class="form-control" id="notes" rows="3"></textarea>
                </div>
                <button type="submit" class="btn btn-primary">Create</button>
            </form>
        </div>
        <div class="row">
            <div class="col mt-2 ml-2">
                <div class="card shadow-kit bgwhite p-4 mb-0">
                    <h3>Total Out-Stocks</h3>
                    <p id="totalout" class="display-4 font-bold">0</p>
                </div>
            </div>
            <div class="col m-2">
                <div class="card shadow-kit bgwhite p-2 mb-0">
                    <h3>Pintasan Download Laporan</h3>
                    <div class="my-4 d-between">
                        <button class="btn btn-sm btn-primary report-btn" data-type="in">
                            <i class="mdi mdi-file-import mr-1"></i> Export In
                        </button>
                        <button class="btn btn-sm btn-success report-btn ml-2" data-type="out">
                            <i class="mdi mdi-file-export mr-1"></i> Export Out
                        </button>
                    </div>
                </div>
            </div>
        </div>
        <div class="col p-0">
            <div class="card shadow-kit bgwhite p-4 mt-2">
                <h3>Aktifitas Terakhir</h3>
                <hr class="mt-2">
                <ul id="activityList">
                    <!-- Data aktivitas akan dimuat di sini oleh JavaScript -->
                </ul>
            </div>
        </div>
    </div>
</div>

<div id="exportModal" class="modal" tabindex="-1" role="dialog" aria-labelledby="exportModalLabel" aria-hidden="true">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="font-bold text-uppercase" id="title">Filter Laporan</h5>
                <span class="close" aria-label="Close">&times;</span>
            </div>
            <form id="exportForm" method="GET">
                <input type="hidden" name="report_type" id="reportType">
                <div class="modal-body">
                    <div class="form-grid">
                        <div class="d-flex gap-3">
                            <div class="form-group flex-grow-1">
                                <label for="start_date">Tanggal Mulai</label>
                                <input type="date" class="form-control" name="start_date">
                            </div>
                            <div class="form-group flex-grow-1">
                                <label for="end_date">Tanggal Selesai</label>
                                <input type="date" class="form-control" name="end_date">
                            </div>
                        </div>
                        <div class="d-flex gap-3">
                            <div class="form-group flex-grow-1">
                                <label for="sort_by">Urutkan Berdasarkan</label>
                                <select class="custom-select" name="sort_by">
                                    <option value="date">Tanggal</option>
                                    <option value="part_name">Nama Part</option>
                                </select>
                            </div>
                            <div class="form-group flex-grow-1 hidden">
                                <label for="site_id">Site</label>
                                <select class="custom-select" name="site_id">
                                    <option value="{{ session('site_id') }}" selected>Semua Site</option>
                                </select>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <!-- <button type="button" class="btn btn-secondary" id="closeModal">Tutup</button> -->
                    <button type="button" class="btn btn-pink btn-rounded waves-effect waves-light" id="exportPdf">
                        <i class="mdi mdi-file-pdf-box mr-1"></i> Export PDF
                    </button>
                    <button type="button" class="btn btn-pink btn-rounded waves-effect waves-light" id="exportExcel">
                        <i class="mdi mdi-file-excel mr-1"></i> Export Excel
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Edit Status Modal -->
<div id="editStatusModal" class="modal" tabindex="-1" role="dialog" aria-labelledby="editStatusModalLabel" aria-hidden="true">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="font-bold text-uppercase">Edit Status Out Stock</h5>
                <span class="close edit-status-close" aria-label="Close">&times;</span>
            </div>
            <form id="editStatusForm">
                <input type="hidden" id="outstock_id">
                <div class="modal-body">
                    <div class="form-group">
                        <label for="status">Status</label>
                        <select class="form-control" id="status">
                            <!-- <option value="out stock">Out Stock</option> -->
                            <option value="Proses Out">Proses Out</option>
                            <option value="Proses Return">Proses Return</option>
                            <option value="Proses PO">Proses PO</option>
                            <option value="Proses MR">Proses MR</option>
                            <option value="Garansi">Garansi</option>
                            <option value="Done">Done</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <p><strong>Part Code:</strong> <span id="edit_part_code"></span></p>
                        <p><strong>Part Name:</strong> <span id="edit_part_name"></span></p>
                        <p><strong>Quantity:</strong> <span id="edit_quantity"></span></p>
                    </div>
                    <div class="form-group">
                        <label for="edit_unit_code">Unit</label>
                        <input type="text" class="form-control" id="edit_unit_code">
                        <input type="hidden" id="edit_unit_id">
                        <div id="edit_unit_code_suggestions" class="autocomplete-suggestions hidden"></div>
                    </div>
                    <div id="edit_po_wo_fields" style="display: none;">
                        <div class="form-group">
                            <label for="edit_po_number">PO Number</label>
                            <input type="text" class="form-control" id="edit_po_number">
                        </div>
                        <div class="form-group">
                            <label for="edit_wo_number">WO Number</label>
                            <input type="text" class="form-control" id="edit_wo_number">
                        </div>
                    </div>
                    <div class="form-group">
                        <label for="edit_price">Harga</label>
                        <div class="input-group">
                            <input type="text" class="form-control" id="edit_price" required>
                            <div class="input-group-append">
                                <span class="input-group-text">IDR</span>
                            </div>
                        </div>
                        <small class="form-text text-muted">Harga Terformat: <span id="formatted_edit_price"></span></small>
                    </div>
                    <div class="form-group">
                        <label for="edit_notes">Keterangan</label>
                        <textarea class="form-control" id="edit_notes" rows="3"></textarea>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary edit-status-close">Batal</button>
                    <button type="submit" class="btn btn-primary">Simpan</button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Detail Modal -->
<div id="detailModal" class="modal" tabindex="-1" role="dialog" aria-labelledby="detailModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <div>
                    <h5 class="font-bold text-uppercase mb-0">Detail Out Stock</h5>
                    <p class="text-muted mb-0" id="detail_title"></p>
                </div>
                <span class="close detail-close" aria-label="Close">&times;</span>
            </div>
            <div class="modal-body">
                <table class="table table-bordered table-detail">
                    <tbody>
                        <tr>
                            <th class="p-1" width="30%">Part Code</th>
                            <td class="p-1" id="detail_part_code"></td>
                        </tr>
                        <tr>
                            <th class="p-1">Part Name</th>
                            <td class="p-1" id="detail_part_name"></td>
                        </tr>
                        <tr>
                            <th class="p-1">Quantity</th>
                            <td class="p-1" id="detail_quantity"></td>
                        </tr>
                        <tr>
                            <th class="p-1">HM/KM</th>
                            <td class="p-1" id="detail_hmkm"></td>
                        </tr>
                        <tr>
                            <th class="p-1">Lokasi</th>
                            <td class="p-1" id="detail_lokasi"></td>
                        </tr>
                        <tr>
                            <th class="p-1">Unit</th>
                            <td class="p-1" id="detail_unit"></td>
                        </tr>
                        <tr>
                            <th class="p-1">Harga</th>
                            <td class="p-1" id="detail_price"></td>
                        </tr>
                        <tr>
                            <th class="p-1">PO Number</th>
                            <td class="p-1" id="detail_po_number"></td>
                        </tr>
                        <tr>
                            <th class="p-1">WO Number</th>
                            <td class="p-1" id="detail_wo_number"></td>
                        </tr>
                        <tr>
                            <th class="p-1">Created At</th>
                            <td class="p-1" id="detail_created_at"></td>
                        </tr>
                        <tr>
                            <th class="p-1">Status</th>
                            <td class="p-1" id="detail_status"></td>
                        </tr>
                        <tr>
                            <th class="p-1">Notes</th>
                            <td class="p-1" id="detail_notes"></td>
                        </tr>
                    </tbody>
                </table>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary detail-close">Tutup</button>
            </div>
        </div>
    </div>
</div>

<style>
    /* Detail button styling */
    .detail-btn {
        padding: 0.15rem 0.2rem;
        border-radius: 50%;
        width: 32px;
        height: 32px;
        display: inline-flex;
        align-items: center;
        justify-content: center;
        transition: all 0.3s ease;
    }
    .detail-btn:hover {
        transform: scale(1.1);
        box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
    }
    .detail-btn i {
        font-size: 16px;
    }
    /* Detail table styling */
    .table-detail {
        border-collapse: collapse;
        width: 100%;
    }
    .table-detail th {
        background-color: #f8f9fa;
        font-weight: 600;
        vertical-align: middle;
    }
    .table-detail td {
        padding: 0.75rem;
        vertical-align: middle;
    }
    .table-detail tr:nth-child(even) {
        background-color: rgba(0, 0, 0, 0.02);
    }

    /* Pagination styling */
    .pagination {
        display: flex;
        justify-content: center;
        list-style: none;
        padding: 0;
        margin-top: 15px;
    }
    .pagination .page-item {
        margin: 0 2px;
    }
    .pagination .page-item .page-link {
        color: #333;
        background-color: #fff;
        border: 1px solid #ddd;
        padding: 6px 12px;
        text-decoration: none;
        border-radius: 4px;
        cursor: pointer;
    }
    .pagination .page-item.active .page-link {
        color: #fff;
        background-color: #28a745;
        border-color: #28a745;
    }
    .pagination .page-item .page-link:hover {
        background-color: #f8f9fa;
    }
</style>
@endsection
@section('resourcesite')
@vite('resources/js/site/Outstocksite.js')
@vite('resources/js/laporan.js')
@endsection