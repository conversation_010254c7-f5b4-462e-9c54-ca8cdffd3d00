<?php

namespace App\Http\Controllers;

use App\Models\LogAktivitas;
use App\Models\Part;
use App\Models\PartInventory;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Validator;

class PartController extends Controller
{
    public function index(Request $request)
    {
        $search = $request->input('search');
        $typeFilter = $request->input('type');
        $partTypes = ['AC', 'TYRE', 'FABRIKASI', 'PERLENGKAPAN AC', 'PERSEDIAAN LAINNYA'];

        if ($request->ajax()) {
            return $this->getPartsData($request);
        }

        // Initial load - pass minimal data for the first render
        $parts = collect(); // Empty collection for initial load
        return view('warehouse.buatdaftarpart', compact('parts', 'partTypes', 'search', 'typeFilter'));
    }

    public function getPartsData(Request $request)
    {
        $search = $request->input('search');
        $typeFilter = $request->input('type');
        $page = $request->input('page', 1);
        $perPage = $request->input('per_page', 20);

        $query = Part::query();
        $query->when($search, function ($q) use ($search) {
            $q->where('part_code', 'like', "%$search%")
                ->orWhere('part_name', 'like', "%$search%");
        });
        $query->when($typeFilter, function ($q) use ($typeFilter) {
            $q->where('part_type', $typeFilter);
        });

        // Get total count for pagination
        $total = $query->count();

        // Get paginated data
        $parts = $query->orderBy('created_at', 'desc')
                      ->skip(($page - 1) * $perPage)
                      ->take($perPage)
                      ->get();

        return response()->json([
            'data' => $parts,
            'current_page' => (int)$page,
            'per_page' => (int)$perPage,
            'last_page' => ceil($total / $perPage),
            'total' => $total
        ]);
    }

    public function store(Request $request)
    {
        try {
            // Clean and prepare the part code
            $cleanPartCode = strtoupper(preg_replace('/\s+/', '', $request->input('part_code_display')));
            $cleanPartName = strtoupper($request->input('part_name'));

            // Log the received data for debugging
            Log::info('Part store request data:', [
                'part_code_display' => $request->input('part_code_display'),
                'part_name' => $request->input('part_name'),
                'bin_location' => $request->input('bin_location'),
                'part_type' => $request->input('part_type'),
                'price' => $request->input('price'),
                'eum' => $request->input('eum'),
                'cleaned_part_code' => $cleanPartCode,
                'cleaned_part_name' => $cleanPartName
            ]);

            $validator = Validator::make([
                'part_code' => $cleanPartCode,
                'part_name' => $cleanPartName,
                'bin_location' => $request->input('bin_location'),
                'part_type' => $request->input('part_type'),
                'price' => $request->input('price'),
                'eum' => $request->input('eum'),
            ], [
            'part_code' => [
                'required',
                'string',
                'unique:parts,part_code',
            ],
            'part_name' => [
                'required',
                'string',
                'max:255',
                'unique:parts,part_name',
            ],
            'bin_location' => 'nullable|string|max:50',
            'part_type' => 'required|in:' . implode(',', Part::PART_TYPES),
            'price' => 'nullable|numeric|min:0',
            'eum' => 'nullable|string|max:5',
        ], [
            'part_code.unique' => 'Kode part sudah terdaftar dalam sistem.',
            'part_name.unique' => 'Nama part sudah terdaftar dalam sistem.',
        ]);

            if ($validator->fails()) {
                Log::warning('Part validation failed:', ['errors' => $validator->errors()->toArray()]);
                return response()->json(['errors' => $validator->errors()], 422);
            }

            $part = new Part([
                'part_code' => $cleanPartCode,
                'part_name' => $cleanPartName,
                'bin_location' => $request->input('bin_location'),
                'part_type' => $request->input('part_type'),
                'price' => $request->input('price'),
                'eum' => $request->input('eum') ?? 'EA',
            ]);
            $part->save();

            Log::info('Part created successfully:', ['part_code' => $part->part_code]);

            // Create log activity directly
            LogAktivitas::create([
                'site_id' => session('site_id'),
                'name' => session('name'),
                'action' => 'Membuat Data Part',
                'description' => "Admin HO " . session('name') . " menambahkan Data Part: " . $cleanPartName,
                'table' => "Data Part",
                'ip_address' => $request->ip(),
            ]);

            return response()->json(['success' => true, 'message' => 'Part berhasil ditambahkan.', 'part' => $part]);
        } catch (\Exception $e) {
            Log::error('Error creating part:', ['error' => $e->getMessage(), 'trace' => $e->getTraceAsString()]);
            return response()->json(['success' => false, 'message' => 'Terjadi kesalahan: ' . $e->getMessage()], 500);
        }
    }

    public function update(Request $request, $part_code)
    {
        $part = Part::where('part_code', $part_code)->first();
        if (!$part) {
            return response()->json(['errors' => ['Part tidak ditemukan']]);
        }
        $validator = Validator::make($request->all(), [
            'part_name' => 'required|string|max:255',
            'bin_location' => 'nullable|string|max:50',
            'part_type' => 'nullable|in:' . implode(',', Part::PART_TYPES),
            'price' => 'nullable|numeric|min:0',
            'eum' => 'nullable|string|max:5',
        ]);
        if ($validator->fails()) {
            return response()->json(['errors' => $validator->errors()], 422);
        }
        $part->part_name = strtoupper($request->input('part_name'));
        $part->bin_location = $request->input('bin_location');
        $part->part_type = $request->input('part_type');
        $part->price = $request->input('price');
        $part->eum = $request->input('eum') ?? 'EA';
        $part->save();

        // Create log activity directly
        LogAktivitas::create([
            'site_id' => session('site_id'),
            'name' => session('name'),
            'action' => 'Mengubah Data Part',
            'description' => "Admin HO " . session('name') . " mengubah Data Part: " . strtoupper($request->input('part_name')),
            'table' => "Data Part",
            'ip_address' => $request->ip(),
        ]);

        return response()->json(['success' => true, 'message' => 'Part berhasil diperbarui.', 'part' => $part]);
    }
    public function destroy(Request $request, $part_code)
    {
        try {
            $relatedInventoryCount = PartInventory::where('part_code', $part_code)->count();
            if ($relatedInventoryCount > 0) {
                return response()->json(['success' => false, 'message' => 'Tidak bisa hapus data | ada relasi tabel']);
            }
            // ambil nama barang dulu
            $partname = Part::where('part_code', $part_code)->first()->part_name;

            $part = Part::where('part_code', $part_code)->firstOrFail();
            $part->delete();

            // Create log activity directly
            LogAktivitas::create([
                'site_id' => session('site_id'),
                'name' => session('name'),
                'action' => 'Hapus data Part',
                'description' => "Admin HO " . session('name') . " menghapus Part " . $partname,
                'table' => "Part",
                'ip_address' => $request->ip(),
            ]);

            return response()->json(['success' => true, 'message' => 'Part Berhasil Dihapus.', 'part_code' => $part_code]);
        } catch (\Throwable $th) {
            return response()->json(['success' => false, 'message' => 'Error : ' . $th->getMessage()]);
        }
    }
}
