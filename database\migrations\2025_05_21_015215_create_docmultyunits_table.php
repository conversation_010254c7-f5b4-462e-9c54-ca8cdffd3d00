<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('docmultyunits', function (Blueprint $table) {
            $table->id();
            $table->date('tanggalstart')->nullable();
            $table->date('tanggalend')->nullable();
            $table->string('noBA')->nullable();
            $table->json('unit_transaction_ids')->nullable();
            $table->string('site_id', 50);
            $table->foreign('site_id')
                ->references('site_id')
                ->on('sites')
                ->onUpdate('cascade');
            $table->string('document_path')->nullable();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('docmultyunits');
    }
};
