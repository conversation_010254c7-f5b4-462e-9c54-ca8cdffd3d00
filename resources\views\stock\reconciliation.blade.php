@extends('layouts.app')

@section('title', 'Stock Reconciliation')

@section('content')
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">Stock Reconciliation</h3>
                    <div class="card-tools">
                        @if($negativeStockParts->count() > 0)
                        <button type="button" class="btn btn-warning" id="fixAllBtn">
                            <i class="fas fa-tools"></i> Fix All Negative Stock
                        </button>
                        @endif
                    </div>
                </div>
                <div class="card-body">
                    @if($negativeStockParts->count() > 0)
                        <div class="alert alert-warning">
                            <h5><i class="icon fas fa-exclamation-triangle"></i> Perhatian!</h5>
                            Ditemukan {{ $negativeStockParts->count() }} part dengan stok negatif. Silakan perbaiki stok ini untuk memastikan sistem berjalan dengan benar.
                        </div>
                        <table class="table table-bordered table-striped">
                            <thead>
                                <tr>
                                    <th>Part Code</th>
                                    <th>Part Name</th>
                                    <th>Current Stock</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                @foreach($negativeStockParts as $part)
                                <tr>
                                    <td>{{ $part->part_code }}</td>
                                    <td>{{ $part->part->part_name }}</td>
                                    <td class="text-danger">{{ $part->stock_quantity }}</td>
                                    <td>
                                        <button type="button" class="btn btn-primary btn-sm fix-stock-btn" 
                                            data-id="{{ $part->part_inventory_id }}" 
                                            data-part-name="{{ $part->part->part_name }}" 
                                            data-current-stock="{{ $part->stock_quantity }}">
                                            <i class="fas fa-edit"></i> Fix Stock
                                        </button>
                                    </td>
                                </tr>
                                @endforeach
                            </tbody>
                        </table>
                    @else
                        <div class="alert alert-success">
                            <h5><i class="icon fas fa-check"></i> Bagus!</h5>
                            Tidak ada part dengan stok negatif. Semua stok dalam kondisi baik.
                        </div>
                    @endif
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Fix Stock Modal -->
<div class="modal fade" id="fixStockModal" tabindex="-1" role="dialog" aria-labelledby="fixStockModalLabel" aria-hidden="true">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="fixStockModalLabel">Fix Stock Quantity</h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="modal-body">
                <form id="fixStockForm">
                    <input type="hidden" id="part_inventory_id" name="part_inventory_id">
                    <div class="form-group">
                        <label for="part_name">Part Name</label>
                        <input type="text" class="form-control" id="part_name" readonly>
                    </div>
                    <div class="form-group">
                        <label for="current_stock">Current Stock</label>
                        <input type="text" class="form-control text-danger" id="current_stock" readonly>
                    </div>
                    <div class="form-group">
                        <label for="new_quantity">New Stock Quantity</label>
                        <input type="number" class="form-control" id="new_quantity" name="new_quantity" min="0" value="0" required>
                        <small class="form-text text-muted">Enter the correct stock quantity for this part.</small>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-dismiss="modal">Cancel</button>
                <button type="button" class="btn btn-primary" id="saveStockBtn">Save Changes</button>
            </div>
        </div>
    </div>
</div>

<!-- Confirm Fix All Modal -->
<div class="modal fade" id="confirmFixAllModal" tabindex="-1" role="dialog" aria-labelledby="confirmFixAllModalLabel" aria-hidden="true">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="confirmFixAllModalLabel">Confirm Fix All Negative Stock</h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="modal-body">
                <p>Anda akan mengubah semua stok negatif menjadi 0. Tindakan ini tidak dapat dibatalkan.</p>
                <p>Apakah Anda yakin ingin melanjutkan?</p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-dismiss="modal">Cancel</button>
                <button type="button" class="btn btn-warning" id="confirmFixAllBtn">Yes, Fix All</button>
            </div>
        </div>
    </div>
</div>
@endsection

@section('scripts')
<script>
    $(function() {
        // Fix individual stock
        $('.fix-stock-btn').click(function() {
            const id = $(this).data('id');
            const partName = $(this).data('part-name');
            const currentStock = $(this).data('current-stock');
            
            $('#part_inventory_id').val(id);
            $('#part_name').val(partName);
            $('#current_stock').val(currentStock);
            $('#new_quantity').val(0);
            
            $('#fixStockModal').modal('show');
        });
        
        // Save stock changes
        $('#saveStockBtn').click(function() {
            const partInventoryId = $('#part_inventory_id').val();
            const newQuantity = $('#new_quantity').val();
            
            $.ajax({
                url: '{{ route("stock.fix-negative") }}',
                type: 'POST',
                data: {
                    part_inventory_id: partInventoryId,
                    new_quantity: newQuantity,
                    _token: '{{ csrf_token() }}'
                },
                success: function(response) {
                    Swal.fire({
                        icon: 'success',
                        title: 'Berhasil',
                        text: 'Stock quantity has been updated successfully',
                        showConfirmButton: false,
                        timer: 1500
                    }).then(() => {
                        location.reload();
                    });
                },
                error: function(xhr) {
                    let errorMessage = 'Failed to update stock quantity';
                    if (xhr.responseJSON && xhr.responseJSON.error) {
                        errorMessage = xhr.responseJSON.error;
                    }
                    
                    Swal.fire({
                        icon: 'error',
                        title: 'Error',
                        text: errorMessage
                    });
                }
            });
            
            $('#fixStockModal').modal('hide');
        });
        
        // Fix all negative stock
        $('#fixAllBtn').click(function() {
            $('#confirmFixAllModal').modal('show');
        });
        
        // Confirm fix all
        $('#confirmFixAllBtn').click(function() {
            $.ajax({
                url: '{{ route("stock.fix-all-negative") }}',
                type: 'POST',
                data: {
                    _token: '{{ csrf_token() }}'
                },
                success: function(response) {
                    Swal.fire({
                        icon: 'success',
                        title: 'Berhasil',
                        text: `${response.count} parts with negative stock have been fixed`,
                        showConfirmButton: false,
                        timer: 1500
                    }).then(() => {
                        location.reload();
                    });
                },
                error: function(xhr) {
                    let errorMessage = 'Failed to fix negative stock quantities';
                    if (xhr.responseJSON && xhr.responseJSON.error) {
                        errorMessage = xhr.responseJSON.error;
                    }
                    
                    Swal.fire({
                        icon: 'error',
                        title: 'Error',
                        text: errorMessage
                    });
                }
            });
            
            $('#confirmFixAllModal').modal('hide');
        });
    });
</script>
@endsection
