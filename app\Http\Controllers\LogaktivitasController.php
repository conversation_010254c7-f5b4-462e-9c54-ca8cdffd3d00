<?php
namespace App\Http\Controllers;

use App\Models\LogAktivitas;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;

class LogaktivitasController extends Controller
{
    public function index(Request $request)
    {
        $siteId = session('site_id');
        $sites = DB::table('sites')->get();
        $logs = LogAktivitas::where('site_id', $siteId)
            ->whereDate('created_at', today())
            ->orderBy('created_at', 'desc')
            ->get();
        $logs = $logs->map(function ($log) {
            $log->formatted_created_at = $log->created_at->format('Y-m-d H:i:s');
            return $log;
        });

        return view('log_aktivitas.index', compact('logs', 'siteId', 'sites'));
    }

    public function search(Request $request)
    {
        $siteId = $request->input('site_id');
        $search = $request->input('search');
        $date = $request->input('date');
        $page = $request->input('page', 1);
        $perPage = $request->input('per_page', 20);

        // Get the logged-in user's site_id from session
        $loggedInSiteId = session('site_id');

        $query = LogAktivitas::query();

        // If site_id is not specified or is 'all', and the user is not an admin (has a site_id),
        // then default to showing only logs from the user's site
        if (($siteId == 'all' || !$siteId) && $loggedInSiteId) {
            $query->where('site_id', $loggedInSiteId);
        } elseif ($siteId && $siteId != 'all') {
            $query->where('site_id', $siteId);
        }
        if ($search) {
            $query->where(function ($query) use ($search) {
                $query->where('name', 'like', "%{$search}%")
                    ->orWhere('action', 'like', "%{$search}%")
                    ->orWhere('decription', 'like', "%{$search}%")
                    ->orWhere('table', 'like', "%{$search}%")
                    ->orWhere('ip_address', 'like', "%{$search}%");
            });
        }
        // Default to today's date if no date is provided
        if ($date) {
            $query->whereDate('created_at', $date);
        } else {
            $query->whereDate('created_at', now()->toDateString());
        }
        $query->orderBy('created_at', 'desc');
        $total = $query->count();
        $logs = $query->skip(($page - 1) * $perPage)
            ->take($perPage)
            ->get()
            ->map(function ($log) {
                $log->formatted_created_at = $log->created_at->format('Y-m-d H:i:s');
                return $log;
            });
        return response()->json([
            'data' => $logs,
            'current_page' => (int)$page,
            'per_page' => (int)$perPage,
            'last_page' => ceil($total / $perPage),
            'total' => $total
        ]);
    }

    public function logsite()
    {
        $site_id = session('site_id');
        $statuses = ['Menambahkan', 'Mengubah', 'Menghapus', 'Login', 'Logout'];
        return view('log_aktivitas.logsite', compact('site_id', 'statuses'));
    }

    public function logsiteData(Request $request)
    {
        $site_id = session('site_id');
        $page = $request->input('page', 1);
        $perPage = $request->input('per_page', 10); // Default to 10 items per page
        $startDate = $request->input('start_date');
        $endDate = $request->input('end_date');
        $search = $request->input('search');
        $action = $request->input('action');

        $query = LogAktivitas::where('site_id', $site_id);

        // Filter by date range
        if ($startDate && $endDate) {
            $query->whereDate('created_at', '>=', $startDate)
                  ->whereDate('created_at', '<=', $endDate);
        } else {
            // Default to today if no date range is provided
            $query->whereDate('created_at', now()->toDateString());
        }

        // Filter by search term
        if ($search) {
            $query->where(function ($q) use ($search) {
                $q->where('name', 'like', "%{$search}%")
                  ->orWhere('action', 'like', "%{$search}%")
                  ->orWhere('decription', 'like', "%{$search}%")
                  ->orWhere('table', 'like', "%{$search}%")
                  ->orWhere('ip_address', 'like', "%{$search}%");
            });
        }

        // Filter by action type
        if ($action && $action !== 'all') {
            $query->where('action', 'like', "%{$action}%");
        }

        // Order by created_at in descending order
        $query->orderBy('created_at', 'desc');

        // Get total count for pagination
        $total = $query->count();

        // Get paginated data
        $logs = $query->skip(($page - 1) * $perPage)
                     ->take($perPage)
                     ->get()
                     ->map(function ($log) {
                         $log->formatted_created_at = $log->created_at->format('Y-m-d H:i:s');
                         return $log;
                     });

        return response()->json([
            'data' => $logs,
            'current_page' => (int)$page,
            'per_page' => (int)$perPage,
            'last_page' => ceil($total / $perPage),
            'total' => $total
        ]);
    }

    public function getlast()
    {
        $site_id = session('site_id');
        $datalog =  LogAktivitas::where('site_id', $site_id)->orderBy('created_at', 'desc')->limit('3')->get();

        $datalog = $datalog->map(function ($log) {
            $log->formatted_created_at = $log->created_at->format('Y-m-d H:i:s');
            return $log;
        });
        return response()->json($datalog);
    }
}