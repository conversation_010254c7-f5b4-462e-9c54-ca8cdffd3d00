<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Try to drop the unique constraint directly
        try {
            DB::statement("ALTER TABLE invoice_unit_transactions DROP INDEX invoice_unit_transactions_unit_transaction_id_unique");
        } catch (\Exception $e) {
            // If it fails, the constraint might not exist, which is fine
            // Log the error for debugging
            Log::info('Error dropping unique constraint: ' . $e->getMessage());
        }
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // We don't want to add the constraint back
    }
};
