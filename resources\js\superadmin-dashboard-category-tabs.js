/**
 * Category tabs functionality for the superadmin dashboard
 * Handles switching between different part type categories
 */
document.addEventListener('DOMContentLoaded', function() {
    // Initialize category tabs
    initCategoryTabs();

    // Initialize part type detail modal
    initPartTypeDetailModal();

    // Initialize best parts settings
    initBestPartsSettings();
});

/**
 * Initialize category tabs functionality
 */
function initCategoryTabs() {
    const categoryTabs = document.querySelectorAll('.category-tab');

    categoryTabs.forEach(tab => {
        tab.addEventListener('click', function() {
            const category = this.getAttribute('data-category');

            // Remove active class and border from all tabs
            categoryTabs.forEach(t => {
                t.classList.remove('active');
                t.style.borderBottom = 'none';
            });

            // Add active class to clicked tab
            this.classList.add('active');

            // Set border color based on category
            if (category === 'AC') {
                this.style.borderBottom = '2px solid #28a745';
            } else if (category === 'TYRE') {
                this.style.borderBottom = '2px solid #58c0f6';
            } else if (category === 'FABRIKASI') {
                this.style.borderBottom = '2px solid #eb3124';
            }

            // Hide all category panes
            document.querySelectorAll('.category-pane').forEach(pane => {
                pane.style.display = 'none';
            });

            // Show the selected category pane
            const selectedPane = document.getElementById(`category-${category}`);
            if (selectedPane) {
                selectedPane.style.display = 'block';
            }
        });
    });
}

/**
 * Initialize part type detail modal
 */
function initPartTypeDetailModal() {
    const revenueCards = document.querySelectorAll('.revenue-card');

    revenueCards.forEach(card => {
        card.addEventListener('click', function() {
            const partType = this.getAttribute('data-part-type');
            const typeName = this.getAttribute('data-type-name');

            // Set modal title and icon
            document.getElementById('part-type-name').textContent = typeName;

            // Set icon based on part type
            const iconElement = document.getElementById('part-type-icon').querySelector('i');
            if (partType === 'AC') {
                iconElement.className = 'mdi mdi-air-conditioner';
                document.getElementById('part-type-icon').style.backgroundColor = '#28a745';
                document.getElementById('part-type-revenue').style.color = '#28a745';
            } else if (partType === 'TYRE') {
                iconElement.className = 'ion ion-md-aperture';
                document.getElementById('part-type-icon').style.backgroundColor = '#58c0f6';
                document.getElementById('part-type-revenue').style.color = '#58c0f6';
            } else if (partType === 'FABRIKASI') {
                iconElement.className = 'mdi mdi-factory';
                document.getElementById('part-type-icon').style.backgroundColor = '#eb3124';
                document.getElementById('part-type-revenue').style.color = '#eb3124';
            }

            // Get the revenue amount from the card
            const revenueElement = this.querySelector('.flex-grow-1 h4');
            if (revenueElement) {
                document.getElementById('part-type-revenue').textContent = revenueElement.textContent;
            }

            // Populate table with part details
            populatePartTypeDetailTable(partType);
        });
    });
}

/**
 * Populate part type detail table with data
 * @param {string} partType - The part type (AC, TYRE, FABRIKASI)
 */
function populatePartTypeDetailTable(partType) {
    const tableBody = document.getElementById('partTypeDetailTableBody');
    const emptyState = document.getElementById('part-type-empty-state');

    // Get the data from the corresponding category pane
    const categoryPane = document.getElementById(`category-${partType}`);
    if (!categoryPane) {
        return;
    }

    // Check if there's data available
    const hasData = categoryPane.querySelector('table tbody tr') !== null;

    if (hasData) {
        // Show table and hide empty state
        document.querySelector('#partTypeDetailModal .table-responsive').style.display = 'block';
        emptyState.style.display = 'none';

        // Clear existing rows
        tableBody.innerHTML = '';

        // Get all rows from the category pane table
        const rows = categoryPane.querySelectorAll('table tbody tr');

        // Add rows to the detail table
        rows.forEach((row, index) => {
            const cells = row.querySelectorAll('td');
            if (cells.length >= 4) {
                const newRow = document.createElement('tr');

                // Add cells to the new row
                newRow.innerHTML = `
                    <td>${index + 1}</td>
                    <td>${cells[0].textContent.split(' - ')[0] || ''}</td>
                    <td>${cells[0].textContent.split(' - ')[1] || cells[0].textContent}</td>
                    <td>${cells[1].textContent}</td>
                    <td>${cells[2].textContent}</td>
                    <td>${cells[3].textContent}</td>
                `;

                tableBody.appendChild(newRow);
            }
        });
    } else {
        // Hide table and show empty state
        document.querySelector('#partTypeDetailModal .table-responsive').style.display = 'none';
        emptyState.style.display = 'block';

        // Set empty state icon based on part type
        const emptyIcon = document.getElementById('part-type-empty-icon');
        if (partType === 'AC') {
            emptyIcon.className = 'mdi mdi-air-conditioner';
            emptyIcon.style.color = '#28a745';
        } else if (partType === 'TYRE') {
            emptyIcon.className = 'ion ion-md-aperture';
            emptyIcon.style.color = '#58c0f6';
        } else if (partType === 'FABRIKASI') {
            emptyIcon.className = 'mdi mdi-factory';
            emptyIcon.style.color = '#eb3124';
        }
    }
}

/**
 * Initialize best parts settings
 */
function initBestPartsSettings() {
    const saveButton = document.getElementById('saveBestPartsSettings');
    if (!saveButton) return;

    saveButton.addEventListener('click', function() {
        const limit = document.getElementById('bestPartsLimit').value;
        const sortBy = document.querySelector('input[name="bestPartsSortBy"]:checked').value;

        // Save settings via AJAX
        fetch('/superadmin/save-best-parts-settings', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
            },
            body: JSON.stringify({
                limit: limit,
                sort_by: sortBy
            })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                // Reload the best parts data
                location.reload();
            }
        })
        .catch(error => {
            console.error('Error saving settings:', error);
        });
    });
}
