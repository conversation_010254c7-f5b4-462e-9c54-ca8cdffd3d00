import Swal from "sweetalert2";

document.addEventListener("DOMContentLoaded", function () {
    // DOM Elements
    const partNameInput = document.getElementById("part_name");
    const combinedNameSuggestionsContainer = document.getElementById("combinedNameSuggestions");
    const newCodePartInput = document.getElementById("new_code_part");
    const itemInput = document.getElementById("itemInput");
    const itemInput2 = document.getElementById("jumlah");
    const suggestionsContainer = document.getElementById("suggestions");
    const queueTableBody = document.getElementById("queue-table-body");
    const partTypeFilter = document.getElementById("partTypeFilter");

    // Pagination elements
    const paginationContainer = document.getElementById("part-merge-pagination");

    // State variables
    let partQueue = [];
    let currentPage = 1;
    const itemsPerPage = 15; // 15 items per page

    // Initialize the form
    function initializeForm() {
        // Set up event listeners for Enter key in search fields
        itemInput.addEventListener('keypress', function(e) {
            if (e.key === 'Enter') {
                e.preventDefault();
                if (itemInput.value.trim() !== '' && itemInput.dataset.code) {
                    itemInput2.focus();
                }
            }
        });

        itemInput2.addEventListener('keypress', function(e) {
            if (e.key === 'Enter') {
                e.preventDefault();
                if (itemInput2.value.trim() !== '') {
                    addToQueue();
                }
            }
        });

        // Clear initial empty row in queue table
        updateQueueTable();
    }

    // Function to create a styled suggestion item
    function createSuggestionItem(item, container, onClickCallback) {
        const suggestionItem = document.createElement("div");
        suggestionItem.classList.add("suggestion-item");

        // Create main text element
        const mainText = document.createElement("div");
        mainText.classList.add("suggestion-main-text");
        mainText.textContent = item.value;
        suggestionItem.appendChild(mainText);

        // Add part type if available
        if (item.type) {
            const typeText = document.createElement("div");
            typeText.classList.add("suggestion-type");
            typeText.textContent = item.type;
            suggestionItem.appendChild(typeText);
        }

        // Add stock info if available
        if (item.stock !== undefined) {
            const stockText = document.createElement("div");
            stockText.classList.add("suggestion-stock");
            stockText.textContent = `Stock: ${item.stock}`;
            suggestionItem.appendChild(stockText);
        }

        // Add click event
        suggestionItem.addEventListener("click", () => onClickCallback(item));

        // Add hover effect
        suggestionItem.addEventListener("mouseenter", () => {
            suggestionItem.classList.add("suggestion-item-hover");
        });

        suggestionItem.addEventListener("mouseleave", () => {
            suggestionItem.classList.remove("suggestion-item-hover");
        });

        container.appendChild(suggestionItem);
        return suggestionItem;
    }

    function showCombinedNameSuggestions(input) {
        if (input.trim() === "") {
            combinedNameSuggestionsContainer.innerHTML = "";
            combinedNameSuggestionsContainer.style.display = "none";
            return;
        }

        // Show loading indicator
        combinedNameSuggestionsContainer.innerHTML = "<div class='suggestion-loading'>Mencari...</div>";
        combinedNameSuggestionsContainer.style.display = "block";

        // Get the current part type filter value
        const partType = partTypeFilter ? partTypeFilter.value : 'All';
        document.getElementById('current-filter-type').textContent = partTypeFilter ? partTypeFilter.options[partTypeFilter.selectedIndex].text : 'Semua Jenis Part';

        // Send AJAX request
        const xhr = new XMLHttpRequest();
        xhr.open("GET", `/part-merge/combined-name-autocomplete?term=${input}&part_type=${partType}`);
        xhr.timeout = 10000; // 10 second timeout

        xhr.onload = function () {
            if (xhr.status >= 200 && xhr.status < 300) {
                try {
                    const data = JSON.parse(xhr.responseText);

                    // Clear old suggestions
                    combinedNameSuggestionsContainer.innerHTML = "";

                    if (data.length === 0) {
                        combinedNameSuggestionsContainer.innerHTML = "<div class='suggestion-no-results'>Tidak ada hasil</div>";
                        return;
                    }

                    // Filter out parts already in the queue
                    const filteredData = data.filter((item) => {
                        return (
                            !partQueue.some((part) => part.code === item.id) &&
                            item.id !== newCodePartInput.value
                        );
                    });
                    if (filteredData.length === 0) {
                        combinedNameSuggestionsContainer.innerHTML = "<div class='suggestion-no-results'>Semua part sudah ada di antrian</div>";
                        return;
                    }

                    // Display suggestions
                    filteredData.forEach((item) => {
                        createSuggestionItem(item, combinedNameSuggestionsContainer, (selectedItem) => {
                            partNameInput.value = selectedItem.value;
                            newCodePartInput.value = selectedItem.id;
                            combinedNameSuggestionsContainer.style.display = "none";
                        });
                    });
                } catch (error) {
                    combinedNameSuggestionsContainer.innerHTML = "<div class='suggestion-error'>Terjadi kesalahan memproses data</div>";
                }
            } else {
                combinedNameSuggestionsContainer.innerHTML = "<div class='suggestion-error'>Gagal mendapatkan data</div>";
            }
        };

        xhr.ontimeout = function() {
            combinedNameSuggestionsContainer.innerHTML = "<div class='suggestion-error'>Permintaan timeout</div>";
        };

        xhr.onerror = function () {
            combinedNameSuggestionsContainer.innerHTML = "<div class='suggestion-error'>Terjadi kesalahan jaringan</div>";
        };

        xhr.send();
    }

    // Event saat mengetik di input "Nama Barang Gabungan"
    partNameInput.addEventListener("input", function () {
        //Renamed
        showCombinedNameSuggestions(this.value);
    });

    // Sembunyikan saran jika klik di luar "Nama Barang Gabungan"
    document.addEventListener("click", function (e) {
        if (
            !partNameInput.contains(e.target) && //Renamed
            !combinedNameSuggestionsContainer.contains(e.target)
        ) {
            combinedNameSuggestionsContainer.style.display = "none";
        }
    });

    // Function to show part suggestions for the first input field
    function showSuggestions(input) {
        if (input.trim() === "") {
            suggestionsContainer.innerHTML = "";
            suggestionsContainer.style.display = "none";
            return;
        }

        // Show loading indicator
        suggestionsContainer.innerHTML = "<div class='suggestion-loading'>Mencari...</div>";
        suggestionsContainer.style.display = "block";

        // Get the current part type filter value
        const partType = partTypeFilter ? partTypeFilter.value : 'All';
        document.getElementById('current-filter-type').textContent = partTypeFilter ? partTypeFilter.options[partTypeFilter.selectedIndex].text : 'Semua Jenis Part';

        // Send AJAX request
        const xhr = new XMLHttpRequest();
        xhr.open("GET", `/part-merge/autocomplete?term=${input}&part_type=${partType}`);
        xhr.timeout = 10000; // 10 second timeout

        xhr.onload = function () {
            if (xhr.status >= 200 && xhr.status < 300) {
                try {
                    const data = JSON.parse(xhr.responseText);

                    // Clear old suggestions
                    suggestionsContainer.innerHTML = "";

                    if (data.length === 0) {
                        suggestionsContainer.innerHTML = "<div class='suggestion-no-results'>Tidak ada hasil</div>";
                        return;
                    }

                    // Filter out parts already in the queue
                    const filteredData = data.filter((item) => {
                        return !partQueue.some((part) => part.code === item.id);
                    });
                    if (filteredData.length === 0) {
                        suggestionsContainer.innerHTML = "<div class='suggestion-no-results'>Semua part sudah ada di antrian</div>";
                        return;
                    }

                    // Display suggestions
                    filteredData.forEach((item) => {
                        createSuggestionItem(item, suggestionsContainer, (selectedItem) => {
                            itemInput.value = selectedItem.value;
                            itemInput.dataset.code = selectedItem.id;
                            itemInput.dataset.type = selectedItem.type;
                            itemInput.dataset.stock = selectedItem.stock;
                            suggestionsContainer.style.display = "none";

                            // Auto-focus quantity field after selecting a part
                            itemInput2.focus();
                        });
                    });
                } catch (error) {
                    suggestionsContainer.innerHTML = "<div class='suggestion-error'>Terjadi kesalahan memproses data</div>";
                }
            } else {
                suggestionsContainer.innerHTML = "<div class='suggestion-error'>Gagal mendapatkan data</div>";
            }
        };

        xhr.ontimeout = function() {
            suggestionsContainer.innerHTML = "<div class='suggestion-error'>Permintaan timeout</div>";
        };

        xhr.onerror = function () {
            suggestionsContainer.innerHTML = "<div class='suggestion-error'>Terjadi kesalahan jaringan</div>";
        };

        xhr.send();
    }

    // Event saat mengetik di input
    itemInput.addEventListener("input", function () {
        showSuggestions(this.value);
    });

    // Sembunyikan saran jika klik di luar
    document.addEventListener("click", function (e) {
        if (
            !itemInput.contains(e.target) &&
            !suggestionsContainer.contains(e.target)
        ) {
            suggestionsContainer.style.display = "none";
        }
    });

    // Function to add part to queue
    window.addToQueue = function () {
        const partName = itemInput.value;
        const jumlah = itemInput2.value;
        const partCode = itemInput.dataset.code;
        const partType = itemInput.dataset.type;
        const partStock = itemInput.dataset.stock;

        // Validate part selection
        if (!partName || !partCode) {
            Swal.fire({
                icon: 'warning',
                title: 'Perhatian',
                text: 'Pilih part terlebih dahulu!'
            });
            return;
        }

        // Validate quantity
        if (!jumlah || jumlah <= 0) {
            Swal.fire({
                icon: 'warning',
                title: 'Perhatian',
                text: 'Masukkan jumlah yang valid!'
            });
            return;
        }

        // Check if quantity exceeds available stock
        const stockQty = parseInt(partStock) || 0;
        if (stockQty > 0 && parseInt(jumlah) > stockQty) {
            Swal.fire({
                icon: 'warning',
                title: 'Perhatian',
                text: `Jumlah melebihi stok tersedia (${stockQty})!`
            });
            return;
        }

        // Check if part already exists in queue
        const existingPart = partQueue.find(part => part.code === partCode);
        if (existingPart) {
            Swal.fire({
                icon: 'warning',
                title: 'Perhatian',
                text: 'Part sudah ada di antrian!'
            });
            return;
        }

        // Add to queue
        partQueue.push({
            code: partCode,
            name: partName,
            jumlah: jumlah,
            type: partType,
            stock: partStock
        });

        // Update UI
        updateQueueTable();
        itemInput.value = "";
        itemInput2.value = "";
        itemInput.dataset.code = "";
        itemInput.dataset.type = "";
        itemInput.dataset.stock = "";

        // After adding to queue, refresh combined name suggestions
        showCombinedNameSuggestions(partNameInput.value);

        // Show success toast
        const Toast = Swal.mixin({
            toast: true,
            position: 'top-end',
            showConfirmButton: false,
            timer: 3000,
            timerProgressBar: true
        });

        Toast.fire({
            icon: 'success',
            title: 'Part berhasil ditambahkan ke antrian'
        });

        // Focus back on the search field
        itemInput.focus();
    };

    // Function to remove part from queue
    window.removeFromQueue = function (code) {
        Swal.fire({
            title: 'Konfirmasi',
            text: 'Apakah Anda yakin ingin menghapus part ini dari antrian?',
            icon: 'question',
            showCancelButton: true,
            confirmButtonText: 'Ya, Hapus',
            cancelButtonText: 'Batal',
            reverseButtons: true
        }).then((result) => {
            if (result.isConfirmed) {
                partQueue = partQueue.filter((part) => part.code !== code);
                updateQueueTable();
                // After removing from queue, refresh combined name suggestions
                showCombinedNameSuggestions(partNameInput.value);

                // Show feedback
                const Toast = Swal.mixin({
                    toast: true,
                    position: 'top-end',
                    showConfirmButton: false,
                    timer: 3000,
                    timerProgressBar: true
                });

                Toast.fire({
                    icon: 'success',
                    title: 'Part berhasil dihapus dari antrian'
                });
            }
        });
    };

    // Function to update queue table display
    function updateQueueTable() {
        queueTableBody.innerHTML = "";

        if (partQueue.length === 0) {
            const emptyRow = document.createElement("tr");
            emptyRow.innerHTML = `<td colspan="4" class="text-center text-muted">Belum ada part yang ditambahkan</td>`;
            queueTableBody.appendChild(emptyRow);
            return;
        }

        partQueue.forEach((part) => {
            const row = document.createElement("tr");
            row.innerHTML = `
                <td>${part.code}</td>
                <td>
                    <div class="d-flex flex-column">
                        <span class="fw-bold">${part.name}</span>
                        <small class="text-muted">${part.type || ''}</small>
                    </div>
                </td>
                <td>${part.jumlah}</td>
                <td>
                    <button class="btn btn-danger btn-sm" onclick="removeFromQueue('${part.code}')">
                        <i class="mdi mdi-delete"></i> Hapus
                    </button>
                </td>
            `;
            queueTableBody.appendChild(row);
        });

        // Add a summary row
        const totalRow = document.createElement("tr");
        totalRow.className = "table-light";
        totalRow.innerHTML = `
            <td colspan="2" class="text-end fw-bold">Total Item:</td>
            <td colspan="2" class="fw-bold">${partQueue.length} part</td>
        `;
        queueTableBody.appendChild(totalRow);
    }
    // Function to submit combined item
    window.submitCombinedItem = function () {
        const newPartCode = newCodePartInput.value;
        const newPartName = partNameInput.value;
        const notes = document.getElementById("notes").value;

        // Validate inputs
        if (partQueue.length === 0) {
            Swal.fire({
                icon: 'warning',
                title: 'Perhatian',
                text: 'Tambahkan part yang akan digabung terlebih dahulu!'
            });
            return;
        }

        if (!newPartCode || !newPartName) {
            Swal.fire({
                icon: 'warning',
                title: 'Perhatian',
                text: 'Pilih Nama barang gabungan terlebih dahulu!'
            });
            return;
        }

        // Show confirmation dialog
        Swal.fire({
            title: 'Konfirmasi',
            text: `Anda akan menggabungkan ${partQueue.length} part menjadi ${newPartName}. Lanjutkan?`,
            icon: 'question',
            showCancelButton: true,
            confirmButtonText: 'Ya, Gabungkan',
            cancelButtonText: 'Batal',
            reverseButtons: true
        }).then((result) => {
            if (result.isConfirmed) {
                // Get user information from hidden fields
                const userRole = document.getElementById('user_role').value;
                const siteId = document.getElementById('site_id').value;
                const employeeId = document.getElementById('employee_id').value;

                // Prepare data
                const data = {
                    old_parts: partQueue.map((part) => ({
                        code: part.code,
                        jumlah: part.jumlah,
                    })),
                    new_part_code: newPartCode,
                    new_part_name: newPartName,
                    notes: notes,
                    user_role: userRole,
                    site_id: siteId,
                    employee_id: employeeId,
                    _token: document.querySelector('meta[name="csrf-token"]').getAttribute("content"),
                };

                // Show loading state
                Swal.fire({
                    title: 'Memproses...',
                    text: 'Mohon tunggu sebentar',
                    allowOutsideClick: false,
                    allowEscapeKey: false,
                    didOpen: () => {
                        Swal.showLoading();
                    }
                });

                // Send request
                const xhr = new XMLHttpRequest();
                xhr.open("POST", "/part-merge/submit");
                xhr.setRequestHeader("Content-Type", "application/json");
                xhr.setRequestHeader("X-CSRF-TOKEN", data._token);

                xhr.onload = function () {
                    if (xhr.status >= 200 && xhr.status < 300) {
                        try {
                            const response = JSON.parse(xhr.responseText);

                            // Show success message
                            Swal.fire({
                                icon: 'success',
                                title: 'Berhasil!',
                                text: response.message || 'Part berhasil digabung!'
                            });

                            // Reset form and queue
                            partQueue = [];
                            updateQueueTable();
                            document.getElementById("part_name").value = "";
                            document.getElementById("notes").value = "";
                            newCodePartInput.value = "";

                            // Reload table data
                            loadTableData(1);
                        } catch (error) {
                            Swal.fire({
                                icon: 'error',
                                title: 'Terjadi Kesalahan',
                                text: 'Gagal memproses respons server'
                            });
                        }
                    } else {
                        try {
                            const errorResponse = JSON.parse(xhr.responseText);
                            Swal.fire({
                                icon: 'error',
                                title: 'Gagal',
                                text: errorResponse.message || 'Gagal menyimpan data'
                            });
                        } catch (error) {
                            Swal.fire({
                                icon: 'error',
                                title: 'Gagal',
                                text: 'Gagal menyimpan data. Silakan coba lagi.'
                            });
                        }
                    }
                };

                xhr.onerror = function () {
                    Swal.fire({
                        icon: 'error',
                        title: 'Kesalahan Jaringan',
                        text: 'Terjadi kesalahan jaringan. Silakan coba lagi.'
                    });
                };

                xhr.send(JSON.stringify(data));
            }
        });
    };

    // //REFRESH PAGE TABEL
    // function refreshTable() {
    //     const xhr = new XMLHttpRequest();
    //     xhr.open("GET", "/part-merge/show-merged-parts");
    //     xhr.onload = function () {
    //         if (xhr.status >= 200 && xhr.status < 300) {
    //             const data = JSON.parse(xhr.responseText);
    //             const tableBody = document.querySelector("#datatable tbody");
    //             tableBody.innerHTML = "";

    //             let rowCount = 1;
    //             let currentDate = null;
    //             let rowspan = 0;
    //             let index = 0;

    //             data.forEach((item, i) => {
    //                 const row = document.createElement("tr");
    //                 let firstRowInDateGroup = false;

    //                 // Calculate rowspan dynamically
    //                 if (item.tanggal !== currentDate) {
    //                     currentDate = item.tanggal;
    //                     rowspan = 0;
    //                     let j = i;
    //                     while (
    //                         j < data.length &&
    //                         data[j].tanggal === currentDate
    //                     ) {
    //                         rowspan++;
    //                         j++;
    //                     }
    //                     firstRowInDateGroup = true;
    //                 }

    //                 // Add the number column
    //                 if (firstRowInDateGroup) {
    //                     let tdNo = document.createElement("td");
    //                     tdNo.rowSpan = rowspan;
    //                     tdNo.textContent = rowCount++;
    //                     row.appendChild(tdNo);
    //                 }

    //                 if (firstRowInDateGroup) {
    //                     let tdNamaPartHasil = document.createElement("td");
    //                     tdNamaPartHasil.rowSpan = rowspan;
    //                     tdNamaPartHasil.textContent = item.nama_part_hasil;
    //                     row.appendChild(tdNamaPartHasil);
    //                 }

    //                 // Sub-part Name
    //                 let tdNamaSubPart = document.createElement("td");
    //                 tdNamaSubPart.textContent = item.nama_sub_part;
    //                 row.appendChild(tdNamaSubPart);

    //                 // Sub-part Quantity
    //                 let tdJumlahSubPart = document.createElement("td");
    //                 tdJumlahSubPart.textContent = item.jumlah_sub_part;
    //                 row.appendChild(tdJumlahSubPart);

    //                 // Note

    //                 // Date Column
    //                 if (firstRowInDateGroup) {
    //                     let tdTanggal = document.createElement("td");
    //                     tdTanggal.rowSpan = rowspan;
    //                     tdTanggal.textContent = new Date(
    //                         item.tanggal
    //                     ).toLocaleDateString();
    //                     row.appendChild(tdTanggal);
    //                 }
    //                 if (firstRowInDateGroup) {
    //                     let tdNote = document.createElement("td");
    //                     tdNote.rowSpan = rowspan;
    //                     tdNote.textContent = item.note;
    //                     row.appendChild(tdNote);
    //                 }

    //                 tableBody.appendChild(row);
    //                 index++;
    //             });
    //         } else {
    //             console.error(
    //                 "Error fetching data:",
    //                 xhr.status,
    //                 xhr.statusText
    //             );
    //         }
    //     };
    //     xhr.onerror = function () {
    //         console.error("Error fetching data:", xhr.status, xhr.statusText);
    //     };
    //     xhr.send();
    // }

    // Function to create pagination item
    function createPaginationItem(page, text, isActive = false) {
        const li = document.createElement('li');
        li.className = `page-item ${isActive ? 'active' : ''}`;

        const a = document.createElement('a');
        a.className = 'page-link';
        a.href = '#';
        a.textContent = text;
        a.dataset.page = page;
        a.addEventListener('click', (e) => {
            e.preventDefault();
            if (page !== '...') {
                loadTableData(parseInt(page));
            }
        });

        li.appendChild(a);
        return li;
    }

    // Function to render pagination
    function renderPagination(data) {
        if (!paginationContainer) return;

        paginationContainer.innerHTML = '';
        const ul = document.createElement('ul');
        ul.className = 'pagination pagination-rounded';

        // Previous page
        ul.appendChild(createPaginationItem(
            data.current_page > 1 ? data.current_page - 1 : 1,
            '«',
            false
        ));

        // First page
        ul.appendChild(createPaginationItem(1, '1', data.current_page === 1));

        // Ellipsis for many pages
        if (data.current_page > 3) {
            ul.appendChild(createPaginationItem('...', '...'));
        }

        // Pages around current
        for (let i = Math.max(2, data.current_page - 1); i <= Math.min(data.last_page - 1, data.current_page + 1); i++) {
            if (i === 1 || i === data.last_page) continue; // Skip first and last page as they're added separately
            ul.appendChild(createPaginationItem(i, i.toString(), i === data.current_page));
        }

        // Ellipsis for many pages
        if (data.current_page < data.last_page - 2) {
            ul.appendChild(createPaginationItem('...', '...'));
        }

        // Last page (if more than 1 page)
        if (data.last_page > 1) {
            ul.appendChild(createPaginationItem(data.last_page, data.last_page.toString(), data.current_page === data.last_page));
        }

        // Next page
        ul.appendChild(createPaginationItem(
            data.current_page < data.last_page ? data.current_page + 1 : data.last_page,
            '»',
            false
        ));

        paginationContainer.appendChild(ul);
    }

    // Function to load table data with pagination
    function loadTableData(page = 1) {
        currentPage = page;
        const partType = partTypeFilter ? partTypeFilter.value : 'All';
        document.getElementById('current-filter-type').textContent = partTypeFilter ? partTypeFilter.options[partTypeFilter.selectedIndex].text : 'Semua Jenis Part';

        // Show loading indicator
        const tableBody = document.querySelector("#datatable tbody");
        tableBody.innerHTML = '<tr><td colspan="6" class="text-center"><div class="spinner-border text-primary" role="status"></div><p>Loading data...</p></td></tr>';

        // Build URL with parameters
        const url = `/part-merge/show-merged-parts?page=${page}&per_page=${itemsPerPage}&part_type=${partType}`;

        const xhr = new XMLHttpRequest();
        xhr.open("GET", url);
        xhr.timeout = 30000; // 30 second timeout

        xhr.onload = function () {
            if (xhr.status >= 200 && xhr.status < 300) {
                try {
                    const response = JSON.parse(xhr.responseText);
                    const data = response.data;

                    tableBody.innerHTML = "";

                    if (data.length === 0) {
                        tableBody.innerHTML = '<tr><td colspan="6" class="text-center">Tidak ada data yang ditemukan</td></tr>';
                        // Clear pagination if no data
                        if (paginationContainer) paginationContainer.innerHTML = '';
                        return;
                    }

                    let rowCount = (response.current_page - 1) * response.per_page + 1;
                    let currentDate = null;
                    let rowspan = 0;
                    let firstRowInDateGroup = false;

                    let i = 0;
                    while (i < data.length) {
                        const item = data[i];
                        const row = document.createElement("tr");
                        firstRowInDateGroup = false;

                        // Calculate rowspan for grouped items
                        if (item.tanggal !== currentDate) {
                            currentDate = item.tanggal;
                            rowspan = 0;
                            let j = i;
                            while (j < data.length && data[j].tanggal === currentDate) {
                                rowspan++;
                                j++;
                            }
                            firstRowInDateGroup = true;
                        }

                        // Number column
                        if (firstRowInDateGroup) {
                            let tdNo = document.createElement("td");
                            tdNo.rowSpan = rowspan;
                            tdNo.textContent = rowCount++;
                            row.appendChild(tdNo);
                        }

                        // Combined part name column
                        if (firstRowInDateGroup) {
                            let tdNamaPartGabungan = document.createElement("td");
                            tdNamaPartGabungan.rowSpan = rowspan;
                            tdNamaPartGabungan.textContent = item.nama_part_hasil;
                            row.appendChild(tdNamaPartGabungan);
                        }

                        // Sub part name column
                        let tdNamaSubPart = document.createElement("td");
                        tdNamaSubPart.textContent = item.nama_sub_part;
                        row.appendChild(tdNamaSubPart);

                        // Sub part quantity column
                        let tdJumlahSubPart = document.createElement("td");
                        tdJumlahSubPart.textContent = item.jumlah_sub_part;
                        row.appendChild(tdJumlahSubPart);

                        // Date column
                        if (firstRowInDateGroup) {
                            let tdTanggal = document.createElement("td");
                            tdTanggal.rowSpan = rowspan;
                            tdTanggal.textContent = new Date(item.tanggal).toLocaleDateString();
                            row.appendChild(tdTanggal);
                        }

                        // Notes column
                        if (firstRowInDateGroup) {
                            let tdCatatan = document.createElement("td");
                            tdCatatan.rowSpan = rowspan;
                            tdCatatan.textContent = item.catatan || '-';
                            row.appendChild(tdCatatan);
                        }

                        tableBody.appendChild(row);
                        i++;
                    }

                    // Render pagination
                    renderPagination({
                        current_page: response.current_page,
                        last_page: response.last_page,
                        total: response.total,
                        per_page: response.per_page
                    });

                } catch (error) {
                    tableBody.innerHTML = '<tr><td colspan="6" class="text-center text-danger">Terjadi kesalahan memproses data</td></tr>';
                }
            } else {
                tableBody.innerHTML = '<tr><td colspan="6" class="text-center text-danger">Gagal memuat data</td></tr>';
            }
        };

        xhr.ontimeout = function() {
            tableBody.innerHTML = '<tr><td colspan="6" class="text-center text-danger">Permintaan timeout</td></tr>';
        };

        xhr.onerror = function () {
            tableBody.innerHTML = '<tr><td colspan="6" class="text-center text-danger">Terjadi kesalahan jaringan</td></tr>';
        };

        xhr.send();
    }

    // Handle part type filter change
    if (partTypeFilter) {
        partTypeFilter.addEventListener('change', function() {
            loadTableData(1); // Reset to first page when filter changes
        });
    }

    // Initialize the form and load table data
    initializeForm();
    loadTableData();

    // Add event listener for form reset
    document.addEventListener('keydown', function(e) {
        // Clear form with Escape key
        if (e.key === 'Escape') {
            if (document.activeElement === itemInput ||
                document.activeElement === itemInput2 ||
                document.activeElement === partNameInput) {
                e.preventDefault();
                itemInput.value = '';
                itemInput2.value = '';
                itemInput.dataset.code = '';
                itemInput.dataset.type = '';
                itemInput.dataset.stock = '';
                suggestionsContainer.style.display = 'none';
                combinedNameSuggestionsContainer.style.display = 'none';
            }
        }
    });

    // Refresh the table every 5 minutes (optional)
    // setInterval(() => loadTableData(currentPage), 300000);
});
